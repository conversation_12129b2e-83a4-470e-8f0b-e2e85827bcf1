# PawPumps Environment Configuration Template
# Copy this file to .env.local and fill in your values

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application Name and Branding
NEXT_PUBLIC_APP_NAME=PawPumps
NEXT_PUBLIC_APP_DESCRIPTION="Decentralized Trading Platform"
NEXT_PUBLIC_APP_VERSION=1.0.0

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3003
NEXT_PUBLIC_API_URL=http://localhost:3003/api

# Environment
NODE_ENV=development
NEXT_PUBLIC_ENVIRONMENT=development

# =============================================================================
# WEB3 & BLOCKCHAIN CONFIGURATION
# =============================================================================

# Wallet Connect Configuration
NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID=your_wallet_connect_project_id_here

# Alchemy API Configuration
NEXT_PUBLIC_ALCHEMY_API_KEY=your_alchemy_api_key_here
NEXT_PUBLIC_ALCHEMY_NETWORK=eth-mainnet

# Infura Configuration (Alternative to Alchemy)
NEXT_PUBLIC_INFURA_PROJECT_ID=your_infura_project_id_here
NEXT_PUBLIC_INFURA_PROJECT_SECRET=your_infura_project_secret_here

# Network Configuration
NEXT_PUBLIC_CHAIN_ID=1
NEXT_PUBLIC_NETWORK_NAME=mainnet
NEXT_PUBLIC_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/your_api_key

# Contract Addresses (Update with actual deployed contracts)
NEXT_PUBLIC_TOKEN_CONTRACT_ADDRESS=******************************************
NEXT_PUBLIC_GOVERNANCE_CONTRACT_ADDRESS=******************************************
NEXT_PUBLIC_TRADING_CONTRACT_ADDRESS=******************************************

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
DATABASE_URL=postgresql://username:password@localhost:5432/pawpumps
DATABASE_DIRECT_URL=postgresql://username:password@localhost:5432/pawpumps

# Redis Cache (Optional)
REDIS_URL=redis://localhost:6379

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# NextAuth.js Configuration
NEXTAUTH_URL=http://localhost:3003
NEXTAUTH_SECRET=your_nextauth_secret_here_minimum_32_characters

# JWT Secret
JWT_SECRET=your_jwt_secret_here_minimum_32_characters

# Admin Authentication
ADMIN_SECRET_KEY=your_admin_secret_key_here

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS=G-XXXXXXXXXX
NEXT_PUBLIC_MIXPANEL_TOKEN=your_mixpanel_token_here

# Error Tracking
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_here
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here

# Email Service (SendGrid, Mailgun, etc.)
EMAIL_FROM=<EMAIL>
SENDGRID_API_KEY=your_sendgrid_api_key_here
MAILGUN_API_KEY=your_mailgun_api_key_here
MAILGUN_DOMAIN=your_mailgun_domain_here

# SMS Service (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================

# CoinGecko API (for price data)
COINGECKO_API_KEY=your_coingecko_api_key_here

# CoinMarketCap API (alternative price data)
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key_here

# The Graph Protocol
NEXT_PUBLIC_GRAPH_API_URL=https://api.thegraph.com/subgraphs/name/your_subgraph

# IPFS Configuration
NEXT_PUBLIC_IPFS_GATEWAY=https://ipfs.io/ipfs/
PINATA_API_KEY=your_pinata_api_key_here
PINATA_SECRET_API_KEY=your_pinata_secret_api_key_here

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Development Tools
NEXT_PUBLIC_ENABLE_DEVTOOLS=true
NEXT_PUBLIC_ENABLE_MOCK_DATA=true
NEXT_PUBLIC_ENABLE_DEBUG_LOGS=true

# Testing Configuration
TEST_DATABASE_URL=postgresql://username:password@localhost:5432/pawpumps_test
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_BROWSER=chromium

# Performance Monitoring
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true
NEXT_PUBLIC_PERFORMANCE_BUDGET_JS=200000
NEXT_PUBLIC_PERFORMANCE_BUDGET_CSS=50000

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature Toggles
NEXT_PUBLIC_ENABLE_GOVERNANCE=true
NEXT_PUBLIC_ENABLE_TRADING=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_SOCIAL=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_MOBILE_APP=false

# Beta Features
NEXT_PUBLIC_ENABLE_BETA_FEATURES=false
NEXT_PUBLIC_ENABLE_EXPERIMENTAL_FEATURES=false

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Vercel Configuration
VERCEL_URL=your_vercel_url_here
VERCEL_ENV=development

# CDN Configuration
NEXT_PUBLIC_CDN_URL=https://cdn.pawpumps.com
NEXT_PUBLIC_ASSETS_URL=https://assets.pawpumps.com

# SSL Configuration
FORCE_HTTPS=false
SSL_CERT_PATH=/path/to/ssl/cert
SSL_KEY_PATH=/path/to/ssl/key

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Application Monitoring
NEXT_PUBLIC_ENABLE_MONITORING=true
DATADOG_API_KEY=your_datadog_api_key_here
NEW_RELIC_LICENSE_KEY=your_new_relic_license_key_here

# Log Level
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Health Check Configuration
HEALTH_CHECK_ENDPOINT=/api/health
HEALTH_CHECK_SECRET=your_health_check_secret_here

# =============================================================================
# RATE LIMITING & SECURITY
# =============================================================================

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_WINDOW_MS=60000

# CORS Configuration
CORS_ORIGIN=http://localhost:3003
CORS_CREDENTIALS=true

# Security Headers
ENABLE_SECURITY_HEADERS=true
CSP_REPORT_URI=https://your-csp-report-endpoint.com

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================

# Database Backup
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_URL=s3://your-backup-bucket

# =============================================================================
# SOCIAL MEDIA & MARKETING
# =============================================================================

# Social Media Links
NEXT_PUBLIC_TWITTER_URL=https://twitter.com/pawpumps
NEXT_PUBLIC_DISCORD_URL=https://discord.gg/pawpumps
NEXT_PUBLIC_TELEGRAM_URL=https://t.me/pawpumps
NEXT_PUBLIC_GITHUB_URL=https://github.com/pawpumps

# Marketing Tools
NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id_here
NEXT_PUBLIC_FACEBOOK_PIXEL_ID=your_facebook_pixel_id_here

# =============================================================================
# NOTES
# =============================================================================

# 1. Copy this file to .env.local for local development
# 2. Copy this file to .env.production for production deployment
# 3. Never commit actual API keys or secrets to version control
# 4. Use strong, unique secrets for production environments
# 5. Regularly rotate API keys and secrets
# 6. Use environment-specific values for different deployment stages
