(index):17  GET http://localhost:3004/_next/static/chunks/trading-interface.js net::ERR_ABORTED 404 (Not Found)
(anonymous) @ (index):17
loadNonCriticalResources @ (index):12
requestIdleCallback
(anonymous) @ (index):23
main-app.js?v=1751810083942:2271 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
webpack.js?v=1751810083942:727 Uncaught (in promise) TypeError: Cannot read properties of undefined (reading 'call')
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
    at eval (VM9899 layout-router.js:27:25)
    at (:3004/app-pages-browser)/node_modules/next/dist/client/components/layout-router.js (http://localhost:3004/_next/static/chunks/app-pages-internals.js:50:1)
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
    at __webpack_require__.t (webpack.js?v=1751810083942:115:38)
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
eval @ VM9899 layout-router.js:27
(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js @ app-pages-internals.js:50
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
__webpack_require__.t @ webpack.js?v=1751810083942:115
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!:9
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false! @ app-pages-internals.js:17
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
__webpack_exec__ @ app-pages-internals.js:199
(anonymous) @ app-pages-internals.js:200
__webpack_require__.O @ webpack.js?v=1751810083942:84
webpackJsonpCallback @ webpack.js?v=1751810083942:1413
(anonymous) @ main-app.js?v=1751810083942:9
webpack.js?v=1751810083942:727 Uncaught (in promise) TypeError: Cannot read properties of undefined (reading 'call')
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
    at eval (passlock.ts:10:41)
    at (:3004/app-pages-browser)/src/lib/passlock.ts (http://localhost:3004/_next/static/chunks/app/layout.js:5021:1)
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
    at eval (PasslockProvider.tsx:10:71)
    at (:3004/app-pages-browser)/src/components/Auth/PasslockProvider.tsx (http://localhost:3004/_next/static/chunks/app/layout.js:4823:1)
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
eval @ passlock.ts:10
(app-pages-browser)/./src/lib/passlock.ts @ layout.js:5021
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
eval @ PasslockProvider.tsx:10
(app-pages-browser)/./src/components/Auth/PasslockProvider.tsx @ layout.js:4823
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FPasslockProvider.tsx%22%2C%22ids%22%3A%5B%22PasslockProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FSiteLock.tsx%22%2C%22ids%22%3A%5B%22SiteLock%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=false!:3
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FPasslockProvider.tsx%22%2C%22ids%22%3A%5B%22PasslockProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FSiteLock.tsx%22%2C%22ids%22%3A%5B%22SiteLock%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=false! @ layout.js:1458
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
__webpack_exec__ @ layout.js:5071
(anonymous) @ layout.js:5072
__webpack_require__.O @ webpack.js?v=1751810083942:84
webpackJsonpCallback @ webpack.js?v=1751810083942:1413
(anonymous) @ main-app.js?v=1751810083942:9
webpack.js?v=1751810083942:727 Uncaught (in promise) TypeError: Cannot read properties of undefined (reading 'call')
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
    at eval (logger.ts:13:41)
    at (:3004/app-pages-browser)/src/lib/logger.ts (http://localhost:3004/_next/static/chunks/app/layout.js:5010:1)
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
    at eval (ErrorBoundary.tsx:10:69)
    at (:3004/app-pages-browser)/src/components/ErrorBoundary.tsx (http://localhost:3004/_next/static/chunks/app/layout.js:4856:1)
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
eval @ logger.ts:13
(app-pages-browser)/./src/lib/logger.ts @ layout.js:5010
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
eval @ ErrorBoundary.tsx:10
(app-pages-browser)/./src/components/ErrorBoundary.tsx @ layout.js:4856
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FPasslockProvider.tsx%22%2C%22ids%22%3A%5B%22PasslockProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FSiteLock.tsx%22%2C%22ids%22%3A%5B%22SiteLock%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=false!:7
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FPasslockProvider.tsx%22%2C%22ids%22%3A%5B%22PasslockProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FSiteLock.tsx%22%2C%22ids%22%3A%5B%22SiteLock%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=false! @ layout.js:1458
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
__webpack_exec__ @ layout.js:5071
(anonymous) @ layout.js:5072
__webpack_require__.O @ webpack.js?v=1751810083942:84
webpackJsonpCallback @ webpack.js?v=1751810083942:1413
(anonymous) @ main-app.js?v=1751810083942:9
webpack.js?v=1751810083942:727 Uncaught (in promise) TypeError: Cannot read properties of undefined (reading 'call')
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
    at eval (index.js:60:41)
    at (:3004/app-pages-browser)/node_modules/@rainbow-me/rainbowkit/dist/index.js (http://localhost:3004/_next/static/chunks/app/layout.js:84:1)
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
    at eval (WalletProvider.tsx:10:80)
    at (:3004/app-pages-browser)/src/components/WalletProvider.tsx (http://localhost:3004/_next/static/chunks/app/layout.js:4878:1)
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
eval @ index.js:60
(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/index.js @ layout.js:84
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
eval @ WalletProvider.tsx:10
(app-pages-browser)/./src/components/WalletProvider.tsx @ layout.js:4878
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FPasslockProvider.tsx%22%2C%22ids%22%3A%5B%22PasslockProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FSiteLock.tsx%22%2C%22ids%22%3A%5B%22SiteLock%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=false!:11
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FPasslockProvider.tsx%22%2C%22ids%22%3A%5B%22PasslockProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FAuth%2FSiteLock.tsx%22%2C%22ids%22%3A%5B%22SiteLock%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FDEV%20Projects%2FDC64%20Launcher%2Fsrc%2Fcomponents%2FWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=false! @ layout.js:1458
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
__webpack_exec__ @ layout.js:5071
(anonymous) @ layout.js:5072
__webpack_require__.O @ webpack.js?v=1751810083942:84
webpackJsonpCallback @ webpack.js?v=1751810083942:1413
(anonymous) @ main-app.js?v=1751810083942:9
error-boundary-callbacks.js:83 Uncaught Error: Cannot read properties of undefined (reading 'call')
    at options.factory (webpack.js?v=1751810083942:727:31)
    at __webpack_require__ (webpack.js?v=1751810083942:37:33)
    at fn (webpack.js?v=1751810083942:384:21)
    at requireModule (react-server-dom-webpack-client.browser.development.js:111:27)
    at initializeModuleChunk (react-server-dom-webpack-client.browser.development.js:1058:21)
    at readChunk (react-server-dom-webpack-client.browser.development.js:932:11)
    at RootLayout (layout.tsx:156:9)
getReactStitchedError @ stitched-error.js:26
onUncaughtError @ error-boundary-callbacks.js:75
onCaughtError @ error-boundary-callbacks.js:41
logCaughtError @ react-dom-client.development.js:7794
runWithFiberInDEV @ react-dom-client.development.js:1511
inst.componentDidCatch.update.callback @ react-dom-client.development.js:7841
callCallback @ react-dom-client.development.js:4589
commitCallbacks @ react-dom-client.development.js:4609
runWithFiberInDEV @ react-dom-client.development.js:1511
commitClassCallbacks @ react-dom-client.development.js:10677
commitLayoutEffectOnFiber @ react-dom-client.development.js:11284
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11212
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11289
flushLayoutEffects @ react-dom-client.development.js:15547
commitRoot @ react-dom-client.development.js:15390
commitRootWhenReady @ react-dom-client.development.js:14644
performWorkOnRoot @ react-dom-client.development.js:14567
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16275
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
RootLayout @ layout.tsx:156
eval @ react-server-dom-webpack-client.browser.development.js:2335
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
getOutlinedModel @ react-server-dom-webpack-client.browser.development.js:1307
parseModelString @ react-server-dom-webpack-client.browser.development.js:1520
eval @ react-server-dom-webpack-client.browser.development.js:2274
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1011
resolveModel @ react-server-dom-webpack-client.browser.development.js:1579
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2268
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
<RootLayout>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2020
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2007
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2043
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2241
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1567
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2376
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2696
eval @ app-index.js:133
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1751810083942:149
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
fn @ webpack.js?v=1751810083942:384
eval @ app-next-dev.js:10
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:9
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1751810083942:171
options.factory @ webpack.js?v=1751810083942:727
__webpack_require__ @ webpack.js?v=1751810083942:37
__webpack_exec__ @ main-app.js?v=1751810083942:2781
(anonymous) @ main-app.js?v=1751810083942:2782
webpackJsonpCallback @ webpack.js?v=1751810083942:1403
(anonymous) @ main-app.js?v=1751810083942:9
:3004/icon.svg:1  GET http://localhost:3004/icon.svg 404 (Not Found)
:3004/site.webmanifest:1  GET http://localhost:3004/site.webmanifest 404 (Not Found)
localhost/:1 Manifest fetch from http://localhost:3004/site.webmanifest failed, code 404
localhost/:1 The resource http://localhost:3004/_next/static/chunks/trading-interface.js was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
