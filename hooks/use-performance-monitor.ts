"use client"

import { useCallback, useRef } from "react"

export function usePerformanceMonitor(componentName: string) {
  const renderTimeRef = useRef<number>(0)

  // Track component render time
  const trackRender = useCallback(() => {
    const startTime = performance.now()

    // Use requestAnimationFrame to measure after render is complete
    requestAnimationFrame(() => {
      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Log render time if it exceeds 16ms (60fps)
      if (renderTime > 16) {
        console.warn(`[Performance] ${componentName} took ${renderTime.toFixed(2)}ms to render`)
      }

      renderTimeRef.current = renderTime
    })
  }, [componentName])

  // Track operation execution time
  const trackOperation = useCallback(
    (operationName: string) => {
      return () => {
        const startTime = performance.now()

        return () => {
          const endTime = performance.now()
          const executionTime = endTime - startTime

          // Log execution time if it exceeds 100ms
          if (executionTime > 100) {
            console.warn(
              `[Performance] ${componentName}.${operationName} took ${executionTime.toFixed(2)}ms to execute`,
            )
          }

          return executionTime
        }
      }
    },
    [componentName],
  )

  return {
    trackRender,
    trackOperation,
    getRenderTime: () => renderTimeRef.current,
  }
}
