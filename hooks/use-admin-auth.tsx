"use client"

import React, { useState, useEffect, createContext, useContext } from "react"

interface AdminAuthContextType {
  isAdmin: boolean
  isLoading: boolean
  login: (password: string) => Promise<void>
  logout: () => void
}

const AdminAuthContext = createContext<AdminAuthContextType>({
  isAdmin: false,
  isLoading: true,
  login: async () => {},
  logout: () => {},
})

export function useAdminAuth() {
  const context = useContext(AdminAuthContext)
  if (!context) {
    throw new Error("useAdminAuth must be used within an AdminAuthProvider")
  }
  return context
}

// For demo purposes, we'll use a simple password
// In a real app, this would be a proper authentication system
const ADMIN_PASSWORD = "pawpumps-admin"

export function AdminAuthProvider({ children }: { children: React.ReactNode }) {
  const [isAdmin, setIsAdmin] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuth = () => {
      const adminToken = localStorage.getItem("admin-token")
      setIsAdmin(!!adminToken)
      setIsLoading(false)
    }

    checkAuth()
  }, [])

  const login = async (password: string) => {
    // In a real app, this would be a proper authentication API call
    return new Promise<void>((resolve, reject) => {
      setTimeout(() => {
        if (password === ADMIN_PASSWORD) {
          localStorage.setItem("admin-token", "demo-token")
          setIsAdmin(true)
          resolve()
        } else {
          reject(new Error("Invalid password"))
        }
      }, 1000)
    })
  }

  const logout = () => {
    localStorage.removeItem("admin-token")
    setIsAdmin(false)
  }

  return <AdminAuthContext.Provider value={{ isAdmin, isLoading, login, logout }}>{children}</AdminAuthContext.Provider>
}
