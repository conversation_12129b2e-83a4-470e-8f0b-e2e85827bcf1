import { useCallback, useState } from 'react'
import { useToast } from '@/hooks/use-toast'
import {
  getGlobalErrorHandler,
  classifyError,
  withRetry,
  ErrorType,
  ErrorSeverity,
  type AppError,
  type ErrorContext,
  type RetryConfig
} from '@/lib/error-handling'

interface UseErrorHandlerOptions {
  showToast?: boolean
  retryConfig?: Partial<RetryConfig>
  context?: ErrorContext
}

interface ErrorState {
  error: AppError | null
  isRetrying: boolean
  retryCount: number
}

export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const { toast } = useToast()
  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isRetrying: false,
    retryCount: 0,
  })

  const handleError = useCallback((error: Error | AppError, context?: ErrorContext) => {
    const appError = error instanceof Error ? classifyError(error, { ...options.context, ...context }) : error
    
    // Update error state
    setErrorState(prev => ({
      error: appError,
      isRetrying: false,
      retryCount: prev.retryCount,
    }))

    // Report error
    getGlobalErrorHandler().handleError(appError)

    // Show toast notification if enabled
    if (options.showToast !== false) {
      const toastVariant = appError.severity === ErrorSeverity.HIGH || appError.severity === ErrorSeverity.CRITICAL 
        ? 'destructive' 
        : 'default'

      toast({
        title: getErrorTitle(appError.type),
        description: getUserFriendlyMessage(appError),
        variant: toastVariant,
      })
    }

    return appError
  }, [options.context, options.showToast, toast])

  const executeWithErrorHandling = useCallback(async <T>(
    operation: () => Promise<T>,
    context?: ErrorContext
  ): Promise<T | null> => {
    try {
      setErrorState(prev => ({ ...prev, error: null }))
      return await operation()
    } catch (error) {
      handleError(error as Error, context)
      return null
    }
  }, [handleError])

  const executeWithRetry = useCallback(async <T>(
    operation: () => Promise<T>,
    context?: ErrorContext
  ): Promise<T | null> => {
    try {
      setErrorState(prev => ({ ...prev, error: null, isRetrying: true }))
      
      const result = await withRetry(operation, options.retryConfig, { ...options.context, ...context })
      
      setErrorState(prev => ({ ...prev, isRetrying: false }))
      return result
    } catch (error) {
      setErrorState(prev => ({ ...prev, isRetrying: false }))
      handleError(error as Error, context)
      return null
    }
  }, [handleError, options.retryConfig, options.context])

  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isRetrying: false,
      retryCount: 0,
    })
  }, [])

  const retry = useCallback(async <T>(operation: () => Promise<T>): Promise<T | null> => {
    if (!errorState.error?.retryable) {
      return null
    }

    const newRetryCount = errorState.retryCount + 1
    const maxRetries = errorState.error.maxRetries || 3

    if (newRetryCount > maxRetries) {
      toast({
        title: "Maximum retries exceeded",
        description: "Please refresh the page or contact support.",
        variant: "destructive",
      })
      return null
    }

    setErrorState(prev => ({ 
      ...prev, 
      isRetrying: true, 
      retryCount: newRetryCount 
    }))

    try {
      const result = await operation()
      setErrorState({
        error: null,
        isRetrying: false,
        retryCount: 0,
      })
      return result
    } catch (error) {
      setErrorState(prev => ({ ...prev, isRetrying: false }))
      handleError(error as Error)
      return null
    }
  }, [errorState.error, errorState.retryCount, handleError, toast])

  return {
    error: errorState.error,
    isRetrying: errorState.isRetrying,
    retryCount: errorState.retryCount,
    handleError,
    executeWithErrorHandling,
    executeWithRetry,
    clearError,
    retry,
  }
}

// Specialized hooks for different domains
export function useTradingErrorHandler() {
  return useErrorHandler({
    context: { component: 'Trading' },
    retryConfig: { maxRetries: 2, baseDelay: 2000 },
  })
}

export function useWalletErrorHandler() {
  return useErrorHandler({
    context: { component: 'Wallet' },
    retryConfig: { maxRetries: 1, baseDelay: 1000 },
  })
}

export function useBlockchainErrorHandler() {
  return useErrorHandler({
    context: { component: 'Blockchain' },
    retryConfig: { maxRetries: 3, baseDelay: 3000 },
  })
}

export function useFormErrorHandler() {
  return useErrorHandler({
    context: { component: 'Form' },
    showToast: false, // Forms usually show inline errors
  })
}

// Helper functions
function getErrorTitle(errorType: ErrorType): string {
  switch (errorType) {
    case ErrorType.NETWORK:
      return "Connection Error"
    case ErrorType.WALLET:
      return "Wallet Error"
    case ErrorType.BLOCKCHAIN:
      return "Transaction Error"
    case ErrorType.AUTHENTICATION:
      return "Authentication Error"
    case ErrorType.AUTHORIZATION:
      return "Permission Error"
    case ErrorType.VALIDATION:
      return "Validation Error"
    case ErrorType.RATE_LIMIT:
      return "Rate Limit Exceeded"
    case ErrorType.SERVER:
      return "Server Error"
    default:
      return "Error"
  }
}

function getUserFriendlyMessage(error: AppError): string {
  switch (error.type) {
    case ErrorType.NETWORK:
      return "Please check your internet connection and try again."
    case ErrorType.WALLET:
      return "Please check your wallet connection and try again."
    case ErrorType.BLOCKCHAIN:
      return "Transaction failed. Please check gas fees and try again."
    case ErrorType.AUTHENTICATION:
      return "Please sign in again to continue."
    case ErrorType.AUTHORIZATION:
      return "You don't have permission to perform this action."
    case ErrorType.VALIDATION:
      return error.message || "Please check your input and try again."
    case ErrorType.RATE_LIMIT:
      return "Too many requests. Please wait a moment and try again."
    case ErrorType.SERVER:
      return "Server is temporarily unavailable. Please try again later."
    default:
      return error.message || "An unexpected error occurred. Please try again."
  }
}

// Hook for handling async operations with loading states
export function useAsyncOperation<T>() {
  const [isLoading, setIsLoading] = useState(false)
  const { handleError, executeWithErrorHandling } = useErrorHandler()

  const execute = useCallback(async (
    operation: () => Promise<T>,
    context?: ErrorContext
  ): Promise<T | null> => {
    setIsLoading(true)
    try {
      const result = await executeWithErrorHandling(operation, context)
      return result
    } finally {
      setIsLoading(false)
    }
  }, [executeWithErrorHandling])

  return {
    isLoading,
    execute,
    handleError,
  }
}

// Hook for handling form submissions with error handling
export function useFormSubmission<T>() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const { handleError } = useFormErrorHandler()

  const submit = useCallback(async (
    operation: () => Promise<T>,
    context?: ErrorContext
  ): Promise<T | null> => {
    setIsSubmitting(true)
    setSubmitError(null)
    
    try {
      const result = await operation()
      return result
    } catch (error) {
      const appError = handleError(error as Error, context)
      setSubmitError(getUserFriendlyMessage(appError))
      return null
    } finally {
      setIsSubmitting(false)
    }
  }, [handleError])

  const clearSubmitError = useCallback(() => {
    setSubmitError(null)
  }, [])

  return {
    isSubmitting,
    submitError,
    submit,
    clearSubmitError,
  }
}
