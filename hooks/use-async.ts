"use client"

import { useState, useCallback, useEffect } from "react"

type AsyncState<T> = {
  data: T | null
  loading: boolean
  error: Error | null
  execute: (...args: any[]) => Promise<T | null>
  reset: () => void
}

export function useAsync<T = any>(asyncFunction: (...args: any[]) => Promise<T>, immediate = true): AsyncState<T> {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState<boolean>(immediate)
  const [error, setError] = useState<Error | null>(null)

  // Function to execute the async function
  const execute = useCallback(
    async (...args: any[]): Promise<T | null> => {
      try {
        setLoading(true)
        setError(null)

        const response = await asyncFunction(...args)
        setData(response)
        return response
      } catch (err) {
        setError(err instanceof Error ? err : new Error(String(err)))
        return null
      } finally {
        setLoading(false)
      }
    },
    [asyncFunction],
  )

  // Reset the state
  const reset = useCallback(() => {
    setData(null)
    setLoading(false)
    setError(null)
  }, [])

  // Execute the function if immediate is true
  useEffect(() => {
    if (immediate) {
      execute()
    }
  }, [immediate, execute])

  return { data, loading, error, execute, reset }
}
