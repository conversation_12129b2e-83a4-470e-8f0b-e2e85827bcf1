"use client"

import { useState, useEffect } from "react"

type Breakpoint = "xs" | "sm" | "md" | "lg" | "xl" | "2xl"

export function useResponsive() {
  const [breakpoint, setBreakpoint] = useState<Breakpoint>("lg")
  const [width, setWidth] = useState<number>(0)

  useEffect(() => {
    // Initialize with current window width
    const updateSize = () => {
      const width = window.innerWidth
      setWidth(width)

      if (width < 640) setBreakpoint("xs")
      else if (width < 768) setBreakpoint("sm")
      else if (width < 1024) setBreakpoint("md")
      else if (width < 1280) setBreakpoint("lg")
      else if (width < 1536) setBreakpoint("xl")
      else setBreakpoint("2xl")
    }

    // Set initial size
    updateSize()

    // Add event listener
    window.addEventListener("resize", updateSize)

    // Clean up
    return () => window.removeEventListener("resize", updateSize)
  }, [])

  return {
    breakpoint,
    width,
    isMobile: breakpoint === "xs" || breakpoint === "sm",
    isTablet: breakpoint === "md",
    isDesktop: breakpoint === "lg" || breakpoint === "xl" || breakpoint === "2xl",
    isSmallScreen: breakpoint === "xs" || breakpoint === "sm" || breakpoint === "md",
    isLargeScreen: breakpoint === "lg" || breakpoint === "xl" || breakpoint === "2xl",
  }
}
