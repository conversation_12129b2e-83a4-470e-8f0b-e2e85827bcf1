"use client"

import React, { useState, useMemo } from "react"
import { ArrowU<PERSON>Down, ChevronUp, ChevronDown } from "lucide-react"

export type SortDirection = 'asc' | 'desc'

export interface SortConfig<T = any> {
  field: keyof T
  direction: SortDirection
}

export interface UseSortingOptions<T> {
  initialField?: keyof T
  initialDirection?: SortDirection
  data: T[]
}

export interface SortingReturn<T> {
  sortedData: T[]
  sortConfig: SortConfig<T>
  handleSort: (field: keyof T) => void
  getSortIcon: (field: keyof T) => React.ReactElement
  isSorted: (field: keyof T) => boolean
  getSortDirection: (field: keyof T) => SortDirection | null
}

/**
 * Standardized sorting hook for consistent sorting behavior across the platform
 * Includes flip animations and consistent styling as per Trade page design
 */
export function useSorting<T extends Record<string, any>>({
  initialField,
  initialDirection = 'desc',
  data
}: UseSortingOptions<T>): SortingReturn<T> {
  const [sortConfig, setSortConfig] = useState<SortConfig<T>>({
    field: initialField || (Object.keys(data[0] || {})[0] as keyof T),
    direction: initialDirection
  })

  // Sort data based on current sort configuration
  const sortedData = useMemo(() => {
    if (!data.length) return []

    const sorted = [...data].sort((a, b) => {
      const aValue = a[sortConfig.field]
      const bValue = b[sortConfig.field]

      // Handle string comparison
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortConfig.direction === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      // Handle number comparison
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc'
          ? aValue - bValue
          : bValue - aValue
      }

      // Handle mixed types - convert to string
      const aStr = String(aValue)
      const bStr = String(bValue)
      return sortConfig.direction === 'asc'
        ? aStr.localeCompare(bStr)
        : bStr.localeCompare(aStr)
    })

    return sorted
  }, [data, sortConfig])

  // Handle column header click for sorting
  const handleSort = (field: keyof T) => {
    setSortConfig(prevConfig => ({
      field,
      direction: prevConfig.field === field && prevConfig.direction === 'desc' ? 'asc' : 'desc'
    }))
  }

  // Get sort icon for column header with flip animation
  const getSortIcon = (field: keyof T): React.ReactElement => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="h-3 w-3 opacity-50 transition-all duration-200 hover:opacity-80" />
    }

    return sortConfig.direction === 'asc'
      ? <ChevronUp className="h-3 w-3 text-doge transition-all duration-300 animate-flip" />
      : <ChevronDown className="h-3 w-3 text-doge transition-all duration-300 animate-flip" />
  }

  // Check if a field is currently being sorted
  const isSorted = (field: keyof T): boolean => {
    return sortConfig.field === field
  }

  // Get current sort direction for a field
  const getSortDirection = (field: keyof T): SortDirection | null => {
    return sortConfig.field === field ? sortConfig.direction : null
  }

  return {
    sortedData,
    sortConfig,
    handleSort,
    getSortIcon,
    isSorted,
    getSortDirection
  }
}

/**
 * Utility function to create sortable table header with consistent styling
 */
export const createSortableHeader = <T,>(
  label: string,
  field: keyof T,
  getSortIcon: (field: keyof T) => React.ReactElement,
  handleSort: (field: keyof T) => void,
  className?: string,
  showIconOnHover?: boolean
) => {
  return (
    <th
      className={`pb-2 text-sm font-medium text-white/60 cursor-pointer hover:text-white transition-colors group ${className || ''}`}
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center gap-1">
        {label}
        <span className={showIconOnHover ? "opacity-0 group-hover:opacity-100 transition-opacity" : ""}>
          {getSortIcon(field)}
        </span>
      </div>
    </th>
  )
}

/**
 * Utility function to create sortable table header with right alignment
 */
export const createSortableHeaderRight = <T,>(
  label: string,
  field: keyof T,
  getSortIcon: (field: keyof T) => React.ReactElement,
  handleSort: (field: keyof T) => void,
  className?: string
) => {
  return (
    <th
      className={`pb-2 text-right text-sm font-medium text-white/60 cursor-pointer hover:text-white transition-colors group ${className || ''}`}
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center justify-end gap-1">
        {label}
        {getSortIcon(field)}
      </div>
    </th>
  )
}
