import { useContext } from 'react'
import { WalletContext } from '@/components/wallet-provider'
import { ethers } from 'ethers'

export function useWallet() {
  const context = useContext(WalletContext)
  
  if (!context) {
    throw new Error('useWallet must be used within a WalletProvider')
  }
  
  return context
}

// Hook for contract interactions
export function useContract(contractAddress: string, abi: any[]) {
  const { signer, provider, isConnected } = useWallet()
  
  if (!isConnected || !signer || !provider) {
    return null
  }
  
  try {
    return new ethers.Contract(contractAddress, abi, signer)
  } catch (error) {
    console.error('Error creating contract instance:', error)
    return null
  }
}

// Hook for token operations
export function useToken(tokenAddress: string) {
  const { signer, provider, isConnected, addToken } = useWallet()
  
  // Standard ERC-20 ABI (minimal)
  const ERC20_ABI = [
    'function name() view returns (string)',
    'function symbol() view returns (string)',
    'function decimals() view returns (uint8)',
    'function totalSupply() view returns (uint256)',
    'function balanceOf(address) view returns (uint256)',
    'function transfer(address to, uint256 amount) returns (bool)',
    'function allowance(address owner, address spender) view returns (uint256)',
    'function approve(address spender, uint256 amount) returns (bool)',
    'function transferFrom(address from, address to, uint256 amount) returns (bool)',
    'event Transfer(address indexed from, address indexed to, uint256 value)',
    'event Approval(address indexed owner, address indexed spender, uint256 value)',
  ]
  
  const contract = useContract(tokenAddress, ERC20_ABI)
  
  const getTokenInfo = async () => {
    if (!contract) return null
    
    try {
      const [name, symbol, decimals, totalSupply] = await Promise.all([
        contract.name(),
        contract.symbol(),
        contract.decimals(),
        contract.totalSupply(),
      ])
      
      return {
        name,
        symbol,
        decimals: Number(decimals),
        totalSupply: ethers.formatUnits(totalSupply, decimals),
        address: tokenAddress,
      }
    } catch (error) {
      console.error('Error fetching token info:', error)
      return null
    }
  }
  
  const getBalance = async (address?: string) => {
    if (!contract || !isConnected) return '0'
    
    try {
      const targetAddress = address || await signer?.getAddress()
      if (!targetAddress) return '0'
      
      const balance = await contract.balanceOf(targetAddress)
      const decimals = await contract.decimals()
      
      return ethers.formatUnits(balance, decimals)
    } catch (error) {
      console.error('Error fetching token balance:', error)
      return '0'
    }
  }
  
  const transfer = async (to: string, amount: string) => {
    if (!contract || !signer) {
      throw new Error('Contract or signer not available')
    }
    
    try {
      const decimals = await contract.decimals()
      const amountWei = ethers.parseUnits(amount, decimals)
      
      const tx = await contract.transfer(to, amountWei)
      return tx
    } catch (error) {
      console.error('Error transferring tokens:', error)
      throw error
    }
  }
  
  const approve = async (spender: string, amount: string) => {
    if (!contract || !signer) {
      throw new Error('Contract or signer not available')
    }
    
    try {
      const decimals = await contract.decimals()
      const amountWei = ethers.parseUnits(amount, decimals)
      
      const tx = await contract.approve(spender, amountWei)
      return tx
    } catch (error) {
      console.error('Error approving tokens:', error)
      throw error
    }
  }
  
  const getAllowance = async (owner: string, spender: string) => {
    if (!contract) return '0'
    
    try {
      const allowance = await contract.allowance(owner, spender)
      const decimals = await contract.decimals()
      
      return ethers.formatUnits(allowance, decimals)
    } catch (error) {
      console.error('Error fetching allowance:', error)
      return '0'
    }
  }
  
  const addToWallet = async () => {
    if (!isConnected) {
      throw new Error('Wallet not connected')
    }
    
    try {
      const tokenInfo = await getTokenInfo()
      if (!tokenInfo) {
        throw new Error('Could not fetch token information')
      }
      
      return await addToken(
        tokenAddress,
        tokenInfo.symbol,
        tokenInfo.decimals,
        undefined // No token image for now
      )
    } catch (error) {
      console.error('Error adding token to wallet:', error)
      throw error
    }
  }
  
  return {
    contract,
    getTokenInfo,
    getBalance,
    transfer,
    approve,
    getAllowance,
    addToWallet,
  }
}

// Hook for transaction utilities
export function useTransaction() {
  const { signer, provider, isConnected } = useWallet()
  
  const sendTransaction = async (to: string, value: string, data?: string) => {
    if (!signer || !isConnected) {
      throw new Error('Wallet not connected')
    }
    
    try {
      const tx = await signer.sendTransaction({
        to,
        value: ethers.parseEther(value),
        data: data || '0x',
      })
      
      return tx
    } catch (error) {
      console.error('Error sending transaction:', error)
      throw error
    }
  }
  
  const waitForTransaction = async (txHash: string) => {
    if (!provider) {
      throw new Error('Provider not available')
    }
    
    try {
      const receipt = await provider.waitForTransaction(txHash)
      return receipt
    } catch (error) {
      console.error('Error waiting for transaction:', error)
      throw error
    }
  }
  
  const getTransactionReceipt = async (txHash: string) => {
    if (!provider) {
      throw new Error('Provider not available')
    }
    
    try {
      const receipt = await provider.getTransactionReceipt(txHash)
      return receipt
    } catch (error) {
      console.error('Error fetching transaction receipt:', error)
      throw error
    }
  }
  
  const estimateGas = async (to: string, value: string, data?: string) => {
    if (!signer || !isConnected) {
      throw new Error('Wallet not connected')
    }
    
    try {
      const gasEstimate = await signer.estimateGas({
        to,
        value: ethers.parseEther(value),
        data: data || '0x',
      })
      
      return gasEstimate
    } catch (error) {
      console.error('Error estimating gas:', error)
      throw error
    }
  }
  
  return {
    sendTransaction,
    waitForTransaction,
    getTransactionReceipt,
    estimateGas,
  }
}

// Hook for network utilities
export function useNetwork() {
  const { chainId, switchNetwork, provider } = useWallet()
  
  const getNetworkName = (id: number) => {
    switch (id) {
      case 1:
        return 'Ethereum Mainnet'
      case 2000:
        return 'Dogechain Mainnet'
      case 568:
        return 'Dogechain Testnet'
      case 137:
        return 'Polygon'
      case 56:
        return 'BSC'
      default:
        return `Chain ${id}`
    }
  }
  
  const isDogechain = chainId === 2000 || chainId === 568
  const networkName = getNetworkName(chainId)
  
  const getGasPrice = async () => {
    if (!provider) return null
    
    try {
      const feeData = await provider.getFeeData()
      return feeData.gasPrice
    } catch (error) {
      console.error('Error fetching gas price:', error)
      return null
    }
  }
  
  const getBlockNumber = async () => {
    if (!provider) return null
    
    try {
      return await provider.getBlockNumber()
    } catch (error) {
      console.error('Error fetching block number:', error)
      return null
    }
  }
  
  return {
    chainId,
    networkName,
    isDogechain,
    switchNetwork,
    getGasPrice,
    getBlockNumber,
  }
}
