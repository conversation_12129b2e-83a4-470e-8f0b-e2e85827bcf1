"use client"

import { useNotification } from "./use-notification"

export function useToast() {
  const { showNotification } = useNotification()

  return {
    toast: ({ title, description, variant = "default" }: { title: string; description: string; variant?: string }) => {
      showNotification({
        title,
        message: description,
        type: variant === "destructive" ? "error" : "success",
      })
    },
  }
}
