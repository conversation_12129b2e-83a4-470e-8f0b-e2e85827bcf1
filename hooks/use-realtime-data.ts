import { useState, useEffect, useRef, useCallback } from 'react'
import { 
  priceDataService, 
  tradeDataService, 
  notificationService,
  PriceUpdate,
  TradeUpdate,
  OrderBookUpdate,
  NotificationUpdate
} from '@/lib/websocket-manager'

// Hook for real-time price data
export function useRealtimePrice(symbol: string) {
  const [price, setPrice] = useState<PriceUpdate | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let unsubscribe: (() => void) | null = null

    const connectAndSubscribe = async () => {
      try {
        await priceDataService.connect()
        setIsConnected(true)
        setError(null)

        unsubscribe = priceDataService.subscribeToPrice(symbol, (priceUpdate) => {
          setPrice(priceUpdate)
        })
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Connection failed')
        setIsConnected(false)
      }
    }

    connectAndSubscribe()

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [symbol])

  return { price, isConnected, error }
}

// Hook for real-time trade data
export function useRealtimeTrades(symbol: string, limit: number = 50) {
  const [trades, setTrades] = useState<TradeUpdate[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let unsubscribe: (() => void) | null = null

    const connectAndSubscribe = async () => {
      try {
        await tradeDataService.connect()
        setIsConnected(true)
        setError(null)

        unsubscribe = tradeDataService.subscribeToTrades(symbol, (trade) => {
          setTrades(prevTrades => {
            const newTrades = [trade, ...prevTrades].slice(0, limit)
            return newTrades
          })
        })
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Connection failed')
        setIsConnected(false)
      }
    }

    connectAndSubscribe()

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [symbol, limit])

  return { trades, isConnected, error }
}

// Hook for real-time order book
export function useRealtimeOrderBook(symbol: string) {
  const [orderBook, setOrderBook] = useState<OrderBookUpdate | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let unsubscribe: (() => void) | null = null

    const connectAndSubscribe = async () => {
      try {
        await tradeDataService.connect()
        setIsConnected(true)
        setError(null)

        unsubscribe = tradeDataService.subscribeToOrderBook(symbol, (orderBookUpdate) => {
          setOrderBook(orderBookUpdate)
        })
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Connection failed')
        setIsConnected(false)
      }
    }

    connectAndSubscribe()

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [symbol])

  return { orderBook, isConnected, error }
}

// Hook for real-time notifications
export function useRealtimeNotifications(userId?: string) {
  const [notifications, setNotifications] = useState<NotificationUpdate[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    let unsubscribe: (() => void) | null = null

    const connectAndSubscribe = async () => {
      try {
        await notificationService.connect(userId)
        setIsConnected(true)
        setError(null)

        unsubscribe = notificationService.subscribeToNotifications((notification) => {
          setNotifications(prev => [notification, ...prev])
          setUnreadCount(prev => prev + 1)
        })
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Connection failed')
        setIsConnected(false)
      }
    }

    connectAndSubscribe()

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [userId])

  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    )
    setUnreadCount(prev => Math.max(0, prev - 1))
  }, [])

  const markAllAsRead = useCallback(() => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    )
    setUnreadCount(0)
  }, [])

  return { 
    notifications, 
    unreadCount, 
    isConnected, 
    error, 
    markAsRead, 
    markAllAsRead 
  }
}

// Hook for aggregated market data
export function useMarketData(symbols: string[]) {
  const [marketData, setMarketData] = useState<Map<string, PriceUpdate>>(new Map())
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const unsubscribers: (() => void)[] = []

    const connectAndSubscribe = async () => {
      try {
        await priceDataService.connect()
        setIsConnected(true)
        setError(null)

        symbols.forEach(symbol => {
          const unsubscribe = priceDataService.subscribeToPrice(symbol, (priceUpdate) => {
            setMarketData(prev => new Map(prev.set(symbol, priceUpdate)))
          })
          unsubscribers.push(unsubscribe)
        })
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Connection failed')
        setIsConnected(false)
      }
    }

    connectAndSubscribe()

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe())
    }
  }, [symbols])

  return { marketData, isConnected, error }
}

// Hook for connection status monitoring
export function useConnectionStatus() {
  const [status, setStatus] = useState({
    price: 'disconnected',
    trade: 'disconnected',
    notification: 'disconnected',
  })

  useEffect(() => {
    const checkStatus = () => {
      // This would need to be implemented in the WebSocketManager
      // For now, we'll use a simple approach
      setStatus({
        price: 'connected', // Placeholder
        trade: 'connected', // Placeholder
        notification: 'connected', // Placeholder
      })
    }

    const interval = setInterval(checkStatus, 5000)
    checkStatus()

    return () => clearInterval(interval)
  }, [])

  return status
}

// Hook for real-time chart data
export function useRealtimeChart(symbol: string, timeframe: string = '1m') {
  const [chartData, setChartData] = useState<Array<{
    timestamp: number
    open: number
    high: number
    low: number
    close: number
    volume: number
  }>>([])
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const maxDataPoints = 100

  useEffect(() => {
    let unsubscribe: (() => void) | null = null

    const connectAndSubscribe = async () => {
      try {
        await tradeDataService.connect()
        setIsConnected(true)
        setError(null)

        unsubscribe = tradeDataService.subscribeToTrades(symbol, (trade) => {
          setChartData(prevData => {
            const now = Date.now()
            const timeframeMs = getTimeframeMs(timeframe)
            const currentCandle = Math.floor(now / timeframeMs) * timeframeMs

            const newData = [...prevData]
            const lastCandle = newData[newData.length - 1]

            if (!lastCandle || lastCandle.timestamp !== currentCandle) {
              // Create new candle
              newData.push({
                timestamp: currentCandle,
                open: trade.price,
                high: trade.price,
                low: trade.price,
                close: trade.price,
                volume: trade.amount,
              })
            } else {
              // Update existing candle
              lastCandle.high = Math.max(lastCandle.high, trade.price)
              lastCandle.low = Math.min(lastCandle.low, trade.price)
              lastCandle.close = trade.price
              lastCandle.volume += trade.amount
            }

            // Keep only last maxDataPoints
            return newData.slice(-maxDataPoints)
          })
        })
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Connection failed')
        setIsConnected(false)
      }
    }

    connectAndSubscribe()

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [symbol, timeframe])

  return { chartData, isConnected, error }
}

// Utility function to convert timeframe to milliseconds
function getTimeframeMs(timeframe: string): number {
  const timeframes: Record<string, number> = {
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '4h': 4 * 60 * 60 * 1000,
    '1d': 24 * 60 * 60 * 1000,
  }
  
  return timeframes[timeframe] || timeframes['1m']
}

// Hook for real-time portfolio updates
export function useRealtimePortfolio(address: string) {
  const [portfolio, setPortfolio] = useState<any>(null)
  const [totalValue, setTotalValue] = useState<number>(0)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // This would connect to a portfolio-specific WebSocket
    // For now, we'll simulate with price updates
    const symbols = ['DOGE', 'SHIB', 'PEPE', 'FLOKI'] // User's holdings
    const unsubscribers: (() => void)[] = []

    const connectAndSubscribe = async () => {
      try {
        await priceDataService.connect()
        setIsConnected(true)
        setError(null)

        symbols.forEach(symbol => {
          const unsubscribe = priceDataService.subscribeToPrice(symbol, (priceUpdate) => {
            // Update portfolio value based on price changes
            setPortfolio((prev: any) => {
              if (!prev) return prev
              
              const updated = { ...prev }
              if (updated[symbol]) {
                updated[symbol].currentPrice = priceUpdate.price
                updated[symbol].value = updated[symbol].amount * priceUpdate.price
              }
              return updated
            })
          })
          unsubscribers.push(unsubscribe)
        })
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Connection failed')
        setIsConnected(false)
      }
    }

    connectAndSubscribe()

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe())
    }
  }, [address])

  // Calculate total portfolio value
  useEffect(() => {
    if (portfolio) {
      const total = Object.values(portfolio).reduce((sum: number, holding: any) => {
        return sum + (holding.value || 0)
      }, 0)
      setTotalValue(total)
    }
  }, [portfolio])

  return { portfolio, totalValue, isConnected, error }
}
