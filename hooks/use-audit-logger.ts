"use client"

import { useCallback } from "react"
import { useWallet } from "@/components/wallet-provider"
import { type AuditAction, logAuditEvent } from "@/utils/audit-logger"

export function useAuditLogger() {
  const { address } = useWallet()

  const logAction = useCallback(
    async (action: AuditAction, details: Record<string, any>, target?: string) => {
      if (!address) {
        console.error("Cannot log audit event: No wallet connected")
        return null
      }

      return await logAuditEvent(action, address, details, target)
    },
    [address],
  )

  return { logAction }
}
