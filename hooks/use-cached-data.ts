import { useState, useEffect, useCallback, useRef } from 'react'
import { CacheManager, cachedFetch } from '@/lib/cache-manager'

interface UseCachedDataOptions<T> {
  cache?: CacheManager<T>
  ttl?: number
  enabled?: boolean
  refetchInterval?: number
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
  retryCount?: number
  retryDelay?: number
}

interface UseCachedDataResult<T> {
  data: T | null
  loading: boolean
  error: Error | null
  refetch: () => Promise<void>
  invalidate: () => void
}

export function useCachedData<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: UseCachedDataOptions<T> = {}
): UseCachedDataResult<T> {
  const {
    cache,
    ttl,
    enabled = true,
    refetchInterval,
    onSuccess,
    onError,
    retryCount = 3,
    retryDelay = 1000,
  } = options

  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const retryCountRef = useRef(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const mountedRef = useRef(true)

  // Check cache first
  useEffect(() => {
    if (!enabled) return

    if (cache) {
      const cached = cache.get(key)
      if (cached) {
        setData(cached)
        onSuccess?.(cached)
        return
      }
    }

    // If not in cache, fetch data
    fetchData()
  }, [key, enabled])

  // Set up refetch interval
  useEffect(() => {
    if (!enabled || !refetchInterval) return

    intervalRef.current = setInterval(() => {
      fetchData()
    }, refetchInterval)

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [refetchInterval, enabled])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  const fetchData = useCallback(async () => {
    if (!enabled) return

    setLoading(true)
    setError(null)
    retryCountRef.current = 0

    const attemptFetch = async (): Promise<void> => {
      try {
        const result = await fetcher()
        
        if (!mountedRef.current) return

        setData(result)
        setLoading(false)
        
        // Store in cache if provided
        if (cache) {
          cache.set(key, result, ttl)
        }
        
        onSuccess?.(result)
      } catch (err) {
        if (!mountedRef.current) return

        const error = err instanceof Error ? err : new Error(String(err))
        
        // Retry logic
        if (retryCountRef.current < retryCount) {
          retryCountRef.current++
          setTimeout(attemptFetch, retryDelay * retryCountRef.current)
          return
        }

        setError(error)
        setLoading(false)
        onError?.(error)
      }
    }

    await attemptFetch()
  }, [fetcher, enabled, cache, key, ttl, onSuccess, onError, retryCount, retryDelay])

  const invalidate = useCallback(() => {
    if (cache) {
      cache.remove(key)
    }
    setData(null)
    setError(null)
  }, [cache, key])

  const refetch = useCallback(async () => {
    invalidate()
    await fetchData()
  }, [invalidate, fetchData])

  return {
    data,
    loading,
    error,
    refetch,
    invalidate,
  }
}

// Specialized hooks for different data types
export function useCachedPriceData(token: string, options: Omit<UseCachedDataOptions<any>, 'cache'> = {}) {
  const { priceCache } = require('@/lib/cache-manager')
  
  return useCachedData(
    `price:${token}`,
    () => cachedFetch(`/api/price/${token}`, {}, { cache: priceCache }),
    {
      ...options,
      cache: priceCache,
      ttl: 30 * 1000, // 30 seconds
      refetchInterval: 30 * 1000,
    }
  )
}

export function useCachedChartData(
  token: string, 
  timeframe: string, 
  options: Omit<UseCachedDataOptions<any>, 'cache'> = {}
) {
  const { chartCache } = require('@/lib/cache-manager')
  
  return useCachedData(
    `chart:${token}:${timeframe}`,
    () => cachedFetch(`/api/chart/${token}/${timeframe}`, {}, { cache: chartCache }),
    {
      ...options,
      cache: chartCache,
      ttl: 5 * 60 * 1000, // 5 minutes
    }
  )
}

export function useCachedUserData(address: string, options: Omit<UseCachedDataOptions<any>, 'cache'> = {}) {
  const { userCache } = require('@/lib/cache-manager')
  
  return useCachedData(
    `user:${address}`,
    () => cachedFetch(`/api/user/${address}`, {}, { cache: userCache }),
    {
      ...options,
      cache: userCache,
      ttl: 10 * 60 * 1000, // 10 minutes
    }
  )
}

// Hook for cached API calls with automatic retries
export function useCachedAPI<T>(
  url: string,
  options: RequestInit = {},
  cacheOptions: UseCachedDataOptions<T> = {}
) {
  return useCachedData(
    `api:${url}:${JSON.stringify(options)}`,
    () => fetch(url, options).then(res => {
      if (!res.ok) throw new Error(`HTTP ${res.status}`)
      return res.json()
    }),
    cacheOptions
  )
}

// Hook for cached computations
export function useCachedComputation<T, Args extends any[]>(
  key: string,
  computation: (...args: Args) => T,
  args: Args,
  options: UseCachedDataOptions<T> = {}
) {
  return useCachedData(
    `computation:${key}:${JSON.stringify(args)}`,
    () => Promise.resolve(computation(...args)),
    {
      ...options,
      ttl: options.ttl || 5 * 60 * 1000, // 5 minutes default
    }
  )
}

// Hook for managing multiple cached queries
export function useCachedQueries<T extends Record<string, any>>(
  queries: {
    [K in keyof T]: {
      key: string
      fetcher: () => Promise<T[K]>
      options?: UseCachedDataOptions<T[K]>
    }
  }
) {
  const results = {} as {
    [K in keyof T]: UseCachedDataResult<T[K]>
  }

  for (const [queryKey, query] of Object.entries(queries)) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    results[queryKey as keyof T] = useCachedData(
      query.key,
      query.fetcher,
      query.options || {}
    )
  }

  const loading = Object.values(results).some(result => result.loading)
  const error = Object.values(results).find(result => result.error)?.error || null

  const refetchAll = useCallback(async () => {
    await Promise.all(
      Object.values(results).map(result => result.refetch())
    )
  }, [results])

  const invalidateAll = useCallback(() => {
    Object.values(results).forEach(result => result.invalidate())
  }, [results])

  return {
    ...results,
    loading,
    error,
    refetchAll,
    invalidateAll,
  }
}

// Hook for cache statistics and management
export function useCacheStats(cache: CacheManager) {
  const [stats, setStats] = useState(cache.getStats())

  useEffect(() => {
    const interval = setInterval(() => {
      setStats(cache.getStats())
    }, 1000)

    return () => clearInterval(interval)
  }, [cache])

  const clearCache = useCallback(() => {
    cache.clear()
    setStats(cache.getStats())
  }, [cache])

  return {
    stats,
    clearCache,
  }
}
