"use client"

import type React from "react"

import { useState } from "react"
import { Bell, Check, AlertCircle, AlertTriangle, Info, Cog, Trash2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useNotifications } from "@/components/notification-provider"
import { NotificationToast } from "@/components/notification-toast"
import { ShimmerText } from "@/components/shimmer-text"
import { NotificationDigest } from "@/components/notification-digest"
import { NotificationSettings } from "@/components/notification-settings"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"

export default function NotificationsPage() {
  const { addNotification, clearAllNotifications } = useNotifications()
  const [toasts, setToasts] = useState<React.ReactNode[]>([])
  const [formData, setFormData] = useState({
    title: "",
    message: "",
    type: "info" as "success" | "error" | "warning" | "info",
    position: "top-right" as "top-right" | "top-left" | "bottom-right" | "bottom-left" | "top-center" | "bottom-center",
  })

  const handleAddNotification = () => {
    if (!formData.title || !formData.message) return

    addNotification({
      title: formData.title,
      message: formData.message,
      type: formData.type,
    })

    // Reset form
    setFormData({
      ...formData,
      title: "",
      message: "",
    })
  }

  const handleShowToast = () => {
    if (!formData.title || !formData.message) return

    const toastId = Date.now()

    let icon
    switch (formData.type) {
      case "success":
        icon = (
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500/20 text-green-500">
            <Check className="h-4 w-4" />
          </div>
        )
        break
      case "error":
        icon = (
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-500/20 text-red-500">
            <AlertCircle className="h-4 w-4" />
          </div>
        )
        break
      case "warning":
        icon = (
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-500/20 text-yellow-500">
            <AlertTriangle className="h-4 w-4" />
          </div>
        )
        break
      case "info":
      default:
        icon = (
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-doge/20 text-doge">
            <Info className="h-4 w-4" />
          </div>
        )
        break
    }

    setToasts((prev) => [
      ...prev,
      <NotificationToast
        key={toastId}
        title={formData.title}
        message={formData.message}
        variant={formData.type}
        position={formData.position}
        icon={icon}
        onClose={() => {
          setToasts((prev) => prev.filter((toast) => (toast as React.ReactElement).key !== toastId.toString()))
        }}
      />,
    ])

    // Reset form
    setFormData({
      ...formData,
      title: "",
      message: "",
    })
  }

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
            <ShimmerText>Notifications</ShimmerText>
          </h1>
          <p className="text-lg text-white/70">Stay updated with platform activities and events</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="border-white/10 bg-white/5" onClick={clearAllNotifications}>
            <Trash2 className="h-4 w-4 mr-2" />
            Clear All
          </Button>
          <Link href="/notifications/settings">
            <Button variant="outline" className="border-white/10 bg-white/5">
              <Cog className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </Link>
        </div>
      </div>

      <Tabs defaultValue="digest" className="space-y-8">
        <TabsList className="glass w-full grid grid-cols-2 max-w-md mx-auto">
          <TabsTrigger value="digest">
            <Bell className="h-4 w-4 mr-2" />
            Notification Digest
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Cog className="h-4 w-4 mr-2" />
            Notification Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="digest" className="space-y-8">
          <NotificationDigest />
        </TabsContent>

        <TabsContent value="settings" className="space-y-8">
          <NotificationSettings />
        </TabsContent>
      </Tabs>

      {/* Render toasts */}
      {toasts}
    </div>
  )
}
