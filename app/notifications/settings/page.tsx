"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { NotificationSettings } from "@/components/notification-settings"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Bell, Calendar, Mail, ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function NotificationSettingsPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8 flex items-center gap-4">
        <Link href="/notifications">
          <Button variant="ghost" size="icon" className="text-white/70 hover:text-white">
            <ArrowLeft className="h-5 w-5" />
            <span className="sr-only">Back to notifications</span>
          </Button>
        </Link>
        <div>
          <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
            <ShimmerText>Notification Settings</ShimmerText>
          </h1>
          <p className="text-lg text-white/70">Customize how and when you receive notifications</p>
        </div>
      </div>

      <div className="grid gap-8 md:grid-cols-3">
        <div className="md:col-span-2">
          <NotificationSettings />
        </div>

        <div className="space-y-6">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle className="text-white">Notification Channels</CardTitle>
              <CardDescription className="text-white/70">Different ways to receive notifications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3 p-3 rounded-md bg-white/5 border border-white/10">
                <Bell className="h-5 w-5 text-white/70 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-white">In-App Notifications</h4>
                  <p className="text-sm text-white/70">Receive notifications directly in the PawPumps platform</p>
                </div>
              </div>
              <div className="flex items-start gap-3 p-3 rounded-md bg-white/5 border border-white/10">
                <Mail className="h-5 w-5 text-white/70 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-white">Email Notifications</h4>
                  <p className="text-sm text-white/70">Get important updates delivered to your email inbox</p>
                </div>
              </div>
              <div className="flex items-start gap-3 p-3 rounded-md bg-white/5 border border-white/10">
                <Calendar className="h-5 w-5 text-white/70 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-white">Calendar Integration</h4>
                  <p className="text-sm text-white/70">Add important events and deadlines to your calendar</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle className="text-white">Notification Types</CardTitle>
              <CardDescription className="text-white/70">Categories of notifications you can receive</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="p-2 rounded-md bg-blue-500/10 border border-blue-500/20">
                <h4 className="text-sm font-medium text-blue-500">Governance</h4>
                <p className="text-xs text-white/70">Proposals, voting deadlines, and governance results</p>
              </div>
              <div className="p-2 rounded-md bg-purple-500/10 border border-purple-500/20">
                <h4 className="text-sm font-medium text-purple-500">Token</h4>
                <p className="text-xs text-white/70">New token launches, token events, and updates</p>
              </div>
              <div className="p-2 rounded-md bg-green-500/10 border border-green-500/20">
                <h4 className="text-sm font-medium text-green-500">Price</h4>
                <p className="text-xs text-white/70">Price movements, market alerts, and trading opportunities</p>
              </div>
              <div className="p-2 rounded-md bg-yellow-500/10 border border-yellow-500/20">
                <h4 className="text-sm font-medium text-yellow-500">System</h4>
                <p className="text-xs text-white/70">Platform updates, maintenance, and important announcements</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
