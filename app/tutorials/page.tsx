"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import { <PERSON>R<PERSON>, <PERSON><PERSON><PERSON>, Clock, Star } from "lucide-react"

const categories = ["All", "Beginners", "Trading", "Launch", "Governance", "Development DAO", "Advanced"]

const tutorials = [
  {
    id: 1,
    title: "Getting Started with PawPumps",
    description: "Learn the basics of PawPumps and how to navigate the platform.",
    image: "/placeholder.svg?height=240&width=360&query=beginner tutorial crypto",
    category: "Beginners",
    duration: "10 min",
    difficulty: "Beginner",
    slug: "getting-started",
    rating: 4.9,
  },
  {
    id: 2,
    title: "How to Connect Your Wallet",
    description: "A step-by-step guide to connecting various wallets to PawPumps.",
    image: "/placeholder.svg?height=240&width=360&query=crypto wallet connection",
    category: "Beginners",
    duration: "5 min",
    difficulty: "Beginner",
    slug: "connect-wallet",
    rating: 4.8,
  },
  {
    id: 3,
    title: "Trading on the PawPumps DEX",
    description: "Master the art of trading memecoins on our decentralized exchange.",
    image: "/placeholder.svg?height=240&width=360&query=trading cryptocurrency dex",
    category: "Trading",
    duration: "15 min",
    difficulty: "Intermediate",
    slug: "trading-basics",
    rating: 4.7,
  },
  {
    id: 4,
    title: "Launching Your First Memecoin",
    description: "Everything you need to know to launch your own memecoin on PawPumps.",
    image: "/placeholder.svg?height=240&width=360&query=launching token cryptocurrency",
    category: "Launch",
    duration: "25 min",
    difficulty: "Intermediate",
    slug: "launching-memecoin",
    rating: 4.9,
  },
  {
    id: 5,
    title: "Understanding Bonding Curves",
    description: "Deep dive into the mathematics and economics of bonding curves.",
    image: "/placeholder.svg?height=240&width=360&query=bonding curve finance math",
    category: "Advanced",
    duration: "20 min",
    difficulty: "Advanced",
    slug: "bonding-curves",
    rating: 4.6,
  },
  {
    id: 6,
    title: "Participating in Governance",
    description: "Learn how to vote on proposals and participate in platform governance.",
    image: "/placeholder.svg?height=240&width=360&query=governance voting blockchain",
    category: "Governance",
    duration: "12 min",
    difficulty: "Intermediate",
    slug: "governance-participation",
    rating: 4.7,
  },
  {
    id: 7,
    title: "Development DAO for Contributors",
    description: "How to join the Development DAO and contribute to the platform.",
    image: "/placeholder.svg?height=240&width=360&query=development dao blockchain",
    category: "Development DAO",
    duration: "18 min",
    difficulty: "Intermediate",
    slug: "development-dao-contributors",
    rating: 4.8,
  },
  {
    id: 8,
    title: "Advanced Trading Strategies",
    description: "Learn advanced trading techniques to maximize your returns on PawPumps.",
    image: "/placeholder.svg?height=240&width=360&query=advanced trading crypto chart",
    category: "Trading",
    duration: "30 min",
    difficulty: "Advanced",
    slug: "advanced-trading",
    rating: 4.5,
  },
]

export default function TutorialsPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-12 text-center">
        <h1 className="mb-3 text-4xl font-bold tracking-tight sm:text-5xl">
          <ShimmerText>PawPumps Tutorials</ShimmerText>
        </h1>
        <p className="mx-auto max-w-2xl text-lg text-white/70">
          Learn how to use the PawPumps platform with our comprehensive tutorials
        </p>
      </div>

      <div className="mb-8 flex flex-wrap justify-center gap-2">
        {categories.map((category) => (
          <Button
            key={category}
            variant={category === "All" ? "default" : "outline"}
            className={category === "All" ? "bg-doge text-black" : "border-white/20 bg-black/20"}
            size="sm"
          >
            {category}
          </Button>
        ))}
      </div>

      {/* Featured Tutorial */}
      <div className="mb-12">
        <Card className="overflow-hidden border-0 bg-gradient-to-br from-black/40 to-black/60 backdrop-blur-sm">
          <div className="grid md:grid-cols-2">
            <div className="relative aspect-video md:aspect-auto">
              <Image
                src="/placeholder.svg?height=480&width=640&query=memecoin launch tutorial featured"
                alt="Launching Your First Memecoin"
                fill
                className="object-cover"
              />
            </div>
            <div className="p-6 md:p-8">
              <div className="mb-4 flex items-center gap-3 text-sm text-white/60">
                <span className="inline-flex items-center rounded-full bg-doge/20 px-2.5 py-0.5 text-xs font-medium text-doge">
                  Featured
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  25 min
                </span>
                <span className="flex items-center gap-1">
                  <Star className="h-3 w-3 text-doge" />
                  4.9
                </span>
              </div>
              <h2 className="mb-4 text-2xl font-bold text-white md:text-3xl">Launching Your First Memecoin</h2>
              <p className="mb-6 text-white/70">
                Everything you need to know to launch your own memecoin on PawPumps. This comprehensive tutorial walks
                you through the entire process from concept to successful launch.
              </p>
              <Link href="/tutorials/launching-memecoin">
                <Button className="bg-doge text-black hover:bg-doge/90">
                  Start Learning
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </Card>
      </div>

      {/* Tutorials Grid */}
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {tutorials
          .filter((tutorial) => tutorial.id !== 4)
          .map((tutorial) => (
            <Card
              key={tutorial.id}
              className="overflow-hidden border-white/5 bg-black/20 transition-all hover:border-doge/30 hover:bg-black/40"
            >
              <div className="relative aspect-[3/2]">
                <Image src={tutorial.image || "/placeholder.svg"} alt={tutorial.title} fill className="object-cover" />
                <div className="absolute right-2 top-2 flex items-center gap-1 rounded-full bg-black/70 px-2 py-1 text-xs text-white">
                  <Star className="h-3 w-3 text-doge" />
                  {tutorial.rating}
                </div>
              </div>
              <CardHeader className="pb-2">
                <div className="mb-2 flex items-center gap-2 text-xs text-white/60">
                  <span className="inline-flex items-center rounded-full bg-doge/20 px-2.5 py-0.5 text-xs font-medium text-doge">
                    {tutorial.category}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {tutorial.duration}
                  </span>
                </div>
                <CardTitle className="line-clamp-2 text-lg">{tutorial.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="line-clamp-3 text-white/70">{tutorial.description}</CardDescription>
              </CardContent>
              <CardFooter className="flex items-center justify-between border-t border-white/5 pt-4">
                <span className="text-xs text-white/60">{tutorial.difficulty} Level</span>
                <Link href={`/tutorials/${tutorial.slug}`}>
                  <Button variant="ghost" size="sm" className="text-doge hover:bg-doge/10">
                    <BookOpen className="mr-1 h-4 w-4" />
                    Read
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
      </div>

      <div className="mt-12 flex justify-center">
        <Button variant="outline" className="border-white/20 bg-black/20">
          View All Tutorials
        </Button>
      </div>
    </div>
  )
}
