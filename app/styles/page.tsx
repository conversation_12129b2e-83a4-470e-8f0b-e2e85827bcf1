"use client"

import type React from "react"
import Image from "next/image"
import {
  Arrow<PERSON>ight,
  Check,
  X,
  AlertTriangle,
  Info,
  Bell,
  User,
  Settings,
  Search,
  Menu,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Plus,
  Minus,
  Edit,
  Trash,
  Download,
  Upload,
  Share,
  Heart,
  Star,
  Calendar,
  Clock,
  Rocket,
  BarChart2,
  Shield,
  Users,
  Zap,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ShimmerText } from "@/components/shimmer-text"
import { StandardizedTicker } from "@/components/standardized-ticker"

export default function StylesPage() {
  return (
    <div className="container py-12 space-y-16">
      <header className="space-y-4">
        <div className="flex items-center gap-3">
          <Image src="/images/pawpumps-icon.png" alt="PawPumps" width={40} height={40} />
          <h1 className="text-4xl font-bold pawpumps-text">PawPumps Style Guide</h1>
        </div>
        <p className="text-lg text-white/70">
          A comprehensive reference for all design elements, components, and styles used in the PawPumps platform.
        </p>
      </header>

      {/* Colors */}
      <section id="colors" className="space-y-6">
        <h2 className="text-3xl font-bold gradient-text">Colors</h2>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Brand Colors</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <ColorSwatch name="Doge Gold" hex="#FFC107" className="bg-doge" />
            <ColorSwatch name="Dogechain Purple" hex="#9C27B0" className="bg-dogechain" />
            <ColorSwatch name="Background" hex="#121214" className="bg-background" />
            <ColorSwatch name="Foreground" hex="#E0E0E0" className="bg-foreground text-background" />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Gradients</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="h-20 rounded-lg" style={{ background: "linear-gradient(135deg, #FFC107, #FF9800)" }}>
              <div className="p-4 font-mono text-sm">linear-gradient(135deg, #FFC107, #FF9800)</div>
            </div>
            <div className="h-20 rounded-lg" style={{ background: "linear-gradient(135deg, #9C27B0, #673AB7)" }}>
              <div className="p-4 font-mono text-sm">linear-gradient(135deg, #9C27B0, #673AB7)</div>
            </div>
            <div
              className="h-20 rounded-lg"
              style={{ background: "linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(200, 200, 220, 0.7))" }}
            >
              <div className="p-4 font-mono text-sm text-black">
                linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(200, 200, 220, 0.7))
              </div>
            </div>
            <div
              className="h-20 rounded-lg"
              style={{
                background:
                  "radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 20%, transparent 60%)",
              }}
            >
              <div className="p-4 font-mono text-sm">
                radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 60%)
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Typography */}
      <section id="typography" className="space-y-6">
        <h2 className="text-3xl font-bold gradient-text">Typography</h2>

        <div className="space-y-8">
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Headings</h3>
            <div className="space-y-4">
              <div>
                <h1 className="text-5xl font-bold">Heading 1</h1>
                <p className="text-sm text-white/60 font-mono mt-1">text-5xl font-bold</p>
              </div>
              <div>
                <h2 className="text-4xl font-bold">Heading 2</h2>
                <p className="text-sm text-white/60 font-mono mt-1">text-4xl font-bold</p>
              </div>
              <div>
                <h3 className="text-3xl font-bold">Heading 3</h3>
                <p className="text-sm text-white/60 font-mono mt-1">text-3xl font-bold</p>
              </div>
              <div>
                <h4 className="text-2xl font-bold">Heading 4</h4>
                <p className="text-sm text-white/60 font-mono mt-1">text-2xl font-bold</p>
              </div>
              <div>
                <h5 className="text-xl font-bold">Heading 5</h5>
                <p className="text-sm text-white/60 font-mono mt-1">text-xl font-bold</p>
              </div>
              <div>
                <h6 className="text-lg font-bold">Heading 6</h6>
                <p className="text-sm text-white/60 font-mono mt-1">text-lg font-bold</p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Text Styles</h3>
            <div className="space-y-4">
              <div>
                <p className="text-lg">Body Large</p>
                <p className="text-sm text-white/60 font-mono mt-1">text-lg</p>
              </div>
              <div>
                <p>Body Regular</p>
                <p className="text-sm text-white/60 font-mono mt-1">text-base (default)</p>
              </div>
              <div>
                <p className="text-sm">Body Small</p>
                <p className="text-sm text-white/60 font-mono mt-1">text-sm</p>
              </div>
              <div>
                <p className="text-xs">Caption</p>
                <p className="text-sm text-white/60 font-mono mt-1">text-xs</p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Special Text Treatments</h3>
            <div className="space-y-4">
              <div>
                <p className="pawpumps-text text-2xl font-bold">PawPumps Gradient Text</p>
                <p className="text-sm text-white/60 font-mono mt-1">pawpumps-text</p>
              </div>
              <div>
                <p className="gradient-text text-2xl font-bold">Gradient Text</p>
                <p className="text-sm text-white/60 font-mono mt-1">gradient-text</p>
              </div>
              <div>
                <p className="text-glow text-2xl font-bold">Text Glow</p>
                <p className="text-sm text-white/60 font-mono mt-1">text-glow</p>
              </div>
              <div>
                <p className="doge-text-glow text-doge text-2xl font-bold">Doge Text Glow</p>
                <p className="text-sm text-white/60 font-mono mt-1">doge-text-glow text-doge</p>
              </div>
              <div>
                <p className="dogechain-text-glow text-dogechain text-2xl font-bold">Dogechain Text Glow</p>
                <p className="text-sm text-white/60 font-mono mt-1">dogechain-text-glow text-dogechain</p>
              </div>
              <div>
                <ShimmerText className="text-2xl font-bold">Shimmer Text Effect</ShimmerText>
                <p className="text-sm text-white/60 font-mono mt-1">{"<ShimmerText>Text</ShimmerText>"}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Buttons */}
      <section id="buttons" className="space-y-6">
        <h2 className="text-3xl font-bold gradient-text">Buttons</h2>

        <div className="space-y-8">
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Primary Buttons</h3>
            <div className="flex flex-wrap gap-4">
              <Button>Default Button</Button>
              <Button className="pawpumps-button">
                <span>Launch a Token</span>
              </Button>
              <div className="cosmic-button flex items-center justify-center gap-2 px-6 py-3">
                <span>Start Trading</span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Button Variants</h3>
            <div className="flex flex-wrap gap-4">
              <Button variant="default">Default</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="link">Link</Button>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Button Sizes</h3>
            <div className="flex flex-wrap items-center gap-4">
              <Button size="lg">Large</Button>
              <Button>Default</Button>
              <Button size="sm">Small</Button>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Button States</h3>
            <div className="flex flex-wrap gap-4">
              <Button>Default</Button>
              <Button disabled>Disabled</Button>
              <Button className="pawpumps-button">Hover Me</Button>
              <div className="cosmic-button flex items-center justify-center gap-2 px-6 py-3">
                <span>Hover Me</span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Custom Buttons</h3>
            <div className="flex flex-wrap gap-4">
              <Button className="doge-button">Doge Button</Button>
              <Button className="dogechain-button">Dogechain Button</Button>
              <Button className="glass-button">Glass Button</Button>
              <Button className="pawpumps-outline-button">Outline Button</Button>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Buttons with Icons</h3>
            <div className="flex flex-wrap gap-4">
              <Button className="flex items-center gap-2">
                <Rocket className="h-4 w-4" />
                <span>Launch</span>
              </Button>
              <Button className="flex items-center gap-2">
                <span>Next</span>
                <ArrowRight className="h-4 w-4" />
              </Button>
              <Button className="flex items-center gap-2" variant="outline">
                <Plus className="h-4 w-4" />
                <span>Add New</span>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Cards */}
      <section id="cards" className="space-y-6">
        <h2 className="text-3xl font-bold gradient-text">Cards</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Default Card</CardTitle>
              <CardDescription>Card description goes here</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Card content with regular styling</p>
            </CardContent>
            <CardFooter>
              <Button>Action</Button>
            </CardFooter>
          </Card>

          <Card className="glass-card">
            <CardHeader>
              <CardTitle>Glass Card</CardTitle>
              <CardDescription>With glass morphism effect</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Card content with glass styling</p>
            </CardContent>
            <CardFooter>
              <Button className="glass-button">Action</Button>
            </CardFooter>
          </Card>

          <Card className="glass-card border-white/5 overflow-hidden liquid-glow">
            <CardHeader>
              <CardTitle>Liquid Glow Card</CardTitle>
              <CardDescription>With liquid glow animation</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Card content with liquid glow effect</p>
            </CardContent>
            <CardFooter>
              <Button className="pawpumps-button">Action</Button>
            </CardFooter>
          </Card>

          <Card className="glass-card border-white/5 border-glow">
            <CardHeader>
              <CardTitle>Border Glow Card</CardTitle>
              <CardDescription>With animated border glow</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Card content with border glow effect</p>
            </CardContent>
            <CardFooter>
              <div className="cosmic-button flex items-center justify-center gap-2 px-6 py-3">
                <span>Action</span>
              </div>
            </CardFooter>
          </Card>

          <Card className="glass-card doge-glow">
            <CardHeader>
              <CardTitle className="doge-gradient-text">Doge Card</CardTitle>
              <CardDescription>With Doge theme</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Card content with Doge styling</p>
            </CardContent>
            <CardFooter>
              <Button className="doge-button">Action</Button>
            </CardFooter>
          </Card>

          <Card className="glass-card dogechain-glow">
            <CardHeader>
              <CardTitle className="dogechain-gradient-text">Dogechain Card</CardTitle>
              <CardDescription>With Dogechain theme</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Card content with Dogechain styling</p>
            </CardContent>
            <CardFooter>
              <Button className="dogechain-button">Action</Button>
            </CardFooter>
          </Card>
        </div>
      </section>

      {/* Icons */}
      <section id="icons" className="space-y-6">
        <h2 className="text-3xl font-bold gradient-text">Icons</h2>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Navigation Icons</h3>
          <div className="flex flex-wrap gap-6">
            <IconDisplay icon={<Menu />} name="Menu" />
            <IconDisplay icon={<Search />} name="Search" />
            <IconDisplay icon={<User />} name="User" />
            <IconDisplay icon={<Settings />} name="Settings" />
            <IconDisplay icon={<Bell />} name="Bell" />
            <IconDisplay icon={<ChevronDown />} name="ChevronDown" />
            <IconDisplay icon={<ChevronUp />} name="ChevronUp" />
            <IconDisplay icon={<ChevronLeft />} name="ChevronLeft" />
            <IconDisplay icon={<ChevronRight />} name="ChevronRight" />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Action Icons</h3>
          <div className="flex flex-wrap gap-6">
            <IconDisplay icon={<Plus />} name="Plus" />
            <IconDisplay icon={<Minus />} name="Minus" />
            <IconDisplay icon={<Edit />} name="Edit" />
            <IconDisplay icon={<Trash />} name="Trash" />
            <IconDisplay icon={<Download />} name="Download" />
            <IconDisplay icon={<Upload />} name="Upload" />
            <IconDisplay icon={<Share />} name="Share" />
            <IconDisplay icon={<Heart />} name="Heart" />
            <IconDisplay icon={<Star />} name="Star" />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Status Icons</h3>
          <div className="flex flex-wrap gap-6">
            <IconDisplay icon={<Check />} name="Check" />
            <IconDisplay icon={<X />} name="X" />
            <IconDisplay icon={<AlertTriangle />} name="AlertTriangle" />
            <IconDisplay icon={<Info />} name="Info" />
            <IconDisplay icon={<Calendar />} name="Calendar" />
            <IconDisplay icon={<Clock />} name="Clock" />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Feature Icons</h3>
          <div className="flex flex-wrap gap-6">
            <IconDisplay icon={<Rocket />} name="Rocket" />
            <IconDisplay icon={<BarChart2 />} name="BarChart2" />
            <IconDisplay icon={<Shield />} name="Shield" />
            <IconDisplay icon={<Users />} name="Users" />
            <IconDisplay icon={<Zap />} name="Zap" />
          </div>
        </div>
      </section>

      {/* Effects & Animations */}
      <section id="effects" className="space-y-6">
        <h2 className="text-3xl font-bold gradient-text">Effects & Animations</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <EffectCard
            title="Liquid Glow"
            className="liquid-glow"
            description="Radial gradient animation that rotates"
            code="liquid-glow"
          />

          <EffectCard
            title="Liquid Shine"
            className="liquid-shine"
            description="Linear gradient that sweeps across"
            code="liquid-shine"
          />

          <EffectCard
            title="Border Glow"
            className="border-glow"
            description="Animated gradient border"
            code="border-glow"
          />

          <EffectCard
            title="Glass Effect"
            className="glass"
            description="Frosted glass with backdrop blur"
            code="glass"
          />

          <EffectCard
            title="Glass Card"
            className="glass-card"
            description="Card with glass morphism"
            code="glass-card"
          />

          <EffectCard
            title="Floating Animation"
            className="animate-float"
            description="Gentle floating movement"
            code="animate-float"
          />

          <Card className="h-40">
            <CardHeader>
              <ShimmerText className="text-xl">Shimmer Text</ShimmerText>
              <CardDescription>Animated shimmer effect on text</CardDescription>
            </CardHeader>
            <CardFooter>
              <p className="text-sm font-mono">{"<ShimmerText>Text</ShimmerText>"}</p>
            </CardFooter>
          </Card>
        </div>
      </section>

      {/* Ticker Stream */}
      <section id="ticker" className="space-y-6">
        <h2 className="text-3xl font-bold gradient-text">Ticker Stream</h2>

        <div className="space-y-4">
          <p className="text-white/70">
            Standardized ticker stream component for displaying real-time token data with smooth scrolling animation.
          </p>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Default Ticker</h3>
            <StandardizedTicker />

            <div className="p-4 bg-black/20 rounded-lg">
              <p className="font-mono text-sm text-white/80">
                {"<StandardizedTicker />"}
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Custom Speed Ticker</h3>
            <StandardizedTicker scrollSpeed={30} />

            <div className="p-4 bg-black/20 rounded-lg">
              <p className="font-mono text-sm text-white/80">
                {"<StandardizedTicker scrollSpeed={30} />"}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="text-lg font-medium">Features:</h4>
            <ul className="list-disc list-inside text-white/70 space-y-1">
              <li>Smooth scrolling animation with customizable speed</li>
              <li>Real-time price updates with reduced flickering</li>
              <li>Responsive design with proper overflow handling</li>
              <li>Configurable update intervals and token data</li>
              <li>Seamless loop with duplicated content</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Standardized Sorting */}
      <section id="sorting" className="space-y-6">
        <h2 className="text-3xl font-bold gradient-text">Standardized Sorting</h2>

        <div className="space-y-4">
          <p className="text-white/70">
            Consistent sorting mechanism with flip animations used across all data tables and lists.
          </p>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Basic Usage</h3>
            <div className="p-4 bg-black/20 rounded-lg">
              <pre className="font-mono text-sm text-white/80 whitespace-pre-wrap">
{`import { useSorting, createSortableHeader, createSortableHeaderRight } from "@/hooks/use-sorting"

const MyComponent = () => {
  const { sortedData, handleSort, getSortIcon } = useSorting({
    initialField: 'volume24h',
    initialDirection: 'desc',
    data: myData
  })

  return (
    <table>
      <thead>
        <tr>
          {createSortableHeader("Token", "name", getSortIcon, handleSort, "text-left", true)}
          {createSortableHeaderRight("Price", "price", getSortIcon, handleSort)}
          {createSortableHeaderRight("Volume", "volume24h", getSortIcon, handleSort)}
        </tr>
      </thead>
      <tbody>
        {sortedData.map(item => (
          <tr key={item.id}>
            <td>{item.name}</td>
            <td>{item.price}</td>
            <td>{item.volume24h}</td>
          </tr>
        ))}
      </tbody>
    </table>
  )
}`}
              </pre>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="text-lg font-medium">Features:</h4>
            <ul className="list-disc list-inside text-white/70 space-y-1">
              <li>Consistent flip animations for sort icons</li>
              <li>Support for string, number, and mixed data types</li>
              <li>Automatic direction toggling (desc → asc → desc)</li>
              <li>Visual feedback with hover states</li>
              <li>Helper functions for left and right-aligned headers</li>
              <li>Optional show-on-hover for sort icons</li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Animation Classes</h3>
            <div className="p-4 bg-black/20 rounded-lg">
              <pre className="font-mono text-sm text-white/80 whitespace-pre-wrap">
{`/* Flip animation for sorting icons */
@keyframes flip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(180deg); }
  100% { transform: rotateY(360deg); }
}

.animate-flip {
  animation: flip 2s ease-in-out infinite;
}

/* Applied to active sort icons */
.text-doge.animate-flip {
  color: hsl(var(--doge));
}`}
              </pre>
            </div>
          </div>
        </div>
      </section>

      {/* Spacing & Layout */}
      <section id="spacing" className="space-y-6">
        <h2 className="text-3xl font-bold gradient-text">Spacing & Layout</h2>

        <div className="space-y-8">
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Spacing Scale</h3>
            <div className="flex flex-col gap-4">
              <SpacingExample size="0" value="0px" />
              <SpacingExample size="1" value="0.25rem (4px)" />
              <SpacingExample size="2" value="0.5rem (8px)" />
              <SpacingExample size="3" value="0.75rem (12px)" />
              <SpacingExample size="4" value="1rem (16px)" />
              <SpacingExample size="6" value="1.5rem (24px)" />
              <SpacingExample size="8" value="2rem (32px)" />
              <SpacingExample size="12" value="3rem (48px)" />
              <SpacingExample size="16" value="4rem (64px)" />
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Container Widths</h3>
            <div className="space-y-4">
              <div className="p-4 border border-white/10 rounded-lg">
                <p className="font-mono text-sm">.container</p>
                <p className="text-white/70">Default container with responsive padding</p>
              </div>
              <div className="p-4 border border-white/10 rounded-lg">
                <p className="font-mono text-sm">.container-sm</p>
                <p className="text-white/70">Small container (max-width: 640px)</p>
              </div>
              <div className="p-4 border border-white/10 rounded-lg">
                <p className="font-mono text-sm">.container-md</p>
                <p className="text-white/70">Medium container (max-width: 768px)</p>
              </div>
              <div className="p-4 border border-white/10 rounded-lg">
                <p className="font-mono text-sm">.container-lg</p>
                <p className="text-white/70">Large container (max-width: 1024px)</p>
              </div>
              <div className="p-4 border border-white/10 rounded-lg">
                <p className="font-mono text-sm">.container-xl</p>
                <p className="text-white/70">Extra large container (max-width: 1280px)</p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Grid Examples</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="h-20 bg-white/5 rounded-lg flex items-center justify-center">
                    Grid Item {i + 1}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

function ColorSwatch({ name, hex, className }: { name: string; hex: string; className: string }) {
  return (
    <div className="space-y-2">
      <div className={`h-20 rounded-lg ${className}`}></div>
      <div>
        <p className="font-medium">{name}</p>
        <p className="text-sm text-white/60 font-mono">{hex}</p>
      </div>
    </div>
  )
}

function IconDisplay({ icon, name }: { icon: React.ReactNode; name: string }) {
  return (
    <div className="flex flex-col items-center gap-2">
      <div className="h-10 w-10 flex items-center justify-center">{icon}</div>
      <p className="text-sm font-mono">{name}</p>
    </div>
  )
}

function EffectCard({
  title,
  description,
  className,
  code,
}: { title: string; description: string; className: string; code: string }) {
  return (
    <Card className={`h-40 ${className}`}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardFooter>
        <p className="text-sm font-mono">{code}</p>
      </CardFooter>
    </Card>
  )
}

function SpacingExample({ size, value }: { size: string; value: string }) {
  return (
    <div className="flex items-center gap-4">
      <div className={`bg-white/20 h-6 w-${size}`}></div>
      <div>
        <p className="font-mono text-sm">
          p-{size}, m-{size}, gap-{size}
        </p>
        <p className="text-white/70">{value}</p>
      </div>
    </div>
  )
}
