import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: "Social Hub | PawPumps - Memecoin Community",
  description: "Connect with the memecoin community on PawPumps Social. Share insights, follow traders, discuss tokens, and stay updated with the latest memecoin trends.",
  keywords: ['social', 'community', 'memecoin', 'discussion', 'trading', 'dogechain', 'crypto social'],
  openGraph: {
    title: "Social Hub | PawPumps Community",
    description: "Connect with the memecoin community and share trading insights",
    type: 'website',
  },
}

export default function SocialLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
