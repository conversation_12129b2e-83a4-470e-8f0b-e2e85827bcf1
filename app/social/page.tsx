"use client"

import dynamic from 'next/dynamic'
import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ShimmerText } from "@/components/shimmer-text"
import { Search, Filter, TrendingUp, Users, Hash, Plus, Loader2 } from 'lucide-react'

// Dynamic imports for heavy social components
const SocialFeed = dynamic(
  () => import('@/components/social/social-feed').then(mod => ({ default: mod.SocialFeed })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-96 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-5 w-5 animate-spin" />
          Loading Social Feed...
        </div>
      </div>
    ),
    ssr: false,
  }
)

const PostComposer = dynamic(
  () => import('@/components/social/post-composer').then(mod => ({ default: mod.PostComposer })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-32 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-4 w-4 animate-spin" />
          Loading Post Composer...
        </div>
      </div>
    ),
    ssr: false,
  }
)

const QuickPostButtons = dynamic(
  () => import('@/components/social/post-composer').then(mod => ({ default: mod.QuickPostButtons })),
  {
    loading: () => <div className="h-12 bg-white/5 rounded-lg animate-pulse" />,
    ssr: false,
  }
)

const SocialSidebar = dynamic(
  () => import('@/components/social/social-sidebar').then(mod => ({ default: mod.SocialSidebar })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-64 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-4 w-4 animate-spin" />
          Loading Sidebar...
        </div>
      </div>
    ),
    ssr: false,
  }
)

const StandardizedTicker = dynamic(
  () => import('@/components/standardized-ticker').then(mod => ({ default: mod.StandardizedTicker })),
  {
    loading: () => <div className="h-12 bg-black/50 animate-pulse" />,
    ssr: false,
  }
)

export default function SocialPage() {
  const [activeTab, setActiveTab] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState('recent')
  const [showComposer, setShowComposer] = useState(false)

  // Set page title
  useEffect(() => {
    document.title = 'Social Feed - PawPumps'
  }, [])

  const handleNewPost = (content: string, sentiment?: string, tags?: string[]) => {
    console.log('New post:', { content, sentiment, tags })
    setShowComposer(false)
    // In real app, this would call API to create post
  }

  const trendingTokens = ['DOGE', 'SHIB', 'PEPE', 'FLOKI', 'BONK']

  return (
    <div className="flex flex-col min-h-screen">
      {/* Ticker Stream with Custom Speed */}
      <StandardizedTicker scrollSpeed={30} />

      {/* Header - Removed sticky behavior */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">
                <ShimmerText>Social Feed</ShimmerText>
              </h1>
              <p className="text-muted-foreground">
                Connect with the memecoin community
              </p>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-green-500">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                Connected
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Create Post Button - Prominent placement */}
            <div className="flex justify-center">
              <Button
                onClick={() => setShowComposer(!showComposer)}
                className="pawpumps-button flex items-center gap-2 px-8 py-3 text-lg"
                size="lg"
              >
                {!showComposer && <Plus className="h-5 w-5" />}
                <span>{showComposer ? 'Cancel Post' : 'Create New Post'}</span>
              </Button>
            </div>

            {/* Post composer */}
            {showComposer && (
              <div className="space-y-4">
                <QuickPostButtons />
                <PostComposer onPost={handleNewPost} />
              </div>
            )}

            {/* Filters and search */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search posts, users, or tags..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex gap-2">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="recent">Recent</SelectItem>
                    <SelectItem value="popular">Popular</SelectItem>
                    <SelectItem value="trending">Trending</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Feed tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="all" className="gap-1">
                  <Hash className="h-4 w-4" />
                  All
                </TabsTrigger>
                <TabsTrigger value="following" className="gap-1">
                  <Users className="h-4 w-4" />
                  Following
                </TabsTrigger>
                <TabsTrigger value="trending" className="gap-1">
                  <TrendingUp className="h-4 w-4" />
                  Trending
                </TabsTrigger>
                <TabsTrigger value="bullish" className="gap-1">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  Bullish
                </TabsTrigger>
                <TabsTrigger value="bearish" className="gap-1">
                  <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />
                  Bearish
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="mt-6">
                <SocialFeed filter="all" />
              </TabsContent>

              <TabsContent value="following" className="mt-6">
                <SocialFeed filter="following" />
              </TabsContent>

              <TabsContent value="trending" className="mt-6">
                <SocialFeed filter="trending" />
              </TabsContent>

              <TabsContent value="bullish" className="mt-6">
                <SocialFeed filter="bullish" />
              </TabsContent>

              <TabsContent value="bearish" className="mt-6">
                <SocialFeed filter="bearish" />
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              <SocialSidebar />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
