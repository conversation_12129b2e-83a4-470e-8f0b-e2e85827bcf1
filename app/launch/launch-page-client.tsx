"use client"

import dynamic from 'next/dynamic'

const TokenLaunchForm = dynamic(() => import("@/components/dynamic-imports").then(mod => ({ default: mod.TokenLaunchForm })), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-64">Loading launch form...</div>
})

const FormErrorBoundary = dynamic(() => import("@/components/error-boundaries/form-error-boundary").then(mod => ({ default: mod.FormErrorBoundary })), {
  ssr: false
})

const ShimmerText = dynamic(() => import("@/components/shimmer-text").then(mod => ({ default: mod.ShimmerText })), {
  ssr: false
})

export function LaunchPageClient() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mx-auto max-w-[800px]">
        <div className="mb-8 text-center">
          <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">
            <ShimmerText>Launch Your Memecoin</ShimmerText>
          </h1>
          <p className="text-lg text-muted-foreground">
            Create and deploy your own memecoin on the Dogechain Network in minutes
          </p>
        </div>

        <FormErrorBoundary formName="Token Launch">
          <TokenLaunchForm />
        </FormErrorBoundary>
      </div>
    </div>
  )
}
