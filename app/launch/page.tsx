import type { Metadata } from 'next'
import { LaunchPageClient } from './launch-page-client'

export const metadata: Metadata = {
  title: "Launch Your Memecoin | PawPumps - No-Code Token Creator",
  description: "Launch your own memecoin in minutes on PawPumps. No coding required! Create tokens with bonding curves, liquidity locking, and fair distribution on Dogechain.",
  keywords: ['launch', 'create', 'memecoin', 'token', 'no-code', 'dogechain', 'bonding curve', 'fair launch'],
  openGraph: {
    title: "Launch Your Memecoin | PawPumps",
    description: "Create your own memecoin in minutes with no coding required",
    url: 'https://pawpumps.com/launch',
    images: ['/images/pawpumps-launch-og.png'],
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Launch Your Memecoin | PawPumps",
    description: "Create your own memecoin in minutes with no coding required",
    images: ['/images/pawpumps-launch-og.png'],
  },
}

export default function LaunchPage() {
  return <LaunchPageClient />
}
