import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: "Analytics Dashboard | PawPumps - Memecoin Market Data",
  description: "Comprehensive analytics for memecoin trading on PawPumps. Track token performance, volume metrics, market trends, and platform statistics on Dogechain.",
  keywords: ['analytics', 'dashboard', 'memecoin', 'market data', 'trading volume', 'dogechain', 'statistics'],
  openGraph: {
    title: "Analytics Dashboard | PawPumps",
    description: "Comprehensive memecoin market analytics and trading data",
    type: 'website',
  },
}

export default function AnalyticsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
