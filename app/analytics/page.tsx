"use client"

import dynamic from "next/dynamic"
import { ShimmerText } from "@/components/shimmer-text"
import { Loader2 } from "lucide-react"

// Dynamic import for the large AnalyticsDashboard component
const AnalyticsDashboard = dynamic(
  () => import("@/components/analytics-dashboard").then(mod => ({ default: mod.AnalyticsDashboard })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-96 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-6 w-6 animate-spin" />
          Loading Analytics Dashboard...
        </div>
      </div>
    ),
    ssr: false,
  }
)

export default function AnalyticsPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Analytics</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Track platform metrics, token performance, and market trends</p>
      </div>

      <AnalyticsDashboard />
    </div>
  )
}
