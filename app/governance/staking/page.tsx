"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { StakingInterface } from "@/components/governance/staking-interface"
import { ErrorBoundary } from "@/components/error-boundary"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>bs<PERSON><PERSON>nt, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Clock, Info, TrendingUp, Users } from "lucide-react"

export default function GovernanceStakingPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Governance Staking</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Stake $PAW tokens to increase your voting power</p>
      </div>

      <UnifiedGovernanceNav />

      <ErrorBoundary>
        <div className="grid gap-8 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <Card className="glass-card border-white/5 doge-glow">
              <CardHeader>
                <CardTitle className="text-white">Governance Staking</CardTitle>
                <CardDescription className="text-white/70">
                  Stake your $PAW tokens to increase your voting power and earn rewards
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="stake" className="w-full">
                  <TabsList className="grid w-full grid-cols-2 glass mb-6">
                    <TabsTrigger value="stake" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                      Stake Tokens
                    </TabsTrigger>
                    <TabsTrigger
                      value="unstake"
                      className="data-[state=active]:text-red-500 data-[state=active]:bg-red-500/10"
                    >
                      Unstake Tokens
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="stake" className="space-y-4">
                    <StakingInterface />
                  </TabsContent>

                  <TabsContent value="unstake" className="space-y-4">
                    <StakingInterface />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            <Card className="glass-card border-white/5">
              <CardHeader>
                <CardTitle className="text-white">Staking Statistics</CardTitle>
                <CardDescription className="text-white/70">Overview of governance staking metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="p-4 rounded-lg bg-white/5 border border-white/10 text-center">
                    <div className="rounded-full bg-white/5 p-2 w-10 h-10 mx-auto mb-2 flex items-center justify-center">
                      <Users className="h-5 w-5 text-doge" />
                    </div>
                    <p className="text-white/70 text-sm mb-1">Total Stakers</p>
                    <p className="text-2xl font-bold text-white">1,245</p>
                  </div>
                  <div className="p-4 rounded-lg bg-white/5 border border-white/10 text-center">
                    <div className="rounded-full bg-white/5 p-2 w-10 h-10 mx-auto mb-2 flex items-center justify-center">
                      <TrendingUp className="h-5 w-5 text-green-500" />
                    </div>
                    <p className="text-white/70 text-sm mb-1">Total Staked</p>
                    <p className="text-2xl font-bold text-white">12.5M</p>
                  </div>
                  <div className="p-4 rounded-lg bg-white/5 border border-white/10 text-center">
                    <div className="rounded-full bg-white/5 p-2 w-10 h-10 mx-auto mb-2 flex items-center justify-center">
                      <Clock className="h-5 w-5 text-dogechain" />
                    </div>
                    <p className="text-white/70 text-sm mb-1">Avg. Stake Time</p>
                    <p className="text-2xl font-bold text-white">45 days</p>
                  </div>
                  <div className="p-4 rounded-lg bg-white/5 border border-white/10 text-center">
                    <div className="rounded-full bg-white/5 p-2 w-10 h-10 mx-auto mb-2 flex items-center justify-center">
                      <TrendingUp className="h-5 w-5 text-purple-500" />
                    </div>
                    <p className="text-white/70 text-sm mb-1">APY</p>
                    <p className="text-2xl font-bold text-white">12.4%</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/90">Staking Participation</span>
                      <span className="text-white/70">25% of total supply</span>
                    </div>
                    <Progress value={25} className="h-2 bg-white/10" indicatorClassName="bg-doge" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/90">Governance Participation</span>
                      <span className="text-white/70">58% of stakers</span>
                    </div>
                    <Progress value={58} className="h-2 bg-white/10" indicatorClassName="bg-dogechain" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card className="glass-card border-white/5">
              <CardHeader>
                <CardTitle className="text-white">Staking Rewards</CardTitle>
                <CardDescription className="text-white/70">Earn rewards for staking your tokens</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Base Rewards</h3>
                  <p className="text-sm text-white/70 mb-3">Earn $PAW tokens for staking in the governance system</p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-white/70">Current APY:</span>
                    <span className="text-doge font-medium">8.5%</span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-1">
                    <span className="text-white/70">Reward distribution:</span>
                    <span className="text-white">Weekly</span>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Voting Bonus</h3>
                  <p className="text-sm text-white/70 mb-3">
                    Additional rewards for active participation in governance
                  </p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-white/70">Bonus APY:</span>
                    <span className="text-doge font-medium">+3.9%</span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-1">
                    <span className="text-white/70">Requirement:</span>
                    <span className="text-white">Vote on 75% of proposals</span>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Time Bonus</h3>
                  <p className="text-sm text-white/70 mb-3">Increased rewards for longer staking periods</p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-white/70">3+ months:</span>
                    <span className="text-doge font-medium">+1.5% APY</span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-1">
                    <span className="text-white/70">6+ months:</span>
                    <span className="text-doge font-medium">+3.0% APY</span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-1">
                    <span className="text-white/70">12+ months:</span>
                    <span className="text-doge font-medium">+5.0% APY</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card border-white/5">
              <CardHeader className="pb-2">
                <CardTitle className="text-white">Staking Guide</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-4 rounded-lg bg-white/5 border border-white/10 flex items-start gap-3">
                  <Info className="h-5 w-5 text-white/70 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-medium text-white mb-1">How Staking Works</h4>
                    <p className="text-sm text-white/70">
                      Staking your $PAW tokens increases your voting power in governance proposals. Each token staked
                      gives you one vote, and tokens must remain staked to maintain voting power.
                    </p>
                    <a
                      href="/docs/governance#staking"
                      className="text-sm text-doge hover:underline mt-2 inline-flex items-center"
                    >
                      Learn more about staking
                    </a>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </ErrorBoundary>
    </div>
  )
}
