"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { RewardsGovernanceIntegration } from "@/components/governance/rewards-governance-integration"

export default function GovernanceRewardsPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Governance Rewards</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Earn rewards for participating in governance and development activities</p>
      </div>

      <UnifiedGovernanceNav />

      <div className="space-y-8">
        <RewardsGovernanceIntegration />
      </div>
    </div>
  )
}
