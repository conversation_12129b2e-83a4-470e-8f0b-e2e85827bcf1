"use client"

import dynamic from "next/dynamic"
import { <PERSON>mmerText } from "@/components/shimmer-text"
import { DelegationInterface } from "@/components/governance/delegation-interface"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"

import { Download, Info, Search, Users, Loader2 } from "lucide-react"
import { useState } from "react"
import { useNotification } from "@/hooks/use-notification"

// Dynamic import for delegation chart component to reduce initial bundle size
const DelegationChart = dynamic(
  () => import("@/components/charts/delegation-chart").then(mod => ({ default: mod.DelegationChart })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-5 w-5 animate-spin" />
          Loading Chart...
        </div>
      </div>
    ),
    ssr: false,
  }
)

// Types
interface DelegateStats {
  name: string
  address: string
  avatar?: string
  votingPower: number
  delegatedPower: number
  delegators: number
  participation: number
}

export default function DelegationPage() {
  const { showNotification } = useNotification()
  const [searchQuery, setSearchQuery] = useState("")

  // Mock data for top delegates
  const topDelegates: DelegateStats[] = [
    {
      name: "DogeWhale",
      address: "0x1234...5678",
      avatar: "/avatar-doge.png",
      votingPower: 250000,
      delegatedPower: 150000,
      delegators: 24,
      participation: 95,
    },
    {
      name: "CryptoWizard",
      address: "0x8765...4321",
      avatar: "/avatar-wizard.png",
      votingPower: 180000,
      delegatedPower: 120000,
      delegators: 18,
      participation: 88,
    },
    {
      name: "BlockchainDev",
      address: "0x5678...1234",
      avatar: "/avatar-developer.png",
      votingPower: 120000,
      delegatedPower: 80000,
      delegators: 12,
      participation: 92,
    },
    {
      name: "MemeEnthusiast",
      address: "0x4321...8765",
      avatar: "/avatar-enthusiast.png",
      votingPower: 90000,
      delegatedPower: 50000,
      delegators: 8,
      participation: 85,
    },
  ]

  // Filter delegates based on search query
  const filteredDelegates = topDelegates.filter(
    (delegate) =>
      delegate.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      delegate.address.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // Mock data for delegation stats chart
  const delegationStatsData = [
    { month: "Jan", delegatedPower: 120000, activeDelegators: 15 },
    { month: "Feb", delegatedPower: 180000, activeDelegators: 22 },
    { month: "Mar", delegatedPower: 250000, activeDelegators: 28 },
    { month: "Apr", delegatedPower: 320000, activeDelegators: 35 },
    { month: "May", delegatedPower: 400000, activeDelegators: 42 },
  ]

  // Handle delegation
  const handleDelegate = async (delegateAddress: string, amount: number) => {
    // In a real app, this would call a smart contract
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        showNotification({
          title: "Delegation Successful",
          message: `You have successfully delegated ${amount} voting power`,
          type: "success",
        })
        resolve()
      }, 2000)
    })
  }

  // Handle revocation
  const handleRevoke = async () => {
    // In a real app, this would call a smart contract
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        showNotification({
          title: "Delegation Revoked",
          message: "You have successfully revoked your delegation",
          type: "success",
        })
        resolve()
      }, 2000)
    })
  }

  // Format number with commas
  const formatNumber = (num: number) => {
    return num.toLocaleString(undefined, { maximumFractionDigits: 2 })
  }

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Voting Delegation</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Delegate your voting power to trusted community members</p>
      </div>

      <UnifiedGovernanceNav />

      <div className="mt-8 grid gap-8 lg:grid-cols-3">
        <div className="lg:col-span-2 space-y-8">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle className="text-white">Delegation Dashboard</CardTitle>
              <CardDescription className="text-white/70">
                Overview of delegation activity in the PawPumps governance system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-3">
                <div className="p-4 rounded-md bg-white/5 border border-white/10 text-center">
                  <p className="text-white/70 text-sm mb-1">Total Delegated Power</p>
                  <p className="text-2xl font-bold text-dogechain">400,000</p>
                </div>
                <div className="p-4 rounded-md bg-white/5 border border-white/10 text-center">
                  <p className="text-white/70 text-sm mb-1">Active Delegators</p>
                  <p className="text-2xl font-bold text-doge">42</p>
                </div>
                <div className="p-4 rounded-md bg-white/5 border border-white/10 text-center">
                  <p className="text-white/70 text-sm mb-1">Delegation Participation</p>
                  <p className="text-2xl font-bold text-green-500">28%</p>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-medium text-white">Delegation Trends</h3>
                <div className="h-[300px] rounded-md border border-white/10 p-4">
                  <DelegationChart data={delegationStatsData} />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-white">Top Delegates</h3>
                  <div className="relative w-64">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
                    <input
                      placeholder="Search delegates..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="glass-input text-white pl-9 w-full"
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  {filteredDelegates.map((delegate) => (
                    <div
                      key={delegate.address}
                      className="p-4 rounded-md bg-white/5 border border-white/10 hover:bg-white/10 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={delegate.avatar || "/placeholder.svg"} alt={delegate.name} />
                          <AvatarFallback>{delegate.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="text-white font-medium">{delegate.name}</h4>
                            <Badge className="bg-dogechain/20 text-dogechain">
                              {formatNumber(delegate.votingPower)} Power
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between mt-1">
                            <p className="text-sm text-white/70">{delegate.address}</p>
                            <p className="text-sm text-white/70">
                              {delegate.delegators} delegators · {delegate.participation}% participation
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex justify-center">
                  <Button variant="outline" className="border-white/10 bg-white/5">
                    <Users className="mr-2 h-4 w-4" />
                    View All Delegates
                  </Button>
                </div>
              </div>

              <div className="p-4 rounded-lg bg-white/5 border border-white/10 flex items-start gap-3">
                <Info className="h-5 w-5 text-white/70 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-white mb-1">About Delegation</h4>
                  <p className="text-sm text-white/70">
                    Delegation allows token holders to assign their voting power to trusted community members without
                    transferring ownership of their tokens. This increases governance participation and allows for more
                    informed voting.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle className="text-white">Delegation Analytics</CardTitle>
              <CardDescription className="text-white/70">
                Detailed analytics on delegation patterns and effectiveness
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="participation">
                <TabsList className="glass w-full grid grid-cols-3 mb-4">
                  <TabsTrigger value="participation">Participation</TabsTrigger>
                  <TabsTrigger value="distribution">Distribution</TabsTrigger>
                  <TabsTrigger value="effectiveness">Effectiveness</TabsTrigger>
                </TabsList>

                <TabsContent value="participation" className="space-y-4">
                  <div className="p-4 rounded-md bg-white/5 border border-white/10">
                    <h3 className="text-lg font-medium text-white mb-2">Voting Participation</h3>
                    <p className="text-white/70 mb-4">
                      Delegates have a 92% average participation rate in governance votes, compared to 34% for
                      non-delegated voters.
                    </p>
                    <Button variant="outline" className="border-white/10 bg-white/5">
                      <Download className="mr-2 h-4 w-4" />
                      Download Report
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="distribution" className="space-y-4">
                  <div className="p-4 rounded-md bg-white/5 border border-white/10">
                    <h3 className="text-lg font-medium text-white mb-2">Power Distribution</h3>
                    <p className="text-white/70 mb-4">
                      The top 10 delegates control 45% of the delegated voting power, with a Nakamoto coefficient of 5.
                    </p>
                    <Button variant="outline" className="border-white/10 bg-white/5">
                      <Download className="mr-2 h-4 w-4" />
                      Download Report
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="effectiveness" className="space-y-4">
                  <div className="p-4 rounded-md bg-white/5 border border-white/10">
                    <h3 className="text-lg font-medium text-white mb-2">Delegation Effectiveness</h3>
                    <p className="text-white/70 mb-4">
                      Proposals with high delegate participation have a 78% implementation success rate, compared to 52%
                      for low delegate participation.
                    </p>
                    <Button variant="outline" className="border-white/10 bg-white/5">
                      <Download className="mr-2 h-4 w-4" />
                      Download Report
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div>
          <DelegationInterface
            userVotingPower={5000}
            currentDelegate="0x1234...5678"
            onDelegate={handleDelegate}
            onRevoke={handleRevoke}
          />
        </div>
      </div>
    </div>
  )
}
