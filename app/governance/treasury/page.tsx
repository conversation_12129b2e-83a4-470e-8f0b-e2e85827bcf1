"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { ErrorBoundary } from "@/components/error-boundary"
import { Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import {
  ArrowUpRight,
  Download,
  ExternalLink,
  Info,
  Landmark,
  PieChart,
  Wallet,
  ChevronRight,
  FileText,
  TrendingUp,
} from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"

export default function GovernanceTreasuryPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Governance Treasury</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Overview of treasury funds and allocations</p>
      </div>

      <UnifiedGovernanceNav />

      <ErrorBoundary>
        <Suspense fallback={<TreasurySkeleton />}>
          <TreasuryDashboard />
        </Suspense>
      </ErrorBoundary>
    </div>
  )
}

function TreasuryDashboard() {
  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Total Treasury</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">$2.45M</div>
                <p className="text-sm text-green-500">+5.2% from previous month</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <Landmark className="h-6 w-6 text-doge" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">$PAW Holdings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">12.5M</div>
                <p className="text-sm text-white/70">~$1.25M USD</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <Wallet className="h-6 w-6 text-dogechain" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Stablecoin Reserves</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">$850K</div>
                <p className="text-sm text-white/70">USDC, USDT, DAI</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <DollarIcon className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Other Assets</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">$350K</div>
                <p className="text-sm text-white/70">ETH, WBTC, DOGE</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <PieChart className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card className="glass-card border-white/5 md:col-span-2">
          <CardHeader>
            <CardTitle className="text-white">Treasury Allocation</CardTitle>
            <CardDescription className="text-white/70">Current distribution of treasury assets</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-white/90">$PAW Token</span>
                  <span className="text-white/70">51% ($1.25M)</span>
                </div>
                <Progress value={51} className="h-2 bg-white/10" indicatorClassName="bg-doge" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-white/90">Stablecoins</span>
                  <span className="text-white/70">35% ($850K)</span>
                </div>
                <Progress value={35} className="h-2 bg-white/10" indicatorClassName="bg-green-500" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-white/90">ETH</span>
                  <span className="text-white/70">8% ($200K)</span>
                </div>
                <Progress value={8} className="h-2 bg-white/10" indicatorClassName="bg-blue-500" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-white/90">WBTC</span>
                  <span className="text-white/70">4% ($100K)</span>
                </div>
                <Progress value={4} className="h-2 bg-white/10" indicatorClassName="bg-orange-500" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-white/90">DOGE</span>
                  <span className="text-white/70">2% ($50K)</span>
                </div>
                <Progress value={2} className="h-2 bg-white/10" indicatorClassName="bg-yellow-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Treasury Multisig</CardTitle>
            <CardDescription className="text-white/70">Governance treasury security</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <h3 className="text-white font-medium mb-2">Multisig Details</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Address:</span>
                  <span className="text-white font-mono">0x1a2b...3c4d</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Signers:</span>
                  <span className="text-white">7</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Threshold:</span>
                  <span className="text-white">4/7</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Timelock:</span>
                  <span className="text-white">48 hours</span>
                </div>
              </div>
              <Button variant="outline" className="w-full mt-4 glass-button">
                <ExternalLink className="mr-2 h-4 w-4" />
                View on Explorer
              </Button>
            </div>

            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <h3 className="text-white font-medium mb-2">Recent Transactions</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-white">Development Fund</span>
                  <span className="text-red-500">-$50,000</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-white">Marketing Allocation</span>
                  <span className="text-red-500">-$25,000</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-white">Trading Fee Revenue</span>
                  <span className="text-green-500">+$32,500</span>
                </div>
              </div>
              <Button variant="outline" className="w-full mt-4 glass-button">
                <ArrowUpRight className="mr-2 h-4 w-4" />
                View All Transactions
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Treasury Management</CardTitle>
          <CardDescription className="text-white/70">How the treasury is managed and allocated</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-3 glass mb-6">
              <TabsTrigger value="overview" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                Overview
              </TabsTrigger>
              <TabsTrigger
                value="allocations"
                className="data-[state=active]:text-dogechain data-[state=active]:bg-dogechain/10"
              >
                Allocations
              </TabsTrigger>
              <TabsTrigger
                value="proposals"
                className="data-[state=active]:text-green-500 data-[state=active]:bg-green-500/10"
              >
                Funding Proposals
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="p-4 rounded-lg bg-white/5 border border-white/10 flex items-start gap-3">
                <Info className="h-5 w-5 text-white/70 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-white mb-1">About the Treasury</h4>
                  <p className="text-sm text-white/70">
                    The PawPumps governance treasury is managed by the community through governance proposals. The
                    treasury funds are used for development, marketing, liquidity incentives, and other initiatives that
                    benefit the ecosystem.
                  </p>
                  <p className="text-sm text-white/70 mt-2">
                    Treasury funds are secured by a multisig wallet with a 48-hour timelock, requiring 4 out of 7
                    signers to approve any transaction.
                  </p>
                  <a
                    href="/docs/governance/treasury"
                    className="text-sm text-doge hover:underline mt-2 inline-flex items-center"
                  >
                    Learn more about treasury management
                  </a>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Treasury Growth</h3>
                  <p className="text-sm text-white/70 mb-3">
                    The treasury has grown by 15% in the last quarter through:
                  </p>
                  <ul className="space-y-1 text-sm text-white/70 list-disc pl-5">
                    <li>Trading fee revenue (0.05% of all trades)</li>
                    <li>Token launch fees (2% of initial supply)</li>
                    <li>Strategic investments</li>
                    <li>Yield farming returns</li>
                  </ul>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Treasury Reports</h3>
                  <p className="text-sm text-white/70 mb-3">
                    Quarterly reports on treasury performance and allocations:
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li>
                      <a href="#" className="text-doge hover:underline flex items-center">
                        <Download className="mr-2 h-4 w-4" />
                        Q2 2025 Treasury Report
                      </a>
                    </li>
                    <li>
                      <a href="#" className="text-doge hover:underline flex items-center">
                        <Download className="mr-2 h-4 w-4" />
                        Q1 2025 Treasury Report
                      </a>
                    </li>
                    <li>
                      <a href="#" className="text-doge hover:underline flex items-center">
                        <Download className="mr-2 h-4 w-4" />
                        Q4 2024 Treasury Report
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="allocations" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Development Fund</h3>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">Allocation:</span>
                    <span className="text-white">$500,000 (20%)</span>
                  </div>
                  <Progress value={20} className="h-2 bg-white/10 mb-3" indicatorClassName="bg-dogechain" />
                  <p className="text-sm text-white/70">
                    Funds allocated for core development, security audits, and technical infrastructure.
                  </p>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Marketing & Growth</h3>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">Allocation:</span>
                    <span className="text-white">$375,000 (15%)</span>
                  </div>
                  <Progress value={15} className="h-2 bg-white/10 mb-3" indicatorClassName="bg-purple-500" />
                  <p className="text-sm text-white/70">
                    Funds for marketing campaigns, community growth, and partnerships.
                  </p>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Liquidity Incentives</h3>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">Allocation:</span>
                    <span className="text-white">$625,000 (25%)</span>
                  </div>
                  <Progress value={25} className="h-2 bg-white/10 mb-3" indicatorClassName="bg-doge" />
                  <p className="text-sm text-white/70">Funds for liquidity mining rewards and trading incentives.</p>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Strategic Reserve</h3>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">Allocation:</span>
                    <span className="text-white">$950,000 (40%)</span>
                  </div>
                  <Progress value={40} className="h-2 bg-white/10 mb-3" indicatorClassName="bg-green-500" />
                  <p className="text-sm text-white/70">
                    Long-term reserve for strategic investments and emergency situations.
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="proposals" className="space-y-4">
              <div className="space-y-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-white font-medium">Developer Grants Program</h3>
                    <span className="px-2 py-0.5 rounded-full text-xs font-medium bg-green-500/10 text-green-500">
                      Approved
                    </span>
                  </div>
                  <p className="text-sm text-white/70 mb-2">
                    Allocate $100,000 for grants to developers building on the PawPumps ecosystem.
                  </p>
                  <div className="flex items-center justify-between text-xs text-white/60">
                    <span>Requested: $100,000</span>
                    <span>Votes: 85% in favor</span>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-white font-medium">Community Marketing Fund</h3>
                    <span className="px-2 py-0.5 rounded-full text-xs font-medium bg-doge/10 text-doge">In Voting</span>
                  </div>
                  <p className="text-sm text-white/70 mb-2">
                    Allocate $50,000 for community-led marketing initiatives and content creation.
                  </p>
                  <div className="flex items-center justify-between text-xs text-white/60">
                    <span>Requested: $50,000</span>
                    <span>Votes: 72% in favor (ongoing)</span>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-white font-medium">Security Audit Fund</h3>
                    <span className="px-2 py-0.5 rounded-full text-xs font-medium bg-red-500/10 text-red-500">
                      Rejected
                    </span>
                  </div>
                  <p className="text-sm text-white/70 mb-2">
                    Allocate $200,000 for comprehensive security audits of all smart contracts.
                  </p>
                  <div className="flex items-center justify-between text-xs text-white/60">
                    <span>Requested: $200,000</span>
                    <span>Votes: 45% in favor</span>
                  </div>
                </div>

                <Button className="w-full doge-button doge-shine">Submit Funding Proposal</Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <div className="mt-8">
        <h2 className="text-2xl font-bold mb-4 text-white">Advanced Treasury Management</h2>
        <div className="grid gap-6 md:grid-cols-3">
          <Link href="/governance/treasury/advanced?tab=diversification" className="block">
            <Card className="glass-card border-white/5 hover:border-white/10 transition-colors h-full">
              <CardContent className="p-6">
                <div className="mb-4 rounded-full bg-doge/10 p-3 w-fit">
                  <PieChart className="h-6 w-6 text-doge" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Diversification Tools</h3>
                <p className="text-white/70">Optimize treasury asset allocation and risk management</p>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="link" className="text-doge p-0">
                  View Diversification Tools
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </Link>

          <Link href="/governance/treasury/advanced?tab=modeling" className="block">
            <Card className="glass-card border-white/5 hover:border-white/10 transition-colors h-full">
              <CardContent className="p-6">
                <div className="mb-4 rounded-full bg-dogechain/10 p-3 w-fit">
                  <TrendingUp className="h-6 w-6 text-dogechain" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Financial Modeling</h3>
                <p className="text-white/70">Create financial projections and scenario analysis</p>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="link" className="text-dogechain p-0">
                  View Financial Models
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </Link>

          <Link href="/governance/treasury/advanced?tab=templates" className="block">
            <Card className="glass-card border-white/5 hover:border-white/10 transition-colors h-full">
              <CardContent className="p-6">
                <div className="mb-4 rounded-full bg-green-500/10 p-3 w-fit">
                  <FileText className="h-6 w-6 text-green-500" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Proposal Templates</h3>
                <p className="text-white/70">Use standardized templates for treasury proposals</p>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="link" className="text-green-500 p-0">
                  View Templates
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}

function TreasurySkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Skeleton className="h-32 w-full rounded-lg" />
        <Skeleton className="h-32 w-full rounded-lg" />
        <Skeleton className="h-32 w-full rounded-lg" />
        <Skeleton className="h-32 w-full rounded-lg" />
      </div>
      <div className="grid gap-6 md:grid-cols-3">
        <Skeleton className="h-80 w-full rounded-lg md:col-span-2" />
        <Skeleton className="h-80 w-full rounded-lg" />
      </div>
      <Skeleton className="h-96 w-full rounded-lg" />
    </div>
  )
}

function DollarIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <line x1="12" x2="12" y1="2" y2="22" />
      <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
    </svg>
  )
}
