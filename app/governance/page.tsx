"use client"

import { GovernanceDashboard } from "@/components/governance-dashboard"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { OnboardingTrigger, QuickTip, governanceOnboardingFlow } from "@/components/governance/onboarding-system"

export default function GovernancePage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
              <ShimmerText>Governance Dashboard</ShimmerText>
            </h1>
            <p className="text-lg text-white/70">Participate in platform governance and vote on proposals</p>
          </div>
          <OnboardingTrigger flow={governanceOnboardingFlow} />
        </div>
      </div>

      <UnifiedGovernanceNav />

      {/* Quick Tips */}
      <div className="mb-8">
        <QuickTip
          title="New to Governance?"
          description="Get started with these essential tips"
          tips={[
            "Connect your wallet to participate in voting",
            "Stake tokens to increase your voting power",
            "Read proposals carefully before voting",
            "Join community discussions for insights",
            "Your vote is final - choose wisely!"
          ]}
        />
      </div>

      <div className="mt-8">
        <GovernanceDashboard />
      </div>
    </div>
  )
}
