"use client"

import dynamic from "next/dynamic"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Download, FileText, Settings, Loader2 } from "lucide-react"

// Dynamic import for GovernanceHealthMetrics to reduce initial bundle size
const GovernanceHealthMetrics = dynamic(
  () => import("@/components/governance/health-metrics").then(mod => ({ default: mod.GovernanceHealthMetrics })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-96">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-6 w-6 animate-spin" />
          Loading Health Metrics...
        </div>
      </div>
    ),
    ssr: false,
  }
)

export default function GovernanceHealthPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Governance Health</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Monitor and improve the health of the governance system</p>
      </div>

      <UnifiedGovernanceNav />

      <div className="mt-8 space-y-8">
        <div className="flex justify-between items-center">
          <Tabs defaultValue="metrics" className="w-full">
            <TabsList className="glass w-full max-w-md mx-auto">
              <TabsTrigger value="metrics">
                <FileText className="h-4 w-4 mr-2" />
                Health Metrics
              </TabsTrigger>
              <TabsTrigger value="recommendations">
                <Settings className="h-4 w-4 mr-2" />
                Recommendations
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <GovernanceHealthMetrics />

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Improvement Recommendations</CardTitle>
            <CardDescription className="text-white/70">
              Actionable steps to improve governance health metrics
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 rounded-md bg-white/5 border border-white/10 space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-white">Increase Voter Participation</h3>
                <Badge className="bg-yellow-500/20 text-yellow-500">Medium Priority</Badge>
              </div>
              <p className="text-white/70">
                Current participation rate is 34%, which is below the target of 50%. This limits the decentralization
                and legitimacy of governance decisions.
              </p>
              <div className="space-y-2 mt-4">
                <h4 className="text-white font-medium">Recommended Actions:</h4>
                <ul className="list-disc list-inside text-white/70 space-y-1">
                  <li>Implement delegation system to allow passive token holders to participate</li>
                  <li>Create educational content about governance importance and process</li>
                  <li>Introduce governance participation rewards</li>
                  <li>Improve mobile voting experience</li>
                </ul>
              </div>
              <div className="flex justify-end mt-4">
                <Button className="doge-button doge-shine">Implement Recommendations</Button>
              </div>
            </div>

            <div className="p-4 rounded-md bg-white/5 border border-white/10 space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-white">Improve Execution Rate</h3>
                <Badge className="bg-red-500/20 text-red-500">High Priority</Badge>
              </div>
              <p className="text-white/70">
                Only 62% of passed proposals are being successfully implemented, well below the target of 90%. This
                creates a gap between governance decisions and actual platform changes.
              </p>
              <div className="space-y-2 mt-4">
                <h4 className="text-white font-medium">Recommended Actions:</h4>
                <ul className="list-disc list-inside text-white/70 space-y-1">
                  <li>Establish specialized working groups for proposal implementation</li>
                  <li>Create standardized implementation templates and processes</li>
                  <li>Implement execution tracking and accountability system</li>
                  <li>Allocate dedicated resources for proposal implementation</li>
                </ul>
              </div>
              <div className="flex justify-end mt-4">
                <Button className="doge-button doge-shine">Implement Recommendations</Button>
              </div>
            </div>

            <div className="p-4 rounded-md bg-white/5 border border-white/10 space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-white">Enhance Governance Decentralization</h3>
                <Badge className="bg-yellow-500/20 text-yellow-500">Medium Priority</Badge>
              </div>
              <p className="text-white/70">
                Governance decentralization score is 68%, below the target of 80%. Voting power is concentrated among a
                small number of large token holders.
              </p>
              <div className="space-y-2 mt-4">
                <h4 className="text-white font-medium">Recommended Actions:</h4>
                <ul className="list-disc list-inside text-white/70 space-y-1">
                  <li>Implement quadratic voting to reduce the influence of large token holders</li>
                  <li>Create governance committees with balanced representation</li>
                  <li>Introduce delegation caps to prevent excessive concentration</li>
                  <li>Develop community grants program to distribute tokens more widely</li>
                </ul>
              </div>
              <div className="flex justify-end mt-4">
                <Button className="doge-button doge-shine">Implement Recommendations</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end">
          <Button variant="outline" className="border-white/10 bg-white/5">
            <Download className="h-4 w-4 mr-2" />
            Download Full Health Report
          </Button>
        </div>
      </div>
    </div>
  )
}
