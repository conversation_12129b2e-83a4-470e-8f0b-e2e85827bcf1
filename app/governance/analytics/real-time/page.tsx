"use client"

import dynamic from "next/dynamic"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { ErrorBoundary } from "@/components/error-boundary"
import { Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2 } from "lucide-react"

// Dynamic imports for heavy analytics components
const RealTimeMetrics = dynamic(
  () => import("@/components/analytics/real-time-metrics"),
  {
    loading: () => (
      <div className="flex items-center justify-center h-64 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-5 w-5 animate-spin" />
          Loading Real-time Metrics...
        </div>
      </div>
    ),
    ssr: false,
  }
)

const HistoricalComparison = dynamic(
  () => import("@/components/analytics/historical-comparison").then(mod => ({ default: mod.HistoricalComparison })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-64 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-5 w-5 animate-spin" />
          Loading Historical Comparison...
        </div>
      </div>
    ),
    ssr: false,
  }
)

const ExportableReports = dynamic(
  () => import("@/components/analytics/exportable-reports").then(mod => ({ default: mod.ExportableReports })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-64 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-5 w-5 animate-spin" />
          Loading Exportable Reports...
        </div>
      </div>
    ),
    ssr: false,
  }
)

export default function RealTimeAnalyticsPage() {
  return (
    <div className="container py-8 md:py:12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Advanced Analytics Dashboard</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Real-time metrics, historical comparisons, and exportable reports</p>
      </div>

      <UnifiedGovernanceNav />

      <div className="mb-6">
        <Tabs defaultValue="real-time" className="w-full">
          <TabsList className="grid w-full grid-cols-3 glass mb-6">
            <TabsTrigger value="real-time" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
              Real-Time Metrics
            </TabsTrigger>
            <TabsTrigger
              value="historical"
              className="data-[state=active]:text-dogechain data-[state=active]:bg-dogechain/10"
            >
              Historical Comparison
            </TabsTrigger>
            <TabsTrigger
              value="reports"
              className="data-[state=active]:text-green-500 data-[state=active]:bg-green-500/10"
            >
              Exportable Reports
            </TabsTrigger>
          </TabsList>

          <TabsContent value="real-time">
            <ErrorBoundary>
              <Suspense fallback={<AnalyticsSkeleton />}>
                <RealTimeMetrics />
              </Suspense>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="historical">
            <ErrorBoundary>
              <Suspense fallback={<AnalyticsSkeleton />}>
                <HistoricalComparison />
              </Suspense>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="reports">
            <ErrorBoundary>
              <Suspense fallback={<AnalyticsSkeleton />}>
                <ExportableReports />
              </Suspense>
            </ErrorBoundary>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

function AnalyticsSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-10 w-40" />
      </div>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Skeleton className="h-32 w-full rounded-lg" />
        <Skeleton className="h-32 w-full rounded-lg" />
        <Skeleton className="h-32 w-full rounded-lg" />
        <Skeleton className="h-32 w-full rounded-lg" />
      </div>
      <div className="grid gap-6 md:grid-cols-2">
        <Skeleton className="h-80 w-full rounded-lg" />
        <Skeleton className="h-80 w-full rounded-lg" />
      </div>
      <Skeleton className="h-96 w-full rounded-lg" />
    </div>
  )
}
