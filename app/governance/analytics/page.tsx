"use client"

import dynamic from "next/dynamic"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { ErrorBoundary } from "@/components/error-boundary"
import { Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"
import { Activity, ChevronRight, FileText, History, Loader2 } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"

// Dynamic import for the large GovernanceAnalyticsDashboard component
const GovernanceAnalyticsDashboard = dynamic(
  () => import("@/components/governance/governance-analytics-dashboard").then(mod => ({ default: mod.GovernanceAnalyticsDashboard })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-96 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-6 w-6 animate-spin" />
          Loading Governance Analytics...
        </div>
      </div>
    ),
    ssr: false,
  }
)

export default function GovernanceAnalyticsPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Governance Analytics</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Insights into governance participation and outcomes</p>
      </div>

      <UnifiedGovernanceNav />

      <ErrorBoundary>
        <Suspense fallback={<AnalyticsSkeleton />}>
          <GovernanceAnalyticsDashboard />
        </Suspense>
      </ErrorBoundary>

      <div className="mt-8">
        <h2 className="text-2xl font-bold mb-4 text-white">Advanced Analytics</h2>
        <div className="grid gap-6 md:grid-cols-3">
          <Link href="/governance/analytics/real-time" className="block">
            <Card className="glass-card border-white/5 hover:border-white/10 transition-colors h-full">
              <CardContent className="p-6">
                <div className="mb-4 rounded-full bg-doge/10 p-3 w-fit">
                  <Activity className="h-6 w-6 text-doge" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Real-time Metrics</h3>
                <p className="text-white/70">Monitor platform activity with live data updates and alerts</p>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="link" className="text-doge p-0">
                  View Real-time Dashboard
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </Link>

          <Link href="/governance/analytics/real-time?tab=historical" className="block">
            <Card className="glass-card border-white/5 hover:border-white/10 transition-colors h-full">
              <CardContent className="p-6">
                <div className="mb-4 rounded-full bg-dogechain/10 p-3 w-fit">
                  <History className="h-6 w-6 text-dogechain" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Historical Comparison</h3>
                <p className="text-white/70">Compare current metrics with historical data to identify trends</p>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="link" className="text-dogechain p-0">
                  View Historical Data
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </Link>

          <Link href="/governance/analytics/real-time?tab=reports" className="block">
            <Card className="glass-card border-white/5 hover:border-white/10 transition-colors h-full">
              <CardContent className="p-6">
                <div className="mb-4 rounded-full bg-green-500/10 p-3 w-fit">
                  <FileText className="h-6 w-6 text-green-500" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Exportable Reports</h3>
                <p className="text-white/70">Generate and download comprehensive governance reports</p>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="link" className="text-green-500 p-0">
                  Generate Reports
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}

function AnalyticsSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-10 w-40" />
      </div>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Skeleton className="h-32 w-full rounded-lg" />
        <Skeleton className="h-32 w-full rounded-lg" />
        <Skeleton className="h-32 w-full rounded-lg" />
        <Skeleton className="h-32 w-full rounded-lg" />
      </div>
      <div className="grid gap-6 md:grid-cols-2">
        <Skeleton className="h-80 w-full rounded-lg" />
        <Skeleton className="h-80 w-full rounded-lg" />
      </div>
      <div className="grid gap-6 md:grid-cols-3">
        <Skeleton className="h-96 w-full rounded-lg md:col-span-2" />
        <Skeleton className="h-96 w-full rounded-lg" />
      </div>
    </div>
  )
}
