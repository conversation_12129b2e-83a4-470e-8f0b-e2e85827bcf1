import { Skeleton } from "@/components/ui/skeleton"


export default function DevelopmentDAOLoading() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <Skeleton className="h-10 w-80 mb-2" />
        <Skeleton className="h-6 w-96" />
      </div>

      {/* Navigation skeleton */}
      <div className="mb-8">
        <div className="flex space-x-2 mb-4">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-24 w-full" />
          ))}
        </div>
      </div>

      {/* Loading state */}
      <div className="mt-8 flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <div className="h-12 w-12 rounded-full border-4 border-t-doge border-white/10 animate-spin"></div>
          <Skeleton className="h-6 w-48" />
        </div>
      </div>
    </div>
  )
}
