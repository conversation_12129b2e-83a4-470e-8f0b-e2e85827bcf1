"use client"

import dynamic from "next/dynamic"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react"
import { useEffect, useState } from "react"
import { ErrorBoundary } from "@/components/error-boundary"
import { LoadingState } from "@/components/ui/loading-state"

// Dynamic import for the large DevelopmentDAOAdmin component
const DevelopmentDAOAdmin = dynamic(
  () => import("@/components/admin/development-dao-admin").then(mod => ({ default: mod.DevelopmentDAOAdmin })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-96 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-6 w-6 animate-spin" />
          Loading Development DAO Admin...
        </div>
      </div>
    ),
    ssr: false,
  }
)

// In a real application, this would be a proper auth check
const checkAdminAccess = async () => {
  // Simulate API call to check admin access
  await new Promise((resolve) => setTimeout(resolve, 500))
  return true // For demo purposes, always return true
}

export default function GovernanceDevelopmentDAOAdminPage() {
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const verifyAccess = async () => {
      try {
        const hasAccess = await checkAdminAccess()
        setIsAdmin(hasAccess)
      } catch (error) {
        console.error("Error verifying admin access:", error)
        setIsAdmin(false)
      } finally {
        setIsLoading(false)
      }
    }

    verifyAccess()
  }, [])

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Development DAO Administration</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Manage development governance, tasks, and community contributions</p>
      </div>

      <UnifiedGovernanceNav />

      <div className="mt-8">
        {isLoading ? (
          <LoadingState title="Verifying admin access..." />
        ) : isAdmin ? (
          <ErrorBoundary>
            <DevelopmentDAOAdmin />
          </ErrorBoundary>
        ) : (
          <Alert className="glass-card border-red-500/20 bg-red-500/5">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <AlertTitle className="text-white">Access Denied</AlertTitle>
            <AlertDescription className="text-white/70">
              You do not have permission to access the Development DAO administration panel.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  )
}
