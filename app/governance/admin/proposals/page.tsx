"use client"

import { AdminProposalManagement } from "@/components/admin/proposal-management"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { AdminPageBreadcrumb } from "@/components/governance/breadcrumb-nav"
import { useEffect } from "react"

// Set page title
const pageTitle = "Proposal Management | Governance Admin | PawPumps"

export default function GovernanceProposalsAdmin() {
  // Set document title
  useEffect(() => {
    document.title = pageTitle
  }, [])

  return (
    <div className="container py-8 md:py-12">
      <AdminPageBreadcrumb
        title="Proposal Management"
        description="Create, edit, and manage governance proposals"
      />

      <UnifiedGovernanceNav />

      <div className="mt-8">
        <AdminProposalManagement />
      </div>
    </div>
  )
}
