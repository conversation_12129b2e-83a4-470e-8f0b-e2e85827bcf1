"use client"

import { useState } from "react"
import { Flag, AlertTriangle, CheckCircle, XCircle, Search, Shield } from "lucide-react"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"

// Mock data for reported content
const MOCK_REPORTS = [
  {
    id: "rep-001",
    postId: "post-123",
    reportedBy: "0x1a2b...3c4d",
    reportedUser: "0x5e6f...7g8h",
    reason: "Spam",
    details: "User is posting the same promotional content across multiple threads",
    timestamp: "2025-04-15T14:30:00Z",
    status: "pending",
    content: "Check out this amazing opportunity! Limited time offer! 🚀 #notascam",
    reportCount: 3,
  },
  {
    id: "rep-002",
    postId: "post-456",
    reportedBy: "0x9i8j...7k6l",
    reportedUser: "0x5m4n...3o2p",
    reason: "Harassment",
    details: "User is making personal attacks against community members",
    timestamp: "2025-04-14T09:15:00Z",
    status: "pending",
    content: "You're all idiots for investing in this project. Only losers would buy this token.",
    reportCount: 7,
  },
  {
    id: "rep-003",
    postId: "post-789",
    reportedBy: "0x1q2r...3s4t",
    reportedUser: "0x5u6v...7w8x",
    reason: "Misinformation",
    details: "User is spreading false information about token launch",
    timestamp: "2025-04-13T16:45:00Z",
    status: "reviewed",
    content: "BREAKING: PawPumps team is selling all their tokens tomorrow! Dump now!",
    reportCount: 12,
    moderatorNotes: "Warning issued to user. Post hidden from public view.",
  },
  {
    id: "rep-004",
    postId: "post-012",
    reportedBy: "0x9y8z...7a6b",
    reportedUser: "0x5c4d...3e2f",
    reason: "Inappropriate Content",
    details: "User posted explicit content inappropriate for the community",
    timestamp: "2025-04-12T11:20:00Z",
    status: "resolved",
    content: "[Content hidden due to community guidelines violation]",
    reportCount: 15,
    moderatorNotes: "User account suspended for 7 days. Content removed.",
    resolution: "User suspended",
  },
  {
    id: "rep-005",
    postId: "post-345",
    reportedBy: "0x1g2h...3i4j",
    reportedUser: "0x5k4l...3m2n",
    reason: "Scam",
    details: "User is attempting to phish for wallet information",
    timestamp: "2025-04-11T08:10:00Z",
    status: "dismissed",
    content: "Having wallet issues? DM me your seed phrase and I'll help you fix it!",
    reportCount: 9,
    moderatorNotes: "False positive. This was a screenshot of a scam as an example of what to avoid.",
  },
]

// Status badge component
function StatusBadge({ status }: { status: string }) {
  const statusConfig = {
    pending: { color: "bg-yellow-500", icon: <AlertTriangle className="h-3 w-3" /> },
    reviewed: { color: "bg-blue-500", icon: <Shield className="h-3 w-3" /> },
    resolved: { color: "bg-green-500", icon: <CheckCircle className="h-3 w-3" /> },
    dismissed: { color: "bg-gray-500", icon: <XCircle className="h-3 w-3" /> },
  }

  const config = statusConfig[status as keyof typeof statusConfig]

  return (
    <span className={`flex items-center gap-1 rounded-full ${config.color} px-2 py-1 text-xs font-medium text-white`}>
      {config.icon}
      <span className="capitalize">{status}</span>
    </span>
  )
}

// Report card component
function ReportCard({ report, onReview }: { report: any; onReview: (id: string) => void }) {
  return (
    <div className="glass-card mb-4 overflow-hidden rounded-lg border border-white/10 bg-black/30 p-4 backdrop-blur-sm">
      <div className="mb-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Flag className="h-4 w-4 text-red-400" />
          <h3 className="font-medium text-white">Report #{report.id}</h3>
          <StatusBadge status={report.status} />
        </div>
        <span className="text-xs text-white/60">{new Date(report.timestamp).toLocaleString()}</span>
      </div>

      <div className="mb-3 rounded-md bg-black/20 p-3">
        <p className="mb-1 text-sm text-white/80">{report.content}</p>
        <div className="flex items-center justify-between">
          <span className="text-xs text-white/60">Posted by: {report.reportedUser}</span>
          <span className="rounded bg-red-500/20 px-1.5 py-0.5 text-xs text-red-400">
            Reported {report.reportCount} times
          </span>
        </div>
      </div>

      <div className="mb-3 grid grid-cols-2 gap-3">
        <div>
          <h4 className="mb-1 text-xs font-medium text-white/60">Reason</h4>
          <p className="text-sm text-white">{report.reason}</p>
        </div>
        <div>
          <h4 className="mb-1 text-xs font-medium text-white/60">Reported By</h4>
          <p className="text-sm text-white">{report.reportedBy}</p>
        </div>
      </div>

      <div className="mb-3">
        <h4 className="mb-1 text-xs font-medium text-white/60">Details</h4>
        <p className="text-sm text-white">{report.details}</p>
      </div>

      {report.moderatorNotes && (
        <div className="mb-3 rounded-md bg-blue-500/10 p-2">
          <h4 className="mb-1 text-xs font-medium text-blue-400">Moderator Notes</h4>
          <p className="text-sm text-white">{report.moderatorNotes}</p>
        </div>
      )}

      {report.status === "pending" && (
        <button
          onClick={() => onReview(report.id)}
          className="w-full rounded-md bg-doge px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-doge/80"
        >
          Review Report
        </button>
      )}
    </div>
  )
}

// Review modal component
function ReviewModal({
  report,
  onClose,
  onAction,
}: { report: any; onClose: () => void; onAction: (action: string, notes: string) => void }) {
  const [notes, setNotes] = useState("")

  if (!report) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
      <div className="glass-card max-h-[80vh] w-full max-w-2xl overflow-y-auto rounded-lg border border-white/10 bg-black/80 p-6 backdrop-blur-md">
        <h2 className="mb-4 text-xl font-bold text-white">Review Report #{report.id}</h2>

        <div className="mb-4 rounded-md bg-black/30 p-4">
          <h3 className="mb-2 text-sm font-medium text-white/80">Reported Content</h3>
          <p className="text-white">{report.content}</p>
          <div className="mt-2 flex justify-between">
            <span className="text-xs text-white/60">Posted by: {report.reportedUser}</span>
            <span className="text-xs text-white/60">Reported {report.reportCount} times</span>
          </div>
        </div>

        <div className="mb-4">
          <h3 className="mb-2 text-sm font-medium text-white/80">Moderator Notes</h3>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="h-32 w-full rounded-md border border-white/10 bg-black/30 p-3 text-white"
            placeholder="Add your notes about this report..."
          />
        </div>

        <div className="mb-4">
          <h3 className="mb-2 text-sm font-medium text-white/80">Take Action</h3>
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={() => onAction("warning", notes)}
              className="rounded-md bg-yellow-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-yellow-700"
            >
              Issue Warning
            </button>
            <button
              onClick={() => onAction("hide", notes)}
              className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700"
            >
              Hide Post
            </button>
            <button
              onClick={() => onAction("suspend", notes)}
              className="rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-red-700"
            >
              Suspend User
            </button>
            <button
              onClick={() => onAction("dismiss", notes)}
              className="rounded-md bg-gray-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-gray-700"
            >
              Dismiss Report
            </button>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="rounded-md bg-white/10 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-white/20"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  )
}

export default function ModerationPage() {
  const [activeTab, setActiveTab] = useState("pending")
  const [searchQuery, setSearchQuery] = useState("")
  const [reviewingReport, setReviewingReport] = useState<any>(null)

  // Filter reports based on active tab and search query
  const filteredReports = MOCK_REPORTS.filter((report) => {
    const matchesTab = report.status === activeTab
    const matchesSearch =
      report.reportedUser.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.reason.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesTab && (searchQuery === "" || matchesSearch)
  })

  const handleReview = (id: string) => {
    const report = MOCK_REPORTS.find((r) => r.id === id)
    setReviewingReport(report)
  }

  const handleAction = (action: string, notes: string) => {
    // In a real app, this would update the database
    console.log(`Taking action: ${action} on report ${reviewingReport?.id}`)
    console.log(`Moderator notes: ${notes}`)

    // Close the modal
    setReviewingReport(null)
  }

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Content Moderation</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Review and manage reported content across the platform</p>
      </div>

      <UnifiedGovernanceNav />

      <div className="mt-8 glass-card mb-6 rounded-lg border border-white/10 bg-black/30 p-6 backdrop-blur-sm">
        <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-2">
            <Flag className="h-5 w-5 text-red-400" />
            <h2 className="text-xl font-bold text-white">Reported Content</h2>
          </div>

          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/50" />
            <input
              type="text"
              placeholder="Search reports..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full rounded-md border border-white/10 bg-black/30 py-2 pl-10 pr-4 text-sm text-white placeholder-white/50"
            />
          </div>
        </div>

        <div className="mb-6 flex border-b border-white/10">
          <button
            onClick={() => setActiveTab("pending")}
            className={`flex items-center gap-1 border-b-2 px-4 py-2 text-sm font-medium ${
              activeTab === "pending" ? "border-doge text-doge" : "border-transparent text-white/70 hover:text-white"
            }`}
          >
            <AlertTriangle className="h-4 w-4" />
            <span>Pending</span>
            <span className="ml-1 rounded-full bg-yellow-500/20 px-2 py-0.5 text-xs text-yellow-400">
              {MOCK_REPORTS.filter((r) => r.status === "pending").length}
            </span>
          </button>

          <button
            onClick={() => setActiveTab("reviewed")}
            className={`flex items-center gap-1 border-b-2 px-4 py-2 text-sm font-medium ${
              activeTab === "reviewed" ? "border-doge text-doge" : "border-transparent text-white/70 hover:text-white"
            }`}
          >
            <Shield className="h-4 w-4" />
            <span>Reviewed</span>
          </button>

          <button
            onClick={() => setActiveTab("resolved")}
            className={`flex items-center gap-1 border-b-2 px-4 py-2 text-sm font-medium ${
              activeTab === "resolved" ? "border-doge text-doge" : "border-transparent text-white/70 hover:text-white"
            }`}
          >
            <CheckCircle className="h-4 w-4" />
            <span>Resolved</span>
          </button>

          <button
            onClick={() => setActiveTab("dismissed")}
            className={`flex items-center gap-1 border-b-2 px-4 py-2 text-sm font-medium ${
              activeTab === "dismissed" ? "border-doge text-doge" : "border-transparent text-white/70 hover:text-white"
            }`}
          >
            <XCircle className="h-4 w-4" />
            <span>Dismissed</span>
          </button>
        </div>

        {filteredReports.length > 0 ? (
          <div>
            {filteredReports.map((report) => (
              <ReportCard key={report.id} report={report} onReview={handleReview} />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center rounded-md bg-black/20 py-12">
            <Flag className="mb-2 h-12 w-12 text-white/20" />
            <h3 className="mb-1 text-lg font-medium text-white">No reports found</h3>
            <p className="text-center text-sm text-white/60">
              {searchQuery ? "No reports match your search criteria" : `There are no ${activeTab} reports at this time`}
            </p>
          </div>
        )}
      </div>

      {reviewingReport && (
        <ReviewModal report={reviewingReport} onClose={() => setReviewingReport(null)} onAction={handleAction} />
      )}
    </div>
  )
}
