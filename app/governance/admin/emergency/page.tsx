"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { AlertTriangle, Clock } from "lucide-react"
import { useState, useEffect } from "react"

// Set page title
const pageTitle = "Emergency Controls | Governance Admin | PawPumps"

// Import the audit logger hook
import { useAuditLog } from "@/utils/audit-logger"
import { RequirePermission } from "@/components/admin/access-control"
// Add imports
import { MultiSignatureConfirmation } from "@/components/admin/multi-signature-confirmation"

// Add the hook to the component
export default function EmergencyControlsPage() {
  // Set document title
  useEffect(() => {
    document.title = pageTitle
  }, [])

  const [systemStatus, setSystemStatus] = useState({
    trading: true,
    tokenLaunch: true,
    governance: true,
    staking: true,
    withdrawals: true,
  })
  const [confirmationOpen, setConfirmationOpen] = useState(false)
  const { logAction } = useAuditLog()

  // Add state for emergency shutdown confirmation
  const [showEmergencyShutdownConfirmation, setShowEmergencyShutdownConfirmation] = useState(false)
  // Add state for pending status change
  const [pendingStatusChange, setPendingStatusChange] = useState<{
    system: keyof typeof systemStatus
    enabled: boolean
  } | null>(null)

  // Update the handleSystemStatusChange function
  const handleSystemStatusChange = async (system: keyof typeof systemStatus, enabled: boolean) => {
    // For critical systems, require confirmation
    if (system === "trading" || system === "withdrawals") {
      setConfirmationOpen(true)
      // Store the pending change to apply after confirmation
      setPendingStatusChange({ system, enabled })
      return
    }

    // For non-critical systems, apply immediately
    setSystemStatus({ ...systemStatus, [system]: enabled })

    // Log the action
    await logAction(
      enabled ? "emergency:resume" : "emergency:pause",
      "admin", // In a real app, this would be the current user's wallet address
      {
        system,
        previousState: systemStatus[system],
        newState: enabled,
      }
    )
  }

  // Add function to handle emergency shutdown
  const handleEmergencyShutdown = () => {
    setShowEmergencyShutdownConfirmation(true)
  }

  // Add function to execute emergency shutdown
  const executeEmergencyShutdown = async () => {
    // Update all systems to paused
    const newStatus = {
      trading: false,
      tokenLaunch: false,
      governance: false,
      staking: false,
      withdrawals: false,
    }

    setSystemStatus(newStatus)

    // Log the action
    await logAction(
      "emergency:shutdown",
      "admin", // In a real app, this would be the current user's wallet address
      {
        previousState: systemStatus,
        newState: newStatus,
      }
    )

    // In a real app, this would call the smart contract to pause all systems
    return new Promise<void>((resolve) => setTimeout(resolve, 2000))
  }

  // Update the JSX to use the new function
  // Replace the Switch components with this updated version:
  // For example, for the trading system:

  // Wrap the entire component content with RequirePermission
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Emergency Controls</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Critical system controls for emergency situations</p>
      </div>

      <UnifiedGovernanceNav />

      <RequirePermission permission="emergency:control">
        <div className="mt-8">
          <Alert className="mb-6 glass-card border-red-500/20 bg-red-500/5">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <AlertTitle className="text-white">Critical Controls</AlertTitle>
            <AlertDescription className="text-white/70">
              These controls should only be used in emergency situations. All actions are logged and require
              multi-signature approval.
            </AlertDescription>
          </Alert>

          <Card className="glass-card border-white/5 bg-black/20 mb-6">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-white">System Status</CardTitle>
                <Badge className="bg-green-500/20 text-green-500">All Systems Operational</Badge>
              </div>
              <CardDescription className="text-white/70">
                Current status of all platform systems and features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Trading System</Label>
                  <p className="text-sm text-white/70">Token swaps and trading functionality</p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    className={systemStatus.trading ? "bg-green-500/20 text-green-500" : "bg-red-500/20 text-red-500"}
                  >
                    {systemStatus.trading ? "Active" : "Paused"}
                  </Badge>
                  <Switch
                    checked={systemStatus.trading}
                    onCheckedChange={(checked) => handleSystemStatusChange("trading", checked)}
                  />
                </div>
              </div>

              <Separator className="bg-white/10" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Token Launch</Label>
                  <p className="text-sm text-white/70">New token creation and launches</p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    className={
                      systemStatus.tokenLaunch ? "bg-green-500/20 text-green-500" : "bg-red-500/20 text-red-500"
                    }
                  >
                    {systemStatus.tokenLaunch ? "Active" : "Paused"}
                  </Badge>
                  <Switch
                    checked={systemStatus.tokenLaunch}
                    onCheckedChange={(checked) => handleSystemStatusChange("tokenLaunch", checked)}
                  />
                </div>
              </div>

              <Separator className="bg-white/10" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Governance</Label>
                  <p className="text-sm text-white/70">Proposal creation and voting</p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    className={
                      systemStatus.governance ? "bg-green-500/20 text-green-500" : "bg-red-500/20 text-red-500"
                    }
                  >
                    {systemStatus.governance ? "Active" : "Paused"}
                  </Badge>
                  <Switch
                    checked={systemStatus.governance}
                    onCheckedChange={(checked) => handleSystemStatusChange("governance", checked)}
                  />
                </div>
              </div>

              <Separator className="bg-white/10" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Staking</Label>
                  <p className="text-sm text-white/70">Token staking and rewards</p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    className={systemStatus.staking ? "bg-green-500/20 text-green-500" : "bg-red-500/20 text-red-500"}
                  >
                    {systemStatus.staking ? "Active" : "Paused"}
                  </Badge>
                  <Switch
                    checked={systemStatus.staking}
                    onCheckedChange={(checked) => handleSystemStatusChange("staking", checked)}
                  />
                </div>
              </div>

              <Separator className="bg-white/10" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Withdrawals</Label>
                  <p className="text-sm text-white/70">Token withdrawals and transfers</p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    className={
                      systemStatus.withdrawals ? "bg-green-500/20 text-green-500" : "bg-red-500/20 text-red-500"
                    }
                  >
                    {systemStatus.withdrawals ? "Active" : "Paused"}
                  </Badge>
                  <Switch
                    checked={systemStatus.withdrawals}
                    onCheckedChange={(checked) => handleSystemStatusChange("withdrawals", checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-6 md:grid-cols-2">
            <Card className="glass-card border-red-500/20 bg-red-500/5">
              <CardHeader>
                <CardTitle className="text-white">Emergency Shutdown</CardTitle>
                <CardDescription className="text-white/70">Immediately pause all platform activity</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4 text-sm text-white/70">
                  This will immediately pause all trading, token launches, governance, staking, and withdrawals. Use
                  only in critical situations.
                </p>
                <Button className="w-full bg-red-600 hover:bg-red-700 text-white" onClick={handleEmergencyShutdown}>
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Initiate Emergency Shutdown
                </Button>
              </CardContent>
            </Card>

            <Card className="glass-card border-white/5 bg-black/20">
              <CardHeader>
                <CardTitle className="text-white">Recent Emergency Actions</CardTitle>
                <CardDescription className="text-white/70">Log of recent emergency control usage</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Clock className="mt-0.5 h-4 w-4 text-white/50" />
                    <div>
                      <p className="text-sm font-medium text-white">Trading System Paused</p>
                      <p className="text-xs text-white/60">Apr 28, 2025 at 14:32 UTC by 0x1a2b...3c4d</p>
                      <p className="text-xs text-white/60">Reason: Unusual trading volume detected</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Clock className="mt-0.5 h-4 w-4 text-white/50" />
                    <div>
                      <p className="text-sm font-medium text-white">Trading System Resumed</p>
                      <p className="text-xs text-white/60">Apr 28, 2025 at 16:45 UTC by 0x1a2b...3c4d</p>
                      <p className="text-xs text-white/60">Reason: Issue resolved after investigation</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </RequirePermission>
      {showEmergencyShutdownConfirmation && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <MultiSignatureConfirmation
            title="Emergency Shutdown"
            description="This will immediately pause all platform activity"
            action="EMERGENCY_SHUTDOWN"
            details={{
              affectedSystems: ["trading", "tokenLaunch", "governance", "staking", "withdrawals"],
              initiatedAt: new Date().toISOString(),
            }}
            requiredSignatures={3}
            onConfirm={executeEmergencyShutdown}
            onCancel={() => setShowEmergencyShutdownConfirmation(false)}
          />
        </div>
      )}

      {confirmationOpen && pendingStatusChange && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <MultiSignatureConfirmation
            title={`${pendingStatusChange.enabled ? "Resume" : "Pause"} ${pendingStatusChange.system}`}
            description={`This will ${pendingStatusChange.enabled ? "resume" : "pause"} the ${pendingStatusChange.system} system`}
            action={`${pendingStatusChange.enabled ? "RESUME" : "PAUSE"}_${pendingStatusChange.system.toUpperCase()}`}
            details={{
              system: pendingStatusChange.system,
              currentState: systemStatus[pendingStatusChange.system],
              newState: pendingStatusChange.enabled,
              initiatedAt: new Date().toISOString(),
            }}
            requiredSignatures={2}
            onConfirm={async () => {
              setSystemStatus({ ...systemStatus, [pendingStatusChange.system]: pendingStatusChange.enabled })

              // Log the action
              await logAction(
                pendingStatusChange.enabled ? "emergency:resume" : "emergency:pause",
                "admin", // In a real app, this would be the current user's wallet address
                {
                  system: pendingStatusChange.system,
                  previousState: systemStatus[pendingStatusChange.system],
                  newState: pendingStatusChange.enabled,
                }
              )

              setPendingStatusChange(null)
              setConfirmationOpen(false)
            }}
            onCancel={() => {
              setPendingStatusChange(null)
              setConfirmationOpen(false)
            }}
          />
        </div>
      )}
    </div>
  )
}
