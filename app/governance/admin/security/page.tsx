"use client"

import type React from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShimmerText } from "@/components/shimmer-text"
import { Shield, Lock, AlertTriangle, Eye, Key, UserX, Activity, CheckCircle } from "lucide-react"

interface SecurityEvent {
  id: string
  type: "login" | "failed_login" | "permission_change" | "suspicious_activity"
  description: string
  severity: "low" | "medium" | "high" | "critical"
  timestamp: string
  userAddress?: string
  ipAddress: string
  status: "resolved" | "investigating" | "open"
}

const mockSecurityEvents: SecurityEvent[] = [
  {
    id: "1",
    type: "suspicious_activity",
    description: "Multiple failed login attempts from same IP",
    severity: "high",
    timestamp: "2024-06-28T14:30:00Z",
    userAddress: "0x1234...5678",
    ipAddress: "*************",
    status: "investigating",
  },
  {
    id: "2",
    type: "permission_change",
    description: "Admin permissions granted to user",
    severity: "medium",
    timestamp: "2024-06-28T12:15:00Z",
    userAddress: "0x9876...4321",
    ipAddress: "*********",
    status: "resolved",
  },
  {
    id: "3",
    type: "failed_login",
    description: "Failed login attempt with invalid signature",
    severity: "low",
    timestamp: "2024-06-28T11:45:00Z",
    ipAddress: "************",
    status: "open",
  },
]

function getSeverityBadge(severity: string) {
  const colors = {
    low: "bg-green-500/20 text-green-500",
    medium: "bg-yellow-500/20 text-yellow-500",
    high: "bg-orange-500/20 text-orange-500",
    critical: "bg-red-500/20 text-red-500",
  }
  return <Badge className={colors[severity as keyof typeof colors]}>{severity}</Badge>
}

function getStatusBadge(status: string) {
  const colors = {
    open: "bg-red-500/20 text-red-500",
    investigating: "bg-yellow-500/20 text-yellow-500",
    resolved: "bg-green-500/20 text-green-500",
  }
  return <Badge className={colors[status as keyof typeof colors]}>{status}</Badge>
}

function getTypeIcon(type: string) {
  switch (type) {
    case "login":
      return <Key className="h-4 w-4" />
    case "failed_login":
      return <UserX className="h-4 w-4" />
    case "permission_change":
      return <Shield className="h-4 w-4" />
    case "suspicious_activity":
      return <AlertTriangle className="h-4 w-4" />
    default:
      return <Activity className="h-4 w-4" />
  }
}

export default function SecuritySettings() {
  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Security Settings</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Configure security settings and monitor threats</p>
      </div>

      <div className="grid gap-6 md:grid-cols-4 mb-8">
        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <Shield className="mr-2 h-5 w-5" />
              Security Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">95%</div>
            <p className="text-xs text-white/70">Excellent</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Active Threats
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-500">2</div>
            <p className="text-xs text-white/70">Under investigation</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <Eye className="mr-2 h-5 w-5" />
              Failed Logins
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">47</div>
            <p className="text-xs text-white/70">Last 24 hours</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <CheckCircle className="mr-2 h-5 w-5" />
              Resolved
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">156</div>
            <p className="text-xs text-white/70">This month</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2 mb-8">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Security Configuration</CardTitle>
            <CardDescription className="text-white/70">
              Configure security policies and settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-white">Two-Factor Authentication</span>
              <Badge className="bg-green-500/20 text-green-500">Enabled</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white">IP Whitelist</span>
              <Badge className="bg-green-500/20 text-green-500">Active</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white">Rate Limiting</span>
              <Badge className="bg-green-500/20 text-green-500">Enabled</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white">Audit Logging</span>
              <Badge className="bg-green-500/20 text-green-500">Active</Badge>
            </div>
            <Button className="w-full glass-button">
              <Lock className="mr-2 h-4 w-4" />
              Configure Settings
            </Button>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Emergency Actions</CardTitle>
            <CardDescription className="text-white/70">
              Emergency security controls
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button variant="outline" className="w-full glass-button">
              <Shield className="mr-2 h-4 w-4" />
              Enable Emergency Mode
            </Button>
            <Button variant="outline" className="w-full glass-button">
              <UserX className="mr-2 h-4 w-4" />
              Suspend All Users
            </Button>
            <Button variant="outline" className="w-full glass-button">
              <Lock className="mr-2 h-4 w-4" />
              Lock All Transactions
            </Button>
            <Button variant="outline" className="w-full glass-button text-red-500 border-red-500/20">
              <AlertTriangle className="mr-2 h-4 w-4" />
              Emergency Shutdown
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Security Events</CardTitle>
          <CardDescription className="text-white/70">
            Recent security events and alerts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockSecurityEvents.map((event) => (
              <div
                key={event.id}
                className="flex items-center justify-between p-4 rounded-lg border border-white/10 bg-white/5"
              >
                <div className="flex items-center space-x-4">
                  <div className="p-2 rounded-full bg-white/5">
                    {getTypeIcon(event.type)}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <p className="font-medium text-white">{event.description}</p>
                      {getSeverityBadge(event.severity)}
                      {getStatusBadge(event.status)}
                    </div>
                    <div className="flex items-center space-x-4 text-xs text-white/50">
                      <span>Time: {new Date(event.timestamp).toLocaleString()}</span>
                      <span>IP: {event.ipAddress}</span>
                      {event.userAddress && <span>User: {event.userAddress}</span>}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" className="glass-button">
                    Investigate
                  </Button>
                  {event.status !== "resolved" && (
                    <Button size="sm" className="glass-button">
                      Resolve
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
