"use client"

import type React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShimmerText } from "@/components/shimmer-text"
import { Bell, Send, Users, Alert<PERSON>riangle, Info, CheckCircle } from "lucide-react"

interface Notification {
  id: string
  title: string
  message: string
  type: "info" | "warning" | "success" | "error"
  recipients: "all" | "admins" | "moderators" | "specific"
  status: "draft" | "sent" | "scheduled"
  createdAt: string
  sentAt?: string
  recipientCount?: number
}

const mockNotifications: Notification[] = [
  {
    id: "1",
    title: "System Maintenance Scheduled",
    message: "Scheduled maintenance will occur on June 30th from 2-4 AM UTC.",
    type: "warning",
    recipients: "all",
    status: "sent",
    createdAt: "2024-06-28T10:00:00Z",
    sentAt: "2024-06-28T10:05:00Z",
    recipientCount: 1247,
  },
  {
    id: "2",
    title: "New Governance Proposal",
    message: "A new proposal for treasury allocation has been submitted for voting.",
    type: "info",
    recipients: "all",
    status: "sent",
    createdAt: "2024-06-27T15:30:00Z",
    sentAt: "2024-06-27T15:35:00Z",
    recipientCount: 1247,
  },
  {
    id: "3",
    title: "Emergency Protocol Activated",
    message: "Emergency protocol has been activated due to suspicious activity.",
    type: "error",
    recipients: "admins",
    status: "draft",
    createdAt: "2024-06-28T14:00:00Z",
    recipientCount: 5,
  },
]

function getTypeIcon(type: string) {
  switch (type) {
    case "warning":
      return <AlertTriangle className="h-4 w-4" />
    case "error":
      return <AlertTriangle className="h-4 w-4" />
    case "success":
      return <CheckCircle className="h-4 w-4" />
    default:
      return <Info className="h-4 w-4" />
  }
}

function getTypeBadge(type: string) {
  const colors = {
    info: "bg-blue-500/20 text-blue-500",
    warning: "bg-yellow-500/20 text-yellow-500",
    success: "bg-green-500/20 text-green-500",
    error: "bg-red-500/20 text-red-500",
  }
  return (
    <Badge className={colors[type as keyof typeof colors]}>
      {getTypeIcon(type)}
      <span className="ml-1">{type}</span>
    </Badge>
  )
}

function getStatusBadge(status: string) {
  const colors = {
    draft: "bg-gray-500/20 text-gray-500",
    sent: "bg-green-500/20 text-green-500",
    scheduled: "bg-blue-500/20 text-blue-500",
  }
  return <Badge className={colors[status as keyof typeof colors]}>{status}</Badge>
}

export default function NotificationsManagement() {
  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Notifications Management</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Manage system notifications and announcements</p>
      </div>

      <div className="grid gap-6 md:grid-cols-4 mb-8">
        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <Send className="mr-2 h-5 w-5" />
              Total Sent
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">156</div>
            <p className="text-xs text-white/70">This month</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <Bell className="mr-2 h-5 w-5" />
              Pending
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-500">3</div>
            <p className="text-xs text-white/70">Awaiting approval</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <Users className="mr-2 h-5 w-5" />
              Recipients
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">1,247</div>
            <p className="text-xs text-white/70">Active users</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <CheckCircle className="mr-2 h-5 w-5" />
              Delivery Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">98.5%</div>
            <p className="text-xs text-white/70">Last 30 days</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2 mb-8">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Create Notification</CardTitle>
            <CardDescription className="text-white/70">
              Send notifications to users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full glass-button">
              <Send className="mr-2 h-4 w-4" />
              Create New Notification
            </Button>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Notification Templates</CardTitle>
            <CardDescription className="text-white/70">
              Manage reusable templates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full glass-button" variant="outline">
              Manage Templates
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Recent Notifications</CardTitle>
          <CardDescription className="text-white/70">
            View and manage sent notifications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockNotifications.map((notification) => (
              <div
                key={notification.id}
                className="flex items-center justify-between p-4 rounded-lg border border-white/10 bg-white/5"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="font-medium text-white">{notification.title}</h3>
                    {getTypeBadge(notification.type)}
                    {getStatusBadge(notification.status)}
                  </div>
                  <p className="text-sm text-white/70 mb-2">{notification.message}</p>
                  <div className="flex items-center space-x-4 text-xs text-white/50">
                    <span>Recipients: {notification.recipients}</span>
                    <span>Count: {notification.recipientCount}</span>
                    <span>Created: {new Date(notification.createdAt).toLocaleDateString()}</span>
                    {notification.sentAt && (
                      <span>Sent: {new Date(notification.sentAt).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" className="glass-button">
                    View
                  </Button>
                  {notification.status === "draft" && (
                    <Button size="sm" className="glass-button">
                      Send
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
