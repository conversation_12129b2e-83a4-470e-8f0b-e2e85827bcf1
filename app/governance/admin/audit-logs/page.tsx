"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { AuditLogViewer } from "@/components/admin/audit-log-viewer"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { RequirePermission } from "@/components/admin/access-control"

export default function AuditLogsPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Audit Logs</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">View and analyze system audit logs</p>
      </div>

      <UnifiedGovernanceNav />

      <div className="mt-8">
        <RequirePermission permission="*">
          <AuditLogViewer />
        </RequirePermission>
      </div>
    </div>
  )
}
