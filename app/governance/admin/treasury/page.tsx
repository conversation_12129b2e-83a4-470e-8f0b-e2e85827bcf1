"use client"

import { useEffect } from "react"
import { ShimmerText } from "@/components/shimmer-text"

// Set page title
const pageTitle = "Treasury Management | Governance Admin | PawPumps"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { ArrowDownRight, ArrowUpRight, Download, Plus, Wallet } from "lucide-react"
import { Progress } from "@/components/ui/progress"

export default function TreasuryManagementPage() {
  // Set document title
  useEffect(() => {
    document.title = pageTitle
  }, [])

  // Mock treasury data
  const treasuryData = {
    totalBalance: 5250000,
    allocations: [
      { name: "Development Fund", amount: 2100000, percentage: 40 },
      { name: "Governance Fund", amount: 1050000, percentage: 20 },
      { name: "Marketing Fund", amount: 787500, percentage: 15 },
      { name: "Liquidity Reserve", amount: 1050000, percentage: 20 },
      { name: "Emergency Fund", amount: 262500, percentage: 5 },
    ],
    recentTransactions: [
      {
        id: "TX-001",
        type: "outgoing",
        amount: 50000,
        destination: "Development DAO",
        date: "May 5, 2025",
        status: "completed",
        description: "Monthly development funding",
      },
      {
        id: "TX-002",
        type: "incoming",
        amount: 125000,
        source: "Trading Fees",
        date: "May 4, 2025",
        status: "completed",
        description: "Weekly fee collection",
      },
      {
        id: "TX-003",
        type: "outgoing",
        amount: 35000,
        destination: "Marketing Campaign",
        date: "May 3, 2025",
        status: "completed",
        description: "Social media promotion",
      },
      {
        id: "TX-004",
        type: "incoming",
        amount: 75000,
        source: "Token Launch Fees",
        date: "May 2, 2025",
        status: "completed",
        description: "New token launch fees",
      },
      {
        id: "TX-005",
        type: "outgoing",
        amount: 25000,
        destination: "Bug Bounty",
        date: "May 1, 2025",
        status: "completed",
        description: "Critical vulnerability reward",
      },
    ],
  }

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Treasury Management</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Manage platform treasury funds and allocations</p>
      </div>

      <UnifiedGovernanceNav />

      <div className="mt-8">
        <div className="grid gap-6 md:grid-cols-2 mb-6">
          <Card className="glass-card border-white/5 bg-black/20">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg text-white">Total Treasury</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Wallet className="h-5 w-5 text-doge" />
                <div className="text-2xl font-bold text-white">{treasuryData.totalBalance.toLocaleString()} PAWP</div>
              </div>
              <div className="mt-2 flex items-center gap-2">
                <Badge className="bg-green-500/20 text-green-500">
                  <ArrowUpRight className="mr-1 h-3 w-3" />
                  +8.5% this month
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/5 bg-black/20">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg text-white">Quick Actions</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button className="flex-1 bg-doge text-black hover:bg-doge/90">
                  <Plus className="mr-2 h-4 w-4" />
                  New Allocation
                </Button>
                <Button className="flex-1" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export Report
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="glass-card border-white/5 bg-black/20 mb-6">
          <CardHeader>
            <CardTitle className="text-white">Treasury Allocations</CardTitle>
            <CardDescription className="text-white/70">Current distribution of treasury funds</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {treasuryData.allocations.map((allocation) => (
                <div key={allocation.name}>
                  <div className="mb-1 flex items-center justify-between">
                    <div className="font-medium text-white">{allocation.name}</div>
                    <div className="text-sm text-white/70">
                      {allocation.amount.toLocaleString()} PAWP ({allocation.percentage}%)
                    </div>
                  </div>
                  <Progress value={allocation.percentage} className="h-2 bg-white/10" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5 bg-black/20">
          <CardHeader>
            <CardTitle className="text-white">Recent Transactions</CardTitle>
            <CardDescription className="text-white/70">Recent treasury fund movements</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow className="border-white/10">
                  <TableHead className="text-white">ID</TableHead>
                  <TableHead className="text-white">Type</TableHead>
                  <TableHead className="text-white">Amount</TableHead>
                  <TableHead className="text-white">Source/Destination</TableHead>
                  <TableHead className="text-white">Date</TableHead>
                  <TableHead className="text-white">Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {treasuryData.recentTransactions.map((tx) => (
                  <TableRow key={tx.id} className="border-white/10">
                    <TableCell className="font-mono text-sm text-white">{tx.id}</TableCell>
                    <TableCell>
                      {tx.type === "incoming" ? (
                        <Badge className="bg-green-500/20 text-green-500">
                          <ArrowUpRight className="mr-1 h-3 w-3" />
                          Incoming
                        </Badge>
                      ) : (
                        <Badge className="bg-blue-500/20 text-blue-500">
                          <ArrowDownRight className="mr-1 h-3 w-3" />
                          Outgoing
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-white">{tx.amount.toLocaleString()} PAWP</TableCell>
                    <TableCell className="text-white">{tx.type === "incoming" ? tx.source : tx.destination}</TableCell>
                    <TableCell className="text-white">{tx.date}</TableCell>
                    <TableCell>
                      <Badge className="bg-green-500/20 text-green-500">{tx.status}</Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
