"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { AdminAuthProvider } from "@/contexts/admin-auth-context"
import { AdminOnly } from "@/components/admin/access-control"
import { FileText, AlertTriangle, Activity, Search } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function AuditAnalyticsPage() {
  return (
    <AdminAuthProvider>
      <AdminOnly>
        <div className="container py-8">
          <div className="flex items-center gap-2 mb-8">
            <Activity className="h-6 w-6 text-doge" />
            <h1 className="text-2xl font-bold text-white">Audit Analytics</h1>
          </div>

          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-white/50" />
              <Input placeholder="Search audit logs..." className="pl-8 glass-input text-white" />
            </div>
            <div className="flex gap-2">
              <Button variant="outline" className="glass-button">
                Export
              </Button>
              <Button className="doge-button doge-shine">Generate Report</Button>
            </div>
          </div>

          <Tabs defaultValue="logs" className="w-full">
            <TabsList className="glass-card border-white/5 bg-black/20 p-1">
              <TabsTrigger value="logs">Audit Logs</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="anomalies">Anomaly Detection</TabsTrigger>
              <TabsTrigger value="compliance">Compliance</TabsTrigger>
            </TabsList>

            <TabsContent value="logs" className="mt-4">
              <Card className="glass-card border-white/5">
                <CardHeader>
                  <CardTitle className="text-white">System Audit Logs</CardTitle>
                  <CardDescription className="text-white/70">
                    Comprehensive logs of all system activities
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <div key={i} className="p-4 rounded-md bg-black/20 border border-white/5">
                        <div className="flex justify-between">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-white/70" />
                            <span className="text-white font-medium">
                              {
                                ["User login", "Proposal created", "Vote cast", "Settings changed", "Admin action"][
                                  i - 1
                                ]
                              }
                            </span>
                          </div>
                          <span className="text-white/50 text-sm">
                            {new Date(Date.now() - i * 3600000).toLocaleString()}
                          </span>
                        </div>
                        <p className="text-white/70 text-sm mt-2">
                          {
                            [
                              "User 0x1234...5678 logged in from 192.168.1.1",
                              "New proposal #123 created by 0x8765...4321",
                              "Vote cast on proposal #120 by 0x2468...1357",
                              "Governance settings updated by admin",
                              "Emergency pause activated by admin",
                            ][i - 1]
                          }
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="mt-4">
              <Card className="glass-card border-white/5">
                <CardHeader>
                  <CardTitle className="text-white">Audit Analytics</CardTitle>
                  <CardDescription className="text-white/70">
                    Visualizations and analytics of system audit data
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px] flex items-center justify-center bg-black/20 rounded-md">
                    <p className="text-white/50">Audit analytics visualization</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="anomalies" className="mt-4">
              <Card className="glass-card border-white/5">
                <CardHeader>
                  <CardTitle className="text-white">Anomaly Detection</CardTitle>
                  <CardDescription className="text-white/70">
                    Automated detection of unusual system behavior
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 rounded-md bg-red-500/10 border border-red-500/20">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-red-500" />
                        <span className="text-white font-medium">High volume of failed login attempts</span>
                      </div>
                      <p className="text-white/70 text-sm mt-2">
                        Detected 25 failed login attempts from IP ************ in the last hour
                      </p>
                    </div>

                    <div className="p-4 rounded-md bg-yellow-500/10 border border-yellow-500/20">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-yellow-500" />
                        <span className="text-white font-medium">Unusual admin activity</span>
                      </div>
                      <p className="text-white/70 text-sm mt-2">
                        Admin user performed 15 configuration changes in 5 minutes
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="compliance" className="mt-4">
              <Card className="glass-card border-white/5">
                <CardHeader>
                  <CardTitle className="text-white">Compliance Reporting</CardTitle>
                  <CardDescription className="text-white/70">Compliance status and reporting tools</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 rounded-md bg-green-500/10 border border-green-500/20">
                      <div className="flex items-center gap-2">
                        <Activity className="h-5 w-5 text-green-500" />
                        <span className="text-white font-medium">GDPR Compliance</span>
                      </div>
                      <p className="text-white/70 text-sm mt-2">All systems are currently GDPR compliant</p>
                    </div>

                    <div className="p-4 rounded-md bg-green-500/10 border border-green-500/20">
                      <div className="flex items-center gap-2">
                        <Activity className="h-5 w-5 text-green-500" />
                        <span className="text-white font-medium">Security Audit</span>
                      </div>
                      <p className="text-white/70 text-sm mt-2">
                        Last security audit completed on May 1, 2023 with no critical findings
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </AdminOnly>
    </AdminAuthProvider>
  )
}
