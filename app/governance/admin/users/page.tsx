"use client"

import type React from "react"
import { useEffect } from "react"

// Set page title
const pageTitle = "User Management | Governance Admin | PawPumps"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShimmerText } from "@/components/shimmer-text"
import { Users, UserCheck, UserX, Shield, Crown, AlertTriangle } from "lucide-react"

interface User {
  id: string
  address: string
  username?: string
  role: "admin" | "moderator" | "user"
  status: "active" | "suspended" | "banned"
  joinDate: string
  lastActive: string
  votingPower: number
  proposalsCreated: number
  votesParticipated: number
}

const mockUsers: User[] = [
  {
    id: "1",
    address: "0x1234...5678",
    username: "<PERSON>e<PERSON><PERSON>",
    role: "admin",
    status: "active",
    joinDate: "2024-01-15",
    lastActive: "2024-06-28",
    votingPower: 50000,
    proposalsCreated: 12,
    votesParticipated: 45,
  },
  {
    id: "2",
    address: "0x9876...4321",
    username: "PawPower",
    role: "moderator",
    status: "active",
    joinDate: "2024-02-20",
    lastActive: "2024-06-27",
    votingPower: 25000,
    proposalsCreated: 8,
    votesParticipated: 38,
  },
  {
    id: "3",
    address: "0x5555...7777",
    role: "user",
    status: "suspended",
    joinDate: "2024-03-10",
    lastActive: "2024-06-25",
    votingPower: 1000,
    proposalsCreated: 2,
    votesParticipated: 15,
  },
]

function getRoleIcon(role: string) {
  switch (role) {
    case "admin":
      return <Crown className="h-4 w-4" />
    case "moderator":
      return <Shield className="h-4 w-4" />
    default:
      return <Users className="h-4 w-4" />
  }
}

function getRoleBadge(role: string) {
  const colors = {
    admin: "bg-yellow-500/20 text-yellow-500",
    moderator: "bg-blue-500/20 text-blue-500",
    user: "bg-gray-500/20 text-gray-500",
  }
  return (
    <Badge className={colors[role as keyof typeof colors]}>
      {getRoleIcon(role)}
      <span className="ml-1">{role}</span>
    </Badge>
  )
}

function getStatusBadge(status: string) {
  const colors = {
    active: "bg-green-500/20 text-green-500",
    suspended: "bg-yellow-500/20 text-yellow-500",
    banned: "bg-red-500/20 text-red-500",
  }
  return <Badge className={colors[status as keyof typeof colors]}>{status}</Badge>
}

export default function UsersManagement() {
  // Set document title
  useEffect(() => {
    document.title = pageTitle
  }, [])

  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Users Management</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Manage user accounts, permissions, and moderation</p>
      </div>

      <div className="grid gap-6 md:grid-cols-4 mb-8">
        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <Users className="mr-2 h-5 w-5" />
              Total Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">1,247</div>
            <p className="text-xs text-white/70">+12% from last month</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <UserCheck className="mr-2 h-5 w-5" />
              Active Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">1,189</div>
            <p className="text-xs text-white/70">95.3% of total</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Suspended
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-500">45</div>
            <p className="text-xs text-white/70">3.6% of total</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <UserX className="mr-2 h-5 w-5" />
              Banned
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">13</div>
            <p className="text-xs text-white/70">1.1% of total</p>
          </CardContent>
        </Card>
      </div>

      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">User Management</CardTitle>
          <CardDescription className="text-white/70">
            Manage user accounts, roles, and permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockUsers.map((user) => (
              <div
                key={user.id}
                className="flex items-center justify-between p-4 rounded-lg border border-white/10 bg-white/5"
              >
                <div className="flex items-center space-x-4">
                  <div>
                    <div className="flex items-center space-x-2">
                      <p className="font-medium text-white">
                        {user.username || user.address}
                      </p>
                      {getRoleBadge(user.role)}
                      {getStatusBadge(user.status)}
                    </div>
                    <p className="text-sm text-white/70">{user.address}</p>
                    <div className="flex items-center space-x-4 text-xs text-white/50 mt-1">
                      <span>Joined: {user.joinDate}</span>
                      <span>Last Active: {user.lastActive}</span>
                      <span>Voting Power: {user.votingPower.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" className="glass-button">
                    Edit
                  </Button>
                  <Button variant="outline" size="sm" className="glass-button">
                    {user.status === "active" ? "Suspend" : "Activate"}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
