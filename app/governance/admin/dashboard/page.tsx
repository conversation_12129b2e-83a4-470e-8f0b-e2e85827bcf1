"use client"

import type React from "react"
import { useEffect } from "react"

// Set page title
const pageTitle = "Admin Dashboard | Governance Admin | PawPumps"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { AdminPageBreadcrumb } from "@/components/governance/breadcrumb-nav"
import { PerformanceDashboard, GovernanceAnalyticsDashboard } from "@/components/governance/advanced-analytics"
import {
  BarChart3,
  Bell,
  Cog,
  Coins,
  FileText,
  Flag,
  Gauge,
  Gift,
  MessageSquare,
  Shield,
  Users,
  AlertTriangle,
  Wallet,
  Code,
  Vote,
} from "lucide-react"

interface AdminCardProps {
  title: string
  description: string
  href: string
  icon: React.ReactNode
  color: string
}

function AdminCard({ title, description, href, icon, color }: AdminCardProps) {
  return (
    <Link href={href}>
      <Card className="glass-card border-white/5 hover:bg-white/5 transition-colors h-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white">{title}</CardTitle>
            <div className={`p-2 rounded-full ${color}`}>{icon}</div>
          </div>
          <CardDescription className="text-white/70">{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button className="w-full glass-button">Manage</Button>
        </CardContent>
      </Card>
    </Link>
  )
}

export default function GovernanceAdminDashboard() {
  // Set document title
  useEffect(() => {
    document.title = pageTitle
  }, [])

  const adminSections: AdminCardProps[] = [
    {
      title: "Analytics",
      description: "View detailed platform analytics and reports",
      href: "/governance/admin/analytics",
      icon: <BarChart3 className="h-5 w-5" />,
      color: "bg-green-500/20 text-green-500",
    },
    {
      title: "Development DAO",
      description: "Manage development governance and tasks",
      href: "/governance/admin/development-dao",
      icon: <Code className="h-5 w-5" />,
      color: "bg-purple-500/20 text-purple-500",
    },
    {
      title: "Proposals",
      description: "Administrative proposal management",
      href: "/governance/admin/proposals",
      icon: <Vote className="h-5 w-5" />,
      color: "bg-yellow-500/20 text-yellow-500",
    },
    {
      title: "Users",
      description: "Manage user accounts and permissions",
      href: "/governance/admin/users",
      icon: <Users className="h-5 w-5" />,
      color: "bg-blue-500/20 text-blue-500",
    },
    {
      title: "Content Moderation",
      description: "Manage reported content",
      href: "/governance/admin/moderation",
      icon: <Flag className="h-5 w-5" />,
      color: "bg-purple-500/20 text-purple-500",
    },
    {
      title: "Emergency Controls",
      description: "Emergency system controls",
      href: "/governance/admin/emergency",
      icon: <AlertTriangle className="h-5 w-5" />,
      color: "bg-red-500/20 text-red-500",
    },
    {
      title: "Treasury Management",
      description: "Manage treasury funds",
      href: "/governance/admin/treasury",
      icon: <Wallet className="h-5 w-5" />,
      color: "bg-yellow-500/20 text-yellow-500",
    },
    {
      title: "Rewards",
      description: "Manage reward programs and distributions",
      href: "/governance/admin/rewards",
      icon: <Gift className="h-5 w-5" />,
      color: "bg-pink-500/20 text-pink-500",
    },
    {
      title: "Feedback",
      description: "Review and manage user feedback",
      href: "/governance/admin/feedback",
      icon: <MessageSquare className="h-5 w-5" />,
      color: "bg-cyan-500/20 text-cyan-500",
    },
    {
      title: "Staking",
      description: "Manage staking pools and rewards",
      href: "/governance/admin/staking",
      icon: <Coins className="h-5 w-5" />,
      color: "bg-orange-500/20 text-orange-500",
    },
    {
      title: "Settings",
      description: "Configure platform settings",
      href: "/governance/admin/settings",
      icon: <Cog className="h-5 w-5" />,
      color: "bg-gray-500/20 text-gray-500",
    },
    {
      title: "Audit Logs",
      description: "System audit logs",
      href: "/governance/admin/audit-logs",
      icon: <FileText className="h-5 w-5" />,
      color: "bg-green-500/20 text-green-500",
    },
    {
      title: "Audit Analytics",
      description: "System audit analytics",
      href: "/governance/admin/audit-analytics",
      icon: <BarChart3 className="h-5 w-5" />,
      color: "bg-cyan-500/20 text-cyan-500",
    },
    {
      title: "Notifications",
      description: "Manage system notifications",
      href: "/governance/admin/notifications",
      icon: <Bell className="h-5 w-5" />,
      color: "bg-orange-500/20 text-orange-500",
    },
    {
      title: "Security",
      description: "Configure security settings",
      href: "/governance/admin/security",
      icon: <Shield className="h-5 w-5" />,
      color: "bg-red-500/20 text-red-500",
    },
    {
      title: "Performance",
      description: "Monitor system performance",
      href: "/governance/admin/performance",
      icon: <Gauge className="h-5 w-5" />,
      color: "bg-blue-500/20 text-blue-500",
    },
  ]

  return (
    <div className="container py-8">
      <AdminPageBreadcrumb
        title="Governance Admin Dashboard"
        description="Manage and monitor all aspects of the PawPumps governance system"
      />

      <UnifiedGovernanceNav />

      {/* Analytics Dashboard */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-white mb-4">System Analytics</h2>
        <div className="grid gap-6 lg:grid-cols-2">
          <PerformanceDashboard />
          <div>
            <GovernanceAnalyticsDashboard />
          </div>
        </div>
      </div>

      {/* Admin Sections */}
      <div>
        <h2 className="text-xl font-semibold text-white mb-4">Administration</h2>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {adminSections.map((section) => (
            <AdminCard key={section.href} {...section} />
          ))}
        </div>
      </div>
    </div>
  )
}
