"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import { Save, Settings, Shield, Sliders, Bell } from "lucide-react"

export default function GovernanceSystemSettingsPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>System Settings</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Configure global platform settings and parameters</p>
      </div>

      <UnifiedGovernanceNav />

      <div className="mt-8">
        <Tabs defaultValue="general" className="space-y-4">
          <TabsList className="bg-black/20">
            <TabsTrigger value="general" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
              <Settings className="mr-2 h-4 w-4" />
              General
            </TabsTrigger>
            <TabsTrigger value="trading" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
              <Sliders className="mr-2 h-4 w-4" />
              Trading
            </TabsTrigger>
            <TabsTrigger value="security" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
              <Shield className="mr-2 h-4 w-4" />
              Security
            </TabsTrigger>
            <TabsTrigger value="notifications" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
              <Bell className="mr-2 h-4 w-4" />
              Notifications
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <Card className="border-white/5 bg-black/20">
              <CardHeader>
                <CardTitle className="text-lg text-white">Platform Settings</CardTitle>
                <CardDescription className="text-white/70">
                  Configure general platform settings and appearance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="platform-name" className="text-white">
                    Platform Name
                  </Label>
                  <Input
                    id="platform-name"
                    defaultValue="PawPumps"
                    className="border-white/20 bg-black/20 text-white"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact-email" className="text-white">
                    Contact Email
                  </Label>
                  <Input
                    id="contact-email"
                    defaultValue="<EMAIL>"
                    className="border-white/20 bg-black/20 text-white"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="support-email" className="text-white">
                    Support Email
                  </Label>
                  <Input
                    id="support-email"
                    defaultValue="<EMAIL>"
                    className="border-white/20 bg-black/20 text-white"
                  />
                </div>

                <Separator className="my-4 bg-white/10" />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Maintenance Mode</Label>
                    <p className="text-sm text-white/70">Temporarily disable the platform for maintenance</p>
                  </div>
                  <Switch />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Enable Analytics</Label>
                    <p className="text-sm text-white/70">Collect anonymous usage data to improve the platform</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Public Roadmap</Label>
                    <p className="text-sm text-white/70">Make the development roadmap publicly visible</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </CardContent>
              <CardFooter className="border-t border-white/10 pt-4">
                <Button className="bg-doge text-black hover:bg-doge/90">
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="trading">
            <Card className="border-white/5 bg-black/20">
              <CardHeader>
                <CardTitle className="text-lg text-white">Trading Configuration</CardTitle>
                <CardDescription className="text-white/70">
                  Configure trading parameters and limits
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="trading-fee" className="text-white">
                    Trading Fee (%)
                  </Label>
                  <Input
                    id="trading-fee"
                    type="number"
                    step="0.01"
                    defaultValue="0.3"
                    className="border-white/20 bg-black/20 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="min-trade" className="text-white">
                    Minimum Trade Amount (DOGE)
                  </Label>
                  <Input
                    id="min-trade"
                    type="number"
                    defaultValue="1"
                    className="border-white/20 bg-black/20 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max-transaction" className="text-white">
                    Maximum Transaction Size (DOGE)
                  </Label>
                  <Input
                    id="max-transaction"
                    type="number"
                    defaultValue="100000"
                    className="border-white/20 bg-black/20 text-white"
                  />
                </div>

                <Separator className="my-4 bg-white/10" />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Fee Discounts for PAWP Holders</Label>
                    <p className="text-sm text-white/70">Apply trading fee discounts for PAWP token holders</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Enable Slippage Protection</Label>
                    <p className="text-sm text-white/70">Protect users from excessive slippage on trades</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </CardContent>
              <CardFooter className="border-t border-white/10 pt-4">
                <Button className="bg-doge text-black hover:bg-doge/90">
                  <Save className="mr-2 h-4 w-4" />
                  Save Trading Settings
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="security">
            <Card className="border-white/5 bg-black/20">
              <CardHeader>
                <CardTitle className="text-lg text-white">Security Settings</CardTitle>
                <CardDescription className="text-white/70">
                  Configure security parameters and access controls
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="session-timeout" className="text-white">
                    Session Timeout (minutes)
                  </Label>
                  <Input
                    id="session-timeout"
                    type="number"
                    defaultValue="30"
                    className="border-white/20 bg-black/20 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max-login-attempts" className="text-white">
                    Maximum Login Attempts
                  </Label>
                  <Input
                    id="max-login-attempts"
                    type="number"
                    defaultValue="5"
                    className="border-white/20 bg-black/20 text-white"
                  />
                </div>

                <Separator className="my-4 bg-white/10" />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Two-Factor Authentication Required</Label>
                    <p className="text-sm text-white/70">Require 2FA for all admin accounts</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">IP Whitelist</Label>
                    <p className="text-sm text-white/70">Restrict admin access to specific IP addresses</p>
                  </div>
                  <Switch />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Audit Logging</Label>
                    <p className="text-sm text-white/70">Log all administrative actions</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </CardContent>
              <CardFooter className="border-t border-white/10 pt-4">
                <Button className="bg-doge text-black hover:bg-doge/90">
                  <Save className="mr-2 h-4 w-4" />
                  Save Security Settings
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="notifications">
            <Card className="border-white/5 bg-black/20">
              <CardHeader>
                <CardTitle className="text-lg text-white">Notification Settings</CardTitle>
                <CardDescription className="text-white/70">
                  Configure system notifications and alerts
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Email Notifications</Label>
                    <p className="text-sm text-white/70">Send email notifications for important events</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Security Alerts</Label>
                    <p className="text-sm text-white/70">Alert on suspicious activities</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">System Status Updates</Label>
                    <p className="text-sm text-white/70">Notify about system maintenance and updates</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">User Feedback Alerts</Label>
                    <p className="text-sm text-white/70">Alert when new feedback is submitted</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </CardContent>
              <CardFooter className="border-t border-white/10 pt-4">
                <Button className="bg-doge text-black hover:bg-doge/90">
                  <Save className="mr-2 h-4 w-4" />
                  Save Notification Settings
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
