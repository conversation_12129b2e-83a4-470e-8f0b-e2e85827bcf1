"use client"

import { useState, useEffect } from "react"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Download, Trash2, MessageSquare, AlertTriangle, CheckCircle, Clock, Bug, Lightbulb, Palette, HelpCircle } from "lucide-react"
import {
  getAllFeedback,
  updateFeedbackStatus,
  deleteFeedback,
  exportFeedbackAsCsv,
  type Feedback,
} from "@/utils/feedback-storage"

export default function GovernanceFeedbackAdminPage() {
  const [feedback, setFeedback] = useState<Feedback[]>([])
  // Status filtering removed for now
  const [activeTab, setActiveTab] = useState("all")

  useEffect(() => {
    // Load feedback from localStorage
    const data = getAllFeedback()
    setFeedback(data)

    // Add event listener to refresh data when localStorage changes
    const handleStorageChange = () => {
      const updatedData = getAllFeedback()
      setFeedback(updatedData)
    }

    window.addEventListener("storage", handleStorageChange)

    return () => {
      window.removeEventListener("storage", handleStorageChange)
    }
  }, [])

  // Filter feedback by status
  const filteredFeedback = activeTab === "all" ? feedback : feedback.filter((item) => item.type === activeTab)

  // Get counts for each type
  const bugCount = feedback.filter((item) => item.type === "bug").length
  const featureCount = feedback.filter((item) => item.type === "feature").length
  const uxCount = feedback.filter((item) => item.type === "ux").length
  const otherCount = feedback.filter((item) => item.type === "other").length

  const handleStatusUpdate = async (id: string, newStatus: string) => {
    try {
      await updateFeedbackStatus(id, newStatus as any)
      const updatedData = getAllFeedback()
      setFeedback(updatedData)
    } catch (error) {
      console.error("Error updating feedback status:", error)
    }
  }

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this feedback?")) {
      try {
        await deleteFeedback(id)
        const updatedData = getAllFeedback()
        setFeedback(updatedData)
      } catch (error) {
        console.error("Error deleting feedback:", error)
      }
    }
  }

  const handleExport = () => {
    try {
      exportFeedbackAsCsv()
    } catch (error) {
      console.error("Error exporting feedback:", error)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "new":
        return <Clock className="h-4 w-4 text-blue-400" />
      case "reviewing":
        return <AlertTriangle className="h-4 w-4 text-yellow-400" />
      case "resolved":
        return <CheckCircle className="h-4 w-4 text-green-400" />
      default:
        return <MessageSquare className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const config = {
      new: "bg-blue-500/20 text-blue-400 border-blue-500/20",
      reviewing: "bg-yellow-500/20 text-yellow-400 border-yellow-500/20",
      resolved: "bg-green-500/20 text-green-400 border-green-500/20",
    }
    return config[status as keyof typeof config] || "bg-gray-500/20 text-gray-400 border-gray-500/20"
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "bug":
        return <Bug className="h-4 w-4 text-red-400" />
      case "feature":
        return <Lightbulb className="h-4 w-4 text-yellow-400" />
      case "ux":
        return <Palette className="h-4 w-4 text-purple-400" />
      default:
        return <HelpCircle className="h-4 w-4 text-gray-400" />
    }
  }

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
            <ShimmerText>Feedback Management</ShimmerText>
          </h1>
          <p className="text-lg text-white/70">Review and manage user feedback and suggestions</p>
        </div>
        <Button onClick={handleExport} className="bg-doge text-black hover:bg-doge/90">
          <Download className="mr-2 h-4 w-4" />
          Export CSV
        </Button>
      </div>

      <UnifiedGovernanceNav />

      <div className="mt-8">
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5 bg-black/20 border border-white/10">
            <TabsTrigger value="all" className="data-[state=active]:bg-white/10">
              All ({feedback.length})
            </TabsTrigger>
            <TabsTrigger value="bug" className="data-[state=active]:bg-white/10">
              <Bug className="mr-2 h-4 w-4" />
              Bugs ({bugCount})
            </TabsTrigger>
            <TabsTrigger value="feature" className="data-[state=active]:bg-white/10">
              <Lightbulb className="mr-2 h-4 w-4" />
              Features ({featureCount})
            </TabsTrigger>
            <TabsTrigger value="ux" className="data-[state=active]:bg-white/10">
              <Palette className="mr-2 h-4 w-4" />
              UX ({uxCount})
            </TabsTrigger>
            <TabsTrigger value="other" className="data-[state=active]:bg-white/10">
              <HelpCircle className="mr-2 h-4 w-4" />
              Other ({otherCount})
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-6">
            <Card className="glass-card border-white/5 bg-black/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  {activeTab === "all" ? "All Feedback" : `${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Feedback`}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {filteredFeedback.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow className="border-white/10">
                        <TableHead className="text-white">Type</TableHead>
                        <TableHead className="text-white">Message</TableHead>
                        <TableHead className="text-white">Email</TableHead>
                        <TableHead className="text-white">Status</TableHead>
                        <TableHead className="text-white">Date</TableHead>
                        <TableHead className="text-white">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredFeedback.map((item) => (
                        <TableRow key={item.id} className="border-white/10">
                          <TableCell className="text-white">
                            <div className="flex items-center gap-2">
                              {getTypeIcon(item.type)}
                              <span className="capitalize">{item.type}</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-white/80 max-w-md">
                            <div className="truncate" title={item.message}>
                              {item.message}
                            </div>
                          </TableCell>
                          <TableCell className="text-white/80">{item.email}</TableCell>
                          <TableCell>
                            <Badge className={getStatusBadge(item.status)}>
                              <div className="flex items-center gap-1">
                                {getStatusIcon(item.status)}
                                <span className="capitalize">{item.status}</span>
                              </div>
                            </Badge>
                          </TableCell>
                          <TableCell className="text-white/80">
                            {new Date(item.createdAt).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Select
                                value={item.status}
                                onValueChange={(value) => handleStatusUpdate(item.id, value)}
                              >
                                <SelectTrigger className="w-32 border-white/10 bg-white/5 text-white">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="new">New</SelectItem>
                                  <SelectItem value="reviewing">Reviewing</SelectItem>
                                  <SelectItem value="resolved">Resolved</SelectItem>
                                </SelectContent>
                              </Select>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDelete(item.id)}
                                className="border-red-500/20 bg-red-500/5 text-red-400 hover:bg-red-500/10"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12">
                    <MessageSquare className="h-12 w-12 text-white/20 mb-4" />
                    <h3 className="text-lg font-medium text-white mb-2">No feedback found</h3>
                    <p className="text-white/60 text-center">
                      {activeTab === "all" 
                        ? "No feedback has been submitted yet." 
                        : `No ${activeTab} feedback has been submitted yet.`}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
