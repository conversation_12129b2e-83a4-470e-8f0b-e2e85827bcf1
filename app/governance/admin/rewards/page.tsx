"use client"

import { useState } from "react"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Gift, Plus, Edit, Pause, Play, Trash2 } from "lucide-react"

// Mock data for reward programs
const mockRewardPrograms = [
  {
    id: "prog-1",
    name: "Trading Volume Rewards",
    description: "Rewards for high-volume traders",
    totalBudget: "100,000 PAWP",
    distributed: "75,000 PAWP",
    remaining: "25,000 PAWP",
    frequency: "Weekly",
    status: "active",
  },
  {
    id: "prog-2",
    name: "Liquidity Provider Incentives",
    description: "Incentives for providing liquidity",
    totalBudget: "50,000 PAWP",
    distributed: "30,000 PAWP",
    remaining: "20,000 PAWP",
    frequency: "Daily",
    status: "active",
  },
  {
    id: "prog-3",
    name: "Governance Participation",
    description: "Rewards for voting on proposals",
    totalBudget: "25,000 PAWP",
    distributed: "15,000 PAWP",
    remaining: "10,000 PAWP",
    frequency: "Monthly",
    status: "paused",
  },
]

// Mock data for recent reward distributions
const mockDistributions = [
  {
    id: "dist-1",
    program: "Trading Volume Rewards",
    date: "2025-04-07",
    amount: "25,000 PAWP",
    recipients: 128,
    status: "completed",
  },
  {
    id: "dist-2",
    program: "Liquidity Provider Incentives",
    date: "2025-04-10",
    amount: "15,000 PAWP",
    recipients: 75,
    status: "completed",
  },
  {
    id: "dist-3",
    program: "Governance Participation",
    date: "2025-04-05",
    amount: "10,000 PAWP",
    recipients: 42,
    status: "completed",
  },
  {
    id: "dist-4",
    program: "Trading Volume Rewards",
    date: "2025-04-14",
    amount: "25,000 PAWP",
    recipients: 0,
    status: "pending",
  },
]

export default function GovernanceRewardsAdmin() {
  const [activeTab, setActiveTab] = useState("programs")

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
            <ShimmerText>Rewards Management</ShimmerText>
          </h1>
          <p className="text-lg text-white/70">Manage reward programs and distributions</p>
        </div>
        <Button className="bg-doge text-black hover:bg-doge/90">
          <Plus className="mr-2 h-4 w-4" />
          Create New Program
        </Button>
      </div>

      <UnifiedGovernanceNav />

      <div className="mt-8">
        <Tabs defaultValue="programs" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-black/20 border border-white/10">
            <TabsTrigger value="programs" className="data-[state=active]:bg-white/10">
              <Gift className="mr-2 h-4 w-4" />
              Reward Programs
            </TabsTrigger>
            <TabsTrigger value="distributions" className="data-[state=active]:bg-white/10">
              Recent Distributions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="programs" className="mt-6">
            <Card className="glass-card border-white/5 bg-black/20">
              <CardHeader>
                <CardTitle className="text-white">Active Reward Programs</CardTitle>
                <CardDescription className="text-white/70">
                  Manage and monitor reward programs across the platform
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/10">
                      <TableHead className="text-white">Name</TableHead>
                      <TableHead className="text-white">Description</TableHead>
                      <TableHead className="text-white">Budget</TableHead>
                      <TableHead className="text-white">Distributed</TableHead>
                      <TableHead className="text-white">Remaining</TableHead>
                      <TableHead className="text-white">Frequency</TableHead>
                      <TableHead className="text-white">Status</TableHead>
                      <TableHead className="text-white">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockRewardPrograms.map((program) => (
                      <TableRow key={program.id} className="border-white/10">
                        <TableCell className="text-white font-medium">{program.name}</TableCell>
                        <TableCell className="text-white/80">{program.description}</TableCell>
                        <TableCell className="text-white/80">{program.totalBudget}</TableCell>
                        <TableCell className="text-white/80">{program.distributed}</TableCell>
                        <TableCell className="text-white/80">{program.remaining}</TableCell>
                        <TableCell className="text-white/80">{program.frequency}</TableCell>
                        <TableCell>
                          <Badge
                            className={
                              program.status === "active"
                                ? "bg-green-500/20 text-green-500 border-green-500/20"
                                : "bg-yellow-500/20 text-yellow-500 border-yellow-500/20"
                            }
                          >
                            {program.status.charAt(0).toUpperCase() + program.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                            >
                              {program.status === "active" ? (
                                <Pause className="h-3 w-3" />
                              ) : (
                                <Play className="h-3 w-3" />
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-red-500/20 bg-red-500/5 text-red-400 hover:bg-red-500/10"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="distributions" className="mt-6">
            <Card className="glass-card border-white/5 bg-black/20">
              <CardHeader>
                <CardTitle className="text-white">Recent Distributions</CardTitle>
                <CardDescription className="text-white/70">
                  Track recent reward distributions and their status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/10">
                      <TableHead className="text-white">Program</TableHead>
                      <TableHead className="text-white">Date</TableHead>
                      <TableHead className="text-white">Amount</TableHead>
                      <TableHead className="text-white">Recipients</TableHead>
                      <TableHead className="text-white">Status</TableHead>
                      <TableHead className="text-white">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockDistributions.map((dist) => (
                      <TableRow key={dist.id} className="border-white/10">
                        <TableCell className="text-white font-medium">{dist.program}</TableCell>
                        <TableCell className="text-white/80">{dist.date}</TableCell>
                        <TableCell className="text-white/80">{dist.amount}</TableCell>
                        <TableCell className="text-white/80">{dist.recipients}</TableCell>
                        <TableCell>
                          <Badge
                            className={
                              dist.status === "completed"
                                ? "bg-green-500/20 text-green-500 border-green-500/20"
                                : "bg-blue-500/20 text-blue-500 border-blue-500/20"
                            }
                          >
                            {dist.status.charAt(0).toUpperCase() + dist.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
