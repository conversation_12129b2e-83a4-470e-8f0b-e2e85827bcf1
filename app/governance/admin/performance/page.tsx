"use client"

import type React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShimmerText } from "@/components/shimmer-text"
import { Gauge, Activity, Zap, Server, Database, Globe, TrendingUp, AlertTriangle } from "lucide-react"

interface PerformanceMetric {
  name: string
  value: number
  unit: string
  status: "good" | "warning" | "critical"
  trend: "up" | "down" | "stable"
  description: string
}

const performanceMetrics: PerformanceMetric[] = [
  {
    name: "Response Time",
    value: 245,
    unit: "ms",
    status: "good",
    trend: "stable",
    description: "Average API response time",
  },
  {
    name: "Throughput",
    value: 1250,
    unit: "req/min",
    status: "good",
    trend: "up",
    description: "Requests processed per minute",
  },
  {
    name: "Error Rate",
    value: 0.12,
    unit: "%",
    status: "good",
    trend: "down",
    description: "Percentage of failed requests",
  },
  {
    name: "CPU Usage",
    value: 68,
    unit: "%",
    status: "warning",
    trend: "up",
    description: "Server CPU utilization",
  },
  {
    name: "Memory Usage",
    value: 72,
    unit: "%",
    status: "warning",
    trend: "stable",
    description: "Server memory utilization",
  },
  {
    name: "Database Latency",
    value: 15,
    unit: "ms",
    status: "good",
    trend: "stable",
    description: "Database query response time",
  },
]

function getStatusBadge(status: string) {
  const colors = {
    good: "bg-green-500/20 text-green-500",
    warning: "bg-yellow-500/20 text-yellow-500",
    critical: "bg-red-500/20 text-red-500",
  }
  return <Badge className={colors[status as keyof typeof colors]}>{status}</Badge>
}

function getTrendIcon(trend: string) {
  switch (trend) {
    case "up":
      return <TrendingUp className="h-4 w-4 text-green-500" />
    case "down":
      return <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />
    default:
      return <Activity className="h-4 w-4 text-gray-500" />
  }
}

function getMetricIcon(name: string) {
  switch (name.toLowerCase()) {
    case "response time":
      return <Zap className="h-5 w-5" />
    case "throughput":
      return <Activity className="h-5 w-5" />
    case "error rate":
      return <AlertTriangle className="h-5 w-5" />
    case "cpu usage":
      return <Server className="h-5 w-5" />
    case "memory usage":
      return <Server className="h-5 w-5" />
    case "database latency":
      return <Database className="h-5 w-5" />
    default:
      return <Gauge className="h-5 w-5" />
  }
}

export default function PerformanceMonitoring() {
  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Performance Monitoring</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Monitor system performance and health metrics</p>
      </div>

      <div className="grid gap-6 md:grid-cols-4 mb-8">
        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <Gauge className="mr-2 h-5 w-5" />
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">98.5%</div>
            <p className="text-xs text-white/70">Excellent</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <Globe className="mr-2 h-5 w-5" />
              Uptime
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">99.9%</div>
            <p className="text-xs text-white/70">30 days</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <Activity className="mr-2 h-5 w-5" />
              Active Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-500">847</div>
            <p className="text-xs text-white/70">Currently online</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-500">3</div>
            <p className="text-xs text-white/70">Active warnings</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2 mb-8">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Quick Actions</CardTitle>
            <CardDescription className="text-white/70">
              Performance management tools
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button className="w-full glass-button">
              <Activity className="mr-2 h-4 w-4" />
              View Detailed Metrics
            </Button>
            <Button variant="outline" className="w-full glass-button">
              <Server className="mr-2 h-4 w-4" />
              Server Status
            </Button>
            <Button variant="outline" className="w-full glass-button">
              <Database className="mr-2 h-4 w-4" />
              Database Health
            </Button>
            <Button variant="outline" className="w-full glass-button">
              <Globe className="mr-2 h-4 w-4" />
              Network Status
            </Button>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">System Resources</CardTitle>
            <CardDescription className="text-white/70">
              Current resource utilization
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-white">CPU Usage</span>
                <span className="text-white">68%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <div className="bg-yellow-500 h-2 rounded-full" style={{ width: "68%" }}></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-white">Memory Usage</span>
                <span className="text-white">72%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <div className="bg-yellow-500 h-2 rounded-full" style={{ width: "72%" }}></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-white">Disk Usage</span>
                <span className="text-white">45%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: "45%" }}></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-white">Network I/O</span>
                <span className="text-white">32%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: "32%" }}></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Performance Metrics</CardTitle>
          <CardDescription className="text-white/70">
            Real-time system performance indicators
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {performanceMetrics.map((metric) => (
              <div
                key={metric.name}
                className="p-4 rounded-lg border border-white/10 bg-white/5"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getMetricIcon(metric.name)}
                    <span className="font-medium text-white">{metric.name}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {getTrendIcon(metric.trend)}
                    {getStatusBadge(metric.status)}
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {metric.value}
                  <span className="text-sm text-white/70 ml-1">{metric.unit}</span>
                </div>
                <p className="text-xs text-white/50">{metric.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
