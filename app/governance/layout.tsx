import type { Metadata } from 'next'
import { ErrorProvider, ErrorBoundary, ErrorNotifications } from '@/components/governance/error-handling'
import { OnboardingProvider, OnboardingModal } from '@/components/governance/onboarding-system'
import { AccessibilityProvider, SkipToContent } from '@/components/governance/accessibility-enhancements'
import { AnalyticsProvider } from '@/components/governance/advanced-analytics'

export const metadata: Metadata = {
  title: "Governance | PawPumps - DAO Voting & Proposals",
  description: "Participate in PawPumps DAO governance. Vote on proposals, stake $PAW tokens, manage treasury, and shape the future of the memecoin launchpad platform.",
  keywords: ['governance', 'DAO', 'voting', 'proposals', 'PAW token', 'staking', 'treasury', 'decentralized'],
  openGraph: {
    title: "Governance | PawPumps DAO",
    description: "Participate in decentralized governance and shape the future of PawPumps",
    type: 'website',
  },
}

export default function GovernanceLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AnalyticsProvider>
      <AccessibilityProvider>
        <ErrorProvider>
          <OnboardingProvider>
            <ErrorBoundary>
              <SkipToContent />
              <main id="main-content">
                {children}
              </main>
              <ErrorNotifications />
              <OnboardingModal />
            </ErrorBoundary>
          </OnboardingProvider>
        </ErrorProvider>
      </AccessibilityProvider>
    </AnalyticsProvider>
  )
}
