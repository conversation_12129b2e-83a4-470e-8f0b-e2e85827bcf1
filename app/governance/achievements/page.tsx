"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ReputationSystem } from "@/components/gamification/reputation-system"
import { BadgesAchievements } from "@/components/gamification/badges-achievements"
import { Leaderboards } from "@/components/gamification/leaderboards"
import { ErrorBoundary } from "@/components/error-boundary"
import { Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"

export default function AchievementsPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Governance Achievements</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Track your reputation, earn badges, and compete on leaderboards</p>
      </div>

      <UnifiedGovernanceNav />

      <div className="mb-6">
        <Tabs defaultValue="reputation" className="w-full">
          <TabsList className="grid w-full grid-cols-3 glass mb-6">
            <TabsTrigger value="reputation" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
              Reputation System
            </TabsTrigger>
            <TabsTrigger
              value="badges"
              className="data-[state=active]:text-dogechain data-[state=active]:bg-dogechain/10"
            >
              Badges & Achievements
            </TabsTrigger>
            <TabsTrigger
              value="leaderboards"
              className="data-[state=active]:text-green-500 data-[state=active]:bg-green-500/10"
            >
              Leaderboards
            </TabsTrigger>
          </TabsList>

          <TabsContent value="reputation">
            <ErrorBoundary>
              <Suspense fallback={<GamificationSkeleton />}>
                <ReputationSystem />
              </Suspense>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="badges">
            <ErrorBoundary>
              <Suspense fallback={<GamificationSkeleton />}>
                <BadgesAchievements />
              </Suspense>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="leaderboards">
            <ErrorBoundary>
              <Suspense fallback={<GamificationSkeleton />}>
                <Leaderboards />
              </Suspense>
            </ErrorBoundary>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

function GamificationSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-10 w-40" />
      </div>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Skeleton className="h-40 w-full rounded-lg" />
        <Skeleton className="h-40 w-full rounded-lg" />
        <Skeleton className="h-40 w-full rounded-lg" />
      </div>
      <div className="grid gap-6 md:grid-cols-2">
        <Skeleton className="h-80 w-full rounded-lg" />
        <Skeleton className="h-80 w-full rounded-lg" />
      </div>
      <Skeleton className="h-96 w-full rounded-lg" />
    </div>
  )
}
