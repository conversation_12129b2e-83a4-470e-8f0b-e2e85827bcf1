"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { WorkingGroups } from "@/components/governance/working-groups"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { useNotification } from "@/hooks/use-notification"
import { FileText, Plus, Users } from "lucide-react"

export default function WorkingGroupsPage() {
  const { showNotification } = useNotification()

  // Mock user's groups
  const userGroups = ["dev-group", "community-group"]

  // <PERSON>le joining a group
  const handleJoinGroup = async (groupId: string) => {
    // In a real app, this would call a smart contract or API
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        showNotification({
          title: "Joined Group",
          message: "You have successfully joined the working group",
          type: "success",
        })
        resolve()
      }, 2000)
    })
  }

  // <PERSON><PERSON> leaving a group
  const handleLeaveGroup = async (groupId: string) => {
    // In a real app, this would call a smart contract or API
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        showNotification({
          title: "Left Group",
          message: "You have successfully left the working group",
          type: "success",
        })
        resolve()
      }, 2000)
    })
  }

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Working Groups</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Specialized committees focused on specific areas of the platform</p>
      </div>

      <UnifiedGovernanceNav />

      <div className="mt-8 grid gap-8">
        <div className="flex justify-between items-center">
          <Tabs defaultValue="groups" className="w-full">
            <TabsList className="glass w-full max-w-md mx-auto">
              <TabsTrigger value="groups">
                <Users className="h-4 w-4 mr-2" />
                Working Groups
              </TabsTrigger>
              <TabsTrigger value="documentation">
                <FileText className="h-4 w-4 mr-2" />
                Documentation
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <WorkingGroups userGroups={userGroups} onJoinGroup={handleJoinGroup} onLeaveGroup={handleLeaveGroup} />

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Create a Working Group</CardTitle>
            <CardDescription className="text-white/70">
              Propose a new specialized committee for a specific area
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-white/70 mb-4">
              If you have identified a need for a new working group that isn't covered by existing committees, you can
              propose to create one. New working groups require a governance proposal and community approval.
            </p>
            <Button className="doge-button doge-shine">
              <Plus className="h-4 w-4 mr-2" />
              Propose New Working Group
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
