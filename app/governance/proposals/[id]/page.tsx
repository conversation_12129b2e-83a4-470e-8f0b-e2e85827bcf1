"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { StatusBadge } from "@/components/governance/status-badge"
import { CategoryBadge } from "@/components/governance/category-badge"
import { ThumbsUp, ThumbsDown, ArrowLeft, Share2, MessageSquare, Code, Send } from "lucide-react"
import { useWallet } from "@/components/wallet-provider"
import { useNotification } from "@/hooks/use-notification"
import { ProposalExecution } from "@/components/governance/proposal-execution"
import { ProposalDiscussion } from "@/components/governance/proposal-discussion"
import { cn } from "@/lib/utils"
import { governanceTokens } from "@/styles/governance-tokens"
import { mockProposals, type Proposal } from "@/data/mock-proposals"
import { BlockchainTransactionStatus } from "@/components/governance/blockchain-transaction-status"
import { simulateBlockchainTransaction } from "@/utils/blockchain-simulation"
import { Textarea } from "@/components/ui/textarea"
import { ConnectWalletButton } from "@/components/connect-wallet-button"

export default function ProposalDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { isConnected, connect } = useWallet()
  const { showNotification } = useNotification()
  const [proposal, setProposal] = useState<Proposal | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isVoting, setIsVoting] = useState(false)
  const [voteType, setVoteType] = useState<"for" | "against" | null>(null)
  const [showExecutionModal, setShowExecutionModal] = useState(false)
  const [transactionStatus, setTransactionStatus] = useState<any>(null)
  const [activeTab, setActiveTab] = useState("details")
  const [comment, setComment] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    // In a real app, this would fetch from the blockchain or API
    const proposalId = params.id
    const foundProposal = mockProposals.find((p) => p.id === proposalId)

    if (foundProposal) {
      setProposal(foundProposal)
    }

    setIsLoading(false)
  }, [params.id])

  if (isLoading) {
    return (
      <div className="container py-8 md:py-12">
        <div className="flex justify-center items-center h-64">
          <div className="animate-pulse space-y-4 w-full max-w-3xl">
            <div className="h-8 bg-white/10 rounded w-3/4"></div>
            <div className="h-4 bg-white/10 rounded w-1/2"></div>
            <div className="h-64 bg-white/5 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!proposal) {
    return (
      <div className="container py-8 md:py-12">
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <h2 className="text-2xl font-bold mb-4">Proposal Not Found</h2>
          <p className="text-white/70 mb-6">The proposal you're looking for doesn't exist or has been removed.</p>
          <Button onClick={() => router.push("/governance/proposals")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Proposals
          </Button>
        </div>
      </div>
    )
  }

  const handleVote = async (vote: "for" | "against") => {
    if (!isConnected) {
      showNotification({
        title: "Wallet Not Connected",
        message: "Please connect your wallet to vote on proposals",
        type: "error",
      })
      return
    }

    setIsVoting(true)
    setVoteType(vote)

    try {
      // Simulate blockchain transaction
      setTransactionStatus({ status: "pending", message: "Submitting vote to blockchain..." })

      const result = await simulateBlockchainTransaction({
        type: "vote",
        proposalId: proposal.id,
        vote: vote,
      })

      if (result.success) {
        setTransactionStatus({ status: "success", message: "Vote submitted successfully!" })
        showNotification({
          title: "Vote Submitted",
          message: `You voted ${vote} proposal "${proposal.title}"`,
          type: "success",
        })
      } else {
        throw new Error(result.error || "Transaction failed")
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred"
      setTransactionStatus({ status: "error", message: errorMessage })
      showNotification({
        title: "Vote Failed",
        message: errorMessage,
        type: "error",
      })
    } finally {
      // In a real app, we would update the proposal data from the blockchain
      setTimeout(() => {
        setIsVoting(false)
        setVoteType(null)
        setTransactionStatus(null)
      }, 3000)
    }
  }

  const handleExecute = async () => {
    setShowExecutionModal(true)
  }

  const handleExecutionSuccess = () => {
    setShowExecutionModal(false)
    showNotification({
      title: "Proposal Executed",
      message: `Proposal ${proposal.id} has been successfully executed`,
      type: "success",
    })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator
        .share({
          title: `PawPumps Governance: ${proposal.title}`,
          text: proposal.description,
          url: window.location.href,
        })
        .catch((error) => console.log("Error sharing", error))
    } else {
      navigator.clipboard.writeText(window.location.href)
      showNotification({
        title: "Link Copied",
        message: "Proposal link copied to clipboard",
        type: "success",
      })
    }
  }

  // Calculate voting statistics
  const totalVotes = proposal.votesFor + proposal.votesAgainst
  const forPercentage = totalVotes > 0 ? (proposal.votesFor / totalVotes) * 100 : 0
  const againstPercentage = totalVotes > 0 ? (proposal.votesAgainst / totalVotes) * 100 : 0
  const quorumPercentage = totalVotes > 0 ? (totalVotes / proposal.quorum) * 100 : 0

  // Format votes for display
  const formatVotes = (votes: number) => {
    if (votes >= 1000000) {
      return `${(votes / 1000000).toFixed(1)}M`
    } else if (votes >= 1000) {
      return `${(votes / 1000).toFixed(1)}K`
    }
    return votes.toString()
  }

  const handleSubmitComment = async () => {
    if (!isConnected) {
      showNotification({
        title: "Wallet Not Connected",
        message: "Please connect your wallet to post comments",
        type: "error",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Simulate submitting comment
      await new Promise((resolve) => setTimeout(resolve, 1500))

      showNotification({
        title: "Comment Posted",
        message: "Your comment has been successfully posted",
        type: "success",
      })
      setComment("")
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred"
      showNotification({
        title: "Failed to Post Comment",
        message: errorMessage,
        type: "error",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <Button
            variant="outline"
            size="sm"
            className="border-white/10 bg-white/5 text-white hover:bg-white/10"
            onClick={() => router.push("/governance/proposals")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Proposals
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="border-white/10 bg-white/5 text-white hover:bg-white/10 ml-auto"
            onClick={handleShare}
          >
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
        </div>

        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>{proposal.title}</ShimmerText>
        </h1>

        <div className="flex flex-wrap items-center gap-3 mb-4">
          <div className="flex items-center gap-2">
            <span className={cn("font-mono", governanceTokens.typography.metadata)}>{proposal.id}</span>
            <StatusBadge status={proposal.status} type="proposal" className="text-base" />
          </div>
          <CategoryBadge category={proposal.category} />
        </div>
      </div>

      <UnifiedGovernanceNav />

      <div className="grid gap-8 lg:grid-cols-3 mt-8">
        <div className="lg:col-span-2 space-y-6">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div className="flex gap-4">
                  <Button
                    variant={activeTab === "details" ? "default" : "ghost"}
                    className={activeTab === "details" ? "bg-doge/20 text-doge hover:bg-doge/30" : ""}
                    onClick={() => setActiveTab("details")}
                  >
                    Details
                  </Button>
                  <Button
                    variant={activeTab === "discussion" ? "default" : "ghost"}
                    className={activeTab === "discussion" ? "bg-doge/20 text-doge hover:bg-doge/30" : ""}
                    onClick={() => setActiveTab("discussion")}
                  >
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Discussion
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              {activeTab === "details" ? (
                <div className="space-y-6">
                  <div className="prose prose-invert max-w-none">
                    <p className="text-white/90 whitespace-pre-wrap">{proposal.description}</p>
                  </div>

                  <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <h3 className="text-lg font-medium text-white mb-4">Voting Status</h3>

                    <div className="space-y-6">
                      <div>
                        <div className="flex justify-between mb-1">
                          <div className="flex items-center">
                            <span className="text-sm text-white/70 mr-2">For</span>
                            <div className="w-3 h-3 rounded-full bg-green-500 mr-1" aria-hidden="true"></div>
                            <span className="text-xs text-white/50">(Green)</span>
                          </div>
                          <span className="text-sm font-medium text-white">
                            {formatVotes(proposal.votesFor)} ({forPercentage.toFixed(1)}%)
                          </span>
                        </div>
                        <Progress
                          value={forPercentage}
                          className="h-2 bg-white/10"
                          indicatorClassName="bg-green-500"
                          aria-label={`For votes: ${forPercentage.toFixed(1)}%`}
                        />
                      </div>

                      <div>
                        <div className="flex justify-between mb-1">
                          <div className="flex items-center">
                            <span className="text-sm text-white/70 mr-2">Against</span>
                            <div className="w-3 h-3 rounded-full bg-red-500 mr-1" aria-hidden="true"></div>
                            <span className="text-xs text-white/50">(Red)</span>
                          </div>
                          <span className="text-sm font-medium text-white">
                            {formatVotes(proposal.votesAgainst)} ({againstPercentage.toFixed(1)}%)
                          </span>
                        </div>
                        <Progress
                          value={againstPercentage}
                          className="h-2 bg-white/10"
                          indicatorClassName="bg-red-500"
                          aria-label={`Against votes: ${againstPercentage.toFixed(1)}%`}
                        />
                      </div>

                      <div>
                        <div className="flex justify-between mb-1">
                          <div className="flex items-center">
                            <span className="text-sm text-white/70 mr-2">Quorum</span>
                            <div className="w-3 h-3 rounded-full bg-blue-500 mr-1" aria-hidden="true"></div>
                            <span className="text-xs text-white/50">(Blue)</span>
                          </div>
                          <span className="text-sm font-medium text-white">
                            {formatVotes(totalVotes)} / {formatVotes(proposal.quorum)} ({quorumPercentage.toFixed(1)}%)
                          </span>
                        </div>
                        <Progress
                          value={Math.min(quorumPercentage, 100)}
                          className="h-2 bg-white/10"
                          indicatorClassName="bg-blue-500"
                          aria-label={`Quorum progress: ${quorumPercentage.toFixed(1)}%`}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                      <h4 className="text-sm font-medium text-white mb-2">Proposal Details</h4>
                      <dl className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <dt className="text-white/70">Creator:</dt>
                          <dd className="text-white font-mono">{proposal.creator}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-white/70">Created:</dt>
                          <dd className="text-white">{proposal.createdAt}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-white/70">
                            {proposal.status === "active" || proposal.status === "pending" ? "Ends:" : "Ended:"}
                          </dt>
                          <dd className="text-white">{proposal.endTime}</dd>
                        </div>
                      </dl>
                    </div>

                    {proposal.actions && proposal.actions.length > 0 && (
                      <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                        <h4 className="text-sm font-medium text-white mb-2">On-Chain Actions</h4>
                        <ul className="space-y-2 text-sm">
                          {proposal.actions.map((action, index) => (
                            <li key={index} className="text-white/90">
                              <span className="text-doge">{action.type}:</span>{" "}
                              {Object.entries(action.params).map(([key, value]) => (
                                <span key={key}>
                                  {key}: {value.toString()}{" "}
                                </span>
                              ))}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <ProposalDiscussion proposalId={proposal.id} />
              )}
            </CardContent>

            {activeTab === "details" && (
              <CardFooter className="flex flex-col sm:flex-row gap-4">
                {proposal.status === "active" && (
                  <>
                    <Button
                      className="flex-1 doge-button doge-shine"
                      onClick={() => handleVote("for")}
                      disabled={isVoting || !isConnected}
                    >
                      {isVoting && voteType === "for" ? (
                        <span className="animate-pulse">Voting...</span>
                      ) : (
                        <>
                          <ThumbsUp className="mr-2 h-4 w-4" />
                          Vote For
                        </>
                      )}
                    </Button>
                    <Button
                      className="flex-1 glass-button"
                      onClick={() => handleVote("against")}
                      disabled={isVoting || !isConnected}
                    >
                      {isVoting && voteType === "against" ? (
                        <span className="animate-pulse">Voting...</span>
                      ) : (
                        <>
                          <ThumbsDown className="mr-2 h-4 w-4" />
                          Vote Against
                        </>
                      )}
                    </Button>
                  </>
                )}

                {proposal.status === "passed" && proposal.actions && proposal.actions.length > 0 && (
                  <Button
                    className="flex-1 bg-doge text-black hover:bg-doge/90"
                    onClick={handleExecute}
                    disabled={!isConnected}
                  >
                    <Code className="mr-2 h-4 w-4" />
                    Execute Proposal
                  </Button>
                )}

                {(proposal.status === "pending" ||
                  proposal.status === "failed" ||
                  proposal.status === "implemented") && (
                  <Button className="flex-1 glass-button" disabled>
                    {proposal.status === "pending"
                      ? "Voting Not Started"
                      : proposal.status === "failed"
                        ? "Proposal Failed"
                        : "Proposal Implemented"}
                  </Button>
                )}
              </CardFooter>
            )}
          </Card>

          {transactionStatus && (
            <BlockchainTransactionStatus status={transactionStatus.status} message={transactionStatus.message} />
          )}
        </div>

        <div className="space-y-6">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle className="text-white text-lg">Your Voting Power</CardTitle>
            </CardHeader>
            <CardContent>
              {isConnected ? (
                <div className="text-center p-4">
                  <p className="text-3xl font-bold text-doge mb-2">5,000</p>
                  <p className="text-white/70">$PAW tokens</p>
                  <p className="text-sm text-white/50 mt-4">
                    Your voting power is based on your $PAW token balance. Each token equals one vote.
                  </p>
                </div>
              ) : (
                <div className="text-center p-4">
                  <p className="text-white/70 mb-4">Connect your wallet to view your voting power</p>
                  <ConnectWalletButton className="doge-button doge-shine" />
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle className="text-white text-lg">Similar Proposals</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockProposals
                  .filter((p) => p.id !== proposal.id && p.category === proposal.category)
                  .slice(0, 3)
                  .map((p) => (
                    <div
                      key={p.id}
                      className="p-3 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 cursor-pointer transition-colors"
                      onClick={() => router.push(`/governance/proposals/${p.id}`)}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <StatusBadge status={p.status} type="proposal" />
                        <span className="text-xs text-white/50">{p.id}</span>
                      </div>
                      <h4 className="font-medium text-white mb-1">{p.title}</h4>
                      <p className="text-xs text-white/70 line-clamp-2">{p.description}</p>
                    </div>
                  ))}

                {mockProposals.filter((p) => p.id !== proposal.id && p.category === proposal.category).length === 0 && (
                  <p className="text-white/50 text-center py-4">No similar proposals found</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {showExecutionModal && (
        <ProposalExecution
          proposalId={proposal.id}
          title={proposal.title}
          description={proposal.description}
          actions={proposal.actions || []}
          onClose={() => setShowExecutionModal(false)}
          onSuccess={handleExecutionSuccess}
        />
      )}

      <Card className="glass-card border-white/5 mt-8 lg:hidden">
        <CardHeader>
          <CardTitle className="text-white text-lg">Discussion</CardTitle>
        </CardHeader>
        <CardContent>
          {isConnected ? (
            <div className="space-y-4">
              <Textarea
                placeholder="Share your thoughts on this proposal..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                className="glass-input text-white min-h-[100px]"
                disabled={isSubmitting}
              />
              <div className="flex justify-end">
                <Button
                  onClick={handleSubmitComment}
                  disabled={!comment.trim() || isSubmitting}
                  className="bg-doge text-black hover:bg-doge/90"
                >
                  {isSubmitting ? (
                    <span className="animate-pulse">Posting...</span>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Post Comment
                    </>
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center p-4 bg-white/5 rounded-lg">
              <p className="text-white/70 mb-4">Connect your wallet to join the discussion</p>
              <ConnectWalletButton className="doge-button doge-shine" />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
