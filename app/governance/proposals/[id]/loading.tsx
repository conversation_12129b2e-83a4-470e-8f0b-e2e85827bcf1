import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"

export default function ProposalDetailLoading() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <Skeleton className="h-9 w-24" />
          <Skeleton className="h-9 w-24 ml-auto" />
        </div>

        <Skeleton className="h-12 w-3/4 mb-2" />
        <div className="flex flex-wrap items-center gap-3 mb-4">
          <Skeleton className="h-6 w-20" />
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-6 w-32" />
        </div>
      </div>

      <Skeleton className="h-12 w-full mb-8" />

      <div className="grid gap-8 lg:grid-cols-3 mt-8">
        <div className="lg:col-span-2 space-y-6">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div className="flex gap-4">
                  <Skeleton className="h-10 w-24" />
                  <Skeleton className="h-10 w-32" />
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <div className="space-y-6">
                <Skeleton className="h-24 w-full" />

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <Skeleton className="h-6 w-48 mb-4" />

                  <div className="space-y-6">
                    <div>
                      <div className="flex justify-between mb-1">
                        <Skeleton className="h-5 w-24" />
                        <Skeleton className="h-5 w-24" />
                      </div>
                      <Skeleton className="h-2 w-full" />
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <Skeleton className="h-5 w-24" />
                        <Skeleton className="h-5 w-24" />
                      </div>
                      <Skeleton className="h-2 w-full" />
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <Skeleton className="h-5 w-24" />
                        <Skeleton className="h-5 w-24" />
                      </div>
                      <Skeleton className="h-2 w-full" />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Skeleton className="h-32 w-full" />
                  <Skeleton className="h-32 w-full" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <Skeleton className="h-6 w-40" />
            </CardHeader>
            <CardContent>
              <div className="text-center p-4">
                <Skeleton className="h-10 w-24 mx-auto mb-2" />
                <Skeleton className="h-5 w-32 mx-auto" />
                <Skeleton className="h-4 w-48 mx-auto mt-4" />
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/5">
            <CardHeader>
              <Skeleton className="h-6 w-40" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
