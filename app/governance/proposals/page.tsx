"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import dynamic from "next/dynamic"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { GitPullRequest, Plus, Filter, Clock, CheckCircle, Search, Settings, Loader2 } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { mockProposals } from "@/data/mock-proposals"
import { useWallet } from "@/components/wallet-provider"
import { useNotification } from "@/hooks/use-notification"
import type { CategoryType } from "@/data/mock-proposals"
import Link from "next/link"

// Dynamic import for Proposal<PERSON>orm to reduce initial bundle size
const ProposalForm = dynamic(
  () => import("@/components/governance/proposal-form").then(mod => ({ default: mod.ProposalForm })),
  {
    loading: () => (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin text-white/60" />
      </div>
    ),
    ssr: false,
  }
)

export default function ProposalsPage() {
  const router = useRouter()
  const { isConnected } = useWallet()
  const { showNotification } = useNotification()
  const [activeTab, setActiveTab] = useState("active")
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [showProposalForm, setShowProposalForm] = useState(false)
  const isAdmin = true // TODO: Replace with actual admin check

  // Filter proposals based on active tab, search query, and category filter
  const filteredProposals = mockProposals.filter((proposal) => {
    // Filter by tab (active/past)
    if (activeTab === "active" && !["active", "pending"].includes(proposal.status)) {
      return false
    }
    if (activeTab === "past" && ["active", "pending"].includes(proposal.status)) {
      return false
    }

    // Filter by search query
    if (
      searchQuery &&
      !proposal.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !proposal.description.toLowerCase().includes(searchQuery.toLowerCase())
    ) {
      return false
    }

    // Filter by category
    if (categoryFilter !== "all" && proposal.category !== categoryFilter) {
      return false
    }

    return true
  })

  const handleCreateProposal = (proposal: {
    title: string
    description: string
    category: CategoryType
  }) => {
    // In a real app, this would send the proposal to the blockchain
    showNotification({
      title: "Proposal Created",
      message: `Your proposal "${proposal.title}" has been submitted for review`,
      type: "success",
    })

    setShowProposalForm(false)
  }

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Governance Proposals</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">View and vote on governance proposals to shape the future of PawPumps</p>
      </div>

      <UnifiedGovernanceNav />

      {isAdmin && (
        <div className="mb-6">
          <Link href="/governance/admin/dashboard">
            <Button variant="outline" className="border-doge/20 bg-doge/10 text-doge hover:bg-doge/20">
              <Settings className="mr-2 h-4 w-4" />
              Admin Dashboard
            </Button>
          </Link>
        </div>
      )}

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="flex items-center gap-2">
          <GitPullRequest className="h-5 w-5 text-doge" />
          <h2 className="text-xl font-bold">Proposals</h2>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
            <Input
              placeholder="Search proposals..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="glass-input text-white pl-9 w-full"
            />
          </div>
          <div className="flex gap-2">
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="glass-input text-white w-[140px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="core-functionality">Core Functionality</SelectItem>
                <SelectItem value="user-experience">User Experience</SelectItem>
                <SelectItem value="visual-design">Visual Design</SelectItem>
                <SelectItem value="mobile">Mobile</SelectItem>
                <SelectItem value="performance">Performance</SelectItem>
                <SelectItem value="accessibility">Accessibility</SelectItem>
                <SelectItem value="content">Content</SelectItem>
                <SelectItem value="technical">Technical</SelectItem>
                <SelectItem value="security">Security</SelectItem>
                <SelectItem value="social">Social</SelectItem>
              </SelectContent>
            </Select>
            <Button
              className="bg-doge text-black hover:bg-doge/90"
              onClick={() => setShowProposalForm(true)}
              disabled={!isConnected}
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Proposal
            </Button>
          </div>
        </div>
      </div>

      <Tabs defaultValue="active" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 bg-black/20 border border-white/10">
          <TabsTrigger value="active" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <Clock className="mr-2 h-4 w-4" />
            Active Proposals
          </TabsTrigger>
          <TabsTrigger value="past" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <CheckCircle className="mr-2 h-4 w-4" />
            Past Proposals
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab}>
          {filteredProposals.length > 0 ? (
            <div className="space-y-6">
              {filteredProposals.map((proposal) => (
                <ProposalCard
                  key={proposal.id}
                  proposal={proposal}
                  onClick={() => router.push(`/governance/proposals/${proposal.id}`)}
                />
              ))}
            </div>
          ) : (
            <div className="text-center p-8 bg-white/5 rounded-lg">
              <p className="text-white/50 mb-4">No proposals found matching your criteria</p>
              {activeTab === "active" && isConnected && (
                <Button className="bg-doge text-black hover:bg-doge/90" onClick={() => setShowProposalForm(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Proposal
                </Button>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {showProposalForm && <ProposalForm onClose={() => setShowProposalForm(false)} onSubmit={handleCreateProposal} />}
    </div>
  )
}

function ProposalCard({ proposal, onClick }: { proposal: any; onClick: () => void }) {
  const totalVotes = proposal.votesFor + proposal.votesAgainst
  const forPercentage = totalVotes > 0 ? (proposal.votesFor / totalVotes) * 100 : 0
  const againstPercentage = totalVotes > 0 ? (proposal.votesAgainst / totalVotes) * 100 : 0
  const quorumPercentage = totalVotes > 0 ? (totalVotes / proposal.quorum) * 100 : 0

  return (
    <Card
      className="glass-card border-white/5 hover:border-doge/30 hover:bg-black/40 transition-all cursor-pointer"
      onClick={onClick}
      tabIndex={0}
      role="button"
      aria-label={`View details for proposal: ${proposal.title}`}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault()
          onClick()
        }
      }}
    >
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-xs font-medium bg-white/10 text-white/70 px-2 py-1 rounded">{proposal.id}</span>
              {proposal.status === "active" && (
                <span className="text-xs font-medium bg-blue-500/20 text-blue-500 px-2 py-1 rounded flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>Active</span>
                </span>
              )}
              {proposal.status === "passed" && (
                <span className="text-xs font-medium bg-green-500/20 text-green-500 px-2 py-1 rounded flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  <span>Passed</span>
                </span>
              )}
              {proposal.status === "failed" && (
                <span className="text-xs font-medium bg-red-500/20 text-red-500 px-2 py-1 rounded flex items-center gap-1">
                  <span>Failed</span>
                </span>
              )}
            </div>
            <CardTitle className="text-white">{proposal.title}</CardTitle>
            <CardDescription className="text-white/70 mt-1 line-clamp-2">{proposal.description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between mb-1">
              <div className="flex items-center">
                <span className="text-sm text-white/70 mr-2">For</span>
                <div className="w-3 h-3 rounded-full bg-green-500 mr-1" aria-hidden="true"></div>
                <span className="text-xs text-white/50">(Green)</span>
              </div>
              <span className="text-sm font-medium text-white">
                {(proposal.votesFor / 1000000).toFixed(2)}M ({forPercentage.toFixed(1)}%)
              </span>
            </div>
            <div className="h-2 w-full bg-white/10 rounded-full overflow-hidden">
              <div
                className="h-full bg-green-500 rounded-full"
                style={{ width: `${forPercentage}%` }}
                aria-label={`For votes: ${forPercentage.toFixed(1)}%`}
              ></div>
            </div>
          </div>

          <div>
            <div className="flex justify-between mb-1">
              <div className="flex items-center">
                <span className="text-sm text-white/70 mr-2">Against</span>
                <div className="w-3 h-3 rounded-full bg-red-500 mr-1" aria-hidden="true"></div>
                <span className="text-xs text-white/50">(Red)</span>
              </div>
              <span className="text-sm font-medium text-white">
                {(proposal.votesAgainst / 1000000).toFixed(2)}M ({againstPercentage.toFixed(1)}%)
              </span>
            </div>
            <div className="h-2 w-full bg-white/10 rounded-full overflow-hidden">
              <div
                className="h-full bg-red-500 rounded-full"
                style={{ width: `${againstPercentage}%` }}
                aria-label={`Against votes: ${againstPercentage.toFixed(1)}%`}
              ></div>
            </div>
          </div>

          <div>
            <div className="flex justify-between mb-1">
              <div className="flex items-center">
                <span className="text-sm text-white/70 mr-2">Quorum</span>
                <div className="w-3 h-3 rounded-full bg-blue-500 mr-1" aria-hidden="true"></div>
                <span className="text-xs text-white/50">(Blue)</span>
              </div>
              <span className="text-sm font-medium text-white">
                {(totalVotes / 1000000).toFixed(2)}M / {(proposal.quorum / 1000000).toFixed(2)}M (
                {quorumPercentage.toFixed(1)}%)
              </span>
            </div>
            <div className="h-2 w-full bg-white/10 rounded-full overflow-hidden">
              <div
                className="h-full bg-blue-500 rounded-full"
                style={{ width: `${Math.min(quorumPercentage, 100)}%` }}
                aria-label={`Quorum progress: ${quorumPercentage.toFixed(1)}%`}
              ></div>
            </div>
          </div>

          <div className="flex justify-between items-center text-sm text-white/70">
            <div>Proposer: {proposal.creator}</div>
            <div>
              {proposal.status === "active" || proposal.status === "pending"
                ? `Ends ${proposal.endTime}`
                : `Ended ${proposal.endTime}`}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
