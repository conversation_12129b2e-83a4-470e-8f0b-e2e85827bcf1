"use client"

import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { ShimmerText } from "@/components/shimmer-text"

export default function DocsPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Documentation</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Learn how to use the PawPumps platform</p>
      </div>

      <Tabs defaultValue="getting-started" className="space-y-8">
        <div className="w-full overflow-x-auto">
          <TabsList className="inline-flex h-10 items-center justify-center rounded-md glass p-1 text-muted-foreground min-w-full md:w-full">
            <TabsTrigger value="getting-started" className="whitespace-nowrap px-3 py-1.5 text-sm font-medium">
              Getting Started
            </TabsTrigger>
            <TabsTrigger value="token-launch" className="whitespace-nowrap px-3 py-1.5 text-sm font-medium">
              Token Launch
            </TabsTrigger>
            <TabsTrigger value="trading" className="whitespace-nowrap px-3 py-1.5 text-sm font-medium">
              Trading
            </TabsTrigger>
            <TabsTrigger value="governance" className="whitespace-nowrap px-3 py-1.5 text-sm font-medium">
              Governance
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="getting-started">
          <Card className="glass-card border-white/5 liquid-glow">
            <CardHeader>
              <CardTitle className="text-white">Getting Started with PawPumps</CardTitle>
              <CardDescription className="text-white/70">
                Learn the basics of using the PawPumps platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl font-bold text-white">Welcome to PawPumps</h2>
                <p className="text-white/80">
                  PawPumps is the premier memecoin launchpad and DEX on the Dogechain Network. Our platform enables
                  creators to launch their own memecoins with ease, while providing traders with a secure and
                  user-friendly environment to trade and earn rewards.
                </p>

                <h3 className="text-xl font-bold text-white mt-6">Connecting Your Wallet</h3>
                <p className="text-white/80">
                  To get started with PawPumps, you&apos;ll need to connect your wallet. We support MetaMask, WalletConnect,
                  and other popular wallets. Click the &quot;Connect Wallet&quot; button in the top right corner of the page to
                  begin.
                </p>
                <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                  <h4 className="font-medium text-white mb-2">Required Network</h4>
                  <p className="text-white/80">
                    Make sure your wallet is connected to the Dogechain Network. If you haven&apos;t added Dogechain to your
                    wallet yet, you can do so with the following details:
                  </p>
                  <ul className="list-disc list-inside mt-2 space-y-1 text-white/80">
                    <li>Network Name: Dogechain</li>
                    <li>RPC URL: https://rpc.dogechain.dog</li>
                    <li>Chain ID: 2000</li>
                    <li>Symbol: wDOGE</li>
                    <li>Block Explorer: https://explorer.dogechain.dog</li>
                  </ul>
                </div>

                <h3 className="text-xl font-bold text-white mt-6">Platform Features</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <Link href="/launch" className="block">
                    <div className="rounded-lg bg-white/5 p-4 border border-white/10 hover:bg-white/10 transition-colors h-full">
                      <h4 className="font-medium text-white mb-2">Token Launch</h4>
                      <p className="text-white/80">
                        Create and deploy your own memecoin on the Dogechain Network in minutes with our intuitive
                        interface.
                      </p>
                    </div>
                  </Link>
                  <Link href="/trade" className="block">
                    <div className="rounded-lg bg-white/5 p-4 border border-white/10 hover:bg-white/10 transition-colors h-full">
                      <h4 className="font-medium text-white mb-2">Trading</h4>
                      <p className="text-white/80">
                        Swap tokens with bonding curve pricing, providing fair and transparent trading for all users.
                      </p>
                    </div>
                  </Link>
                  <Link href="/perpetuals" className="block">
                    <div className="rounded-lg bg-white/5 p-4 border border-white/10 hover:bg-white/10 transition-colors h-full">
                      <h4 className="font-medium text-white mb-2">Perpetual Trading</h4>
                      <p className="text-white/80">
                        Trade perpetual contracts with up to 100x leverage on your favorite memecoins.
                      </p>
                    </div>
                  </Link>
                  <Link href="/governance" className="block">
                    <div className="rounded-lg bg-white/5 p-4 border border-white/10 hover:bg-white/10 transition-colors h-full">
                      <h4 className="font-medium text-white mb-2">Governance</h4>
                      <p className="text-white/80">
                        Participate in platform governance by voting on proposals with your $PAW tokens.
                      </p>
                    </div>
                  </Link>
                </div>

                <h3 className="text-xl font-bold text-white mt-6">Getting Help</h3>
                <p className="text-white/80">
                  If you need assistance, you can reach out to our community on Discord or Twitter, or check out our FAQ
                  section for answers to common questions.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="token-launch">
          <Card className="glass-card border-white/5 liquid-glow">
            <CardHeader>
              <CardTitle className="text-white">Token Launch Guide</CardTitle>
              <CardDescription className="text-white/70">
                Learn how to create and launch your own memecoin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl font-bold text-white">Creating Your Memecoin</h2>
                <p className="text-white/80">
                  PawPumps makes it easy to create and launch your own memecoin on the Dogechain Network. Our intuitive
                  interface guides you through the process, from setting up your token parameters to deploying your
                  contract.
                </p>

                <h3 className="text-xl font-bold text-white mt-6">Token Parameters</h3>
                <div className="space-y-4">
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Token Name & Symbol</h4>
                    <p className="text-white/80">
                      Choose a unique and memorable name for your token, along with a short symbol (ticker). The name
                      can be 3-32 characters long, while the symbol should be 2-6 characters.
                    </p>
                  </div>
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Total Supply</h4>
                    <p className="text-white/80">
                      Determine the total supply of your token. This can range from 1 million to 100 billion tokens. The
                      supply affects the perceived value and distribution of your token.
                    </p>
                  </div>
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Bonding Curve</h4>
                    <p className="text-white/80">
                      Select a bonding curve type that determines how your token's price will change as more tokens are
                      bought and sold:
                    </p>
                    <ul className="list-disc list-inside mt-2 space-y-1 text-white/80">
                      <li>
                        <strong>Linear:</strong> Price increases linearly with supply sold, providing a predictable
                        price curve.
                      </li>
                      <li>
                        <strong>Exponential:</strong> Price rises faster as supply diminishes, creating scarcity value.
                      </li>
                      <li>
                        <strong>Logarithmic:</strong> Price stabilizes as supply grows, encouraging wider distribution.
                      </li>
                    </ul>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-white mt-6">Deployment Process</h3>
                <p className="text-white/80">
                  Once you've configured your token parameters, you can deploy your token to the Dogechain Network. This
                  process requires a small gas fee (approximately 0.1 wDOGE) to cover the cost of contract deployment.
                </p>
                <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                  <h4 className="font-medium text-white mb-2">Security Features</h4>
                  <p className="text-white/80">
                    PawPumps automatically implements several security features in your token contract:
                  </p>
                  <ul className="list-disc list-inside mt-2 space-y-1 text-white/80">
                    <li>Liquidity locking to prevent rug pulls</li>
                    <li>Anti-bot mechanisms to prevent front-running</li>
                    <li>Ownership renunciation options for true decentralization</li>
                  </ul>
                </div>

                <h3 className="text-xl font-bold text-white mt-6">After Launch</h3>
                <p className="text-white/80">
                  After your token is deployed, you can manage it through the platform. This includes monitoring trading
                  activity, adding liquidity, and promoting your token through the social features.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trading">
          <Card className="glass-card border-white/5 liquid-glow">
            <CardHeader>
              <CardTitle className="text-white">Trading Guide</CardTitle>
              <CardDescription className="text-white/70">
                Learn how to trade tokens and provide liquidity
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl font-bold text-white">Trading on PawPumps</h2>
                <p className="text-white/80">
                  PawPumps offers a user-friendly trading interface that allows you to swap tokens, provide liquidity,
                  and trade perpetual contracts with ease.
                </p>

                <h3 className="text-xl font-bold text-white mt-6">Swapping Tokens</h3>
                <p className="text-white/80">
                  The swap interface allows you to exchange one token for another. Simply select the tokens you want to
                  swap, enter the amount, and confirm the transaction.
                </p>
                <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                  <h4 className="font-medium text-white mb-2">Slippage Tolerance</h4>
                  <p className="text-white/80">
                    Slippage refers to the difference between the expected price of a trade and the price at which the
                    trade is executed. You can adjust your slippage tolerance to account for price fluctuations during
                    the transaction.
                  </p>
                </div>

                <h3 className="text-xl font-bold text-white mt-6">Providing Liquidity</h3>
                <p className="text-white/80">
                  Liquidity providers add pairs of tokens to liquidity pools, enabling trading on the platform. In
                  return, they earn a portion of the trading fees generated by the pool.
                </p>
                <div className="space-y-4">
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Adding Liquidity</h4>
                    <p className="text-white/80">
                      To add liquidity, you need to provide equal values of two tokens. You'll receive LP tokens
                      representing your share of the pool, which automatically earn fees proportional to your share.
                    </p>
                  </div>
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Removing Liquidity</h4>
                    <p className="text-white/80">
                      You can remove your liquidity at any time by burning your LP tokens. This will return your
                      original tokens plus any fees earned during the time your liquidity was provided.
                    </p>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-white mt-6">Perpetual Trading</h3>
                <p className="text-white/80">
                  Perpetual contracts allow you to trade with leverage, potentially amplifying your gains (and losses).
                  Unlike spot trading, perpetuals don't have an expiry date.
                </p>
                <div className="space-y-4">
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Opening a Position</h4>
                    <p className="text-white/80">
                      To open a position, select the market, choose long (if you expect the price to rise) or short (if
                      you expect the price to fall), set your position size and leverage, and confirm the trade.
                    </p>
                  </div>
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Liquidation</h4>
                    <p className="text-white/80">
                      When trading with leverage, be aware of the liquidation price. If the market moves against your
                      position and reaches this price, your position will be automatically closed to prevent further
                      losses.
                    </p>
                  </div>
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Risk Management</h4>
                    <p className="text-white/80">
                      Use take profit and stop loss orders to manage risk. These allow you to automatically close your
                      position when certain price levels are reached.
                    </p>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-white mt-6">Trading Fees</h3>
                <p className="text-white/80">
                  PawPumps charges a 0.5% fee on all trades. This fee is split between token creators (0.25%) and the
                  platform (0.25%), with a portion of the platform's share used to buy and burn $PAW tokens.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="governance">
          <Card className="glass-card border-white/5 liquid-glow">
            <CardHeader>
              <CardTitle className="text-white">Governance Guide</CardTitle>
              <CardDescription className="text-white/70">
                Learn how to participate in platform governance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl font-bold text-white">Governance on PawPumps</h2>
                <p className="text-white/80">
                  PawPumps is governed by its community through a decentralized autonomous organization (DAO). $PAW
                  token holders can vote on proposals to shape the future of the platform.
                </p>

                <h3 className="text-xl font-bold text-white mt-6">Voting Power</h3>
                <p className="text-white/80">
                  Your voting power is determined by the amount of $PAW tokens you have staked. The more tokens you
                  stake, the more influence you have in governance decisions.
                </p>
                <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                  <h4 className="font-medium text-white mb-2">Staking $PAW</h4>
                  <p className="text-white/80">
                    To participate in governance, you need to stake your $PAW tokens. Staking not only gives you voting
                    power but also earns you staking rewards in the form of additional $PAW tokens.
                  </p>
                </div>

                <h3 className="text-xl font-bold text-white mt-6">Proposals</h3>
                <p className="text-white/80">
                  Proposals are suggestions for changes to the platform. They can range from technical upgrades to fee
                  adjustments to new feature implementations.
                </p>
                <div className="space-y-4">
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Creating Proposals</h4>
                    <p className="text-white/80">
                      Any user with a minimum amount of staked $PAW tokens can create a proposal. The proposal should
                      include a clear description of the change, the rationale behind it, and any technical details
                      required for implementation.
                    </p>
                  </div>
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Voting on Proposals</h4>
                    <p className="text-white/80">
                      When a proposal is active, eligible voters can cast their votes either for or against it. The
                      voting period typically lasts for 7 days, after which the proposal is either passed or failed
                      based on the voting results.
                    </p>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-white mt-6">Proposal Lifecycle</h3>
                <p className="text-white/80">Proposals go through several stages before they are implemented:</p>
                <div className="space-y-4">
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Pending</h4>
                    <p className="text-white/80">
                      After a proposal is created, it enters a pending state where it is reviewed by the community. This
                      period allows for discussion and refinement of the proposal.
                    </p>
                  </div>
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Active</h4>
                    <p className="text-white/80">
                      Once the review period is over, the proposal becomes active and voting begins. During this time,
                      eligible voters can cast their votes.
                    </p>
                  </div>
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Passed/Failed</h4>
                    <p className="text-white/80">
                      After the voting period ends, the proposal is either passed or failed based on the voting results.
                      A proposal is passed if it receives more votes in favor than against and meets the quorum
                      requirement.
                    </p>
                  </div>
                  <div className="rounded-lg bg-white/5 p-4 border border-white/10">
                    <h4 className="font-medium text-white mb-2">Implementation</h4>
                    <p className="text-white/80">
                      If a proposal is passed, it is implemented by the development team according to the timeline
                      specified in the proposal.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
