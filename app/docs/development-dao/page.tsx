"use client"

import { DevelopmentDAODocumentation } from "@/components/development-dao-documentation"
import { ShimmerText } from "@/components/shimmer-text"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronRight, GitPullRequest, Lock, BarChart2, Workflow, Shield } from "lucide-react"
import Link from "next/link"

export default function DevelopmentDAODocsPage() {
  const relatedLinks = [
    {
      title: "Governance Proposals",
      description: "View and vote on active governance proposals",
      icon: <GitPullRequest className="h-5 w-5" />,
      href: "/governance",
    },
    {
      title: "Staking",
      description: "Stake tokens to increase your voting power",
      icon: <Lock className="h-5 w-5" />,
      href: "/governance/staking",
    },
    {
      title: "Analytics",
      description: "View governance participation metrics",
      icon: <BarChart2 className="h-5 w-5" />,
      href: "/governance/analytics",
    },
    {
      title: "Development Tracker",
      description: "Track development progress",
      icon: <Workflow className="h-5 w-5" />,
      href: "/development-tracker",
    },
    {
      title: "Development Staking",
      description: "Stake tokens to influence development",
      icon: <Shield className="h-5 w-5" />,
      href: "/development-staking",
    },
  ]

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Development DAO Documentation</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Comprehensive guide to the development governance system</p>
      </div>

      <DevelopmentDAODocumentation />

      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6">Related Governance Features</h2>
        <div className="grid gap-6 md:grid-cols-3">
          {relatedLinks.map((link) => (
            <Card
              key={link.href}
              className="glass-card border-white/5 hover:border-doge/30 hover:bg-black/40 transition-all"
            >
              <CardHeader>
                <div className="flex items-center gap-2">
                  <div className="rounded-full bg-doge/10 p-2 text-doge">{link.icon}</div>
                  <CardTitle className="text-white">{link.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-white/70">{link.description}</CardDescription>
              </CardContent>
              <CardFooter>
                <Link href={link.href} className="w-full">
                  <Button
                    variant="outline"
                    className="w-full justify-between border-white/10 bg-white/5 text-white hover:bg-white/10"
                  >
                    Go to {link.title}
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
