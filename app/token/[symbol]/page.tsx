"use client"

import { TokenDetail } from "@/components/token-detail"
import { ShimmerText } from "@/components/shimmer-text"
import { use } from "react"

export default function TokenDetailPage({ params }: { params: Promise<{ symbol: string }> }) {
  const { symbol } = use(params)

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Token Details</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Detailed information about {symbol}</p>
      </div>

      <TokenDetail symbol={symbol} />
    </div>
  )
}
