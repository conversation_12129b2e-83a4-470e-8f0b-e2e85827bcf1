"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { PerformanceMonitor } from "@/components/performance-monitor"
import { PerformanceDashboard } from "@/components/performance-dashboard"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertTriangle, Activity, BarChart2, Cpu, Database, Globe, Server } from "lucide-react"
import { useWallet } from "@/components/wallet-provider"
import { useState, useEffect } from "react"

export default function PerformancePage() {
  const { isConnected, address } = useWallet()
  const [isAdmin, setIsAdmin] = useState(false)

  useEffect(() => {
    // Check if connected wallet is an admin
    if (isConnected && address) {
      // For demo purposes, we'll just set isAdmin to true
      setIsAdmin(true)
    } else {
      setIsAdmin(false)
    }
  }, [isConnected, address])

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Performance Monitoring</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Monitor application performance metrics and system health</p>
      </div>

      {!isConnected || !isAdmin ? (
        <Alert className="mt-8 glass-card border-yellow-500/20 bg-yellow-500/5">
          <AlertTriangle className="h-5 w-5 text-yellow-500" />
          <AlertTitle className="text-white">Admin Access Required</AlertTitle>
          <AlertDescription className="text-white/70">
            Please connect with an admin wallet to access performance monitoring tools.
          </AlertDescription>
        </Alert>
      ) : (
        <Tabs defaultValue="dashboard" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-black/20 border border-white/10">
            <TabsTrigger value="dashboard" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
              <BarChart2 className="mr-2 h-4 w-4" />
              Dashboard
            </TabsTrigger>
            <TabsTrigger value="realtime" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
              <Activity className="mr-2 h-4 w-4" />
              Real-time
            </TabsTrigger>
            <TabsTrigger value="system" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
              <Cpu className="mr-2 h-4 w-4" />
              System
            </TabsTrigger>
            <TabsTrigger value="network" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
              <Globe className="mr-2 h-4 w-4" />
              Network
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard">
            <div className="grid gap-6 md:grid-cols-2">
              <PerformanceDashboard />
              <div className="space-y-6">
                <PerformanceMonitor />
                <Alert className="glass-card border-white/5 bg-black/20">
                  <Server className="h-5 w-5 text-doge" />
                  <AlertTitle className="text-white">System Status: Operational</AlertTitle>
                  <AlertDescription className="text-white/70">
                    All systems are running normally. Last checked: {new Date().toLocaleString()}
                  </AlertDescription>
                </Alert>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="realtime">
            <div className="grid gap-6 md:grid-cols-2">
              <PerformanceMonitor />
              <Alert className="glass-card border-white/5 bg-black/20">
                <Activity className="h-5 w-5 text-doge" />
                <AlertTitle className="text-white">Real-time Monitoring</AlertTitle>
                <AlertDescription className="text-white/70">
                  Real-time performance metrics are being collected. The data refreshes automatically every 5 seconds.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>

          <TabsContent value="system">
            <div className="grid gap-6 md:grid-cols-2">
              <PerformanceDashboard />
              <Alert className="glass-card border-white/5 bg-black/20">
                <Cpu className="h-5 w-5 text-doge" />
                <AlertTitle className="text-white">System Resources</AlertTitle>
                <AlertDescription className="text-white/70">
                  Monitoring CPU, memory, and disk usage across all application servers.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>

          <TabsContent value="network">
            <div className="grid gap-6 md:grid-cols-2">
              <PerformanceDashboard />
              <Alert className="glass-card border-white/5 bg-black/20">
                <Database className="h-5 w-5 text-doge" />
                <AlertTitle className="text-white">Network & Database</AlertTitle>
                <AlertDescription className="text-white/70">
                  Monitoring network latency, request throughput, and database performance.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}
