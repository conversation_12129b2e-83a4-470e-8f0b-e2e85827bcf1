"use client"

import { <PERSON><PERSON>Text } from "@/components/shimmer-text"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useState } from "react"

// Mock data for staking pools
const mockStakingPools = [
  {
    id: "pool-1",
    name: "Development DAO Staking",
    token: "PAWP",
    totalStaked: "1,250,000",
    apy: "12.5%",
    lockPeriod: "30 days",
    status: "active",
  },
  {
    id: "pool-2",
    name: "Governance Staking",
    token: "PAWP",
    totalStaked: "3,750,000",
    apy: "8.2%",
    lockPeriod: "None",
    status: "active",
  },
  {
    id: "pool-3",
    name: "Liquidity Provider Rewards",
    token: "LP-PAWP-DOGE",
    totalStaked: "850,000",
    apy: "18.7%",
    lockPeriod: "14 days",
    status: "active",
  },
  {
    id: "pool-4",
    name: "Early Adopter Bonus",
    token: "PAWP",
    totalStaked: "500,000",
    apy: "25.0%",
    lockPeriod: "90 days",
    status: "ended",
  },
]

// Mock data for stakers
const mockStakers = [
  {
    address: "0x1a2b...3c4d",
    pool: "Development DAO Staking",
    amountStaked: "125,000",
    rewards: "4,562",
    stakedSince: "2025-02-15",
    unlockDate: "2025-03-17",
  },
  {
    address: "0x5e6f...7g8h",
    pool: "Governance Staking",
    amountStaked: "350,000",
    rewards: "8,750",
    stakedSince: "2025-01-20",
    unlockDate: "N/A",
  },
  {
    address: "0x9i0j...1k2l",
    pool: "Liquidity Provider Rewards",
    amountStaked: "75,000",
    rewards: "3,281",
    stakedSince: "2025-03-01",
    unlockDate: "2025-03-15",
  },
  {
    address: "0x3m4n...5o6p",
    pool: "Early Adopter Bonus",
    amountStaked: "200,000",
    rewards: "12,500",
    stakedSince: "2024-12-10",
    unlockDate: "2025-03-10",
  },
]

export default function StakingAdmin() {
  const [activeTab, setActiveTab] = useState("pools")

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
            <ShimmerText>Staking Management</ShimmerText>
          </h1>
          <p className="text-lg text-white/70">Manage staking pools and monitor stakers</p>
        </div>
        <Button className="bg-doge text-black hover:bg-doge/90">Create New Pool</Button>
      </div>

      <Tabs defaultValue="pools" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-black/20 border border-white/10">
          <TabsTrigger value="pools" className="data-[state=active]:bg-white/10">
            Staking Pools
          </TabsTrigger>
          <TabsTrigger value="stakers" className="data-[state=active]:bg-white/10">
            Stakers
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pools" className="mt-6">
          <Card className="glass-card border-white/5 bg-black/20">
            <CardHeader>
              <CardTitle className="text-white">Active Staking Pools</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow className="border-white/10">
                    <TableHead className="text-white">Name</TableHead>
                    <TableHead className="text-white">Token</TableHead>
                    <TableHead className="text-white">Total Staked</TableHead>
                    <TableHead className="text-white">APY</TableHead>
                    <TableHead className="text-white">Lock Period</TableHead>
                    <TableHead className="text-white">Status</TableHead>
                    <TableHead className="text-white">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockStakingPools.map((pool) => (
                    <TableRow key={pool.id} className="border-white/10">
                      <TableCell className="text-white font-medium">{pool.name}</TableCell>
                      <TableCell className="text-white/80">{pool.token}</TableCell>
                      <TableCell className="text-white/80">{pool.totalStaked}</TableCell>
                      <TableCell className="text-white/80">{pool.apy}</TableCell>
                      <TableCell className="text-white/80">{pool.lockPeriod}</TableCell>
                      <TableCell>
                        <Badge
                          className={
                            pool.status === "active"
                              ? "bg-green-500/20 text-green-500 border-green-500/20"
                              : "bg-gray-500/20 text-gray-500 border-gray-500/20"
                          }
                        >
                          {pool.status.charAt(0).toUpperCase() + pool.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                          >
                            Edit
                          </Button>
                          {pool.status === "active" && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-red-500/20 bg-red-500/5 text-red-500 hover:bg-red-500/10"
                            >
                              Pause
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stakers" className="mt-6">
          <Card className="glass-card border-white/5 bg-black/20">
            <CardHeader>
              <CardTitle className="text-white">Active Stakers</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow className="border-white/10">
                    <TableHead className="text-white">Address</TableHead>
                    <TableHead className="text-white">Pool</TableHead>
                    <TableHead className="text-white">Amount Staked</TableHead>
                    <TableHead className="text-white">Rewards</TableHead>
                    <TableHead className="text-white">Staked Since</TableHead>
                    <TableHead className="text-white">Unlock Date</TableHead>
                    <TableHead className="text-white">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockStakers.map((staker, index) => (
                    <TableRow key={index} className="border-white/10">
                      <TableCell className="text-white/80">{staker.address}</TableCell>
                      <TableCell className="text-white/80">{staker.pool}</TableCell>
                      <TableCell className="text-white/80">{staker.amountStaked}</TableCell>
                      <TableCell className="text-white/80">{staker.rewards}</TableCell>
                      <TableCell className="text-white/80">{staker.stakedSince}</TableCell>
                      <TableCell className="text-white/80">{staker.unlockDate}</TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
