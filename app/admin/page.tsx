"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowRight, Shield, Clock, CheckCircle } from "lucide-react"
import Link from "next/link"

export default function AdminRedirect() {
  const router = useRouter()
  const [countdown, setCountdown] = useState(3)
  const [redirected, setRedirected] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          setRedirected(true)
          router.push("/governance/admin/dashboard")
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [router])

  if (redirected) {
    return (
      <div className="container py-8 flex items-center justify-center min-h-[50vh]">
        <Card className="glass-card border-green-500/20 bg-green-500/5 max-w-md">
          <CardHeader className="text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
            <CardTitle className="text-green-400">Redirected Successfully</CardTitle>
            <CardDescription>Taking you to the admin dashboard...</CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-8 flex items-center justify-center min-h-[50vh]">
      <Card className="glass-card border-white/10 max-w-md">
        <CardHeader className="text-center">
          <Shield className="h-12 w-12 text-doge mx-auto mb-2" />
          <CardTitle className="text-white">Admin Dashboard Moved</CardTitle>
          <CardDescription>
            The admin dashboard is now part of the unified Governance section for better organization.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-center gap-2 text-white/70">
            <Clock className="h-4 w-4" />
            <span>Redirecting in {countdown} seconds...</span>
          </div>

          <div className="flex flex-col gap-2">
            <Link href="/governance/admin/dashboard">
              <Button className="w-full glass-button">
                Go to Admin Dashboard
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
            <Link href="/governance">
              <Button variant="outline" className="w-full border-white/10 bg-white/5 hover:bg-white/10">
                Browse Governance
              </Button>
            </Link>
          </div>

          <div className="text-xs text-white/50 text-center">
            <p>Future admin links will point directly to the new location.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
