import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"

export default function GovernanceAdminLoading() {
  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <Skeleton className="h-10 w-64 bg-white/10" />
          <Skeleton className="h-5 w-96 bg-white/10 mt-2" />
        </div>
        <Skeleton className="h-10 w-32 bg-white/10" />
      </div>

      <Skeleton className="h-10 w-full bg-white/10 mb-6" />

      <Card className="glass-card border-white/5 bg-black/20">
        <CardHeader>
          <Skeleton className="h-7 w-64 bg-white/10" />
          <Skeleton className="h-5 w-96 bg-white/10 mt-1" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full bg-white/10" />
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-16 w-full bg-white/10" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
