"use client"

import { useState } from "react"
import dynamic from "next/dynamic"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { ArrowLeft, BarChart3, MessageSquare, ShieldAlert, Vote, Loader2 } from "lucide-react"

// Dynamic imports for heavy admin components - only load when needed
const AdminProposalManagement = dynamic(
  () => import("@/components/admin/proposal-management").then(mod => ({ default: mod.AdminProposalManagement })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-64 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-5 w-5 animate-spin" />
          Loading Proposal Management...
        </div>
      </div>
    ),
    ssr: false,
  }
)

const AdminCommentModeration = dynamic(
  () => import("@/components/admin/comment-moderation").then(mod => ({ default: mod.AdminCommentModeration })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-64 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-5 w-5 animate-spin" />
          Loading Comment Moderation...
        </div>
      </div>
    ),
    ssr: false,
  }
)

const AdminGovernanceAnalytics = dynamic(
  () => import("@/components/admin/governance-analytics").then(mod => ({ default: mod.AdminGovernanceAnalytics })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-64 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-5 w-5 animate-spin" />
          Loading Analytics...
        </div>
      </div>
    ),
    ssr: false,
  }
)

const GovernanceEmergencyControls = dynamic(
  () => import("@/components/admin/governance-emergency-controls").then(mod => ({ default: mod.GovernanceEmergencyControls })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-64 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-5 w-5 animate-spin" />
          Loading Emergency Controls...
        </div>
      </div>
    ),
    ssr: false,
  }
)

export default function GovernanceAdminPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("proposals")

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white">Governance Administration</h1>
          <p className="text-white/70">Manage proposals, comments, analytics, and emergency controls</p>
        </div>
        <Button
          variant="outline"
          className="border-white/10 bg-white/5 text-white hover:bg-white/10"
          onClick={() => router.push("/admin")}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Admin
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 glass">
          <TabsTrigger value="proposals" className="flex items-center gap-2">
            <Vote className="h-4 w-4" />
            <span>Proposals</span>
          </TabsTrigger>
          <TabsTrigger value="comments" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <span>Comments</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="emergency" className="flex items-center gap-2">
            <ShieldAlert className="h-4 w-4" />
            <span>Emergency</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="proposals" className="space-y-6">
          <AdminProposalManagement />
        </TabsContent>

        <TabsContent value="comments" className="space-y-6">
          <AdminCommentModeration />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <AdminGovernanceAnalytics />
        </TabsContent>

        <TabsContent value="emergency" className="space-y-6">
          <GovernanceEmergencyControls />
        </TabsContent>
      </Tabs>
    </div>
  )
}
