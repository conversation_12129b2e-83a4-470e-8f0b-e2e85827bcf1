"use client"

import { DevelopmentDAOAdmin } from "@/components/admin/development-dao-admin"
import { ShimmerText } from "@/components/shimmer-text"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertTriangle } from "lucide-react"
import { useEffect, useState } from "react"
import { ErrorBoundary } from "@/components/error-boundary"
import { LoadingState } from "@/components/ui/loading-state"

// In a real application, this would be a proper auth check
const checkAdminAccess = async () => {
  // Simulate API call to check admin access
  await new Promise((resolve) => setTimeout(resolve, 500))
  return true // For demo purposes, always return true
}

export default function DevelopmentDAOAdminPage() {
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const verifyAccess = async () => {
      try {
        const hasAccess = await checkAdminAccess()
        setIsAdmin(hasAccess)
      } catch (error) {
        console.error("Error verifying admin access:", error)
        setIsAdmin(false)
      } finally {
        setIsLoading(false)
      }
    }

    verifyAccess()
  }, [])

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Governance & Development Admin</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Comprehensive platform administration and management</p>
      </div>

      {isLoading ? (
        <LoadingState title="Verifying admin access..." />
      ) : isAdmin ? (
        <ErrorBoundary>
          <DevelopmentDAOAdmin />
        </ErrorBoundary>
      ) : (
        <Alert className="glass-card border-red-500/20 bg-red-500/5">
          <AlertTriangle className="h-5 w-5 text-red-500" />
          <AlertTitle className="text-white">Access Denied</AlertTitle>
          <AlertDescription className="text-white/70">
            You do not have permission to access the administration panel.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
