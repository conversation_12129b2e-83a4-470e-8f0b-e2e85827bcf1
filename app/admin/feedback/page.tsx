"use client"

import { useState, useEffect } from "react"
import { ShimmerText } from "@/components/shimmer-text"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Download, Trash2, MessageSquare, AlertTriangle, CheckCircle, Clock } from "lucide-react"
import {
  getAllFeedback,
  updateFeedbackStatus,
  deleteFeedback,
  exportFeedbackAsCsv,
  type Feedback,
} from "@/utils/feedback-storage"

export default function FeedbackAdminPage() {
  const [feedback, setFeedback] = useState<Feedback[]>([])
  // Status filtering removed for now
  const [activeTab, setActiveTab] = useState("all")

  useEffect(() => {
    // Load feedback from localStorage
    const data = getAllFeedback()
    setFeedback(data)

    // Add event listener to refresh data when localStorage changes
    const handleStorageChange = () => {
      const updatedData = getAllFeedback()
      setFeedback(updatedData)
    }

    window.addEventListener("storage", handleStorageChange)

    return () => {
      window.removeEventListener("storage", handleStorageChange)
    }
  }, [])

  // Filter feedback by status
  const filteredFeedback = activeTab === "all" ? feedback : feedback.filter((item) => item.type === activeTab)

  // Get counts for each type
  const bugCount = feedback.filter((item) => item.type === "bug").length
  const featureCount = feedback.filter((item) => item.type === "feature").length
  const uxCount = feedback.filter((item) => item.type === "ux").length
  // otherCount removed as unused

  // Handle status change
  const handleStatusChange = (id: string, status: Feedback["status"]) => {
    updateFeedbackStatus(id, status)
    setFeedback((prev) =>
      prev.map((item) => {
        if (item.id === id) {
          return { ...item, status, updatedAt: new Date() }
        }
        return item
      }),
    )
  }

  // Handle delete
  const handleDelete = (id: string) => {
    if (confirm("Are you sure you want to delete this feedback?")) {
      deleteFeedback(id)
      setFeedback((prev) => prev.filter((item) => item.id !== id))
    }
  }

  // Handle export
  const handleExport = () => {
    const csv = exportFeedbackAsCsv()

    // Create a blob and download link
    const blob = new Blob([csv], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `pawpumps-feedback-${new Date().toISOString().split("T")[0]}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "new":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case "reviewing":
        return <Clock className="h-4 w-4 text-blue-500" />
      case "resolved":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "closed":
        return <CheckCircle className="h-4 w-4 text-gray-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    }
  }

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>User Feedback</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Manage and respond to user feedback and suggestions</p>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <MessageSquare className="h-5 w-5 text-doge" />
          <span className="text-white font-medium">Total Feedback: {feedback.length}</span>
        </div>

        <Button onClick={handleExport} className="glass-button">
          <Download className="mr-2 h-4 w-4" />
          Export CSV
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-white">All</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{feedback.length}</div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-white">Bugs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">{bugCount}</div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-white">Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-500">{featureCount}</div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-white">UX</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-500">{uxCount}</div>
          </CardContent>
        </Card>
      </div>

      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Feedback Management</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-5 mb-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="bug">Bugs</TabsTrigger>
              <TabsTrigger value="feature">Features</TabsTrigger>
              <TabsTrigger value="ux">UX</TabsTrigger>
              <TabsTrigger value="other">Other</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab}>
              {filteredFeedback.length === 0 ? (
                <div className="text-center py-12 bg-white/5 rounded-lg">
                  <p className="text-white/50">No feedback found</p>
                </div>
              ) : (
                <div className="rounded-md border border-white/10 overflow-hidden overflow-x-auto">
                  <Table>
                    <TableHeader className="bg-white/5">
                      <TableRow className="hover:bg-transparent border-white/10">
                        <TableHead className="text-white/70">Type</TableHead>
                        <TableHead className="text-white/70">Message</TableHead>
                        <TableHead className="text-white/70">Email</TableHead>
                        <TableHead className="text-white/70">Status</TableHead>
                        <TableHead className="text-white/70">Date</TableHead>
                        <TableHead className="text-white/70">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredFeedback.map((item) => (
                        <TableRow key={item.id} className="hover:bg-white/5 border-white/10">
                          <TableCell>
                            <Badge
                              className={
                                item.type === "bug"
                                  ? "bg-red-500/20 text-red-500 hover:bg-red-500/30"
                                  : item.type === "feature"
                                    ? "bg-blue-500/20 text-blue-500 hover:bg-blue-500/30"
                                    : item.type === "ux"
                                      ? "bg-purple-500/20 text-purple-500 hover:bg-purple-500/30"
                                      : "bg-gray-500/20 text-gray-500 hover:bg-gray-500/30"
                              }
                            >
                              {item.type}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-medium text-white max-w-xs truncate">{item.message}</TableCell>
                          <TableCell className="text-white/60">{item.email || "-"}</TableCell>
                          <TableCell>
                            <Select
                              value={item.status}
                              onValueChange={(value) => handleStatusChange(item.id, value as Feedback["status"])}
                            >
                              <SelectTrigger className="w-[130px] bg-white/5 border-white/10">
                                <SelectValue placeholder="Status">
                                  <div className="flex items-center gap-2">
                                    {getStatusIcon(item.status)}
                                    <span className="capitalize">{item.status}</span>
                                  </div>
                                </SelectValue>
                              </SelectTrigger>
                              <SelectContent className="bg-black border-white/10">
                                <SelectItem value="new">New</SelectItem>
                                <SelectItem value="reviewing">Reviewing</SelectItem>
                                <SelectItem value="resolved">Resolved</SelectItem>
                                <SelectItem value="closed">Closed</SelectItem>
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell className="text-white/60">{new Date(item.createdAt).toLocaleString()}</TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDelete(item.id)}
                              className="h-8 w-8 text-red-500 hover:text-red-400 hover:bg-red-500/10"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
