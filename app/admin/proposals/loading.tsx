import { ShimmerText } from "@/components/shimmer-text"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, MessageSquare, Settings, Shield } from "lucide-react"

export default function ProposalsAdminLoading() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Governance Administration</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Manage and monitor governance proposals and settings</p>
      </div>

      <Tabs defaultValue="proposals" className="space-y-6">
        <TabsList className="bg-black/20 border border-white/10">
          <TabsTrigger value="proposals" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <Settings className="mr-2 h-4 w-4" />
            Proposals
          </TabsTrigger>
          <TabsTrigger value="comments" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <MessageSquare className="mr-2 h-4 w-4" />
            Comments
          </TabsTrigger>
          <TabsTrigger value="analytics" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <BarChart className="mr-2 h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="emergency" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <Shield className="mr-2 h-4 w-4" />
            Emergency Controls
          </TabsTrigger>
        </TabsList>

        <TabsContent value="proposals">
          <Card className="glass-card border-white/5 bg-black/20">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-white">Governance Proposals</CardTitle>
                <div className="h-4 w-48 mt-1">
                  <Skeleton className="h-full w-full bg-white/10" />
                </div>
              </div>
              <div className="h-10 w-32">
                <Skeleton className="h-full w-full bg-white/10" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center gap-4">
                    <Skeleton className="h-12 w-full bg-white/10" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
