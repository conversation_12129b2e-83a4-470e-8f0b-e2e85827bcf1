import type { ReactNode } from "react"
import type { Metadata } from 'next'
import { AdminAuthProvider } from "@/contexts/admin-auth-context"
import { AdminOnly } from "@/components/admin/access-control"
import { UnifiedAdminNav } from "@/components/admin/unified-admin-nav"

export const metadata: Metadata = {
  title: "Admin Dashboard | PawPumps - Platform Management",
  description: "Access PawPumps admin dashboard for platform management, user administration, and system controls.",
  keywords: ['admin', 'dashboard', 'management', 'platform', 'administration', 'controls'],
}

export default function AdminLayout({ children }: { children: ReactNode }) {
  return (
    <AdminAuthProvider>
      <AdminOnly>
        <div className="flex min-h-screen">
          <div className="hidden md:flex w-64 flex-col border-r border-white/10 bg-black/20 p-4">
            <div className="mb-8">
              <h1 className="text-xl font-bold text-white">PawPumps Admin</h1>
              <p className="text-sm text-white/70">Platform Management</p>
            </div>
            <UnifiedAdminNav />
          </div>
          <div className="flex-1 overflow-auto">{children}</div>
        </div>
      </AdminOnly>
    </AdminAuthProvider>
  )
}
