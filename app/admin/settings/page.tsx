"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import { AlertCircle, Lock, RefreshCw, Save, Settings, Shield, Sliders, WalletCards } from "lucide-react"

export default function SystemSettingsPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>System Settings</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Configure global platform settings and parameters</p>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="bg-black/20">
          <TabsTrigger value="general" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <Settings className="mr-2 h-4 w-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="trading" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <Sliders className="mr-2 h-4 w-4" />
            Trading
          </TabsTrigger>
          <TabsTrigger value="security" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <Shield className="mr-2 h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="fees" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <WalletCards className="mr-2 h-4 w-4" />
            Fees & Limits
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card className="border-white/5 bg-black/20">
            <CardHeader>
              <CardTitle className="text-lg text-white">Platform Settings</CardTitle>
              <CardDescription className="text-white/70">
                Configure general platform settings and appearance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="platform-name" className="text-white">
                  Platform Name
                </Label>
                <Input id="platform-name" defaultValue="PawPumps" className="border-white/20 bg-black/20 text-white" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="platform-url" className="text-white">
                  Platform URL
                </Label>
                <Input
                  id="platform-url"
                  defaultValue="https://pawpumps.io"
                  className="border-white/20 bg-black/20 text-white"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contact-email" className="text-white">
                  Contact Email
                </Label>
                <Input
                  id="contact-email"
                  defaultValue="<EMAIL>"
                  className="border-white/20 bg-black/20 text-white"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="support-email" className="text-white">
                  Support Email
                </Label>
                <Input
                  id="support-email"
                  defaultValue="<EMAIL>"
                  className="border-white/20 bg-black/20 text-white"
                />
              </div>

              <Separator className="my-4 bg-white/10" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Maintenance Mode</Label>
                  <p className="text-sm text-white/70">Temporarily disable the platform for maintenance</p>
                </div>
                <Switch />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Enable Analytics</Label>
                  <p className="text-sm text-white/70">Collect anonymous usage data to improve the platform</p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Public Roadmap</Label>
                  <p className="text-sm text-white/70">Make the development roadmap publicly visible</p>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
            <CardFooter className="border-t border-white/10 pt-4">
              <Button className="bg-doge text-black hover:bg-doge/90">
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="trading">
          <Card className="border-white/5 bg-black/20">
            <CardHeader>
              <CardTitle className="text-lg text-white">Trading Settings</CardTitle>
              <CardDescription className="text-white/70">
                Configure trading parameters and bonding curves
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="default-slippage" className="text-white">
                  Default Slippage Tolerance (%)
                </Label>
                <Input
                  id="default-slippage"
                  type="number"
                  defaultValue="0.5"
                  min="0.1"
                  max="5"
                  step="0.1"
                  className="border-white/20 bg-black/20 text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="default-bonding-curve" className="text-white">
                  Default Bonding Curve Function
                </Label>
                <Input
                  id="default-bonding-curve"
                  defaultValue="y = x^2"
                  className="border-white/20 bg-black/20 text-white"
                />
                <p className="text-sm text-white/70">Bonding curve formula used for new token launches</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="min-liquidity" className="text-white">
                  Minimum Initial Liquidity (PAWP)
                </Label>
                <Input
                  id="min-liquidity"
                  type="number"
                  defaultValue="10000"
                  className="border-white/20 bg-black/20 text-white"
                />
              </div>

              <Separator className="my-4 bg-white/10" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Enable New Token Launches</Label>
                  <p className="text-sm text-white/70">Allow users to launch new tokens on the platform</p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Trading Volume Alerts</Label>
                  <p className="text-sm text-white/70">Send alerts for unusual trading volume</p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Price Movement Alerts</Label>
                  <p className="text-sm text-white/70">Send alerts for significant price movements</p>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
            <CardFooter className="border-t border-white/10 pt-4">
              <Button className="bg-doge text-black hover:bg-doge/90">
                <Save className="mr-2 h-4 w-4" />
                Save Trading Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card className="border-white/5 bg-black/20">
            <CardHeader>
              <CardTitle className="text-lg text-white">Security Settings</CardTitle>
              <CardDescription className="text-white/70">
                Configure platform security and access controls
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="rounded-md border border-yellow-500/20 bg-yellow-500/5 p-4">
                <div className="flex items-start gap-3">
                  <AlertCircle className="mt-1 h-5 w-5 text-yellow-500" />
                  <div>
                    <h3 className="text-sm font-medium text-white">Security Notice</h3>
                    <p className="text-sm text-white/70">
                      Changing security settings can impact platform access. Please ensure you understand the
                      implications before making changes.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="admin-addresses" className="text-white">
                  Admin Wallet Addresses
                </Label>
                <Input
                  id="admin-addresses"
                  defaultValue="******************************************, ******************************************"
                  className="border-white/20 bg-black/20 text-white"
                />
                <p className="text-sm text-white/70">Comma-separated list of wallet addresses with admin access</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="emergency-pause" className="text-white">
                  Emergency Pause Threshold (%)
                </Label>
                <Input
                  id="emergency-pause"
                  type="number"
                  defaultValue="66"
                  min="51"
                  max="100"
                  className="border-white/20 bg-black/20 text-white"
                />
                <p className="text-sm text-white/70">Percentage of admins required to trigger an emergency pause</p>
              </div>

              <Separator className="my-4 bg-white/10" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Two-Factor Authentication</Label>
                  <p className="text-sm text-white/70">Require 2FA for administrative actions</p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Multi-signature Requirements</Label>
                  <p className="text-sm text-white/70">Require multiple admins to approve critical changes</p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Security Audit Mode</Label>
                  <p className="text-sm text-white/70">Log all administrative actions for audit purposes</p>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between border-t border-white/10 pt-4">
              <Button variant="outline" className="border-white/20 bg-black/20 text-white">
                <RefreshCw className="mr-2 h-4 w-4" />
                Reset to Defaults
              </Button>
              <Button className="bg-doge text-black hover:bg-doge/90">
                <Lock className="mr-2 h-4 w-4" />
                Update Security Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="fees">
          <Card className="border-white/5 bg-black/20">
            <CardHeader>
              <CardTitle className="text-lg text-white">Fees & Limits Settings</CardTitle>
              <CardDescription className="text-white/70">Configure transaction fees and trading limits</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="trading-fee" className="text-white">
                  Trading Fee (%)
                </Label>
                <Input
                  id="trading-fee"
                  type="number"
                  defaultValue="0.3"
                  min="0"
                  max="5"
                  step="0.1"
                  className="border-white/20 bg-black/20 text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="launch-fee" className="text-white">
                  Token Launch Fee (%)
                </Label>
                <Input
                  id="launch-fee"
                  type="number"
                  defaultValue="1.0"
                  min="0"
                  max="5"
                  step="0.1"
                  className="border-white/20 bg-black/20 text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dao-allocation" className="text-white">
                  Development DAO Allocation (%)
                </Label>
                <Input
                  id="dao-allocation"
                  type="number"
                  defaultValue="30"
                  min="0"
                  max="100"
                  className="border-white/20 bg-black/20 text-white"
                />
                <p className="text-sm text-white/70">Percentage of fees allocated to the Development DAO</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="max-transaction" className="text-white">
                  Maximum Transaction Size (DOGE)
                </Label>
                <Input
                  id="max-transaction"
                  type="number"
                  defaultValue="100000"
                  className="border-white/20 bg-black/20 text-white"
                />
              </div>

              <Separator className="my-4 bg-white/10" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Fee Discounts for PAWP Holders</Label>
                  <p className="text-sm text-white/70">Apply trading fee discounts for PAWP token holders</p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-white">Dynamic Fee Adjustment</Label>
                  <p className="text-sm text-white/70">Automatically adjust fees based on network conditions</p>
                </div>
                <Switch />
              </div>
            </CardContent>
            <CardFooter className="border-t border-white/10 pt-4">
              <Button className="bg-doge text-black hover:bg-doge/90">
                <Save className="mr-2 h-4 w-4" />
                Save Fee Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
