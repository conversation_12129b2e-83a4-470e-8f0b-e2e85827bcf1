"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { BarChart, BarChart2, Calendar, Download, LineChart, PieChart, Users, Wallet } from "lucide-react"

export default function AnalyticsAndReportingPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Analytics & Reporting</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">View detailed platform analytics and reports</p>
      </div>

      <div className="mb-6 flex flex-col sm:flex-row items-center gap-4">
        <div className="relative w-full sm:w-48">
          <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
          <Input
            type="date"
            placeholder="Start Date"
            className="pl-10 border-white/20 bg-black/20"
            defaultValue="2025-05-01"
          />
        </div>
        <div className="relative w-full sm:w-48">
          <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
          <Input
            type="date"
            placeholder="End Date"
            className="pl-10 border-white/20 bg-black/20"
            defaultValue="2025-05-07"
          />
        </div>
        <Button className="bg-doge text-black hover:bg-doge/90 w-full sm:w-auto">Apply Filter</Button>
        <Button variant="outline" className="border-white/20 bg-black/20 text-white w-full sm:w-auto">
          <Download className="mr-2 h-4 w-4" />
          Export Data
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="bg-black/20">
          <TabsTrigger value="overview" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <BarChart2 className="mr-2 h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="trading" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <LineChart className="mr-2 h-4 w-4" />
            Trading
          </TabsTrigger>
          <TabsTrigger value="users" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <Users className="mr-2 h-4 w-4" />
            Users
          </TabsTrigger>
          <TabsTrigger value="revenue" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
            <Wallet className="mr-2 h-4 w-4" />
            Revenue
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="border-white/5 bg-black/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white">Total Volume (7d)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">12.5M DOGE</div>
                <p className="text-xs text-green-500">↑ 15.3% from previous period</p>
              </CardContent>
            </Card>
            <Card className="border-white/5 bg-black/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white">Active Users (7d)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">4,281</div>
                <p className="text-xs text-green-500">↑ 8.7% from previous period</p>
              </CardContent>
            </Card>
            <Card className="border-white/5 bg-black/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white">New Tokens (7d)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">15</div>
                <p className="text-xs text-red-500">↓ 6.2% from previous period</p>
              </CardContent>
            </Card>
            <Card className="border-white/5 bg-black/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white">Revenue (7d)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">37,500 DOGE</div>
                <p className="text-xs text-green-500">↑ 12.8% from previous period</p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-4 grid gap-4 md:grid-cols-2">
            <Card className="border-white/5 bg-black/20">
              <CardHeader>
                <CardTitle className="text-lg text-white">Trading Volume Trend</CardTitle>
                <CardDescription className="text-white/70">Daily trading volume over the past week</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <div className="flex h-full items-center justify-center">
                  <BarChart className="h-16 w-16 text-white/30" />
                  <p className="ml-4 text-white/70">Chart visualization would go here</p>
                </div>
              </CardContent>
            </Card>
            <Card className="border-white/5 bg-black/20">
              <CardHeader>
                <CardTitle className="text-lg text-white">Top Trading Pairs</CardTitle>
                <CardDescription className="text-white/70">Most active trading pairs by volume</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80 flex items-center justify-center">
                  <PieChart className="h-16 w-16 text-white/30" />
                  <p className="ml-4 text-white/70">Chart visualization would go here</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trading">
          <Card className="border-white/5 bg-black/20">
            <CardHeader>
              <CardTitle className="text-lg text-white">Trading Activity</CardTitle>
              <CardDescription className="text-white/70">Detailed trading statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <Card className="border-white/10 bg-black/10">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-white">Total Trades</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">32,145</div>
                    <p className="text-xs text-green-500">↑ 11.2% from previous period</p>
                  </CardContent>
                </Card>
                <Card className="border-white/10 bg-black/10">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-white">Average Trade Size</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">389 DOGE</div>
                    <p className="text-xs text-green-500">↑ 3.7% from previous period</p>
                  </CardContent>
                </Card>
                <Card className="border-white/10 bg-black/10">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-white">Unique Traders</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">2,864</div>
                    <p className="text-xs text-green-500">↑ 5.9% from previous period</p>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-6">
                <h3 className="mb-4 text-lg font-medium text-white">Top Tokens by Volume</h3>
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableHead className="text-white">Token</TableHead>
                      <TableHead className="text-white">Symbol</TableHead>
                      <TableHead className="text-white">Volume (DOGE)</TableHead>
                      <TableHead className="text-white">Trades</TableHead>
                      <TableHead className="text-white">Price Change</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="font-medium text-white">DogeWow</TableCell>
                      <TableCell className="text-white">WOW</TableCell>
                      <TableCell className="text-white">2,450,000</TableCell>
                      <TableCell className="text-white">5,241</TableCell>
                      <TableCell className="text-green-500">+14.2%</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="font-medium text-white">Bonk</TableCell>
                      <TableCell className="text-white">BONK</TableCell>
                      <TableCell className="text-white">1,870,000</TableCell>
                      <TableCell className="text-white">4,125</TableCell>
                      <TableCell className="text-red-500">-3.8%</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="font-medium text-white">MoonShibe</TableCell>
                      <TableCell className="text-white">MSHIB</TableCell>
                      <TableCell className="text-white">1,520,000</TableCell>
                      <TableCell className="text-white">3,562</TableCell>
                      <TableCell className="text-green-500">****%</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="font-medium text-white">DogePump</TableCell>
                      <TableCell className="text-white">DPUMP</TableCell>
                      <TableCell className="text-white">980,000</TableCell>
                      <TableCell className="text-white">2,145</TableCell>
                      <TableCell className="text-green-500">+21.5%</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="font-medium text-white">CatDoge</TableCell>
                      <TableCell className="text-white">CATD</TableCell>
                      <TableCell className="text-white">750,000</TableCell>
                      <TableCell className="text-white">1,892</TableCell>
                      <TableCell className="text-red-500">-5.2%</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            <CardFooter className="justify-end border-t border-white/10 pt-4">
              <Button variant="outline" className="border-white/20 bg-black/20 text-white">
                <Download className="mr-2 h-4 w-4" />
                Export Trading Report
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card className="border-white/5 bg-black/20">
            <CardHeader>
              <CardTitle className="text-lg text-white">User Analytics</CardTitle>
              <CardDescription className="text-white/70">User activity and demographics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <Card className="border-white/10 bg-black/10">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-white">New Users</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">387</div>
                    <p className="text-xs text-green-500">↑ 15.8% from previous period</p>
                  </CardContent>
                </Card>
                <Card className="border-white/10 bg-black/10">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-white">Total Users</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">12,854</div>
                    <p className="text-xs text-green-500">↑ 3.1% from previous period</p>
                  </CardContent>
                </Card>
                <Card className="border-white/10 bg-black/10">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-white">Retention Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">68.4%</div>
                    <p className="text-xs text-green-500">↑ 2.5% from previous period</p>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-6 grid gap-4 md:grid-cols-2">
                <div>
                  <h3 className="mb-4 text-lg font-medium text-white">Geographic Distribution</h3>
                  <div className="h-72 flex items-center justify-center border border-white/10 rounded-md">
                    <BarChart className="h-16 w-16 text-white/30" />
                    <p className="ml-4 text-white/70">Geographic chart would go here</p>
                  </div>
                </div>
                <div>
                  <h3 className="mb-4 text-lg font-medium text-white">User Activities</h3>
                  <div className="h-72 flex items-center justify-center border border-white/10 rounded-md">
                    <PieChart className="h-16 w-16 text-white/30" />
                    <p className="ml-4 text-white/70">User activity chart would go here</p>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <h3 className="mb-4 text-lg font-medium text-white">Top Users by Volume</h3>
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableHead className="text-white">User Address</TableHead>
                      <TableHead className="text-white">Volume (DOGE)</TableHead>
                      <TableHead className="text-white">Trades</TableHead>
                      <TableHead className="text-white">First Active</TableHead>
                      <TableHead className="text-white">Last Active</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="font-mono text-sm text-white">0x1a2b...3c4d</TableCell>
                      <TableCell className="text-white">520,000</TableCell>
                      <TableCell className="text-white">243</TableCell>
                      <TableCell className="text-white">Apr 15, 2025</TableCell>
                      <TableCell className="text-white">May 7, 2025</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="font-mono text-sm text-white">0x4e5f...6g7h</TableCell>
                      <TableCell className="text-white">428,000</TableCell>
                      <TableCell className="text-white">197</TableCell>
                      <TableCell className="text-white">Mar 28, 2025</TableCell>
                      <TableCell className="text-white">May 6, 2025</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="font-mono text-sm text-white">0x8i9j...0k1l</TableCell>
                      <TableCell className="text-white">352,000</TableCell>
                      <TableCell className="text-white">176</TableCell>
                      <TableCell className="text-white">Apr 5, 2025</TableCell>
                      <TableCell className="text-white">May 7, 2025</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="font-mono text-sm text-white">0xm2n3...o4p5</TableCell>
                      <TableCell className="text-white">298,000</TableCell>
                      <TableCell className="text-white">142</TableCell>
                      <TableCell className="text-white">Apr 12, 2025</TableCell>
                      <TableCell className="text-white">May 5, 2025</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="font-mono text-sm text-white">0xq6r7...s8t9</TableCell>
                      <TableCell className="text-white">265,000</TableCell>
                      <TableCell className="text-white">128</TableCell>
                      <TableCell className="text-white">Apr 8, 2025</TableCell>
                      <TableCell className="text-white">May 7, 2025</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            <CardFooter className="justify-end border-t border-white/10 pt-4">
              <Button variant="outline" className="border-white/20 bg-black/20 text-white">
                <Download className="mr-2 h-4 w-4" />
                Export User Report
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="revenue">
          <Card className="border-white/5 bg-black/20">
            <CardHeader>
              <CardTitle className="text-lg text-white">Revenue Analytics</CardTitle>
              <CardDescription className="text-white/70">Fee collection and distribution</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <Card className="border-white/10 bg-black/10">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-white">Total Fees Collected</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">37,500 DOGE</div>
                    <p className="text-xs text-green-500">↑ 12.8% from previous period</p>
                  </CardContent>
                </Card>
                <Card className="border-white/10 bg-black/10">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-white">Development DAO Allocation</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">11,250 DOGE</div>
                    <p className="text-xs text-white/60">30% of total fees</p>
                  </CardContent>
                </Card>
                <Card className="border-white/10 bg-black/10">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-white">Protocol Revenue</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">26,250 DOGE</div>
                    <p className="text-xs text-white/60">70% of total fees</p>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-6">
                <h3 className="mb-4 text-lg font-medium text-white">Revenue by Source</h3>
                <div className="h-72 flex items-center justify-center border border-white/10 rounded-md">
                  <PieChart className="h-16 w-16 text-white/30" />
                  <p className="ml-4 text-white/70">Revenue breakdown chart would go here</p>
                </div>
              </div>

              <div className="mt-6">
                <h3 className="mb-4 text-lg font-medium text-white">Daily Revenue</h3>
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableHead className="text-white">Date</TableHead>
                      <TableHead className="text-white">Trading Fees</TableHead>
                      <TableHead className="text-white">Launch Fees</TableHead>
                      <TableHead className="text-white">Other</TableHead>
                      <TableHead className="text-white">Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="text-white">May 7, 2025</TableCell>
                      <TableCell className="text-white">5,200</TableCell>
                      <TableCell className="text-white">1,500</TableCell>
                      <TableCell className="text-white">300</TableCell>
                      <TableCell className="text-white">7,000</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="text-white">May 6, 2025</TableCell>
                      <TableCell className="text-white">4,800</TableCell>
                      <TableCell className="text-white">1,200</TableCell>
                      <TableCell className="text-white">250</TableCell>
                      <TableCell className="text-white">6,250</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="text-white">May 5, 2025</TableCell>
                      <TableCell className="text-white">5,100</TableCell>
                      <TableCell className="text-white">800</TableCell>
                      <TableCell className="text-white">350</TableCell>
                      <TableCell className="text-white">6,250</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="text-white">May 4, 2025</TableCell>
                      <TableCell className="text-white">4,900</TableCell>
                      <TableCell className="text-white">1,000</TableCell>
                      <TableCell className="text-white">200</TableCell>
                      <TableCell className="text-white">6,100</TableCell>
                    </TableRow>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableCell className="text-white">May 3, 2025</TableCell>
                      <TableCell className="text-white">5,300</TableCell>
                      <TableCell className="text-white">1,300</TableCell>
                      <TableCell className="text-white">300</TableCell>
                      <TableCell className="text-white">6,900</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            <CardFooter className="justify-end border-t border-white/10 pt-4">
              <Button variant="outline" className="border-white/20 bg-black/20 text-white">
                <Download className="mr-2 h-4 w-4" />
                Export Revenue Report
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
