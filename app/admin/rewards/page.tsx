"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useState } from "react"

// Mock data for reward programs
const mockRewardPrograms = [
  {
    id: "reward-1",
    name: "Trading Volume Rewards",
    description: "Rewards based on weekly trading volume",
    totalBudget: "500,000 PAWP",
    distributed: "125,000 PAWP",
    remaining: "375,000 PAWP",
    status: "active",
    frequency: "Weekly",
  },
  {
    id: "reward-2",
    name: "Liquidity Provider Incentives",
    description: "Rewards for providing liquidity to pools",
    totalBudget: "1,000,000 PAWP",
    distributed: "450,000 PAWP",
    remaining: "550,000 PAWP",
    status: "active",
    frequency: "Daily",
  },
  {
    id: "reward-3",
    name: "Governance Participation",
    description: "Rewards for voting on proposals",
    totalBudget: "250,000 PAWP",
    distributed: "75,000 PAWP",
    remaining: "175,000 PAWP",
    status: "active",
    frequency: "Per proposal",
  },
  {
    id: "reward-4",
    name: "Launch Participation",
    description: "Rewards for participating in token launches",
    totalBudget: "300,000 PAWP",
    distributed: "120,000 PAWP",
    remaining: "180,000 PAWP",
    status: "paused",
    frequency: "Per launch",
  },
]

// Mock data for recent reward distributions
const mockDistributions = [
  {
    id: "dist-1",
    program: "Trading Volume Rewards",
    date: "2025-04-07",
    amount: "25,000 PAWP",
    recipients: 128,
    status: "completed",
  },
  {
    id: "dist-2",
    program: "Liquidity Provider Incentives",
    date: "2025-04-10",
    amount: "15,000 PAWP",
    recipients: 75,
    status: "completed",
  },
  {
    id: "dist-3",
    program: "Governance Participation",
    date: "2025-04-05",
    amount: "10,000 PAWP",
    recipients: 42,
    status: "completed",
  },
  {
    id: "dist-4",
    program: "Trading Volume Rewards",
    date: "2025-04-14",
    amount: "25,000 PAWP",
    recipients: 0,
    status: "pending",
  },
]

export default function RewardsAdmin() {
  const [activeTab, setActiveTab] = useState("programs")

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
            <ShimmerText>Rewards Management</ShimmerText>
          </h1>
          <p className="text-lg text-white/70">Manage reward programs and distributions</p>
        </div>
        <Button className="bg-doge text-black hover:bg-doge/90">Create New Program</Button>
      </div>

      <Tabs defaultValue="programs" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-black/20 border border-white/10">
          <TabsTrigger value="programs" className="data-[state=active]:bg-white/10">
            Reward Programs
          </TabsTrigger>
          <TabsTrigger value="distributions" className="data-[state=active]:bg-white/10">
            Recent Distributions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="programs" className="mt-6">
          <Card className="glass-card border-white/5 bg-black/20">
            <CardHeader>
              <CardTitle className="text-white">Active Reward Programs</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow className="border-white/10">
                    <TableHead className="text-white">Name</TableHead>
                    <TableHead className="text-white">Description</TableHead>
                    <TableHead className="text-white">Budget</TableHead>
                    <TableHead className="text-white">Distributed</TableHead>
                    <TableHead className="text-white">Remaining</TableHead>
                    <TableHead className="text-white">Frequency</TableHead>
                    <TableHead className="text-white">Status</TableHead>
                    <TableHead className="text-white">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockRewardPrograms.map((program) => (
                    <TableRow key={program.id} className="border-white/10">
                      <TableCell className="text-white font-medium">{program.name}</TableCell>
                      <TableCell className="text-white/80">{program.description}</TableCell>
                      <TableCell className="text-white/80">{program.totalBudget}</TableCell>
                      <TableCell className="text-white/80">{program.distributed}</TableCell>
                      <TableCell className="text-white/80">{program.remaining}</TableCell>
                      <TableCell className="text-white/80">{program.frequency}</TableCell>
                      <TableCell>
                        <Badge
                          className={
                            program.status === "active"
                              ? "bg-green-500/20 text-green-500 border-green-500/20"
                              : "bg-yellow-500/20 text-yellow-500 border-yellow-500/20"
                          }
                        >
                          {program.status.charAt(0).toUpperCase() + program.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                          >
                            Edit
                          </Button>
                          {program.status === "active" ? (
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-yellow-500/20 bg-yellow-500/5 text-yellow-500 hover:bg-yellow-500/10"
                            >
                              Pause
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-green-500/20 bg-green-500/5 text-green-500 hover:bg-green-500/10"
                            >
                              Activate
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distributions" className="mt-6">
          <Card className="glass-card border-white/5 bg-black/20">
            <CardHeader>
              <CardTitle className="text-white">Recent Reward Distributions</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow className="border-white/10">
                    <TableHead className="text-white">Program</TableHead>
                    <TableHead className="text-white">Date</TableHead>
                    <TableHead className="text-white">Amount</TableHead>
                    <TableHead className="text-white">Recipients</TableHead>
                    <TableHead className="text-white">Status</TableHead>
                    <TableHead className="text-white">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockDistributions.map((dist) => (
                    <TableRow key={dist.id} className="border-white/10">
                      <TableCell className="text-white font-medium">{dist.program}</TableCell>
                      <TableCell className="text-white/80">{dist.date}</TableCell>
                      <TableCell className="text-white/80">{dist.amount}</TableCell>
                      <TableCell className="text-white/80">{dist.recipients}</TableCell>
                      <TableCell>
                        <Badge
                          className={
                            dist.status === "completed"
                              ? "bg-green-500/20 text-green-500 border-green-500/20"
                              : "bg-blue-500/20 text-blue-500 border-blue-500/20"
                          }
                        >
                          {dist.status.charAt(0).toUpperCase() + dist.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
