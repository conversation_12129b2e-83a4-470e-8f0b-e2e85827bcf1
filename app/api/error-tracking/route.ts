import { NextRequest, NextResponse } from 'next/server'
import { error<PERSON><PERSON><PERSON>, ErrorSeverity, ErrorCategory } from '@/lib/error-handler'

interface ErrorReport {
  message: string
  stack?: string
  context?: Record<string, unknown>
  timestamp: string
  severity?: ErrorSeverity
  category?: ErrorCategory
}

export async function POST(request: NextRequest) {
  try {
    const errorReport: ErrorReport = await request.json()

    // Validate error report
    if (!errorReport.message || !errorReport.timestamp) {
      return NextResponse.json(
        { error: 'Invalid error report format' },
        { status: 400 }
      )
    }

    // Create structured error report
    const structuredError = {
      id: generateErrorId(),
      timestamp: errorReport.timestamp,
      message: errorReport.message,
      stack: errorReport.stack,
      context: errorReport.context,
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || 'unknown',
      userAgent: request.headers.get('user-agent'),
      ip: getClient<PERSON>(request),
      url: errorReport.context?.url,
    }

    // Use our error handler to track the error
    const appError = errorHandler.handle(
      errorReport.message,
      errorReport.severity || ErrorSeverity.MEDIUM,
      errorReport.category || ErrorCategory.UI,
      {
        component: 'client-error',
        metadata: structuredError
      }
    )

    // Log to server console with ERROR level
    console.error('CLIENT_ERROR:', JSON.stringify(structuredError, null, 2))

    // In production, you would send this to error tracking services:
    // - Sentry: Sentry.captureException(error, { contexts: structuredError })
    // - Bugsnag: Bugsnag.notify(error, { metaData: structuredError })
    // - Rollbar: rollbar.error(error, structuredError)
    // - DataDog: await datadogClient.error(structuredError)

    // Store in database for analysis (in production)
    // await storeErrorReport(structuredError)

    return NextResponse.json({ 
      success: true, 
      errorId: structuredError.id 
    })
  } catch (error) {
    console.error('Error Tracking API Error:', error)
    return NextResponse.json(
      { error: 'Failed to process error report' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Error tracking API is operational' },
    { status: 200 }
  )
}

function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}
