import { NextRequest, NextResponse } from 'next/server'

// Cache for price data to improve performance
interface PriceResponse {
  symbol: string
  price: number
  change24h: number
  volume24h: number
  marketCap: number
  timestamp: string
  source?: string
  status?: string
  fallback?: boolean
  message?: string
}

const priceDataCache = new Map<string, { data: PriceResponse, timestamp: number }>()
const CACHE_DURATION = 15000 // 15 seconds

// Helper function to get consistent response headers
function getResponseHeaders() {
  return {
    'Cache-Control': 'public, max-age=15',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
  }
}

// Mock price data for development
const MOCK_PRICES: Record<string, { price: number; change24h: number; volume24h: number; marketCap: number }> = {
  DOGE: { price: 0.08234, change24h: 5.67, volume24h: 1234567890, marketCap: 12000000000 },
  SHIB: { price: 0.000008234, change24h: -2.34, volume24h: 987654321, marketCap: 4800000000 },
  PEPE: { price: 0.00000123, change24h: 12.45, volume24h: 456789123, marketCap: 520000000 },
  FLOKI: { price: 0.000034, change24h: 8.91, volume24h: 234567890, marketCap: 320000000 },
  wDOGE: { price: 0.08234, change24h: 5.67, volume24h: 1234567890, marketCap: 12000000000 },
  PAW: { price: 0.00123, change24h: 15.23, volume24h: 345678901, marketCap: 123000000 },
}

// Duplicate interface removed - using the one at the top of the file

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    // Parse and validate input
    const { token: tokenParam } = await params
    if (!tokenParam || typeof tokenParam !== 'string') {
      return NextResponse.json(
        { 
          error: 'Invalid token parameter',
          code: 'INVALID_INPUT',
          timestamp: new Date().toISOString()
        } as const,
        { status: 400, headers: getResponseHeaders() }
      )
    }
    
    const token = tokenParam.toUpperCase()

    // Check cache first for better performance
    const cached = priceDataCache.get(token)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return NextResponse.json(cached.data, {
        headers: getResponseHeaders()
      })
    }

    // Simulate occasional network issues for testing error handling
    if (process.env.NODE_ENV !== 'production' && Math.random() < 0.02) {
      console.warn('Simulating network timeout for testing')
      throw new Error('Network timeout')
    }

    // Get base data with fallback for unknown tokens
    const baseData = MOCK_PRICES[token] || {
      price: 0.00000001,
      change24h: 0,
      volume24h: 0,
      marketCap: 0
    }
    
    // Add some random variation to make it more realistic
    const variation = 0.95 + (Math.random() * 0.1) // 0.95 to 1.05
    const priceData: PriceResponse = {
      symbol: token,
      price: parseFloat((baseData.price * variation).toFixed(8)),
      change24h: parseFloat(baseData.change24h.toFixed(2)),
      volume24h: baseData.volume24h,
      marketCap: baseData.marketCap,
      timestamp: new Date().toISOString(),
      source: 'mock-api',
      status: 'success'
    }

    // Cache the successful response
    priceDataCache.set(token, {
      data: priceData,
      timestamp: Date.now()
    })

    return NextResponse.json(priceData, {
      headers: getResponseHeaders()
    })

  } catch (error: unknown) {
    console.error('Price API Error:', error)
    
    // Return fallback data on error
    const fallbackData: PriceResponse = {
      symbol: (await params).token?.toUpperCase() || 'UNKNOWN',
      price: 0.00000001,
      change24h: 0.00,
      volume24h: 0,
      marketCap: 0,
      timestamp: new Date().toISOString(),
      source: 'fallback',
      status: 'error',
      fallback: true,
      message: 'Service temporarily unavailable, showing fallback values'
    }
    
    return NextResponse.json(fallbackData, {
      status: 200, // Return 200 with fallback data
      headers: getResponseHeaders()
    })
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: getResponseHeaders()
  })
}
