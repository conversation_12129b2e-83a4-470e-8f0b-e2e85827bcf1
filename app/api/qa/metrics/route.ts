import { NextRequest, NextResponse } from 'next/server'

interface QualityMetrics {
  // Development metrics
  testCoverage: number
  buildSuccessRate: number
  codeReviewTime: number
  defectDensity: number
  technicalDebtRatio: number
  
  // Production metrics
  errorRate: number
  performanceScore: number
  uptime: number
  userJourneySuccessRate: number
  timeToResolution: number
  
  // Process metrics
  leadTime: number
  deploymentFrequency: number
  mttr: number
  changeFailureRate: number
  
  // Metadata
  timestamp: string
  environment: string
  version: string
}

interface TrendAnalysis {
  metric: string
  trend: 'improving' | 'declining' | 'stable'
  changeRate: number
  significance: 'high' | 'medium' | 'low'
  recommendation: string
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const timeframe = searchParams.get('timeframe') || '7d'
    const metric = searchParams.get('metric')
    const format = searchParams.get('format') || 'json'

    // Generate mock quality metrics for demonstration
    const metrics = generateQualityMetrics(timeframe)
    
    // Filter by specific metric if requested
    const filteredMetrics = metric ? filterByMetric(metrics, metric) : metrics
    
    // Perform trend analysis
    const trends = analyzeTrends(filteredMetrics)
    
    // Calculate summary statistics
    const summary = calculateSummary(filteredMetrics)

    const response = {
      metrics: filteredMetrics,
      trends,
      summary,
      metadata: {
        timeframe,
        totalDataPoints: filteredMetrics.length,
        generatedAt: new Date().toISOString(),
      },
    }

    if (format === 'csv') {
      return new NextResponse(convertToCSV(filteredMetrics), {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename=quality-metrics.csv',
        },
      })
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('QA Metrics API Error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch quality metrics' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const metrics: QualityMetrics = await request.json()

    // Validate metrics data
    const validationErrors = validateMetrics(metrics)
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Invalid metrics data', details: validationErrors },
        { status: 400 }
      )
    }

    // Enrich with server-side data
    const enrichedMetrics = {
      ...metrics,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      serverMetadata: {
        ip: getClientIP(request),
        userAgent: request.headers.get('user-agent'),
      },
    }

    // Log metrics
    console.log('QA_METRICS_RECEIVED:', JSON.stringify(enrichedMetrics, null, 2))

    // In production, store in database
    // await storeQualityMetrics(enrichedMetrics)

    // Analyze for immediate alerts
    const alerts = checkMetricThresholds(enrichedMetrics)
    if (alerts.length > 0) {
      console.warn('QA_METRIC_ALERTS:', JSON.stringify(alerts, null, 2))
    }

    return NextResponse.json({
      success: true,
      metrics: enrichedMetrics,
      alerts,
      message: 'Quality metrics recorded successfully',
    })
  } catch (error) {
    console.error('QA Metrics Recording Error:', error)
    return NextResponse.json(
      { error: 'Failed to record quality metrics' },
      { status: 500 }
    )
  }
}

function generateQualityMetrics(timeframe: string): QualityMetrics[] {
  const metrics: QualityMetrics[] = []
  const now = new Date()
  
  // Calculate number of data points based on timeframe
  const dataPoints = getDataPointsForTimeframe(timeframe)
  const intervalMs = getIntervalForTimeframe(timeframe)

  for (let i = 0; i < dataPoints; i++) {
    const timestamp = new Date(now.getTime() - (i * intervalMs))
    
    metrics.push({
      // Development metrics with realistic variations
      testCoverage: Math.max(75, Math.min(95, 85 + (Math.random() - 0.5) * 10)),
      buildSuccessRate: Math.max(90, Math.min(100, 96 + (Math.random() - 0.5) * 8)),
      codeReviewTime: Math.max(2, Math.min(48, 12 + (Math.random() - 0.5) * 16)),
      defectDensity: Math.max(0, Math.min(0.5, 0.08 + (Math.random() - 0.5) * 0.1)),
      technicalDebtRatio: Math.max(0, Math.min(15, 4 + (Math.random() - 0.5) * 6)),
      
      // Production metrics
      errorRate: Math.max(0, Math.min(5, 0.8 + (Math.random() - 0.5) * 1.5)),
      performanceScore: Math.max(80, Math.min(100, 92 + (Math.random() - 0.5) * 12)),
      uptime: Math.max(99, Math.min(100, 99.8 + (Math.random() - 0.5) * 0.4)),
      userJourneySuccessRate: Math.max(85, Math.min(100, 94 + (Math.random() - 0.5) * 10)),
      timeToResolution: Math.max(0.5, Math.min(24, 3 + (Math.random() - 0.5) * 4)),
      
      // Process metrics
      leadTime: Math.max(1, Math.min(168, 24 + (Math.random() - 0.5) * 48)),
      deploymentFrequency: Math.max(0.1, Math.min(10, 2 + (Math.random() - 0.5) * 3)),
      mttr: Math.max(0.1, Math.min(8, 1.5 + (Math.random() - 0.5) * 2)),
      changeFailureRate: Math.max(0, Math.min(20, 3 + (Math.random() - 0.5) * 4)),
      
      // Metadata
      timestamp: timestamp.toISOString(),
      environment: 'production',
      version: '1.0.0',
    })
  }

  return metrics.reverse() // Return in chronological order
}

function getDataPointsForTimeframe(timeframe: string): number {
  switch (timeframe) {
    case '1d': return 24 // Hourly data points
    case '7d': return 7 * 4 // 6-hour intervals
    case '30d': return 30 // Daily data points
    case '90d': return 90 // Daily data points
    default: return 7 * 4
  }
}

function getIntervalForTimeframe(timeframe: string): number {
  switch (timeframe) {
    case '1d': return 60 * 60 * 1000 // 1 hour
    case '7d': return 6 * 60 * 60 * 1000 // 6 hours
    case '30d': return 24 * 60 * 60 * 1000 // 1 day
    case '90d': return 24 * 60 * 60 * 1000 // 1 day
    default: return 6 * 60 * 60 * 1000
  }
}

function filterByMetric(metrics: QualityMetrics[], metricName: string): any[] {
  return metrics.map(m => ({
    timestamp: m.timestamp,
    value: (m as any)[metricName],
    metric: metricName,
  })).filter(m => m.value !== undefined)
}

function analyzeTrends(metrics: QualityMetrics[]): TrendAnalysis[] {
  if (metrics.length < 2) return []

  const trends: TrendAnalysis[] = []
  const metricKeys = Object.keys(metrics[0]).filter(key => 
    typeof metrics[0][key as keyof QualityMetrics] === 'number'
  )

  metricKeys.forEach(metricKey => {
    const values = metrics.map(m => (m as any)[metricKey]).filter(v => typeof v === 'number')
    if (values.length < 2) return

    const firstHalf = values.slice(0, Math.floor(values.length / 2))
    const secondHalf = values.slice(Math.floor(values.length / 2))
    
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length
    
    const changeRate = ((secondAvg - firstAvg) / firstAvg) * 100
    const absChangeRate = Math.abs(changeRate)
    
    let trend: 'improving' | 'declining' | 'stable'
    let significance: 'high' | 'medium' | 'low'
    
    // Determine trend direction (context-dependent)
    const isPositiveMetric = ['testCoverage', 'buildSuccessRate', 'performanceScore', 'uptime', 'userJourneySuccessRate', 'deploymentFrequency'].includes(metricKey)
    
    if (absChangeRate < 2) {
      trend = 'stable'
    } else if (isPositiveMetric) {
      trend = changeRate > 0 ? 'improving' : 'declining'
    } else {
      trend = changeRate < 0 ? 'improving' : 'declining'
    }
    
    // Determine significance
    if (absChangeRate > 10) significance = 'high'
    else if (absChangeRate > 5) significance = 'medium'
    else significance = 'low'
    
    trends.push({
      metric: metricKey,
      trend,
      changeRate: Math.round(changeRate * 100) / 100,
      significance,
      recommendation: generateRecommendation(metricKey, trend, significance, changeRate),
    })
  })

  return trends
}

function generateRecommendation(metric: string, trend: string, significance: string, changeRate: number): string {
  if (trend === 'stable') {
    return `${metric} is stable. Continue current practices.`
  }
  
  if (trend === 'improving' && significance === 'high') {
    return `Excellent improvement in ${metric} (${changeRate.toFixed(1)}%). Document and share successful practices.`
  }
  
  if (trend === 'declining' && significance === 'high') {
    return `Significant decline in ${metric} (${changeRate.toFixed(1)}%). Immediate investigation and action required.`
  }
  
  if (trend === 'declining' && significance === 'medium') {
    return `${metric} is declining (${changeRate.toFixed(1)}%). Monitor closely and consider corrective actions.`
  }
  
  return `${metric} shows ${trend} trend. Continue monitoring.`
}

function calculateSummary(metrics: QualityMetrics[]): Record<string, any> {
  if (metrics.length === 0) return {}

  const latest = metrics[metrics.length - 1]
  const summary: Record<string, any> = {}

  // Calculate averages and status for key metrics
  const keyMetrics = ['testCoverage', 'buildSuccessRate', 'errorRate', 'performanceScore', 'uptime']
  
  keyMetrics.forEach(metric => {
    const values = metrics.map(m => (m as any)[metric]).filter(v => typeof v === 'number')
    const average = values.reduce((sum, val) => sum + val, 0) / values.length
    const current = latest[metric as keyof QualityMetrics] as number
    
    summary[metric] = {
      current: Math.round(current * 100) / 100,
      average: Math.round(average * 100) / 100,
      status: getMetricStatus(metric, current),
    }
  })

  return summary
}

function getMetricStatus(metric: string, value: number): 'good' | 'warning' | 'critical' {
  const thresholds: Record<string, { warning: number; critical: number; inverted?: boolean }> = {
    testCoverage: { warning: 80, critical: 70 },
    buildSuccessRate: { warning: 95, critical: 90 },
    errorRate: { warning: 2, critical: 5, inverted: true },
    performanceScore: { warning: 85, critical: 75 },
    uptime: { warning: 99.5, critical: 99 },
  }

  const threshold = thresholds[metric]
  if (!threshold) return 'good'

  if (threshold.inverted) {
    // For metrics where lower is better (like error rate)
    if (value >= threshold.critical) return 'critical'
    if (value >= threshold.warning) return 'warning'
    return 'good'
  } else {
    // For metrics where higher is better
    if (value <= threshold.critical) return 'critical'
    if (value <= threshold.warning) return 'warning'
    return 'good'
  }
}

function validateMetrics(metrics: QualityMetrics): string[] {
  const errors: string[] = []
  
  // Validate required fields
  const requiredFields = ['testCoverage', 'buildSuccessRate', 'errorRate', 'performanceScore']
  requiredFields.forEach(field => {
    if (typeof (metrics as any)[field] !== 'number') {
      errors.push(`${field} is required and must be a number`)
    }
  })
  
  // Validate ranges
  if (metrics.testCoverage < 0 || metrics.testCoverage > 100) {
    errors.push('testCoverage must be between 0 and 100')
  }
  
  if (metrics.buildSuccessRate < 0 || metrics.buildSuccessRate > 100) {
    errors.push('buildSuccessRate must be between 0 and 100')
  }
  
  return errors
}

function checkMetricThresholds(metrics: QualityMetrics): string[] {
  const alerts: string[] = []
  
  if (metrics.testCoverage < 70) {
    alerts.push(`Critical: Test coverage is ${metrics.testCoverage}% (threshold: 70%)`)
  }
  
  if (metrics.buildSuccessRate < 90) {
    alerts.push(`Critical: Build success rate is ${metrics.buildSuccessRate}% (threshold: 90%)`)
  }
  
  if (metrics.errorRate > 5) {
    alerts.push(`Critical: Error rate is ${metrics.errorRate}% (threshold: 5%)`)
  }
  
  return alerts
}

function convertToCSV(metrics: QualityMetrics[]): string {
  if (metrics.length === 0) return ''
  
  const headers = Object.keys(metrics[0]).join(',')
  const rows = metrics.map(metric => 
    Object.values(metric).map(value => 
      typeof value === 'string' && value.includes(',') ? `"${value}"` : value
    ).join(',')
  )
  
  return [headers, ...rows].join('\n')
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}
