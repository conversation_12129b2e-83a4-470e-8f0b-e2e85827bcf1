import { NextRequest, NextResponse } from 'next/server'

// Cache for generated chart data to improve performance
interface ChartDataPoint {
  time: number
  open: number
  high: number
  low: number
  close: number
  volume: number
}

const chartDataCache = new Map<string, { data: ChartDataPoint[], timestamp: number }>()
const CACHE_DURATION = 30000 // 30 seconds

// Optimized chart data generation with reduced complexity
function generateChartData(timeframe: string, basePrice: number = 0.08234) {
  // Check cache first
  const cacheKey = `${timeframe}-${basePrice}`
  const cached = chartDataCache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data
  }

  const now = Date.now()
  const intervals: Record<string, { duration: number; points: number }> = {
    '1H': { duration: 60 * 1000, points: 30 }, // Reduced from 60 to 30 points
    '4H': { duration: 4 * 60 * 1000, points: 40 }, // Reduced from 60 to 40 points
    '1D': { duration: 24 * 60 * 1000, points: 24 }, // Keep 24 points
    '1W': { duration: 7 * 24 * 60 * 1000, points: 84 }, // Reduced from 168 to 84 points
  }

  const config = intervals[timeframe] || intervals['1D']
  const data = []

  let currentPrice = basePrice
  const timeStep = config.duration / config.points

  // Pre-calculate some values to reduce computation in loop
  const volatility = 0.015 // Reduced volatility for faster calculation
  const trendFactor = 0.0008

  for (let i = 0; i < config.points; i++) {
    // Simplified price movement calculation
    const trend = Math.sin(i * 0.1) * trendFactor
    const randomChange = (Math.random() - 0.5) * volatility

    currentPrice = Math.max(0.001, currentPrice * (1 + trend + randomChange))
    const timestamp = Math.floor(now - (config.duration - (i * timeStep)))

    // Simplified data structure with fewer calculations
    const price = parseFloat(currentPrice.toFixed(8))
    data.push({
      time: timestamp,
      volume: Math.floor(Math.random() * 500000 + 100000), // Reduced range
      high: parseFloat((price * 1.005).toFixed(8)), // Simplified calculation
      low: parseFloat((price * 0.995).toFixed(8)), // Simplified calculation
      open: parseFloat((price * 0.998).toFixed(8)), // Simplified calculation
      close: price
    })
  }

  // Cache the result
  chartDataCache.set(cacheKey, { data, timestamp: Date.now() })

  return data
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string; timeframe: string }> }
) {
  const { token: tokenParam, timeframe: timeframeParam } = await params
  const token = tokenParam.toUpperCase()
  const timeframe = timeframeParam.toUpperCase()

  try {

    // Simulate occasional network issues for testing error handling
    if (Math.random() < 0.03) { // 3% chance of simulated failure
      throw new Error('Simulated chart data timeout')
    }

    // Validate timeframe
    const validTimeframes = ['1H', '4H', '1D', '1W']
    if (!validTimeframes.includes(timeframe)) {
      // Return fallback data for invalid timeframes instead of error
      const fallbackData = generateChartData('1D', 0.001)
      return NextResponse.json({
        symbol: token,
        timeframe: '1D', // Default to 1D
        data: fallbackData,
        metadata: {
          dataPoints: fallbackData.length,
          startTime: fallbackData[0]?.time,
          endTime: fallbackData[fallbackData.length - 1]?.time,
          source: 'fallback',
          message: `Invalid timeframe '${timeframe}', showing 1D data`
        }
      }, {
        status: 200,
        headers: {
          'Cache-Control': 'public, max-age=60',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      })
    }

    // Base prices for different tokens
    const basePrices: Record<string, number> = {
      DOGE: 0.08234,
      SHIB: 0.000008234,
      PEPE: 0.00000123,
      FLOKI: 0.000034,
      wDOGE: 0.08234,
      PAW: 0.00123,
    }

    const basePrice = basePrices[token] || 0.001
    const chartData = generateChartData(timeframe, basePrice)

    const response = {
      symbol: token,
      timeframe: timeframe,
      data: chartData,
      metadata: {
        dataPoints: chartData.length,
        startTime: chartData[0]?.time,
        endTime: chartData[chartData.length - 1]?.time,
        source: 'mock-api',
        status: 'success'
      }
    }

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    })
  } catch (error) {
    console.error('Chart API error:', error)

    // Return fallback chart data even on server errors
    const fallbackData = generateChartData('1D', 0.001)
    const fallbackResponse = {
      symbol: token.toUpperCase(),
      timeframe: timeframe.toUpperCase(),
      data: fallbackData,
      metadata: {
        dataPoints: fallbackData.length,
        startTime: fallbackData[0]?.time,
        endTime: fallbackData[fallbackData.length - 1]?.time,
        source: 'fallback',
        error: true,
        message: 'Chart service temporarily unavailable, showing sample data'
      }
    }

    return NextResponse.json(fallbackResponse, {
      status: 200, // Return 200 with fallback instead of 500
      headers: {
        'Cache-Control': 'public, max-age=30', // Short cache for error fallback
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    })
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
