import { NextRequest, NextResponse } from 'next/server'
import { Alert, AlertSeverity, AlertType } from '@/lib/monitoring/alerting'

export async function POST(request: NextRequest) {
  try {
    const alert: Alert = await request.json()

    // Validate alert data
    if (!alert.id || !alert.title || !alert.message || !alert.timestamp) {
      return NextResponse.json(
        { error: 'Invalid alert format' },
        { status: 400 }
      )
    }

    // Enrich alert with server-side data
    const enrichedAlert = {
      ...alert,
      serverTimestamp: new Date().toISOString(),
      ip: getClientIP(request),
      userAgent: request.headers.get('user-agent'),
      environment: process.env.NODE_ENV,
    }

    // Log alert with appropriate severity
    const logLevel = getLogLevel(alert.severity)
    console.log(`[${logLevel}] ALERT_RECEIVED:`, JSON.stringify(enrichedAlert, null, 2))

    // Process alert based on severity
    await processAlert(enrichedAlert)

    // Store alert in database (in production)
    // await storeAlert(enrichedAlert)

    return NextResponse.json({ 
      success: true, 
      alertId: alert.id,
      processed: true 
    })
  } catch (error) {
    console.error('Monitoring Alerts API Error:', error)
    return NextResponse.json(
      { error: 'Failed to process alert' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const severity = searchParams.get('severity') as AlertSeverity
    const type = searchParams.get('type') as AlertType
    const limit = parseInt(searchParams.get('limit') || '50')

    // In production, this would fetch from database
    const mockAlerts = generateMockAlerts(limit, severity, type)

    return NextResponse.json({
      alerts: mockAlerts,
      total: mockAlerts.length,
      filters: { severity, type, limit },
    })
  } catch (error) {
    console.error('Monitoring Alerts API Error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch alerts' },
      { status: 500 }
    )
  }
}

async function processAlert(alert: Alert): Promise<void> {
  // Process based on severity
  switch (alert.severity) {
    case AlertSeverity.CRITICAL:
      await handleCriticalAlert(alert)
      break
    case AlertSeverity.HIGH:
      await handleHighAlert(alert)
      break
    case AlertSeverity.MEDIUM:
      await handleMediumAlert(alert)
      break
    case AlertSeverity.LOW:
      await handleLowAlert(alert)
      break
  }

  // Process based on type
  switch (alert.type) {
    case AlertType.PERFORMANCE:
      await handlePerformanceAlert(alert)
      break
    case AlertType.ERROR:
      await handleErrorAlert(alert)
      break
    case AlertType.SECURITY:
      await handleSecurityAlert(alert)
      break
    case AlertType.BUSINESS:
      await handleBusinessAlert(alert)
      break
  }
}

async function handleCriticalAlert(alert: Alert): Promise<void> {
  // Critical alerts require immediate attention
  console.error('🚨 CRITICAL ALERT:', alert.title, alert.message)
  
  // In production, you would:
  // - Send to PagerDuty: await pagerDuty.triggerIncident(alert)
  // - Send SMS to on-call engineer: await smsService.sendAlert(alert)
  // - Create Slack incident channel: await slack.createIncidentChannel(alert)
  // - Send email to leadership: await emailService.sendCriticalAlert(alert)
}

async function handleHighAlert(alert: Alert): Promise<void> {
  console.warn('⚠️ HIGH ALERT:', alert.title, alert.message)
  
  // In production:
  // - Send to Slack: await slack.sendToChannel('#alerts', alert)
  // - Email engineering team: await emailService.sendHighAlert(alert)
  // - Create Jira ticket: await jira.createTicket(alert)
}

async function handleMediumAlert(alert: Alert): Promise<void> {
  console.warn('📊 MEDIUM ALERT:', alert.title, alert.message)
  
  // In production:
  // - Send to monitoring channel: await slack.sendToChannel('#monitoring', alert)
  // - Log to monitoring dashboard: await dashboard.logAlert(alert)
}

async function handleLowAlert(alert: Alert): Promise<void> {
  console.info('ℹ️ LOW ALERT:', alert.title, alert.message)
  
  // In production:
  // - Log to monitoring system: await monitoring.logAlert(alert)
  // - Update metrics dashboard: await metrics.updateAlert(alert)
}

async function handlePerformanceAlert(alert: Alert): Promise<void> {
  // Performance-specific handling
  console.log('🚀 Performance Alert Processing:', alert.title)
  
  // In production:
  // - Trigger auto-scaling: await autoScaler.scaleUp()
  // - Clear CDN cache: await cdn.purgeCache()
  // - Restart slow services: await serviceManager.restartSlow()
}

async function handleErrorAlert(alert: Alert): Promise<void> {
  // Error-specific handling
  console.log('🐛 Error Alert Processing:', alert.title)
  
  // In production:
  // - Trigger error investigation: await errorTracker.investigate(alert)
  // - Create bug report: await bugTracker.createBug(alert)
  // - Rollback if needed: await deployment.rollbackIfNeeded(alert)
}

async function handleSecurityAlert(alert: Alert): Promise<void> {
  // Security-specific handling
  console.log('🔒 Security Alert Processing:', alert.title)
  
  // In production:
  // - Block suspicious IPs: await firewall.blockIPs(alert.metadata?.suspiciousIPs)
  // - Trigger security scan: await securityScanner.scan()
  // - Notify security team: await security.notifyTeam(alert)
}

async function handleBusinessAlert(alert: Alert): Promise<void> {
  // Business-specific handling
  console.log('💼 Business Alert Processing:', alert.title)
  
  // In production:
  // - Notify business stakeholders: await business.notifyStakeholders(alert)
  // - Update business dashboard: await dashboard.updateBusinessMetrics(alert)
  // - Trigger business continuity plan: await bcp.trigger(alert)
}

function getLogLevel(severity: AlertSeverity): string {
  switch (severity) {
    case AlertSeverity.CRITICAL:
      return 'CRITICAL'
    case AlertSeverity.HIGH:
      return 'ERROR'
    case AlertSeverity.MEDIUM:
      return 'WARN'
    case AlertSeverity.LOW:
      return 'INFO'
    default:
      return 'INFO'
  }
}

function generateMockAlerts(limit: number, severity?: AlertSeverity, type?: AlertType): Alert[] {
  const alerts: Alert[] = []
  const now = Date.now()

  for (let i = 0; i < limit; i++) {
    const alertSeverity = severity || Object.values(AlertSeverity)[Math.floor(Math.random() * 4)]
    const alertType = type || Object.values(AlertType)[Math.floor(Math.random() * 5)]
    
    alerts.push({
      id: `mock_alert_${i}`,
      type: alertType,
      severity: alertSeverity,
      title: `Mock ${alertSeverity} ${alertType} Alert`,
      message: `This is a mock alert for testing purposes`,
      timestamp: now - (i * 60000), // Spread alerts over time
      metadata: {
        mock: true,
        index: i,
      },
      resolved: Math.random() > 0.7, // 30% chance of being resolved
    })
  }

  return alerts
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}
