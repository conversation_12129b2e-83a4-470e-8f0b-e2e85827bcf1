import { NextRequest, NextResponse } from 'next/server'
import { 
  getAlertConfig, 
  updateAlertConfig, 
  validateThreshold, 
  validateEscalationLevel,
  AlertConfiguration,
  AlertThreshold,
  EscalationLevel 
} from '@/lib/monitoring/alert-config'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const section = searchParams.get('section')

    const config = getAlertConfig()

    // Return specific section if requested
    if (section) {
      switch (section) {
        case 'thresholds':
          return NextResponse.json({ thresholds: config.thresholds })
        case 'rules':
          return NextResponse.json({ rules: config.rules })
        case 'escalation':
          return NextResponse.json({ escalation: config.escalation })
        case 'channels':
          return NextResponse.json({ channels: config.channels })
        case 'settings':
          return NextResponse.json({ globalSettings: config.globalSettings })
        default:
          return NextResponse.json(
            { error: 'Invalid section. Valid sections: thresholds, rules, escalation, channels, settings' },
            { status: 400 }
          )
      }
    }

    // Return full configuration
    return NextResponse.json({
      config,
      metadata: {
        totalThresholds: config.thresholds.length,
        totalRules: config.rules.length,
        totalEscalationLevels: config.escalation.length,
        enabledChannels: config.channels.filter(c => c.enabled).length,
        alertingEnabled: config.globalSettings.enableAlerting,
      },
    })
  } catch (error) {
    console.error('Monitoring Config API Error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch monitoring configuration' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const updates: Partial<AlertConfiguration> = await request.json()

    // Validate the updates
    const validationErrors = validateConfigUpdates(updates)
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { status: 400 }
      )
    }

    // Update configuration
    const updatedConfig = updateAlertConfig(updates)

    // Log configuration change
    console.log('MONITORING_CONFIG_UPDATED:', JSON.stringify({
      timestamp: new Date().toISOString(),
      updates,
      ip: getClientIP(request),
      userAgent: request.headers.get('user-agent'),
    }, null, 2))

    return NextResponse.json({
      success: true,
      config: updatedConfig,
      message: 'Monitoring configuration updated successfully',
    })
  } catch (error) {
    console.error('Monitoring Config Update Error:', error)
    return NextResponse.json(
      { error: 'Failed to update monitoring configuration' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'test-alert':
        return await handleTestAlert(request)
      case 'validate-config':
        return await handleValidateConfig(request)
      case 'reset-config':
        return await handleResetConfig(request)
      default:
        return NextResponse.json(
          { error: 'Invalid action. Valid actions: test-alert, validate-config, reset-config' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Monitoring Config Action Error:', error)
    return NextResponse.json(
      { error: 'Failed to execute action' },
      { status: 500 }
    )
  }
}

async function handleTestAlert(request: NextRequest): Promise<NextResponse> {
  const { severity = 'medium', type = 'system' } = await request.json()

  // Simulate a test alert
  const testAlert = {
    id: `test_${Date.now()}`,
    type,
    severity,
    title: 'Test Alert',
    message: 'This is a test alert to verify the monitoring system is working correctly.',
    timestamp: Date.now(),
    metadata: {
      test: true,
      triggeredBy: getClientIP(request),
    },
  }

  console.log('TEST_ALERT_TRIGGERED:', JSON.stringify(testAlert, null, 2))

  return NextResponse.json({
    success: true,
    alert: testAlert,
    message: 'Test alert triggered successfully',
  })
}

async function handleValidateConfig(request: NextRequest): Promise<NextResponse> {
  const config: Partial<AlertConfiguration> = await request.json()
  const errors = validateConfigUpdates(config)

  return NextResponse.json({
    valid: errors.length === 0,
    errors,
    message: errors.length === 0 ? 'Configuration is valid' : 'Configuration validation failed',
  })
}

async function handleResetConfig(request: NextRequest): Promise<NextResponse> {
  // Reset to default configuration
  const defaultConfig = getAlertConfig()

  console.log('MONITORING_CONFIG_RESET:', JSON.stringify({
    timestamp: new Date().toISOString(),
    ip: getClientIP(request),
    userAgent: request.headers.get('user-agent'),
  }, null, 2))

  return NextResponse.json({
    success: true,
    config: defaultConfig,
    message: 'Monitoring configuration reset to defaults',
  })
}

function validateConfigUpdates(updates: Partial<AlertConfiguration>): string[] {
  const errors: string[] = []

  // Validate thresholds
  if (updates.thresholds) {
    updates.thresholds.forEach((threshold, index) => {
      const thresholdErrors = validateThreshold(threshold)
      thresholdErrors.forEach(error => {
        errors.push(`Threshold ${index + 1}: ${error}`)
      })
    })
  }

  // Validate escalation levels
  if (updates.escalation) {
    updates.escalation.forEach((level, index) => {
      const levelErrors = validateEscalationLevel(level)
      levelErrors.forEach(error => {
        errors.push(`Escalation level ${index + 1}: ${error}`)
      })
    })
  }

  // Validate global settings
  if (updates.globalSettings) {
    const settings = updates.globalSettings
    
    if (settings.defaultCooldown !== undefined && settings.defaultCooldown < 1) {
      errors.push('Default cooldown must be at least 1 minute')
    }
    
    if (settings.maxAlertsPerHour !== undefined && settings.maxAlertsPerHour < 1) {
      errors.push('Max alerts per hour must be at least 1')
    }
    
    if (settings.quietHours) {
      const { start, end } = settings.quietHours
      if (!isValidTimeFormat(start) || !isValidTimeFormat(end)) {
        errors.push('Quiet hours must be in HH:MM format')
      }
    }
  }

  // Validate notification channels
  if (updates.channels) {
    updates.channels.forEach((channel, index) => {
      if (!channel.id || !channel.name || !channel.type) {
        errors.push(`Channel ${index + 1}: ID, name, and type are required`)
      }
      
      if (!['email', 'slack', 'sms', 'webhook', 'pagerduty'].includes(channel.type)) {
        errors.push(`Channel ${index + 1}: Invalid channel type`)
      }
      
      // Validate channel-specific configuration
      if (channel.type === 'email' && channel.enabled) {
        if (!channel.config.smtpHost || !channel.config.fromEmail) {
          errors.push(`Channel ${index + 1}: SMTP host and from email are required for email channels`)
        }
      }
      
      if (channel.type === 'slack' && channel.enabled) {
        if (!channel.config.webhookUrl) {
          errors.push(`Channel ${index + 1}: Webhook URL is required for Slack channels`)
        }
      }
      
      if (channel.type === 'webhook' && channel.enabled) {
        if (!channel.config.url) {
          errors.push(`Channel ${index + 1}: URL is required for webhook channels`)
        }
      }
    })
  }

  return errors
}

function isValidTimeFormat(time: string): boolean {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(time)
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}
