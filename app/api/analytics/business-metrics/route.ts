import { NextRequest, NextResponse } from 'next/server'
import { BusinessMetric } from '@/lib/monitoring/uptime'

export async function POST(request: NextRequest) {
  try {
    const metric: BusinessMetric = await request.json()

    // Validate metric data
    if (!metric.name || typeof metric.value !== 'number' || !metric.timestamp) {
      return NextResponse.json(
        { error: 'Invalid business metric format' },
        { status: 400 }
      )
    }

    // Enrich metric with server-side data
    const enrichedMetric = {
      ...metric,
      serverTimestamp: new Date().toISOString(),
      ip: getClientIP(request),
      userAgent: request.headers.get('user-agent'),
      environment: process.env.NODE_ENV,
    }

    // Log business metric
    console.log('BUSINESS_METRIC:', JSON.stringify(enrichedMetric, null, 2))

    // Process metric for insights
    const insights = analyzeBusinessMetric(enrichedMetric)
    
    if (insights.alerts.length > 0) {
      console.warn('BUSINESS_METRIC_ALERTS:', JSON.stringify(insights.alerts, null, 2))
    }

    // In production, send to business intelligence systems:
    // - Google Analytics: gtag('event', 'business_metric', { ... })
    // - Mixpanel: mixpanel.track('Business Metric', enrichedMetric)
    // - Amplitude: amplitude.logEvent('business_metric', enrichedMetric)
    // - Custom BI: await biSystem.recordMetric(enrichedMetric)

    // Store in database for analysis
    // await storeBusinessMetric(enrichedMetric)

    return NextResponse.json({ 
      success: true,
      insights 
    })
  } catch (error) {
    console.error('Business Metrics API Error:', error)
    return NextResponse.json(
      { error: 'Failed to process business metric' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const metricName = searchParams.get('name')
    const timeframe = searchParams.get('timeframe') || '24h'
    const limit = parseInt(searchParams.get('limit') || '100')

    // Generate mock business metrics for demonstration
    const metrics = generateMockBusinessMetrics(metricName, timeframe, limit)
    const summary = generateBusinessSummary(metrics)

    return NextResponse.json({
      metrics,
      summary,
      timeframe,
      total: metrics.length,
    })
  } catch (error) {
    console.error('Business Metrics API Error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch business metrics' },
      { status: 500 }
    )
  }
}

function analyzeBusinessMetric(metric: BusinessMetric): { insights: string[]; alerts: string[] } {
  const insights: string[] = []
  const alerts: string[] = []

  // Analyze different types of business metrics
  if (metric.name.includes('uptime_percentage')) {
    if (metric.value < 99) {
      alerts.push(`Low uptime detected: ${metric.value.toFixed(2)}%`)
    } else if (metric.value >= 99.9) {
      insights.push(`Excellent uptime: ${metric.value.toFixed(2)}%`)
    }
  }

  if (metric.name.includes('journey_success_rate')) {
    if (metric.value < 80) {
      alerts.push(`Poor user journey success rate: ${metric.value.toFixed(1)}%`)
    } else if (metric.value >= 95) {
      insights.push(`Excellent user journey performance: ${metric.value.toFixed(1)}%`)
    }
  }

  if (metric.name.includes('response_time')) {
    if (metric.value > 5000) {
      alerts.push(`Slow response time detected: ${metric.value}ms`)
    } else if (metric.value < 100) {
      insights.push(`Excellent response time: ${metric.value}ms`)
    }
  }

  if (metric.name === 'active_users') {
    if (metric.value > 1000) {
      insights.push(`High user activity: ${metric.value} active users`)
    } else if (metric.value < 100) {
      alerts.push(`Low user activity: ${metric.value} active users`)
    }
  }

  if (metric.name === 'daily_transactions') {
    if (metric.value > 100) {
      insights.push(`High transaction volume: ${metric.value} transactions`)
    } else if (metric.value < 10) {
      alerts.push(`Low transaction volume: ${metric.value} transactions`)
    }
  }

  return { insights, alerts }
}

function generateMockBusinessMetrics(metricName?: string | null, timeframe = '24h', limit = 100): BusinessMetric[] {
  const metrics: BusinessMetric[] = []
  const now = Date.now()
  
  // Calculate time range
  const timeRanges: Record<string, number> = {
    '1h': 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000,
    '7d': 7 * 24 * 60 * 60 * 1000,
    '30d': 30 * 24 * 60 * 60 * 1000,
  }
  
  const timeRange = timeRanges[timeframe] || timeRanges['24h']
  const interval = timeRange / limit

  const metricTypes = metricName ? [metricName] : [
    'overall_uptime_percentage',
    'active_users',
    'daily_transactions',
    'wallet_connections',
    'journey_success_rate_trading',
    'journey_success_rate_token_launch',
    'uptime_check_response_time',
  ]

  metricTypes.forEach(type => {
    for (let i = 0; i < limit; i++) {
      const timestamp = now - (timeRange - (i * interval))
      const value = generateMockValue(type, i, limit)
      
      metrics.push({
        name: type,
        value,
        timestamp,
        metadata: {
          mock: true,
          timeframe,
          index: i,
        },
      })
    }
  })

  return metrics.sort((a, b) => a.timestamp - b.timestamp)
}

function generateMockValue(metricType: string, index: number, total: number): number {
  const progress = index / total
  const noise = (Math.random() - 0.5) * 0.1 // ±5% noise

  switch (metricType) {
    case 'overall_uptime_percentage':
      return Math.max(95, Math.min(100, 99.5 + noise))
    
    case 'active_users':
      // Simulate daily pattern with peak in the middle
      const userBase = 500
      const dailyVariation = Math.sin(progress * Math.PI * 2) * 200
      return Math.max(0, userBase + dailyVariation + (noise * 100))
    
    case 'daily_transactions':
      // Simulate transaction volume with some growth trend
      const baseTransactions = 50
      const growth = progress * 20 // Growing trend
      return Math.max(0, baseTransactions + growth + (noise * 20))
    
    case 'wallet_connections':
      // Simulate wallet connections
      const baseConnections = 100
      const variation = Math.sin(progress * Math.PI * 4) * 50
      return Math.max(0, baseConnections + variation + (noise * 30))
    
    case 'journey_success_rate_trading':
      return Math.max(80, Math.min(100, 92 + (noise * 5)))
    
    case 'journey_success_rate_token_launch':
      return Math.max(70, Math.min(100, 85 + (noise * 8)))
    
    case 'uptime_check_response_time':
      // Simulate response times with occasional spikes
      const baseTime = 150
      const spike = Math.random() < 0.05 ? Math.random() * 1000 : 0 // 5% chance of spike
      return Math.max(50, baseTime + spike + (noise * 50))
    
    default:
      return Math.random() * 100
  }
}

function generateBusinessSummary(metrics: BusinessMetric[]): Record<string, any> {
  const summary: Record<string, any> = {
    totalMetrics: metrics.length,
    timeRange: {
      start: Math.min(...metrics.map(m => m.timestamp)),
      end: Math.max(...metrics.map(m => m.timestamp)),
    },
    metricTypes: [...new Set(metrics.map(m => m.name))],
    averages: {},
    trends: {},
    alerts: [],
  }

  // Calculate averages and trends for each metric type
  summary.metricTypes.forEach((metricType: string) => {
    const typeMetrics = metrics.filter(m => m.name === metricType)
    
    if (typeMetrics.length > 0) {
      const values = typeMetrics.map(m => m.value)
      const average = values.reduce((sum, val) => sum + val, 0) / values.length
      
      summary.averages[metricType] = Math.round(average * 100) / 100
      
      // Calculate trend (simple linear regression slope)
      if (values.length > 1) {
        const n = values.length
        const sumX = (n * (n - 1)) / 2
        const sumY = values.reduce((sum, val) => sum + val, 0)
        const sumXY = values.reduce((sum, val, idx) => sum + (val * idx), 0)
        const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
        summary.trends[metricType] = slope > 0 ? 'increasing' : slope < 0 ? 'decreasing' : 'stable'
      }
      
      // Generate alerts based on thresholds
      if (metricType.includes('uptime') && average < 99) {
        summary.alerts.push(`Low average uptime for ${metricType}: ${average.toFixed(2)}%`)
      }
      
      if (metricType.includes('success_rate') && average < 85) {
        summary.alerts.push(`Low success rate for ${metricType}: ${average.toFixed(1)}%`)
      }
      
      if (metricType.includes('response_time') && average > 1000) {
        summary.alerts.push(`High response time for ${metricType}: ${average.toFixed(0)}ms`)
      }
    }
  })

  return summary
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}
