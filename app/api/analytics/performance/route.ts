import { NextRequest, NextResponse } from 'next/server'
import { PerformanceMetric } from '@/lib/monitoring/performance'

export async function POST(request: NextRequest) {
  try {
    const metric: PerformanceMetric = await request.json()

    // Validate metric data
    if (!metric.name || typeof metric.value !== 'number' || !metric.timestamp) {
      return NextResponse.json(
        { error: 'Invalid performance metric format' },
        { status: 400 }
      )
    }

    // Enrich metric with server-side data
    const enrichedMetric = {
      ...metric,
      serverTimestamp: new Date().toISOString(),
      ip: getClientIP(request),
      country: request.headers.get('cf-ipcountry') || 'unknown',
      city: request.headers.get('cf-ipcity') || 'unknown',
      userAgent: request.headers.get('user-agent') || undefined,
    }

    // Log performance metric
    console.log('PERFORMANCE_METRIC:', JSON.stringify(enrichedMetric, null, 2))

    // In production, send to analytics services:
    // - Google Analytics 4: gtag('event', 'web_vitals', { ... })
    // - DataDog RUM: DD_RUM.addTiming(metric.name, metric.value)
    // - New Relic: newrelic.recordMetric(metric.name, metric.value)
    // - Custom analytics: await analyticsClient.track(enrichedMetric)

    // Store in database for analysis
    // await storePerformanceMetric(enrichedMetric)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Performance Analytics API Error:', error)
    return NextResponse.json(
      { error: 'Failed to process performance metric' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Performance analytics API is operational' },
    { status: 200 }
  )
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}
