import { NextRequest, NextResponse } from 'next/server'
import { PerformanceMetric } from '@/lib/monitoring/performance'

export async function POST(request: NextRequest) {
  try {
    const metrics: PerformanceMetric[] = await request.json()

    // Validate metrics array
    if (!Array.isArray(metrics) || metrics.length === 0) {
      return NextResponse.json(
        { error: 'Invalid metrics array' },
        { status: 400 }
      )
    }

    // Validate each metric
    for (const metric of metrics) {
      if (!metric.name || typeof metric.value !== 'number' || !metric.timestamp) {
        return NextResponse.json(
          { error: 'Invalid metric format in batch' },
          { status: 400 }
        )
      }
    }

    // Enrich metrics with server-side data
    const enrichedMetrics = metrics.map(metric => ({
      ...metric,
      serverTimestamp: new Date().toISOString(),
      ip: getClientIP(request),
      country: request.headers.get('cf-ipcountry') || 'unknown',
      city: request.headers.get('cf-ipcity') || 'unknown',
      userAgent: request.headers.get('user-agent') || undefined,
    }))

    // Log batch metrics
    console.log('PERFORMANCE_METRICS_BATCH:', JSON.stringify({
      count: enrichedMetrics.length,
      metrics: enrichedMetrics,
    }, null, 2))

    // Process metrics for insights
    const insights = analyzeMetrics(enrichedMetrics)
    console.log('PERFORMANCE_INSIGHTS:', JSON.stringify(insights, null, 2))

    // In production, send to analytics services in batch:
    // - Google Analytics 4: gtag('event', 'web_vitals_batch', { ... })
    // - DataDog RUM: DD_RUM.addTimings(metrics)
    // - New Relic: newrelic.recordMetrics(metrics)
    // - Custom analytics: await analyticsClient.trackBatch(enrichedMetrics)

    // Store in database for analysis
    // await storePerformanceMetricsBatch(enrichedMetrics)

    return NextResponse.json({ 
      success: true, 
      processed: enrichedMetrics.length,
      insights 
    })
  } catch (error) {
    console.error('Performance Analytics Batch API Error:', error)
    return NextResponse.json(
      { error: 'Failed to process performance metrics batch' },
      { status: 500 }
    )
  }
}

function analyzeMetrics(metrics: PerformanceMetric[]): Record<string, any> {
  const insights: Record<string, any> = {
    totalMetrics: metrics.length,
    coreWebVitals: {},
    performance: {},
    issues: [],
  }

  // Analyze Core Web Vitals
  const coreVitals = ['LCP', 'FID', 'CLS', 'FCP']
  coreVitals.forEach(vital => {
    const vitalMetrics = metrics.filter(m => m.name === vital)
    if (vitalMetrics.length > 0) {
      const latest = vitalMetrics[vitalMetrics.length - 1]
      insights.coreWebVitals[vital] = {
        value: latest.value,
        rating: latest.rating,
        timestamp: latest.timestamp,
      }

      // Flag performance issues
      if (latest.rating === 'poor') {
        insights.issues.push({
          type: 'core_web_vital',
          metric: vital,
          value: latest.value,
          rating: latest.rating,
          severity: 'high',
        })
      }
    }
  })

  // Analyze page load performance
  const pageLoadMetrics = metrics.filter(m => m.name === 'Page Load Time')
  if (pageLoadMetrics.length > 0) {
    const avgPageLoad = pageLoadMetrics.reduce((sum, m) => sum + m.value, 0) / pageLoadMetrics.length
    insights.performance.averagePageLoad = Math.round(avgPageLoad)
    
    if (avgPageLoad > 3000) {
      insights.issues.push({
        type: 'page_load',
        metric: 'Page Load Time',
        value: avgPageLoad,
        severity: avgPageLoad > 5000 ? 'high' : 'medium',
      })
    }
  }

  // Analyze resource loading
  const resourceMetrics = metrics.filter(m => m.name.startsWith('Resource:'))
  if (resourceMetrics.length > 0) {
    const slowResources = resourceMetrics.filter(m => m.value > 1000)
    if (slowResources.length > 0) {
      insights.performance.slowResources = slowResources.length
      insights.issues.push({
        type: 'slow_resources',
        count: slowResources.length,
        severity: 'medium',
      })
    }
  }

  // Calculate performance score
  const coreVitalScores = Object.values(insights.coreWebVitals).map((vital: any) => {
    switch (vital.rating) {
      case 'good': return 100
      case 'needs-improvement': return 70
      case 'poor': return 30
      default: return 50
    }
  })

  if (coreVitalScores.length > 0) {
    insights.performanceScore = Math.round(
      coreVitalScores.reduce((sum, score) => sum + score, 0) / coreVitalScores.length
    )
  }

  return insights
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}
