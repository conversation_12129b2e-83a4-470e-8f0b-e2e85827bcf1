import { NextRequest, NextResponse } from 'next/server'
import { LogEntry, LogLevel } from '@/lib/monitoring/logger'

export async function POST(request: NextRequest) {
  try {
    const logEntry: LogEntry = await request.json()

    // Validate log entry
    if (!logEntry.message || typeof logEntry.level !== 'number') {
      return NextResponse.json(
        { error: 'Invalid log entry format' },
        { status: 400 }
      )
    }

    // In production, this would send to external logging service
    // For now, we'll log to console with structured format
    const timestamp = new Date().toISOString()
    const levelName = LogLevel[logEntry.level]
    
    const structuredLog = {
      timestamp,
      level: levelName,
      message: logEntry.message,
      context: logEntry.context,
      error: logEntry.error ? {
        name: logEntry.error.name,
        message: logEntry.error.message,
        stack: logEntry.stack,
      } : undefined,
    }

    // Log to server console
    console.log('CLIENT_LOG:', JSON.stringify(structuredLog, null, 2))

    // In production, you would send this to:
    // - DataDog: await datadogClient.log(structuredLog)
    // - LogRocket: LogRocket.log(structuredLog)
    // - CloudWatch: await cloudWatchLogs.putLogEvents(...)
    // - Elasticsearch: await elasticClient.index(...)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Logging API Error:', error)
    return NextResponse.json(
      { error: 'Failed to process log entry' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Logging API is operational' },
    { status: 200 }
  )
}
