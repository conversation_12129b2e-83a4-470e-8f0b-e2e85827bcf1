"use client"

import dynamic from "next/dynamic"
import { ShimmerText } from "@/components/shimmer-text"
import { DocumentationLinks } from "@/components/documentation-links"
import { AdminAccess } from "@/components/admin-access"
import { ErrorBoundary } from "@/components/error-boundary"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { Loader2 } from "lucide-react"

// Temporarily disabled DevelopmentStakingRewards component
// const DevelopmentStakingRewards = dynamic(
//   () => import("@/components/development-staking-rewards").then(mod => ({ default: mod.DevelopmentStakingRewards })),
//   {
//     loading: () => (
//       <div className="flex items-center justify-center h-96 glass-card rounded-lg">
//         <div className="flex items-center gap-2 text-white/60">
//           <Loader2 className="h-6 w-6 animate-spin" />
//           Loading Development Staking...
//         </div>
//       </div>
//     ),
//     ssr: false,
//   }
// )

export default function DevelopmentStakingPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Development Staking</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Stake $PAW tokens to influence development priorities</p>
      </div>

      <UnifiedGovernanceNav />

      <ErrorBoundary>
        <div className="p-6 glass-card rounded-lg">
          <h1 className="text-2xl font-bold text-white mb-4">Development Staking Rewards</h1>
          <p className="text-white/60">This component is under development.</p>
        </div>
      </ErrorBoundary>

      <div className="mt-8">
        <DocumentationLinks
          links={[
            {
              title: "Development DAO Documentation",
              href: "/docs/development-dao",
              description: "Comprehensive guide to the development governance system",
            },
            {
              title: "Staking Guide",
              href: "/docs/development-dao#staking",
              description: "Learn about staking mechanics and rewards",
            },
          ]}
        />
      </div>

      <div className="mt-4">
        <AdminAccess />
      </div>
    </div>
  )
}
