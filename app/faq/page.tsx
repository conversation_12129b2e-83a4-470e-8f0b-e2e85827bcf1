"use client"

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ShimmerText } from "@/components/shimmer-text"

export default function FAQPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Frequently Asked Questions</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Find answers to common questions about PawPumps</p>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        <Card className="glass-card border-white/5 liquid-glow">
          <CardHeader>
            <CardTitle className="text-white">General Questions</CardTitle>
            <CardDescription className="text-white/70">Basic information about PawPumps</CardDescription>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">What is PawPumps?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  PawPumps is the premier memecoin launchpad and DEX on the Dogechain Network. It enables creators to
                  launch their own memecoins with ease, while providing traders with a secure and user-friendly
                  environment to trade and earn rewards.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">
                  What is the Dogechain Network?
                </AccordionTrigger>
                <AccordionContent className="text-white/80">
                  Dogechain Network is a Layer 2 EVM sidechain for Dogecoin, built on Polygon Edge CDK. It allows for
                  smart contract functionality and DeFi applications using wrapped Dogecoin (wDOGE) as the gas token.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-3" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">What is the $PAW token?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  $PAW is the governance and utility token of the PawPumps platform. It enables holders to participate
                  in governance decisions, earn rewards, and access premium features. A portion of all trading fees is
                  used to buy and burn $PAW tokens, creating deflationary pressure.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-4" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">How do I get started?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  To get started with PawPumps, you&apos;ll need to connect your wallet (MetaMask, WalletConnect, etc.) to
                  the Dogechain Network. Once connected, you can explore the platform's features, including token
                  creation, trading, and governance participation.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-5" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">Is PawPumps secure?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  PawPumps prioritizes security through multiple measures, including smart contract audits, liquidity
                  locking, anti-bot mechanisms, and transparent operations. However, as with any DeFi platform, users
                  should exercise caution and conduct their own research before participating.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5 liquid-glow">
          <CardHeader>
            <CardTitle className="text-white">Token Launch Questions</CardTitle>
            <CardDescription className="text-white/70">Information about creating memecoins</CardDescription>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">How do I create a memecoin?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  To create a memecoin, navigate to the Launch page, connect your wallet, and fill out the token
                  creation form. You'll need to provide a name, symbol, total supply, and select a bonding curve type.
                  Once you've configured your token, click "Launch Token" to deploy it to the Dogechain Network.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">What is a bonding curve?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  A bonding curve is a mathematical function that determines how a token's price changes as the supply
                  changes. PawPumps offers three types of bonding curves: Linear (price increases linearly with supply),
                  Exponential (price rises faster as supply diminishes), and Logarithmic (price stabilizes as supply
                  grows).
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-3" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">
                  How much does it cost to launch a token?
                </AccordionTrigger>
                <AccordionContent className="text-white/80">
                  Launching a token on PawPumps requires a small gas fee (approximately 0.1 wDOGE, or about $0.02) to
                  cover the cost of contract deployment on the Dogechain Network. There are no additional platform fees
                  for token creation.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-4" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">
                  What security features are included in my token?
                </AccordionTrigger>
                <AccordionContent className="text-white/80">
                  PawPumps automatically implements several security features in your token contract, including
                  liquidity locking to prevent rug pulls, anti-bot mechanisms to prevent front-running, and ownership
                  renunciation options for true decentralization.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-5" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">
                  How do I earn revenue from my token?
                </AccordionTrigger>
                <AccordionContent className="text-white/80">
                  As a token creator, you earn 0.25% of all trading volume generated by your token on the PawPumps DEX.
                  These fees are automatically distributed to your wallet and can be claimed through the platform.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5 liquid-glow">
          <CardHeader>
            <CardTitle className="text-white">Trading Questions</CardTitle>
            <CardDescription className="text-white/70">Information about trading on PawPumps</CardDescription>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">How do I swap tokens?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  To swap tokens, navigate to the Trade page, select the tokens you want to swap from the dropdown
                  menus, enter the amount you want to swap, and click the "Swap" button. Confirm the transaction in your
                  wallet to complete the swap.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">What are the trading fees?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  PawPumps charges a 0.5% fee on all trades. This fee is split between token creators (0.25%) and the
                  platform (0.25%), with a portion of the platform's share used to buy and burn $PAW tokens.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-3" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">How do I provide liquidity?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  To provide liquidity, navigate to the Trade page and select the "Liquidity" tab. Choose the token pair
                  you want to provide liquidity for, enter the amounts, and click "Add Liquidity." You'll receive LP
                  tokens representing your share of the pool, which automatically earn fees.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-4" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">What is perpetual trading?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  Perpetual trading allows you to trade with leverage, potentially amplifying your gains (and losses).
                  Unlike spot trading, perpetuals don't have an expiry date. You can go long (bet on price increases) or
                  short (bet on price decreases) with up to 100x leverage.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-5" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">What is slippage tolerance?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  Slippage tolerance is the maximum difference between the expected price of a trade and the price at
                  which the trade is executed that you're willing to accept. It accounts for price fluctuations that may
                  occur during the transaction processing time.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5 liquid-glow">
          <CardHeader>
            <CardTitle className="text-white">Rewards & Governance</CardTitle>
            <CardDescription className="text-white/70">Information about rewards and governance</CardDescription>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">How do I earn rewards?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  You can earn rewards on PawPumps in several ways: by providing liquidity and earning trading fees, by
                  staking $PAW tokens to earn staking rewards, by creating tokens and earning creator fees, and by
                  completing various platform activities to earn achievement rewards.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">What are achievements?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  Achievements are rewards for completing specific actions on the platform, such as making your first
                  trade, providing liquidity, or participating in governance. Each achievement comes with a reward in
                  $PAW tokens and contributes to your profile's status.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-3" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">
                  How do I participate in governance?
                </AccordionTrigger>
                <AccordionContent className="text-white/80">
                  To participate in governance, you need to stake your $PAW tokens. Once staked, you can vote on active
                  proposals or create your own proposals if you meet the minimum staking requirement. Your voting power
                  is proportional to the amount of $PAW you have staked.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-4" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">What can I vote on?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  Governance proposals can cover a wide range of topics, including platform fee adjustments, new feature
                  implementations, token listings, reward distribution changes, and technical upgrades. Any aspect of
                  the platform can potentially be modified through governance.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-5" className="border-white/10">
                <AccordionTrigger className="text-white hover:text-doge">How do I stake $PAW tokens?</AccordionTrigger>
                <AccordionContent className="text-white/80">
                  To stake $PAW tokens, navigate to the Governance page and use the staking interface in the sidebar.
                  Enter the amount you want to stake and confirm the transaction. Staked tokens earn rewards and grant
                  voting power, but they are locked for the duration of the staking period.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
