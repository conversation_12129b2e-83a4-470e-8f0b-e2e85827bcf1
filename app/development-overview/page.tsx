"use client"

import dynamic from "next/dynamic"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { Loader2 } from "lucide-react"

// Dynamic import for the large DevelopmentOverview component
const DevelopmentOverview = dynamic(
  () => import("@/components/development-overview").then(mod => ({ default: mod.DevelopmentOverview })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-96 glass-card rounded-lg">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-6 w-6 animate-spin" />
          Loading Development Overview...
        </div>
      </div>
    ),
    ssr: false,
  }
)

export default function DevelopmentOverviewPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Development Overview</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Comprehensive view of development progress and roadmap</p>
      </div>

      <UnifiedGovernanceNav />

      <DevelopmentOverview />
    </div>
  )
}
