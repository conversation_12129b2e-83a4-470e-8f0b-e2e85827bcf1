/* Arc Browser Specific Theme Fixes */
/* This file contains aggressive CSS fixes specifically for Arc Browser */

/* Arc Browser Detection and Fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  /* Target Webkit browsers including Arc */
  
  html {
    background: hsl(240, 10%, 4%) !important;
    background-color: hsl(240, 10%, 4%) !important;
  }
  
  html.dark,
  html[data-theme="dark"] {
    --background: 240 10% 4% !important;
    --foreground: 0 0% 88% !important;
    background: hsl(240, 10%, 4%) !important;
    background-color: hsl(240, 10%, 4%) !important;
    color: hsl(0, 0%, 88%) !important;
  }
  
  body {
    background: hsl(240, 10%, 4%) !important;
    background-color: hsl(240, 10%, 4%) !important;
    color: hsl(0, 0%, 88%) !important;
  }
  
  html.dark body,
  html[data-theme="dark"] body {
    background: hsl(240, 10%, 4%) !important;
    background-color: hsl(240, 10%, 4%) !important;
    color: hsl(0, 0%, 88%) !important;
  }
}

/* Additional Arc Browser specific selectors */
@supports (-webkit-appearance: none) and (not (-moz-appearance: none)) {
  /* This targets Webkit browsers but excludes Firefox */
  
  html,
  html.dark,
  html[data-theme="dark"] {
    background: hsl(240, 10%, 4%) !important;
    background-color: hsl(240, 10%, 4%) !important;
  }
  
  body,
  html.dark body,
  html[data-theme="dark"] body {
    background: hsl(240, 10%, 4%) !important;
    background-color: hsl(240, 10%, 4%) !important;
    color: hsl(0, 0%, 88%) !important;
  }
  
  /* Force all background elements to be dark */
  div[class*="bg-background"],
  section[class*="bg-background"],
  main[class*="bg-background"] {
    background-color: hsl(240, 10%, 4%) !important;
  }
}

/* Arc Browser User Agent specific fixes */
/* Arc Browser often identifies as Chrome but has unique behaviors */
html[data-arc-browser="true"],
html[data-browser="arc"] {
  background: hsl(240, 10%, 4%) !important;
  background-color: hsl(240, 10%, 4%) !important;
}

html[data-arc-browser="true"] body,
html[data-browser="arc"] body {
  background: hsl(240, 10%, 4%) !important;
  background-color: hsl(240, 10%, 4%) !important;
  color: hsl(0, 0%, 88%) !important;
}

/* Force dark theme on all possible selectors */
html:not([data-theme="light"]) {
  background: hsl(240, 10%, 4%) !important;
  background-color: hsl(240, 10%, 4%) !important;
}

html:not([data-theme="light"]) body {
  background: hsl(240, 10%, 4%) !important;
  background-color: hsl(240, 10%, 4%) !important;
  color: hsl(0, 0%, 88%) !important;
}

/* Override any potential light theme leakage */
html:not(.light):not([data-theme="light"]) {
  --background: 240 10% 4% !important;
  --foreground: 0 0% 88% !important;
  background: hsl(240, 10%, 4%) !important;
  background-color: hsl(240, 10%, 4%) !important;
}

html:not(.light):not([data-theme="light"]) body {
  background: hsl(240, 10%, 4%) !important;
  background-color: hsl(240, 10%, 4%) !important;
  color: hsl(0, 0%, 88%) !important;
}

/* Ensure root element styling */
#__next,
#root,
[data-testid="app-loaded"] {
  background-color: hsl(240, 10%, 4%) !important;
  color: hsl(0, 0%, 88%) !important;
}

/* Force dark theme on common layout elements */
.min-h-screen {
  background-color: hsl(240, 10%, 4%) !important;
}

/* Arc Browser sometimes ignores CSS custom properties, so use direct values */
.bg-background {
  background-color: hsl(240, 10%, 4%) !important;
}

.text-foreground {
  color: hsl(0, 0%, 88%) !important;
}

/* Additional safety net for Arc Browser */
* {
  --tw-bg-opacity: 1 !important;
}

html[data-theme="dark"] *:not([class*="bg-"]):not([style*="background"]) {
  background-color: transparent !important;
}
