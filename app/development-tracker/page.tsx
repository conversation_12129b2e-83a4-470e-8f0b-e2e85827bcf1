import { DevelopmentTracker } from "@/components/development-tracker"
import { DocumentationLinks } from "@/components/documentation-links"
import { ErrorBoundary } from "@/components/error-boundary"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"

export default function DevelopmentTrackerPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Development Tracker</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Track the progress of PawPumps development</p>
      </div>

      <UnifiedGovernanceNav />

      <ErrorBoundary>
        <DevelopmentTracker />
      </ErrorBoundary>
      <div className="mt-8">
        <DocumentationLinks
          links={[
            {
              title: "Development DAO Documentation",
              href: "/docs/development-dao",
              description: "Comprehensive guide to the development governance system",
            },
            {
              title: "Technical Documentation",
              href: "/docs/development-dao#technical",
              description: "Technical details of the development tracking system",
            },
          ]}
        />
      </div>
    </div>
  )
}
