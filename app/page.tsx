"use client"

import type React from "react"


import { HeroSection } from "@/components/hero-section"
import { StatsSection } from "@/components/stats-section"
import { BenefitsSection } from "@/components/benefits-section"
import { ScrollToTop } from "@/components/scroll-to-top"
import { ShimmerText } from "@/components/shimmer-text"
import Link from "next/link"
import { ArrowRight, Rocket, BarChart2, Shield, Zap } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

// AnimatedParticles moved to global layout for all pages

export default function HomePage() {
  return (
    <div className="relative min-h-screen">
      <HeroSection />
      <StatsSection />
      <BenefitsSection />

      {/* Features Section */}
      <section className="container py-16 md:py-24 relative">
        {/* Background effect */}
        <div className="absolute inset-0 bg-gradient-radial from-white/[0.02] to-transparent opacity-70"></div>

        <div className="mb-12 text-center relative z-10">
          <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl leading-relaxed pb-1">
            <ShimmerText>Redefining Memecoin Creation</ShimmerText>
          </h2>
          <p className="mx-auto mt-4 max-w-[700px] text-lg text-white/70">
            PawPumps combines cutting-edge technology with user-friendly design to make memecoin creation accessible to
            everyone
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 relative z-10">
          <FeatureCard
            icon={Rocket}
            title="No-Code Token Creation"
            description="Launch your memecoin in minutes with our intuitive form-based interface. No coding skills required."
          />
          <FeatureCard
            icon={BarChart2}
            title="Bonding Curve Pricing"
            description="Ensure fair token distribution with customizable bonding curves that define transparent pricing dynamics."
          />
          <FeatureCard
            icon={Shield}
            title="Enhanced Security"
            description="Built-in protection with liquidity locking, anti-bot mechanisms, and ownership renunciation options."
          />
          <FeatureCard
            icon={Zap}
            title="Ultra-Low Fees"
            description="Benefit from Dogechain's minimal gas fees, making token creation and trading affordable for everyone."
          />
          <FeatureCard
            icon={Shield}
            title="Community Governance"
            description="Shape the platform's future through DAO-based voting with your staked $PAW tokens."
          />
          <FeatureCard
            icon={BarChart2}
            title="Advanced Analytics"
            description="Track your token's performance with real-time charts, volume metrics, and holder statistics."
          />
        </div>
      </section>

      {/* CTA Section */}
      <section className="container py-16 md:py-24 relative">
        <div className="relative overflow-hidden rounded-xl glass-card border-white/5 liquid-glow p-8 md:p-12">
          <div className="relative z-10 text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              <ShimmerText>Ready to Launch Your Memecoin?</ShimmerText>
            </h2>
            <p className="mx-auto mb-8 max-w-[600px] text-lg text-white/70">
              Join thousands of creators who have successfully launched their memecoins on PawPumps. Start your journey
              today!
            </p>
            <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
              <Link
                href="/launch"
                className="pawpumps-button flex items-center justify-center gap-2 px-6 py-3 rounded-full text-white font-medium"
              >
                <span>Launch a Token</span>
              </Link>
              <Link
                href="/docs"
                className="glass-button flex items-center justify-center gap-2 px-6 py-3 rounded-full text-white font-medium"
              >
                <span>Read the Docs</span>
                <ArrowRight className="h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      <ScrollToTop />
    </div>
  )
}

function FeatureCard({
  icon: Icon,
  title,
  description,
}: {
  icon: React.ElementType
  title: string
  description: string
}) {
  return (
    <Card className="glass-card border-white/5 overflow-hidden transition-all duration-300 hover:border-white/10 liquid-glow">
      <CardHeader className="pb-2 flex flex-col items-center text-center">
        <div className="mb-2 inline-flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 text-black" aria-hidden="true">
          <Icon className="h-6 w-6" />
        </div>
        <CardTitle className="text-xl text-white">{title}</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <CardDescription className="text-base text-white/70">{description}</CardDescription>
      </CardContent>
    </Card>
  )
}
