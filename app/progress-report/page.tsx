"use client"

import dynamic from "next/dynamic"
import { ShimmerText } from "@/components/shimmer-text"
import { UnifiedGovernanceNav } from "@/components/unified-governance-nav"
import { Loader2 } from "lucide-react"

// Dynamic import for ProgressReport to reduce initial bundle size
const ProgressReport = dynamic(
  () => import("@/components/progress-report").then(mod => ({ default: mod.ProgressReport })),
  {
    loading: () => (
      <div className="flex items-center justify-center h-96">
        <div className="flex items-center gap-2 text-white/60">
          <Loader2 className="h-6 w-6 animate-spin" />
          Loading Progress Report...
        </div>
      </div>
    ),
    ssr: false,
  }
)

export default function ProgressReportPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Progress Report</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">Detailed metrics on development progress and activity</p>
      </div>

      <UnifiedGovernanceNav />

      <ProgressReport />
    </div>
  )
}
