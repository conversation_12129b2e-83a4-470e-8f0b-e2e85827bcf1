import type { Metadata } from 'next'
import { TradePageClient } from './trade-page-client'

export const metadata: Metadata = {
  title: "Trade Memecoins | PawPumps - Dogechain DEX",
  description: "Trade memecoins on PawPumps DEX with real-time charts, low fees, and instant swaps. Access DOGE, SHIB, PEPE, and more on Dogechain Network.",
  keywords: ['trade', 'memecoin', 'DEX', 'swap', 'dogechain', 'DOGE', 'SHIB', 'PEPE', 'trading'],
  openGraph: {
    title: "Trade Memecoins | PawPumps DEX",
    description: "Trade memecoins with real-time charts and low fees on PawPumps DEX",
    url: 'https://pawpumps.com/trade',
    images: ['/images/pawpumps-trade-og.png'],
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Trade Memecoins | PawPumps DEX",
    description: "Trade memecoins with real-time charts and low fees",
    images: ['/images/pawpumps-trade-og.png'],
  },
}

export default function TradePage() {
  return <TradePageClient />
}
