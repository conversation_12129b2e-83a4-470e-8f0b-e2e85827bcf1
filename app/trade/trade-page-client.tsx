"use client"

import dynamic from 'next/dynamic'

const TradingInterface = dynamic(() => import("@/components/dynamic-imports").then(mod => ({ default: mod.TradingInterface })), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-64">Loading trading interface...</div>
})

const TradingErrorBoundary = dynamic(() => import("@/components/error-boundaries/trading-error-boundary").then(mod => ({ default: mod.TradingErrorBoundary })), {
  ssr: false
})

const ShimmerText = dynamic(() => import("@/components/shimmer-text").then(mod => ({ default: mod.ShimmerText })), {
  ssr: false
})

const StandardizedTicker = dynamic(() => import("@/components/standardized-ticker").then(mod => ({ default: mod.StandardizedTicker })), {
  ssr: false,
  loading: () => <div className="h-12 bg-black/50 animate-pulse" />
})

export function TradePageClient() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Standardized Ticker with Custom Speed */}
      <StandardizedTicker scrollSpeed={30} />

      <div className="container py-8 md:py-12 flex-1">
        <div className="mb-8">
          <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
            <ShimmerText>Trade Memecoins</ShimmerText>
          </h1>
          <p className="text-lg text-white/70">Swap, provide liquidity, and track your favorite memecoins</p>
        </div>

        <TradingErrorBoundary>
          <TradingInterface />
        </TradingErrorBoundary>
      </div>
    </div>
  )
}
