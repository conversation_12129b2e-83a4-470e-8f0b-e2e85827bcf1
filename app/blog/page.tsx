"use client"

import { ShimmerText } from "@/components/shimmer-text"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import { ArrowRight, Calendar, User } from "lucide-react"

const blogPosts = [
  {
    id: 1,
    title: "Introducing PawPumps: The Future of Dogecoin Ecosystem",
    excerpt:
      "Discover how PawPumps is revolutionizing the Dogechain ecosystem with innovative memecoin launchpad and DEX technology.",
    imageUrl: "/placeholder.svg?key=rrp8f",
    date: "May 1, 2025",
    author: {
      name: "<PERSON><PERSON>",
      avatar: "/placeholder.svg?key=n6hx7",
    },
    category: "Announcement",
    slug: "introducing-pawpumps",
  },
  {
    id: 2,
    title: "Understanding Bonding Curves: The Math Behind PawPumps",
    excerpt:
      "A deep dive into the mathematics of bonding curves and how they create fair launch conditions for new memecoins.",
    imageUrl: "/placeholder.svg?key=o5le4",
    date: "May 3, 2025",
    author: {
      name: "Crypto Mathematician",
      avatar: "/placeholder.svg?key=qud1f",
    },
    category: "Education",
    slug: "understanding-bonding-curves",
  },
  {
    id: 3,
    title: "The Development DAO: Community-Driven Progress",
    excerpt:
      "How our Development DAO is changing the way crypto projects evolve with transparent governance and incentivized participation.",
    imageUrl: "/placeholder.svg?key=l3v61",
    date: "May 5, 2025",
    author: {
      name: "DAO Expert",
      avatar: "/placeholder.svg?height=40&width=40&query=blockchain developer",
    },
    category: "Governance",
    slug: "development-dao-community-driven-progress",
  },
  {
    id: 4,
    title: "Roadmap 2025: What's Next for PawPumps",
    excerpt:
      "Explore our ambitious plans for the coming year, including new features, partnerships, and ecosystem expansion.",
    imageUrl: "/placeholder.svg?height=400&width=800&query=roadmap strategy future",
    date: "May 7, 2025",
    author: {
      name: "PawPumps Team",
      avatar: "/placeholder.svg?height=40&width=40&query=developer team",
    },
    category: "Roadmap",
    slug: "roadmap-2025",
  },
  {
    id: 5,
    title: "Tokenomics Deep Dive: PAWP Distribution and Utility",
    excerpt:
      "A comprehensive analysis of PAWP token distribution, utility, and how it powers the entire PawPumps ecosystem.",
    imageUrl: "/placeholder.svg?height=400&width=800&query=cryptocurrency tokens",
    date: "May 10, 2025",
    author: {
      name: "Tokenomics Analyst",
      avatar: "/placeholder.svg?height=40&width=40&query=financial analyst",
    },
    category: "Tokenomics",
    slug: "tokenomics-deep-dive",
  },
  {
    id: 6,
    title: "Community Spotlight: Top Projects Launched on PawPumps",
    excerpt:
      "Highlighting the most successful and innovative memecoin projects that have launched through our platform.",
    imageUrl: "/placeholder.svg?height=400&width=800&query=successful projects launch",
    date: "May 15, 2025",
    author: {
      name: "Community Manager",
      avatar: "/placeholder.svg?height=40&width=40&query=community manager",
    },
    category: "Community",
    slug: "community-spotlight-top-projects",
  },
]

const categories = [
  "All Categories",
  "Announcement",
  "Education",
  "Governance",
  "Roadmap",
  "Tokenomics",
  "Community",
  "Technical",
]

export default function BlogPage() {
  return (
    <div className="container py-8 md:py-12">
      <div className="mb-12 text-center">
        <h1 className="mb-3 text-4xl font-bold tracking-tight sm:text-5xl">
          <ShimmerText>PawPumps Blog</ShimmerText>
        </h1>
        <p className="mx-auto max-w-2xl text-lg text-white/70">
          Insights, updates, and education from the premier memecoin launchpad on Dogechain
        </p>
      </div>

      <div className="mb-8 flex flex-wrap justify-center gap-2">
        {categories.map((category) => (
          <Button
            key={category}
            variant={category === "All Categories" ? "default" : "outline"}
            className={category === "All Categories" ? "bg-doge text-black" : "border-white/20 bg-black/20"}
            size="sm"
          >
            {category}
          </Button>
        ))}
      </div>

      {/* Featured Post */}
      <div className="mb-12">
        <Card className="overflow-hidden border-0 bg-gradient-to-br from-black/40 to-black/60 backdrop-blur-sm">
          <div className="grid md:grid-cols-2">
            <div className="relative aspect-video md:aspect-auto">
              <Image
                src={blogPosts[0].imageUrl || "/placeholder.svg"}
                alt={blogPosts[0].title}
                fill
                className="object-cover"
              />
            </div>
            <div className="p-6 md:p-8">
              <div className="mb-4 flex items-center gap-2 text-sm text-white/60">
                <span className="inline-flex items-center rounded-full bg-doge/20 px-2.5 py-0.5 text-xs font-medium text-doge">
                  {blogPosts[0].category}
                </span>
                <span className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {blogPosts[0].date}
                </span>
                <span className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {blogPosts[0].author.name}
                </span>
              </div>
              <h2 className="mb-4 text-2xl font-bold text-white md:text-3xl">{blogPosts[0].title}</h2>
              <p className="mb-6 text-white/70">{blogPosts[0].excerpt}</p>
              <Link href={`/blog/${blogPosts[0].slug}`}>
                <Button className="bg-doge text-black hover:bg-doge/90">
                  Read Article
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </Card>
      </div>

      {/* Blog Posts Grid */}
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {blogPosts.slice(1).map((post) => (
          <Card
            key={post.id}
            className="overflow-hidden border-white/5 bg-black/20 transition-all hover:border-doge/30 hover:bg-black/40"
          >
            <div className="relative aspect-video">
              <Image src={post.imageUrl || "/placeholder.svg"} alt={post.title} fill className="object-cover" />
            </div>
            <CardHeader className="pb-2">
              <div className="mb-2 flex items-center gap-2 text-xs text-white/60">
                <span className="inline-flex items-center rounded-full bg-doge/20 px-2.5 py-0.5 text-xs font-medium text-doge">
                  {post.category}
                </span>
                <span className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {post.date}
                </span>
              </div>
              <CardTitle className="line-clamp-2 text-lg">{post.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="line-clamp-3 text-white/70">{post.excerpt}</CardDescription>
            </CardContent>
            <CardFooter className="flex items-center justify-between border-t border-white/5 pt-4">
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={post.author.avatar || "/placeholder.svg"} alt={post.author.name} />
                  <AvatarFallback>{post.author.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <span className="text-xs text-white/60">{post.author.name}</span>
              </div>
              <Link href={`/blog/${post.slug}`}>
                <Button variant="ghost" size="sm" className="text-doge hover:bg-doge/10">
                  Read More
                </Button>
              </Link>
            </CardFooter>
          </Card>
        ))}
      </div>

      <div className="mt-12 flex justify-center">
        <Button variant="outline" className="border-white/20 bg-black/20">
          Load More Articles
        </Button>
      </div>
    </div>
  )
}
