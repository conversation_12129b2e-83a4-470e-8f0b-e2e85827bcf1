import type { <PERSON>ada<PERSON>, View<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { WalletProvider } from "@/components/wallet-provider"
import { NotificationProvider } from "@/components/notification-provider-simple"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { GlobalErrorBoundary } from "@/components/error-boundaries/global-error-boundary"
import { PerformanceDashboardOptimized } from "@/components/monitoring/performance-dashboard-optimized"
import { DevelopmentOptimizer } from "@/components/development-optimizer"
import { GlobalBackground } from "@/components/global-background"
import { FeedbackButton } from "@/components/user-feedback/feedback-button"
import { StagewiseClient } from "@/components/stagewise-client";

// Initialize monitoring system - ensure this is imported before any other app code
import "@/lib/monitoring/init"

// Optimize font loading with specific weights and subsets
const inter = Inter({
  subsets: ["latin"],
  display: 'swap',
  weight: ['400', '500', '600', '700'],
  variable: '--font-inter',
  adjustFontFallback: false,
  preload: true,
})

// SEO and metadata configuration
export const metadata: Metadata = {
  metadataBase: new URL('https://pawpumps.com'),
  title: {
    default: "PawPumps - Premier Memecoin Launchpad & DEX",
    template: "%s | PawPumps"
  },
  description: "Launch and trade memecoins on the Dogechain Network with PawPumps - the sleek, professional-grade platform for memecoin creators, traders, and enthusiasts.",
  keywords: ['memecoin', 'launchpad', 'DEX', 'dogechain', 'cryptocurrency', 'trading', 'DeFi'],
  authors: [{ name: 'PawPumps Team' }],
  robots: 'index, follow',
  openGraph: {
    title: "PawPumps - Premier Memecoin Launchpad & DEX",
    description: "Launch and trade memecoins on the Dogechain Network with PawPumps",
    url: 'https://pawpumps.com',
    siteName: 'PawPumps',
    images: [
      {
        url: '/images/pawpumps-og-image.png',
        width: 1200,
        height: 630,
        alt: 'PawPumps - Premier Memecoin Launchpad & DEX',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "PawPumps - Premier Memecoin Launchpad & DEX",
    description: "Launch and trade memecoins on the Dogechain Network",
    images: ['/images/pawpumps-og-image.png'],
    creator: '@pawpumps',
    site: '@pawpumps',
  },
  other: {
    'theme-color': '#D4AF37',
  }
}

// Viewport configuration
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#000000' },
  ],
  viewportFit: 'cover'
}

interface RootLayoutProps {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html 
      lang="en" 
      suppressHydrationWarning 
      className={`${inter.variable} h-full`}
    >
      <head>
        {/* Preload critical resources */}
        <link 
          rel="preload" 
          href="/_next/static/css/app/layout.css" 
          as="style" 
        />
        <link 
          rel="stylesheet" 
          href="/_next/static/css/app/layout.css" 
        />
        
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link 
          rel="preconnect" 
          href="https://fonts.gstatic.com" 
          crossOrigin="anonymous" 
        />
        
        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* Performance optimization script */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Performance optimization utilities
              function optimizePageLoad() {
                // Preload critical fonts
                const criticalFonts = [
                  'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
                ];

                criticalFonts.forEach(href => {
                  const link = document.createElement('link');
                  link.rel = 'preload';
                  link.href = href;
                  link.as = 'style';
                  link.onload = function() { this.rel = 'stylesheet'; };
                  document.head.appendChild(link);
                });
              }

              // Optimize when the page is idle
              if (window.requestIdleCallback) {
                window.requestIdleCallback(optimizePageLoad);
              } else {
                // Fallback for browsers that don't support requestIdleCallback
                window.addEventListener('load', () => {
                  setTimeout(optimizePageLoad, 1000);
                });
              }
            `,
          }}
        />
      </head>
      
      <body className="min-h-screen bg-background font-sans antialiased">
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem={false}
          disableTransitionOnChange
        >
          <DevelopmentOptimizer />
          <NotificationProvider>
            <WalletProvider>
              <GlobalErrorBoundary>
                <div className="flex flex-col min-h-screen relative" data-testid="app-loaded">
                  <GlobalBackground />
                  <Navbar />
                  <main className="flex-1 relative z-10">
                    {children}
                                        {process.env.NEXT_PUBLIC_NODE_ENV === 'development' && <StagewiseClient />}
                  </main>
                  <Footer />
                </div>
                <FeedbackButton />
                <PerformanceDashboardOptimized />
              </GlobalErrorBoundary>
            </WalletProvider>
          </NotificationProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
