import { Suspense } from "react"
import { ShimmerText } from "@/components/shimmer-text"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { TokenPerformanceSSR } from "@/components/token-performance-ssr"
import { LoadingState } from "@/components/ui/loading-state"

// This is a Server Component that fetches data on the server
export default async function SSRExamplePage() {
  // In a real app, this would be a database or API call
  const pageData = await fetchPageData()

  return (
    <div className="container py-8 md:py-12">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
          <ShimmerText>Server-Side Rendering</ShimmerText>
        </h1>
        <p className="text-lg text-white/70">
          This page demonstrates server-side rendering for improved performance and SEO
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle>Server-Side Rendered Content</CardTitle>
            <CardDescription>This content was rendered on the server</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p>Page generated at: {pageData.timestamp}</p>
              <p>Server environment: {pageData.environment}</p>
              <div className="grid gap-2">
                <h3 className="text-sm font-medium">Top Tokens:</h3>
                <ul className="list-disc list-inside space-y-1">
                  {pageData.topTokens.map((token) => (
                    <li key={token.id}>
                      {token.name} ({token.symbol}) - ${token.price.toFixed(4)}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-6">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle>Streaming Server Components</CardTitle>
              <CardDescription>Data streams in progressively with Suspense</CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<LoadingState title="Loading token performance data..." description="Fetching the latest performance metrics." />}>
                <TokenPerformanceSSR />
              </Suspense>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle>Benefits of SSR</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="list-disc list-inside space-y-2">
                <li>Faster initial page load and First Contentful Paint (FCP)</li>
                <li>Improved SEO with fully rendered content for search engines</li>
                <li>Better performance on low-powered devices</li>
                <li>Reduced client-side JavaScript bundle size</li>
                <li>Progressive enhancement for better user experience</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

// Mock data fetching function (in a real app, this would be a database or API call)
async function fetchPageData() {
  // Only simulate delay in development, not during build
  if (process.env.NODE_ENV === 'development') {
    await new Promise((resolve) => setTimeout(resolve, 100))
  }

  return {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
    topTokens: [
      { id: 1, name: "DogeCoin", symbol: "DOGE", price: 0.1234 },
      { id: 2, name: "ShibaInu", symbol: "SHIB", price: 0.00002345 },
      { id: 3, name: "PawPumps", symbol: "PAW", price: 0.5678 },
      { id: 4, name: "DogeKing", symbol: "DOGEK", price: 0.0345 },
      { id: 5, name: "CatCoin", symbol: "CAT", price: 0.2468 },
    ],
  }
}
