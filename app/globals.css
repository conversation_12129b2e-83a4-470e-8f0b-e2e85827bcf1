@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 0 0% 98%;
    --radius: 0.5rem;
    --doge: 45 100% 50%;
    --dogechain: 270 60% 50%;
    --chart-1: 45 100% 50%;
    --chart-2: 270 60% 50%;
    --chart-3: 0 0% 98%;
    --chart-4: 210 100% 50%;
    --chart-5: 120 100% 50%;
  }

  .dark {
    --background: 240 10% 4%;
    --foreground: 0 0% 88%;
    --card: 240 10% 6%;
    --card-foreground: 0 0% 88%;
    --popover: 240 10% 6%;
    --popover-foreground: 0 0% 88%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 10%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --doge: 45 100% 50%;
    --dogechain: 270 60% 50%;
    --chart-1: 45 100% 50%;
    --chart-2: 270 60% 50%;
    --chart-3: 0 0% 98%;
    --chart-4: 210 100% 50%;
    --chart-5: 120 100% 50%;
  }

  /* Arc Browser specific fixes - Arc has unique CSS handling */
  html.dark,
  html[data-theme="dark"],
  [data-theme="dark"] {
    --background: 240 10% 4% !important;
    --foreground: 0 0% 88% !important;
    background-color: hsl(240, 10%, 4%) !important;
    color: hsl(0, 0%, 88%) !important;
  }

  /* Force dark theme for Arc Browser and Chromium variants */
  @supports (-webkit-appearance: none) {
    html.dark,
    html[data-theme="dark"] {
      background: hsl(240, 10%, 4%) !important;
      background-color: hsl(240, 10%, 4%) !important;
    }

    html.dark body,
    html[data-theme="dark"] body,
    [data-theme="dark"] body {
      background: hsl(240, 10%, 4%) !important;
      background-color: hsl(240, 10%, 4%) !important;
      color: hsl(0, 0%, 88%) !important;
    }
  }

  /* Arc Browser specific user agent detection */
  @media screen and (-webkit-min-device-pixel-ratio: 0) {
    html.dark,
    html[data-theme="dark"] {
      background: hsl(240, 10%, 4%) !important;
    }

    html.dark body,
    html[data-theme="dark"] body {
      background: hsl(240, 10%, 4%) !important;
    }
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html,
  body {
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
  }

  body {
    @apply bg-background text-foreground;
    overflow-y: auto !important;
    /* Ensure consistent background across all browsers */
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  /* Additional browser compatibility fixes */
  * {
    box-sizing: border-box;
  }

  /* Webkit-specific optimizations */
  @supports (-webkit-appearance: none) {
    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }

  /* Firefox-specific optimizations */
  @-moz-document url-prefix() {
    body {
      background-color: hsl(var(--background)) !important;
    }
  }
}

/* Dogecoin and Dogechain colors */
.text-doge {
  color: hsl(var(--doge));
}

.bg-doge {
  background-color: hsl(var(--doge));
}

.text-dogechain {
  color: hsl(var(--dogechain));
}

.bg-dogechain {
  background-color: hsl(var(--dogechain));
}

.doge-gradient-text {
  background: linear-gradient(to right, hsl(var(--doge)), hsl(45, 100%, 70%));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.dogechain-gradient-text {
  background: linear-gradient(to right, hsl(var(--dogechain)), hsl(270, 60%, 70%));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.doge-glow {
  box-shadow: 0 0 15px rgba(255, 193, 7, 0.1);
}

.dogechain-glow {
  box-shadow: 0 0 15px rgba(138, 43, 226, 0.1);
}

.doge-text-glow {
  text-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

.dogechain-text-glow {
  text-shadow: 0 0 10px rgba(138, 43, 226, 0.5);
}

.doge-button {
  background: linear-gradient(135deg, hsl(var(--doge)), hsl(45, 100%, 45%));
  color: #000;
  font-weight: 500;
  border: none;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
  transition: all 0.3s ease;
}

.doge-button:hover {
  background: linear-gradient(135deg, hsl(45, 100%, 55%), hsl(45, 100%, 50%));
  box-shadow: 0 6px 16px rgba(255, 193, 7, 0.4);
  transform: translateY(-1px);
}

.dogechain-button {
  background: linear-gradient(135deg, hsl(var(--dogechain)), hsl(270, 60%, 45%));
  color: #fff;
  font-weight: 500;
  border: none;
  box-shadow: 0 4px 12px rgba(138, 43, 226, 0.3);
  transition: all 0.3s ease;
}

.dogechain-button:hover {
  background: linear-gradient(135deg, hsl(270, 60%, 55%), hsl(270, 60%, 50%));
  box-shadow: 0 6px 16px rgba(138, 43, 226, 0.4);
  transform: translateY(-1px);
}

/* Custom button styles for Launch and Trade */
.launch-button,
.trade-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-decoration: none;
  min-width: 180px;
}

.launch-button {
  background: linear-gradient(135deg, #ffc107, #ff9800);
  color: #000;
  box-shadow: 0 0 20px rgba(255, 193, 7, 0.4);
  border: 2px solid rgba(255, 193, 7, 0.6);
}

.trade-button {
  background: rgba(20, 20, 25, 0.7);
  color: #fff;
  box-shadow: 0 0 15px rgba(156, 39, 176, 0.3);
  border: 2px solid;
  border-image: linear-gradient(45deg, #ffc107, #9c27b0) 1;
}

.launch-button::before,
.trade-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.launch-button:hover::before,
.trade-button:hover::before {
  left: 100%;
}

.launch-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 0 25px rgba(255, 193, 7, 0.6);
}

.trade-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 0 20px rgba(156, 39, 176, 0.4);
}

.rocket-icon,
.chart-icon {
  margin-right: 0.5rem;
  font-size: 1.25rem;
}

.arrow-icon {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.launch-button:hover .arrow-icon,
.trade-button:hover .arrow-icon {
  transform: translateX(3px);
}

.button-text {
  position: relative;
  z-index: 1;
}

/* PawPumps specific styles */
.pawpumps-text {
  background: linear-gradient(to right, #ffc107, #ffd54f);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 0 10px rgba(255, 193, 7, 0.3);
}

/* Button styles */
.pawpumps-button {
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 4px 14px rgba(255, 193, 7, 0.3);
  border: none;
}

.pawpumps-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

.pawpumps-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(255, 193, 7, 0.2);
}

.pawpumps-outline-button {
  background: transparent;
  border: 2px solid;
  border-image: linear-gradient(45deg, #ffc107, #ff9800) 1;
  color: #fff;
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: 0 0 10px rgba(255, 193, 7, 0.2);
  transition: all 0.3s ease;
}

.pawpumps-outline-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
  opacity: 0;
  transition: all 0.3s ease;
  z-index: -1;
}

.pawpumps-outline-button:hover::after {
  opacity: 1;
}

.pawpumps-outline-button:hover {
  box-shadow: 0 0 15px rgba(255, 193, 7, 0.4);
  transform: translateY(-2px);
}

/* Glass morphism effects */
.glass {
  background: rgba(15, 15, 20, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

.glass-card {
  background: linear-gradient(135deg, rgba(20, 20, 25, 0.7), rgba(10, 10, 15, 0.8));
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

.glass-card-solid {
  background: linear-gradient(135deg, rgb(20, 20, 25), rgb(10, 10, 15));
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

.mobile-menu-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: red !important;
  z-index: 999999 !important;
  overflow-y: auto !important;
  display: block !important;
  width: 100vw !important;
  height: 100vh !important;
}

@media (min-width: 768px) {
  .mobile-menu-overlay {
    display: none !important;
  }
}

.glass-input {
  background: rgba(15, 15, 20, 0.3);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.glass-button {
  background: linear-gradient(135deg, rgba(40, 40, 50, 0.7), rgba(20, 20, 30, 0.9));
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.9);
}

.glass-button:hover {
  background: linear-gradient(135deg, rgba(50, 50, 60, 0.8), rgba(30, 30, 40, 0.9));
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.4);
  transform: translateY(-1px);
  color: rgba(255, 255, 255, 1);
}

/* Liquid animations */
.liquid-glow {
  position: relative;
  overflow: hidden;
}

.liquid-glow::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 20%,
    transparent 60%
  );
  transform: rotate(0deg);
  animation: rotate 15s linear infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.liquid-shine {
  position: relative;
  overflow: hidden;
}

.liquid-shine::after {
  content: "";
  position: absolute;
  top: -100%;
  left: -100%;
  width: 50%;
  height: 300%;
  background: linear-gradient(to right, transparent 0%, rgba(255, 255, 255, 0.05) 50%, transparent 100%);
  transform: rotate(25deg);
  animation: shine 6s ease-in-out infinite;
  pointer-events: none;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

/* Futuristic border glow */
.border-glow {
  position: relative;
  z-index: 1;
}

.border-glow::before {
  content: "";
  position: absolute;
  inset: -1px;
  z-index: -1;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05) 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 75%,
    rgba(255, 255, 255, 0.1)
  );
  border-radius: inherit;
  animation: borderGlow 8s linear infinite;
}

@keyframes borderGlow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 200%;
  }
}

/* Subtle text glow */
.text-glow {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 255, 255, 0.3);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(200, 200, 220, 0.7));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Gradient background */
.gradient-bg {
  background: linear-gradient(135deg, rgba(40, 40, 50, 0.8), rgba(20, 20, 30, 0.9));
}

/* Animated background */
.animated-bg {
  position: relative;
}

.animated-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(40, 40, 60, 0.3) 0%,
    rgba(20, 20, 30, 0.6) 50%,
    rgba(10, 10, 15, 0.8) 100%
  );
  z-index: -1;
  animation: pulse 15s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.5;
  }
}

/* Responsive utilities */
@media (max-width: 640px) {
  .glass-card {
    padding: 1rem;
  }

  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .text-3xl {
    font-size: 1.75rem;
  }

  .text-2xl {
    font-size: 1.5rem;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .liquid-glow::before {
    opacity: 0.5;
  }

  .doge-button,
  .dogechain-button {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .glass-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Touch device optimizations */
@media (hover: none) {
  .hover\:bg-white\/10:hover {
    background-color: transparent;
  }

  .hover\:text-doge:hover {
    color: inherit;
  }

  .glass-button:active,
  .doge-button:active {
    transform: translateY(1px);
  }
}

/* New cosmic button style with black glass and gold accents */
.cosmic-button,
button.cosmic-button {
  position: relative !important;
  background: rgba(15, 15, 20, 0.7) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 193, 7, 0.3) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(255, 193, 7, 0.15), inset 0 0 10px rgba(255, 193, 7, 0.05) !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
}

.cosmic-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 193, 7, 0.2), transparent);
  transition: all 0.6s ease;
}

.cosmic-button::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgba(255, 193, 7, 0.1) 0%, transparent 60%);
  opacity: 0;
  transition: opacity 0.6s ease;
  transform: rotate(0deg);
  animation: rotate 15s linear infinite;
  pointer-events: none;
  z-index: 0;
}

.cosmic-button:hover,
button.cosmic-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 25px rgba(255, 193, 7, 0.25), inset 0 0 15px rgba(255, 193, 7, 0.1) !important;
  border-color: rgba(255, 193, 7, 0.5) !important;
  background: rgba(15, 15, 20, 0.7) !important;
}

.cosmic-button:hover::before {
  left: 100%;
}

.cosmic-button:hover::after {
  opacity: 1;
}

.cosmic-button:active,
button.cosmic-button:active {
  transform: translateY(1px) !important;
  box-shadow: 0 2px 10px rgba(255, 193, 7, 0.15), inset 0 0 5px rgba(255, 193, 7, 0.05) !important;
}

.cosmic-button span {
  position: relative;
  z-index: 1;
  background: linear-gradient(to right, #ffc107, #ffd54f);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.cosmic-button svg {
  position: relative;
  z-index: 1;
  color: #ffc107;
  transition: transform 0.3s ease;
}

.cosmic-button:hover svg {
  transform: translateX(3px);
}

/* Animation for floating elements */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Completely new shimmer text implementation */
.shimmer-text {
  position: relative;
  display: inline-block;
  color: #ffc107; /* Fallback color */
}

/* Base gradient text */
.shimmer-text {
  background: linear-gradient(to right, #ffc107, #ffd54f);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Shimmer overlay */
.shimmer-text::after {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: shimmer 3s infinite;
  background-size: 200% 100%;
  background-position: 100% 0;
}

@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

/* Ticker scroll animation */
@keyframes scroll {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(-50%, 0, 0);
  }
}

.animate-scroll {
  animation: scroll 60s linear infinite;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.animate-scroll:hover {
  animation-play-state: paused;
}

/* Flip animation for sorting icons */
@keyframes flip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(180deg); }
  100% { transform: rotateY(360deg); }
}

.animate-flip {
  animation: flip 2s ease-in-out infinite;
}

/* Swap arrow rotation */
.group-active\:rotate-360 {
  transform: rotate(360deg);
}

/* Volume Bars - Final Styles */
.volume-bar {
  shape-rendering: crispEdges;
  outline: none !important;
}

/* Style the rectangle elements */
.recharts-bar-rectangle {
  fill: url(#volumeBarGradient) !important;
  stroke: none !important;
  shape-rendering: crispEdges;
  transition: opacity 0.15s ease;
  pointer-events: auto !important;
  outline: none !important;
}

/* Hover state - prevent flicker */
.recharts-bar-rectangle:hover {
  opacity: 0.8 !important;
  transition: none !important;
}

/* Tooltip styles */
.recharts-tooltip-wrapper {
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.8) !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
}

.recharts-tooltip-label,
.recharts-tooltip-item-name,
.recharts-tooltip-item-value,
.recharts-tooltip-item-separator {
  color: white !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.recharts-tooltip-item {
  margin: 0 !important;
  padding: 0 !important;
}

/* Remove default hover effects */
.recharts-tooltip-cursor {
  display: none !important;
}

/* Chart container styles */
.recharts-surface,
.recharts-wrapper {
  background: transparent !important;
  outline: none !important;
}

.recharts-wrapper:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Make sure the bars are not hidden */
.recharts-bar {
  shape-rendering: crispEdges;
  outline: none !important;
}

/* Ensure the bars are above other elements */
.recharts-bar-rectangles {
  z-index: 1;
}

/* Remove focus outline */
.volume-bar:focus,
.volume-bar:active,
.volume-bar:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}

/* Accessibility enhancements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* High contrast mode */
.high-contrast {
  --background: 0 0% 0%;
  --foreground: 0 0% 100%;
  --card: 0 0% 10%;
  --card-foreground: 0 0% 100%;
  --border: 0 0% 100%;
  --input: 0 0% 20%;
  --primary: 0 0% 100%;
  --primary-foreground: 0 0% 0%;
  --secondary: 0 0% 30%;
  --secondary-foreground: 0 0% 100%;
  --accent: 0 0% 40%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 100% 50%;
  --destructive-foreground: 0 0% 100%;
  --ring: 0 0% 100%;
}

.high-contrast .glass-card {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 2px solid #ffffff !important;
}

.high-contrast .doge-button {
  background: #ffffff !important;
  color: #000000 !important;
  border: 2px solid #ffffff !important;
}

/* Reduced motion */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Large text */
.large-text {
  font-size: 1.125em;
}

.large-text h1 {
  font-size: 2.5rem;
}

.large-text h2 {
  font-size: 2rem;
}

.large-text h3 {
  font-size: 1.5rem;
}

/* Extra large text */
.extra-large-text {
  font-size: 1.25em;
}

.extra-large-text h1 {
  font-size: 3rem;
}

.extra-large-text h2 {
  font-size: 2.5rem;
}

.extra-large-text h3 {
  font-size: 2rem;
}

/* Enhanced focus indicators */
*:focus {
  outline: 2px solid #ffd700 !important;
  outline-offset: 2px !important;
}

button:focus,
a:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #ffd700 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.3) !important;
}

/* Skip link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #ffd700;
  color: #000000;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  font-weight: bold;
}

.skip-link:focus {
  top: 6px;
}
