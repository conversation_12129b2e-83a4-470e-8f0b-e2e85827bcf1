"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { OptimizedTokenLaunchForm } from "@/components/optimized-token-launch-form"
import { ResponsiveForm } from "@/components/responsive-form"
import { PerformanceMonitor } from "@/components/performance-monitor"
import { SkipLink } from "@/components/a11y/skip-link"
import { FocusTrap } from "@/components/a11y/focus-trap"
import { useResponsive } from "@/hooks/use-responsive"
import { required, isEmail, minLength } from "@/utils/form-validation"
import { ShimmerText } from "@/components/shimmer-text"

export default function OptimizedDemoPage() {
  const { isMobile, breakpoint, width } = useResponsive()
  const [activeTab, setActiveTab] = useState("overview")

  const contactFormFields = [
    {
      name: "name",
      label: "Full Name",
      type: "text",
      placeholder: "Enter your full name",
      required: true,
      autoComplete: "name",
    },
    {
      name: "email",
      label: "Email Address",
      type: "email",
      placeholder: "Enter your email address",
      required: true,
      autoComplete: "email",
    },
    {
      name: "subject",
      label: "Subject",
      type: "text",
      placeholder: "What is this regarding?",
      required: true,
    },
    {
      name: "message",
      label: "Message",
      type: "text",
      placeholder: "Your message",
      required: true,
    },
  ]

  const contactFormValidations = {
    name: [required(), minLength(3, "Name must be at least 3 characters")],
    email: [required(), isEmail()],
    subject: [required()],
    message: [required(), minLength(10, "Message must be at least 10 characters")],
  }

  const handleContactSubmit = async (values: Record<string, any>) => {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500))
    console.log("Form submitted:", values)
  }

  return (
    <div className="container py-8 md:py-12">
      <SkipLink />

      <div id="main-content" tabIndex={-1} className="outline-none">
        <div className="mb-8">
          <h1 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl">
            <ShimmerText>Optimized Components Demo</ShimmerText>
          </h1>
          <p className="text-lg text-white/70">
            Showcasing mobile responsiveness, form validation, accessibility, and performance optimizations
          </p>

          <div className="mt-4 p-4 bg-white/5 rounded-md border border-white/10">
            <h2 className="text-lg font-medium text-white mb-2">Current Device Information</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-white/60">Breakpoint</p>
                <p className="text-white font-medium">{breakpoint}</p>
              </div>
              <div>
                <p className="text-sm text-white/60">Screen Width</p>
                <p className="text-white font-medium">{width}px</p>
              </div>
              <div>
                <p className="text-sm text-white/60">Device Type</p>
                <p className="text-white font-medium">{isMobile ? "Mobile" : "Desktop"}</p>
              </div>
              <div>
                <p className="text-sm text-white/60">Optimized UI</p>
                <p className="text-white font-medium">Enabled</p>
              </div>
            </div>
          </div>
        </div>

        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={`grid ${isMobile ? "grid-cols-2" : "grid-cols-4"} w-full`}>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="form">Form Validation</TabsTrigger>
            <TabsTrigger value="token">Token Launch</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6 space-y-6">
            <Card className="glass-card border-white/5">
              <CardHeader>
                <CardTitle className="text-white">Optimization Overview</CardTitle>
                <CardDescription className="text-white/70">
                  Key improvements made to the PawPumps platform
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium text-white">Mobile Responsiveness</h3>
                    <ul className="list-disc list-inside space-y-1 text-white/80">
                      <li>Created responsive utility hooks</li>
                      <li>Implemented adaptive layouts</li>
                      <li>Optimized touch interactions</li>
                      <li>Improved mobile navigation</li>
                      <li>Enhanced mobile form experience</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium text-white">Form Validation</h3>
                    <ul className="list-disc list-inside space-y-1 text-white/80">
                      <li>Added comprehensive validation rules</li>
                      <li>Implemented real-time validation</li>
                      <li>Enhanced error messaging</li>
                      <li>Added input sanitization</li>
                      <li>Improved form submission handling</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium text-white">Accessibility</h3>
                    <ul className="list-disc list-inside space-y-1 text-white/80">
                      <li>Added ARIA attributes</li>
                      <li>Implemented keyboard navigation</li>
                      <li>Added focus management</li>
                      <li>Improved screen reader compatibility</li>
                      <li>Enhanced color contrast</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium text-white">Performance</h3>
                    <ul className="list-disc list-inside space-y-1 text-white/80">
                      <li>Implemented code splitting</li>
                      <li>Added lazy loading</li>
                      <li>Optimized re-renders</li>
                      <li>Added debouncing for inputs</li>
                      <li>Improved memory management</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="form" className="mt-6 space-y-6">
            <Card className="glass-card border-white/5">
              <CardHeader>
                <CardTitle className="text-white">Enhanced Form Validation</CardTitle>
                <CardDescription className="text-white/70">
                  Try submitting the form with invalid data to see validation in action
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FocusTrap>
                  <ResponsiveForm
                    fields={contactFormFields}
                    validations={contactFormValidations}
                    onSubmit={handleContactSubmit}
                    submitLabel="Submit Message"
                    successMessage="Your message has been sent successfully!"
                  />
                </FocusTrap>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="token" className="mt-6 space-y-6">
            <OptimizedTokenLaunchForm />
          </TabsContent>

          <TabsContent value="performance" className="mt-6 space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <PerformanceMonitor />

              <Card className="glass-card border-white/5">
                <CardHeader>
                  <CardTitle className="text-white">Performance Optimizations</CardTitle>
                  <CardDescription className="text-white/70">
                    Key techniques used to improve application performance
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h3 className="text-md font-medium text-white">Code Splitting</h3>
                    <p className="text-sm text-white/80">
                      Implemented dynamic imports and lazy loading to reduce initial bundle size and improve load times.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-md font-medium text-white">Memoization</h3>
                    <p className="text-sm text-white/80">
                      Used React.memo, useMemo, and useCallback to prevent unnecessary re-renders and calculations.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-md font-medium text-white">Debouncing</h3>
                    <p className="text-sm text-white/80">
                      Applied debouncing to input handlers to reduce the frequency of expensive operations.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-md font-medium text-white">Virtualization</h3>
                    <p className="text-sm text-white/80">
                      Implemented virtualized lists for large data sets to render only visible items.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-md font-medium text-white">Image Optimization</h3>
                    <p className="text-sm text-white/80">
                      Used responsive images, lazy loading, and modern formats to improve loading performance.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
