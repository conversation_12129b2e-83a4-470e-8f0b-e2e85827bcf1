import type { Meta, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { ValidatedInput, ValidatedTextarea } from '@/components/ui/validated-input'

const meta = {
  title: 'Forms/ValidatedInput',
  component: ValidatedInput,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Input components with real-time validation, error states, and accessibility features.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'Label for the input field',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text',
    },
    required: {
      control: 'boolean',
      description: 'Whether the field is required',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the field is disabled',
    },
    type: {
      control: 'select',
      options: ['text', 'email', 'password', 'number', 'url'],
      description: 'Input type',
    },
    helpText: {
      control: 'text',
      description: 'Help text displayed below the label',
    },
  },
} satisfies Meta<typeof ValidatedInput>

export default meta
type Story = StoryObj<typeof meta>

// Basic input
export const Default: Story = {
  render: (args) => {
    const [value, setValue] = useState('')
    return (
      <div className="w-80">
        <ValidatedInput
          {...args}
          value={value}
          onChange={setValue}
        />
      </div>
    )
  },
  args: {
    id: 'default-input',
    label: 'Default Input',
    placeholder: 'Enter some text...',
    value: '',
    onChange: () => {},
  },
}

// Email validation
export const EmailValidation: Story = {
  args: {
    id: 'email-input',
    label: 'Email',
    value: '',
    onChange: () => {},
  },
  render: () => {
    const [value, setValue] = useState('')
    const [isValid, setIsValid] = useState(true)
    
    const validateEmail = (email: string) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      const valid = emailRegex.test(email)
      return {
        isValid: valid,
        error: valid ? undefined : 'Please enter a valid email address'
      }
    }
    
    return (
      <div className="w-80">
        <ValidatedInput
          id="email-input"
          label="Email Address"
          type="email"
          value={value}
          onChange={setValue}
          onValidation={setIsValid}
          validator={validateEmail}
          placeholder="<EMAIL>"
          required
          helpText="We'll never share your email"
        />
      </div>
    )
  },
}

// Password validation
export const PasswordValidation: Story = {
  args: {
    id: 'password-input',
    label: 'Password',
    value: '',
    onChange: () => {},
  },
  render: () => {
    const [value, setValue] = useState('')
    
    const validatePassword = (password: string) => {
      if (password.length < 8) {
        return {
          isValid: false,
          error: 'Password must be at least 8 characters long'
        }
      }
      if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
        return {
          isValid: false,
          error: 'Password must contain uppercase, lowercase, and number'
        }
      }
      return { isValid: true }
    }
    
    return (
      <div className="w-80">
        <ValidatedInput
          id="password-input"
          label="Password"
          type="password"
          value={value}
          onChange={setValue}
          validator={validatePassword}
          placeholder="Enter secure password"
          required
          helpText="Must be 8+ characters with uppercase, lowercase, and number"
        />
      </div>
    )
  },
}

// Number validation
export const NumberValidation: Story = {
  args: {
    id: 'number-input',
    label: 'Number',
    value: '',
    onChange: () => {},
  },
  render: () => {
    const [value, setValue] = useState('')
    
    const validateNumber = (num: string) => {
      const number = parseFloat(num)
      if (isNaN(number)) {
        return {
          isValid: false,
          error: 'Please enter a valid number'
        }
      }
      if (number < 1 || number > 1000000) {
        return {
          isValid: false,
          error: 'Number must be between 1 and 1,000,000'
        }
      }
      return { isValid: true }
    }
    
    return (
      <div className="w-80">
        <ValidatedInput
          id="number-input"
          label="Token Amount"
          type="number"
          value={value}
          onChange={setValue}
          validator={validateNumber}
          placeholder="1000000"
          required
          helpText="Enter amount between 1 and 1,000,000"
        />
      </div>
    )
  },
}

// Disabled state
export const Disabled: Story = {
  args: {
    id: 'disabled-input',
    label: 'Disabled Input',
    value: '',
    onChange: () => {},
  },
  render: () => {
    const [value, setValue] = useState('Disabled value')
    
    return (
      <div className="w-80">
        <ValidatedInput
          id="disabled-input"
          label="Disabled Input"
          value={value}
          onChange={setValue}
          disabled
          helpText="This field is disabled"
        />
      </div>
    )
  },
}

// Textarea validation
export const TextareaValidation: Story = {
  args: {
    id: 'textarea-input',
    label: 'Textarea',
    value: '',
    onChange: () => {},
  },
  render: () => {
    const [value, setValue] = useState('')
    
    const validateDescription = (text: string) => {
      if (text.length < 10) {
        return {
          isValid: false,
          error: 'Description must be at least 10 characters'
        }
      }
      if (text.length > 500) {
        return {
          isValid: false,
          error: 'Description must be less than 500 characters'
        }
      }
      return { isValid: true }
    }
    
    return (
      <div className="w-80">
        <ValidatedTextarea
          id="description-input"
          label="Project Description"
          value={value}
          onChange={setValue}
          validator={validateDescription}
          placeholder="Describe your project..."
          required
          helpText="10-500 characters describing your project"
          rows={4}
        />
      </div>
    )
  },
}

// Form example
export const FormExample: Story = {
  args: {
    id: 'form-input',
    label: 'Form Input',
    value: '',
    onChange: () => {},
  },
  render: () => {
    const [formData, setFormData] = useState({
      name: '',
      email: '',
      amount: '',
      description: ''
    })
    const [validationStates, setValidationStates] = useState({
      name: true,
      email: true,
      amount: true,
      description: true
    })
    
    const validateName = (name: string) => ({
      isValid: name.length >= 2,
      error: name.length < 2 ? 'Name must be at least 2 characters' : undefined
    })
    
    const validateEmail = (email: string) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return {
        isValid: emailRegex.test(email),
        error: emailRegex.test(email) ? undefined : 'Invalid email address'
      }
    }
    
    const validateAmount = (amount: string) => {
      const num = parseFloat(amount)
      return {
        isValid: !isNaN(num) && num > 0,
        error: isNaN(num) || num <= 0 ? 'Amount must be greater than 0' : undefined
      }
    }
    
    const validateDescription = (desc: string) => ({
      isValid: desc.length >= 10 && desc.length <= 200,
      error: desc.length < 10 ? 'Too short' : desc.length > 200 ? 'Too long' : undefined
    })
    
    const isFormValid = Object.values(validationStates).every(Boolean) && 
                       Object.values(formData).every(value => value.length > 0)
    
    return (
      <div className="w-96 space-y-4">
        <h3 className="text-lg font-semibold text-white">Token Launch Form</h3>
        
        <ValidatedInput
          id="name"
          label="Token Name"
          value={formData.name}
          onChange={(value) => setFormData(prev => ({ ...prev, name: value }))}
          onValidation={(isValid) => setValidationStates(prev => ({ ...prev, name: isValid }))}
          validator={validateName}
          placeholder="My Token"
          required
        />
        
        <ValidatedInput
          id="email"
          label="Contact Email"
          type="email"
          value={formData.email}
          onChange={(value) => setFormData(prev => ({ ...prev, email: value }))}
          onValidation={(isValid) => setValidationStates(prev => ({ ...prev, email: isValid }))}
          validator={validateEmail}
          placeholder="<EMAIL>"
          required
        />
        
        <ValidatedInput
          id="amount"
          label="Initial Supply"
          type="number"
          value={formData.amount}
          onChange={(value) => setFormData(prev => ({ ...prev, amount: value }))}
          onValidation={(isValid) => setValidationStates(prev => ({ ...prev, amount: isValid }))}
          validator={validateAmount}
          placeholder="1000000"
          required
        />
        
        <ValidatedTextarea
          id="description"
          label="Description"
          value={formData.description}
          onChange={(value) => setFormData(prev => ({ ...prev, description: value }))}
          onValidation={(isValid) => setValidationStates(prev => ({ ...prev, description: isValid }))}
          validator={validateDescription}
          placeholder="Describe your token..."
          required
          rows={3}
        />
        
        <button
          className={`w-full px-4 py-2 rounded-md font-medium transition-colors ${
            isFormValid 
              ? 'bg-blue-600 hover:bg-blue-700 text-white' 
              : 'bg-gray-600 text-gray-400 cursor-not-allowed'
          }`}
          disabled={!isFormValid}
        >
          Launch Token
        </button>
        
        <div className="text-sm text-gray-400">
          Form valid: {isFormValid ? '✅' : '❌'}
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: 'Complete form example showing multiple validated inputs working together.',
      },
    },
  },
}
