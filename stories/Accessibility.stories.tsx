import type { <PERSON>a, StoryObj } from '@storybook/react'
import { But<PERSON> } from '@/components/ui/button'
import { ValidatedInput } from '@/components/ui/validated-input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useState } from 'react'

const meta = {
  title: 'Accessibility/Testing',
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Accessibility testing examples demonstrating WCAG 2.1 AA compliance, keyboard navigation, screen reader support, and inclusive design patterns.',
      },
    },
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
          },
          {
            id: 'keyboard-navigation',
            enabled: true,
          },
          {
            id: 'focus-management',
            enabled: true,
          },
        ],
      },
    },
  },
  tags: ['autodocs'],
} satisfies Meta

export default meta
type Story = StoryObj<typeof meta>

// Color contrast testing
export const ColorContrast: Story = {
  render: () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Color Contrast Testing</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Good Contrast Examples</CardTitle>
            <CardDescription>These examples meet WCAG AA standards</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-white text-black p-4 rounded">
              <p>Black text on white background (21:1 ratio)</p>
            </div>
            <div className="bg-doge text-black p-4 rounded">
              <p>Black text on Doge gold background (8.2:1 ratio)</p>
            </div>
            <div className="bg-blue-600 text-white p-4 rounded">
              <p>White text on blue background (5.9:1 ratio)</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Problematic Contrast</CardTitle>
            <CardDescription>These examples fail accessibility standards</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-300 text-gray-400 p-4 rounded">
              <p>Low contrast text (2.1:1 ratio) - Fails AA</p>
            </div>
            <div className="bg-yellow-200 text-yellow-300 p-4 rounded">
              <p>Very low contrast (1.4:1 ratio) - Fails AA</p>
            </div>
            <div className="bg-red-500 text-red-300 p-4 rounded">
              <p>Insufficient contrast (2.8:1 ratio) - Fails AA</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  ),
}

// Keyboard navigation
export const KeyboardNavigation: Story = {
  render: () => {
    const [focusedElement, setFocusedElement] = useState<string>('')
    
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Keyboard Navigation Testing</h2>
        <p className="text-muted-foreground">
          Use Tab, Shift+Tab, Enter, and Space to navigate through these elements.
          Current focus: <Badge variant="outline">{focusedElement || 'None'}</Badge>
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Interactive Elements</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onFocus={() => setFocusedElement('Primary Button')}
                onBlur={() => setFocusedElement('')}
              >
                Primary Action
              </Button>
              
              <Button 
                variant="outline"
                onFocus={() => setFocusedElement('Secondary Button')}
                onBlur={() => setFocusedElement('')}
              >
                Secondary Action
              </Button>
              
              <Button 
                variant="ghost"
                onFocus={() => setFocusedElement('Ghost Button')}
                onBlur={() => setFocusedElement('')}
              >
                Ghost Action
              </Button>
              
              <a 
                href="#"
                className="text-blue-500 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
                onFocus={() => setFocusedElement('Link')}
                onBlur={() => setFocusedElement('')}
              >
                Accessible Link
              </a>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Form Elements</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <ValidatedInput
                id="keyboard-input"
                label="Text Input"
                value=""
                onChange={() => {}}
                onFocus={() => setFocusedElement('Text Input')}
                onBlur={() => setFocusedElement('')}
              />
              
              <div>
                <label htmlFor="select-element" className="block text-sm font-medium mb-2">
                  Select Element
                </label>
                <select 
                  id="select-element"
                  className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  onFocus={() => setFocusedElement('Select')}
                  onBlur={() => setFocusedElement('')}
                >
                  <option>Option 1</option>
                  <option>Option 2</option>
                  <option>Option 3</option>
                </select>
              </div>
              
              <div>
                <label className="flex items-center space-x-2">
                  <input 
                    type="checkbox"
                    className="focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onFocus={() => setFocusedElement('Checkbox')}
                    onBlur={() => setFocusedElement('')}
                  />
                  <span>Checkbox Option</span>
                </label>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  },
}

// Screen reader support
export const ScreenReaderSupport: Story = {
  render: () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Screen Reader Support</h2>
      
      <Card>
        <CardHeader>
          <CardTitle>ARIA Labels and Descriptions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">Button with ARIA Label</h3>
            <Button aria-label="Close dialog window">
              ×
            </Button>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-2">Input with Description</h3>
            <ValidatedInput
              id="password-input"
              label="Password"
              type="password"
              value=""
              onChange={() => {}}
              aria-describedby="password-help"
              helpText="Must be at least 8 characters long"
            />
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-2">Live Region</h3>
            <div aria-live="polite" aria-atomic="true" className="sr-only">
              Status updates will be announced here
            </div>
            <Button onClick={() => {
              const liveRegion = document.querySelector('[aria-live="polite"]')
              if (liveRegion) {
                liveRegion.textContent = `Status updated at ${new Date().toLocaleTimeString()}`
              }
            }}>
              Update Status
            </Button>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-2">Progress Indicator</h3>
            <div 
              role="progressbar" 
              aria-valuenow={65} 
              aria-valuemin={0} 
              aria-valuemax={100}
              aria-label="Upload progress"
              className="w-full bg-gray-200 rounded-full h-2"
            >
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                style={{ width: '65%' }}
              />
            </div>
            <p className="text-sm text-muted-foreground mt-1">65% complete</p>
          </div>
        </CardContent>
      </Card>
    </div>
  ),
}

// Focus management
export const FocusManagement: Story = {
  render: () => {
    const [modalOpen, setModalOpen] = useState(false)
    
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Focus Management</h2>
        
        <Card>
          <CardHeader>
            <CardTitle>Modal Focus Trap</CardTitle>
            <CardDescription>
              Focus should be trapped within the modal when open
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => setModalOpen(true)}>
              Open Modal
            </Button>
            
            {modalOpen && (
              <div 
                className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
                role="dialog"
                aria-modal="true"
                aria-labelledby="modal-title"
              >
                <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
                  <h3 id="modal-title" className="text-lg font-semibold mb-4 text-black">
                    Example Modal
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Focus should be trapped within this modal. Try tabbing through the elements.
                  </p>
                  <div className="flex gap-2">
                    <Button variant="outline" className="text-black border-gray-300">
                      Cancel
                    </Button>
                    <Button 
                      onClick={() => setModalOpen(false)}
                      className="bg-blue-600 text-white"
                    >
                      Confirm
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Skip Links</CardTitle>
            <CardDescription>
              Skip links help keyboard users navigate efficiently
            </CardDescription>
          </CardHeader>
          <CardContent>
            <a 
              href="#main-content"
              className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded z-50"
            >
              Skip to main content
            </a>
            <div id="main-content" tabIndex={-1}>
              <p>Main content area (focus target for skip link)</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  },
}

// Error handling and feedback
export const ErrorHandling: Story = {
  render: () => {
    const [errors, setErrors] = useState<Record<string, string>>({})
    const [values, setValues] = useState({ email: '', amount: '' })
    
    const validateForm = () => {
      const newErrors: Record<string, string> = {}
      
      if (!values.email) {
        newErrors.email = 'Email is required'
      } else if (!/\S+@\S+\.\S+/.test(values.email)) {
        newErrors.email = 'Please enter a valid email address'
      }
      
      if (!values.amount) {
        newErrors.amount = 'Amount is required'
      } else if (parseFloat(values.amount) <= 0) {
        newErrors.amount = 'Amount must be greater than 0'
      }
      
      setErrors(newErrors)
      return Object.keys(newErrors).length === 0
    }
    
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Error Handling & Feedback</h2>
        
        <Card>
          <CardHeader>
            <CardTitle>Accessible Form Validation</CardTitle>
            <CardDescription>
              Errors are announced to screen readers and clearly associated with inputs
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="email-field" className="block text-sm font-medium mb-2">
                Email Address *
              </label>
              <input
                id="email-field"
                type="email"
                value={values.email}
                onChange={(e) => setValues(prev => ({ ...prev, email: e.target.value }))}
                aria-invalid={!!errors.email}
                aria-describedby={errors.email ? 'email-error' : undefined}
                className={`w-full p-2 border rounded focus:outline-none focus:ring-2 ${
                  errors.email 
                    ? 'border-red-500 focus:ring-red-500' 
                    : 'border-gray-300 focus:ring-blue-500'
                }`}
              />
              {errors.email && (
                <p id="email-error" role="alert" className="text-red-500 text-sm mt-1">
                  {errors.email}
                </p>
              )}
            </div>
            
            <div>
              <label htmlFor="amount-field" className="block text-sm font-medium mb-2">
                Amount *
              </label>
              <input
                id="amount-field"
                type="number"
                value={values.amount}
                onChange={(e) => setValues(prev => ({ ...prev, amount: e.target.value }))}
                aria-invalid={!!errors.amount}
                aria-describedby={errors.amount ? 'amount-error' : undefined}
                className={`w-full p-2 border rounded focus:outline-none focus:ring-2 ${
                  errors.amount 
                    ? 'border-red-500 focus:ring-red-500' 
                    : 'border-gray-300 focus:ring-blue-500'
                }`}
              />
              {errors.amount && (
                <p id="amount-error" role="alert" className="text-red-500 text-sm mt-1">
                  {errors.amount}
                </p>
              )}
            </div>
            
            <Button onClick={validateForm}>
              Validate Form
            </Button>
            
            {Object.keys(errors).length === 0 && values.email && values.amount && (
              <div role="alert" className="text-green-600 text-sm">
                ✓ Form is valid
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    )
  },
}

// Mobile accessibility
export const MobileAccessibility: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
  render: () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Mobile Accessibility</h2>
      
      <Card>
        <CardHeader>
          <CardTitle>Touch Targets</CardTitle>
          <CardDescription>
            All interactive elements meet the 44px minimum touch target size
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button className="min-h-[44px] w-full">
            Large Touch Target Button
          </Button>
          
          <div className="flex gap-2">
            <Button size="icon" className="min-h-[44px] min-w-[44px]">
              ♥
            </Button>
            <Button size="icon" className="min-h-[44px] min-w-[44px]">
              ⭐
            </Button>
            <Button size="icon" className="min-h-[44px] min-w-[44px]">
              📤
            </Button>
          </div>
          
          <div className="space-y-2">
            <label className="flex items-center space-x-3 min-h-[44px]">
              <input type="checkbox" className="w-5 h-5" />
              <span>Large checkbox with adequate spacing</span>
            </label>
            <label className="flex items-center space-x-3 min-h-[44px]">
              <input type="radio" name="radio-group" className="w-5 h-5" />
              <span>Large radio button option 1</span>
            </label>
            <label className="flex items-center space-x-3 min-h-[44px]">
              <input type="radio" name="radio-group" className="w-5 h-5" />
              <span>Large radio button option 2</span>
            </label>
          </div>
        </CardContent>
      </Card>
    </div>
  ),
}
