import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

const meta = {
  title: 'Design System/Overview',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'PawPumps Design System - A comprehensive overview of colors, typography, components, and patterns.',
      },
    },
  },
  tags: ['autodocs'],
} satisfies Meta

export default meta
type Story = StoryObj<typeof meta>

export const Colors: Story = {
  render: () => (
    <div className="p-8 bg-black min-h-screen">
      <h1 className="text-3xl font-bold text-white mb-8">Color Palette</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {/* Primary Colors */}
        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Primary Colors</CardTitle>
            <CardDescription>Main brand colors for PawPumps</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-doge border border-white/20"></div>
              <div>
                <div className="text-white font-medium">Doge Gold</div>
                <div className="text-white/60 text-sm">#D4AF37</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-yellow-400 border border-white/20"></div>
              <div>
                <div className="text-white font-medium">Bright Gold</div>
                <div className="text-white/60 text-sm">#FFD700</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-orange-500 border border-white/20"></div>
              <div>
                <div className="text-white font-medium">Orange</div>
                <div className="text-white/60 text-sm">#F97316</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Neutral Colors */}
        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Neutral Colors</CardTitle>
            <CardDescription>Background and text colors</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-black border border-white/20"></div>
              <div>
                <div className="text-white font-medium">Black</div>
                <div className="text-white/60 text-sm">#000000</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-white/10 border border-white/20"></div>
              <div>
                <div className="text-white font-medium">Glass</div>
                <div className="text-white/60 text-sm">rgba(255,255,255,0.1)</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-white border border-white/20"></div>
              <div>
                <div className="text-white font-medium">White</div>
                <div className="text-white/60 text-sm">#FFFFFF</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status Colors */}
        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Status Colors</CardTitle>
            <CardDescription>Success, warning, and error states</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-green-500 border border-white/20"></div>
              <div>
                <div className="text-white font-medium">Success</div>
                <div className="text-white/60 text-sm">#10B981</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-yellow-500 border border-white/20"></div>
              <div>
                <div className="text-white font-medium">Warning</div>
                <div className="text-white/60 text-sm">#EAB308</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-red-500 border border-white/20"></div>
              <div>
                <div className="text-white font-medium">Error</div>
                <div className="text-white/60 text-sm">#EF4444</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  ),
}

export const Typography: Story = {
  render: () => (
    <div className="p-8 bg-black min-h-screen">
      <h1 className="text-3xl font-bold text-white mb-8">Typography</h1>
      
      <Card className="glass-card border-white/10">
        <CardContent className="p-8 space-y-6">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">Heading 1</h1>
            <p className="text-white/60 text-sm">text-4xl font-bold</p>
          </div>
          
          <div>
            <h2 className="text-3xl font-semibold text-white mb-2">Heading 2</h2>
            <p className="text-white/60 text-sm">text-3xl font-semibold</p>
          </div>
          
          <div>
            <h3 className="text-2xl font-semibold text-white mb-2">Heading 3</h3>
            <p className="text-white/60 text-sm">text-2xl font-semibold</p>
          </div>
          
          <div>
            <h4 className="text-xl font-medium text-white mb-2">Heading 4</h4>
            <p className="text-white/60 text-sm">text-xl font-medium</p>
          </div>
          
          <div>
            <p className="text-base text-white mb-2">Body Text - This is the standard body text used throughout the application. It should be readable and accessible.</p>
            <p className="text-white/60 text-sm">text-base</p>
          </div>
          
          <div>
            <p className="text-sm text-white/80 mb-2">Small Text - Used for captions, labels, and secondary information.</p>
            <p className="text-white/60 text-sm">text-sm text-white/80</p>
          </div>
          
          <div>
            <p className="text-xs text-white/60 mb-2">Extra Small Text - Used for fine print and metadata.</p>
            <p className="text-white/60 text-sm">text-xs text-white/60</p>
          </div>
        </CardContent>
      </Card>
    </div>
  ),
}

export const Components: Story = {
  render: () => (
    <div className="p-8 bg-black min-h-screen">
      <h1 className="text-3xl font-bold text-white mb-8">Component Showcase</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Buttons */}
        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Buttons</CardTitle>
            <CardDescription>Various button styles and states</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Button>Default</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button className="pawpumps-button">PawPumps</Button>
              <Button className="glass-button liquid-shine">Glass Effect</Button>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button size="sm">Small</Button>
              <Button size="lg">Large</Button>
              <Button disabled>Disabled</Button>
            </div>
          </CardContent>
        </Card>

        {/* Badges */}
        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Badges</CardTitle>
            <CardDescription>Status indicators and labels</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Badge>Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="destructive">Destructive</Badge>
            </div>
            <div className="flex flex-wrap gap-2">
              <Badge className="bg-green-600">Success</Badge>
              <Badge className="bg-yellow-600">Warning</Badge>
              <Badge className="bg-blue-600">Info</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Glass Effects */}
        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Glass Effects</CardTitle>
            <CardDescription>Glassmorphism design elements</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="glass-card p-4 border-white/10">
              <p className="text-white">Glass Card</p>
              <p className="text-white/60 text-sm">Subtle transparency with blur</p>
            </div>
            <div className="glass-input p-3 border-white/10 rounded-md">
              <p className="text-white">Glass Input Style</p>
            </div>
            <div className="glass-button liquid-shine p-3 rounded-md text-center">
              <p className="text-white">Glass Button with Shine</p>
            </div>
          </CardContent>
        </Card>

        {/* Animations */}
        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Animations</CardTitle>
            <CardDescription>Interactive animations and effects</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="border-glow p-4 rounded-md border border-doge/50">
              <p className="text-white">Border Glow Effect</p>
            </div>
            <div className="liquid-shine p-4 rounded-md bg-white/5">
              <p className="text-white">Liquid Shine Animation</p>
            </div>
            <div className="animate-pulse p-4 rounded-md bg-white/10">
              <p className="text-white">Pulse Animation</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  ),
}

export const Layout: Story = {
  render: () => (
    <div className="p-8 bg-black min-h-screen">
      <h1 className="text-3xl font-bold text-white mb-8">Layout Patterns</h1>
      
      <div className="space-y-8">
        {/* Grid Layout */}
        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Grid Layout</CardTitle>
            <CardDescription>Responsive grid system</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="glass-card p-4 border-white/10">
                  <p className="text-white">Grid Item {i}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Flex Layout */}
        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Flex Layout</CardTitle>
            <CardDescription>Flexible box layouts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4 justify-between items-center">
              <div className="glass-card p-4 border-white/10 flex-1 min-w-0">
                <p className="text-white">Flex Item 1</p>
              </div>
              <div className="glass-card p-4 border-white/10 flex-1 min-w-0">
                <p className="text-white">Flex Item 2</p>
              </div>
              <div className="glass-card p-4 border-white/10 flex-1 min-w-0">
                <p className="text-white">Flex Item 3</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Spacing */}
        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Spacing Scale</CardTitle>
            <CardDescription>Consistent spacing throughout the app</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="bg-white/10 h-2 w-8"></div>
              <p className="text-white/60 text-sm">2 (0.5rem)</p>
            </div>
            <div className="space-y-2">
              <div className="bg-white/10 h-2 w-16"></div>
              <p className="text-white/60 text-sm">4 (1rem)</p>
            </div>
            <div className="space-y-2">
              <div className="bg-white/10 h-2 w-24"></div>
              <p className="text-white/60 text-sm">6 (1.5rem)</p>
            </div>
            <div className="space-y-2">
              <div className="bg-white/10 h-2 w-32"></div>
              <p className="text-white/60 text-sm">8 (2rem)</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  ),
}
