import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { TradingChart } from '@/components/trading-chart'

// Mock data for the chart
const generateMockData = (days: number, trend: 'up' | 'down' | 'volatile' = 'up') => {
  const data = []
  let basePrice = 0.000001
  
  for (let i = 0; i < days; i++) {
    const date = new Date()
    date.setDate(date.getDate() - (days - i))
    
    let change = 0
    switch (trend) {
      case 'up':
        change = (Math.random() - 0.3) * 0.1 // Slight upward bias
        break
      case 'down':
        change = (Math.random() - 0.7) * 0.1 // Slight downward bias
        break
      case 'volatile':
        change = (Math.random() - 0.5) * 0.2 // High volatility
        break
    }
    
    basePrice = Math.max(basePrice * (1 + change), 0.0000001)
    
    data.push({
      timestamp: date.toISOString(),
      price: basePrice,
      volume: Math.random() * 1000000 + 100000,
      high: basePrice * (1 + Math.random() * 0.05),
      low: basePrice * (1 - Math.random() * 0.05),
      open: basePrice * (1 + (Math.random() - 0.5) * 0.02),
      close: basePrice,
    })
  }
  
  return data
}

const meta = {
  title: 'Trading/TradingChart',
  component: TradingChart,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Interactive trading chart component with price data, volume, and technical indicators.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    data: {
      description: 'Array of price data points',
    },
    height: {
      control: 'number',
      description: 'Height of the chart in pixels',
    },
    showVolume: {
      control: 'boolean',
      description: 'Whether to show volume chart',
    },
    timeframe: {
      control: 'select',
      options: ['1H', '4H', '1D', '1W', '1M'],
      description: 'Chart timeframe',
    },
  },
} satisfies Meta<typeof TradingChart>

export default meta
type Story = StoryObj<typeof meta>

// Default chart
export const Default: Story = {
  args: {
    data: generateMockData(30),
    height: 400,
    showVolume: true,
    timeframe: '1D',
  },
}

// Upward trend
export const UpwardTrend: Story = {
  args: {
    data: generateMockData(30, 'up'),
    height: 400,
    showVolume: true,
    timeframe: '1D',
  },
  parameters: {
    docs: {
      description: {
        story: 'Chart showing an upward price trend over 30 days.',
      },
    },
  },
}

// Downward trend
export const DownwardTrend: Story = {
  args: {
    data: generateMockData(30, 'down'),
    height: 400,
    showVolume: true,
    timeframe: '1D',
  },
  parameters: {
    docs: {
      description: {
        story: 'Chart showing a downward price trend over 30 days.',
      },
    },
  },
}

// Volatile market
export const VolatileMarket: Story = {
  args: {
    data: generateMockData(30, 'volatile'),
    height: 400,
    showVolume: true,
    timeframe: '1D',
  },
  parameters: {
    docs: {
      description: {
        story: 'Chart showing high volatility with frequent price swings.',
      },
    },
  },
}

// Without volume
export const WithoutVolume: Story = {
  args: {
    data: generateMockData(30),
    height: 400,
    showVolume: false,
    timeframe: '1D',
  },
  parameters: {
    docs: {
      description: {
        story: 'Price chart without volume data.',
      },
    },
  },
}

// Different timeframes
export const HourlyTimeframe: Story = {
  args: {
    data: generateMockData(24, 'volatile'),
    height: 400,
    showVolume: true,
    timeframe: '1H',
  },
  parameters: {
    docs: {
      description: {
        story: 'Hourly chart showing 24 hours of data.',
      },
    },
  },
}

export const WeeklyTimeframe: Story = {
  args: {
    data: generateMockData(52, 'up'),
    height: 400,
    showVolume: true,
    timeframe: '1W',
  },
  parameters: {
    docs: {
      description: {
        story: 'Weekly chart showing 52 weeks of data.',
      },
    },
  },
}

// Different heights
export const CompactChart: Story = {
  args: {
    data: generateMockData(30),
    height: 200,
    showVolume: false,
    timeframe: '1D',
  },
  parameters: {
    docs: {
      description: {
        story: 'Compact chart suitable for dashboard widgets.',
      },
    },
  },
}

export const TallChart: Story = {
  args: {
    data: generateMockData(30),
    height: 600,
    showVolume: true,
    timeframe: '1D',
  },
  parameters: {
    docs: {
      description: {
        story: 'Tall chart for detailed analysis.',
      },
    },
  },
}

// Loading state
export const LoadingState: Story = {
  args: {
    data: [],
    height: 400,
    showVolume: true,
    timeframe: '1D',
  },
  parameters: {
    docs: {
      description: {
        story: 'Chart in loading state with no data.',
      },
    },
  },
}

// Error state
export const ErrorState: Story = {
  render: () => (
    <div className="w-full h-96 bg-black border border-red-500/20 rounded-lg flex items-center justify-center">
      <div className="text-center">
        <div className="text-red-500 text-lg font-semibold mb-2">Failed to Load Chart</div>
        <div className="text-red-400 text-sm">Unable to fetch price data</div>
        <button className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm">
          Retry
        </button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Chart error state when data fails to load.',
      },
    },
  },
}

// Multiple charts comparison
export const MultipleCharts: Story = {
  render: () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-4">
      <div>
        <h3 className="text-white text-lg font-semibold mb-4">DOGE/USD</h3>
        <TradingChart
          data={generateMockData(30, 'up')}
          height={300}
          showVolume={true}
          timeframe="1D"
        />
      </div>
      <div>
        <h3 className="text-white text-lg font-semibold mb-4">SHIB/USD</h3>
        <TradingChart
          data={generateMockData(30, 'down')}
          height={300}
          showVolume={true}
          timeframe="1D"
        />
      </div>
      <div>
        <h3 className="text-white text-lg font-semibold mb-4">PEPE/USD</h3>
        <TradingChart
          data={generateMockData(30, 'volatile')}
          height={300}
          showVolume={true}
          timeframe="1D"
        />
      </div>
      <div>
        <h3 className="text-white text-lg font-semibold mb-4">FLOKI/USD</h3>
        <TradingChart
          data={generateMockData(30, 'up')}
          height={300}
          showVolume={true}
          timeframe="1D"
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Multiple charts showing different tokens for comparison.',
      },
    },
  },
}
