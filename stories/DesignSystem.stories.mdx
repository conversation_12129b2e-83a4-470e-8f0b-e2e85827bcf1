import { Meta, ColorPalette, ColorItem, Typeset, Source } from '@storybook/blocks'

<Meta title="Design System/Overview" />

# PawPumps Design System

Welcome to the PawPumps design system documentation. This comprehensive guide covers all the design tokens, components, and patterns used throughout the application.

## Brand Identity

PawPumps is the premier memecoin launchpad and DEX on the Dogechain Network. Our design reflects professionalism while maintaining the playful spirit of the memecoin community.

### Brand Values
- **Professional**: Clean, modern interface that inspires trust
- **Accessible**: Inclusive design for all users
- **Performant**: Optimized for speed and efficiency
- **Playful**: Subtle nods to meme culture without compromising usability

## Color Palette

Our color system is built around the iconic Dogecoin gold, with carefully selected complementary colors for various UI states and contexts.

<ColorPalette>
  <ColorItem
    title="Primary Colors"
    subtitle="Main brand colors"
    colors={{
      'Doge Gold': '#D4AF37',
      'Doge Gold Light': '#E6C757',
      'Doge Gold Dark': '#B8941F',
    }}
  />
  <ColorItem
    title="Dogechain Colors"
    subtitle="Network-specific colors"
    colors={{
      'Dogechain Primary': '#F7931A',
      'Dogechain Light': '#FFB84D',
      'Dogechain Dark': '#E6851A',
    }}
  />
  <ColorItem
    title="Neutral Colors"
    subtitle="Background and text colors"
    colors={{
      'Background': '#0a0a0a',
      'Card': '#1a1a1a',
      'Border': '#2a2a2a',
      'Muted': '#6a6a6a',
      'Foreground': '#ffffff',
    }}
  />
  <ColorItem
    title="Semantic Colors"
    subtitle="Status and feedback colors"
    colors={{
      'Success': '#22c55e',
      'Warning': '#f59e0b',
      'Error': '#ef4444',
      'Info': '#3b82f6',
    }}
  />
</ColorPalette>

## Typography

Our typography system uses Inter as the primary font family, providing excellent readability across all devices and contexts.

<Typeset
  fontFamily="Inter"
  fontSizes={[12, 14, 16, 18, 20, 24, 32, 40, 48]}
  fontWeight={400}
  sampleText="The quick brown fox jumps over the lazy dog"
/>

### Font Weights
- **Light (300)**: Used sparingly for large headings
- **Regular (400)**: Body text and most UI elements
- **Medium (500)**: Emphasized text and labels
- **Semibold (600)**: Headings and important actions
- **Bold (700)**: Strong emphasis and primary CTAs

### Type Scale
- **Display**: 48px - Hero headings and major page titles
- **Heading 1**: 40px - Primary page headings
- **Heading 2**: 32px - Section headings
- **Heading 3**: 24px - Subsection headings
- **Heading 4**: 20px - Component headings
- **Body Large**: 18px - Important body text
- **Body**: 16px - Default body text
- **Body Small**: 14px - Secondary text
- **Caption**: 12px - Labels and captions

## Spacing System

Our spacing system follows an 8px grid for consistent rhythm and alignment.

<Source
  language="css"
  code={`
/* Spacing Scale (8px base) */
--space-1: 0.25rem;  /* 4px */
--space-2: 0.5rem;   /* 8px */
--space-3: 0.75rem;  /* 12px */
--space-4: 1rem;     /* 16px */
--space-5: 1.25rem;  /* 20px */
--space-6: 1.5rem;   /* 24px */
--space-8: 2rem;     /* 32px */
--space-10: 2.5rem;  /* 40px */
--space-12: 3rem;    /* 48px */
--space-16: 4rem;    /* 64px */
--space-20: 5rem;    /* 80px */
--space-24: 6rem;    /* 96px */
  `}
/>

## Border Radius

Consistent border radius values create visual harmony throughout the interface.

<Source
  language="css"
  code={`
/* Border Radius Scale */
--radius-sm: 0.125rem;  /* 2px */
--radius: 0.375rem;     /* 6px */
--radius-md: 0.5rem;    /* 8px */
--radius-lg: 0.75rem;   /* 12px */
--radius-xl: 1rem;      /* 16px */
--radius-full: 9999px;  /* Full circle */
  `}
/>

## Shadows

Our shadow system provides depth and hierarchy to interface elements.

<Source
  language="css"
  code={`
/* Shadow Scale */
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

/* Glass Effect */
--glass-bg: rgba(255, 255, 255, 0.05);
--glass-border: rgba(255, 255, 255, 0.1);
--glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  `}
/>

## Animation & Motion

Smooth, purposeful animations enhance the user experience without being distracting.

<Source
  language="css"
  code={`
/* Animation Durations */
--duration-fast: 150ms;
--duration-normal: 300ms;
--duration-slow: 500ms;

/* Easing Functions */
--ease-in: cubic-bezier(0.4, 0, 1, 1);
--ease-out: cubic-bezier(0, 0, 0.2, 1);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

/* Common Animations */
.fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

.slide-up {
  animation: slideUp var(--duration-normal) var(--ease-out);
}

.liquid-shine {
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  background-size: 200% 200%;
  animation: shine 2s infinite;
}
  `}
/>

## Component Guidelines

### Buttons
- Use primary buttons for main actions
- Secondary buttons for supporting actions
- Destructive buttons for dangerous actions
- Ghost buttons for subtle actions
- Always include proper focus states

### Forms
- Clear labels and helpful error messages
- Real-time validation feedback
- Consistent spacing and alignment
- Proper keyboard navigation

### Cards
- Use for grouping related content
- Consistent padding and spacing
- Subtle borders and shadows
- Hover states for interactive cards

### Navigation
- Clear hierarchy and organization
- Active states for current page
- Responsive design for mobile
- Keyboard accessible

## Accessibility Standards

We follow WCAG 2.1 AA guidelines to ensure our interface is accessible to all users.

### Color Contrast
- Text on background: 4.5:1 minimum
- Large text on background: 3:1 minimum
- Interactive elements: 3:1 minimum

### Focus Management
- Visible focus indicators
- Logical tab order
- Focus trapping in modals
- Skip links for navigation

### Screen Readers
- Semantic HTML structure
- Proper ARIA labels
- Live regions for dynamic content
- Alternative text for images

## Usage Guidelines

### Do's
✅ Use consistent spacing from our scale
✅ Follow the established color palette
✅ Maintain proper contrast ratios
✅ Use semantic HTML elements
✅ Test with keyboard navigation
✅ Provide alternative text for images

### Don'ts
❌ Create custom spacing values
❌ Use colors outside the palette
❌ Ignore accessibility guidelines
❌ Use color alone to convey information
❌ Create overly complex animations
❌ Forget to test on mobile devices

## Resources

- [Figma Design Files](https://figma.com/pawpumps-design)
- [Component Library](https://storybook.pawpumps.com)
- [Accessibility Checklist](https://a11y.pawpumps.com)
- [Brand Guidelines](https://brand.pawpumps.com)

---

*This design system is a living document that evolves with our product. For questions or suggestions, please reach out to the design team.*
