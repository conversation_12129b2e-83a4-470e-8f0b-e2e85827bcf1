"use client"

// Performance metrics to track
export type PerformanceMetrics = {
  // Core Web Vitals
  FCP: number | null // First Contentful Paint
  LCP: number | null // Largest Contentful Paint
  FID: number | null // First Input Delay
  CLS: number | null // Cumulative Layout Shift
  TTI: number | null // Time to Interactive
  TBT: number | null // Total Blocking Time

  // Custom metrics
  TTFB: number | null // Time to First Byte
  domLoad: number | null // DOM Content Loaded
  fullLoad: number | null // Full Page Load
  jsHeapSize: number | null // JavaScript Heap Size
  resourceCount: number | null // Number of resources loaded
  resourceSize: number | null // Total resource size
}

// Log a performance metric
export function logPerformanceMetric(name: string, value: number) {
  console.log(`[Performance] ${name}: ${value.toFixed(2)}ms`)
}

// Get performance budget for a metric
export function getPerformanceBudget(metricName: string): number | null {
  switch (metricName) {
    case "FCP":
      return 1500 // milliseconds
    case "LCP":
      return 2500 // milliseconds
    case "CLS":
      return 0.1 // unitless
    case "FID":
      return 100 // milliseconds
    case "TTFB":
      return 800 // milliseconds
    default:
      return null
  }
}

// Check if a metric is within its performance budget
export function isMetricWithinBudget(metricName: string, value: number): boolean {
  const budget = getPerformanceBudget(metricName)
  if (budget === null) {
    return true // No budget defined, so consider it within budget
  }
  return value <= budget
}

// Initialize performance monitoring
export function initPerformanceMonitoring() {
  if (typeof window === "undefined") return

  // Observe performance entries
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.entryType === "paint") {
        logPerformanceMetric(entry.name.toUpperCase(), entry.startTime)
      } else if (entry.entryType === "first-input") {
        logPerformanceMetric("FID", (entry as any).processingStart - entry.startTime)
      } else if (entry.entryType === "layout-shift") {
        logPerformanceMetric("CLS", (entry as any).value)
      } else if (entry.entryType === "navigation") {
        logPerformanceMetric("TTFB", (entry as any).responseStart)
      }
    })
  })

  observer.observe({ type: "paint", buffered: true })
  observer.observe({ type: "first-input", buffered: true })
  observer.observe({ type: "layout-shift", buffered: true })
  observer.observe({ type: "navigation", buffered: true })
}
