export type ValidationRule = {
  test: (value: any) => boolean
  message: string
}

export type FieldValidation = {
  [key: string]: ValidationRule[]
}

export type ValidationErrors = {
  [key: string]: string[]
}

export const validateField = (value: any, rules: ValidationRule[]): string[] => {
  return rules.filter((rule) => !rule.test(value)).map((rule) => rule.message)
}

export const validateForm = (values: Record<string, any>, validations: FieldValidation): ValidationErrors => {
  const errors: ValidationErrors = {}

  Object.keys(validations).forEach((field) => {
    const fieldErrors = validateField(values[field], validations[field])
    if (fieldErrors.length > 0) {
      errors[field] = fieldErrors
    }
  })

  return errors
}

// Common validation rules
export const required = (message = "This field is required"): ValidationRule => ({
  test: (value) => value !== undefined && value !== null && value !== "",
  message,
})

export const minLength = (length: number, message?: string): ValidationRule => ({
  test: (value) => value && value.length >= length,
  message: message || `Must be at least ${length} characters`,
})

export const maxLength = (length: number, message?: string): ValidationRule => ({
  test: (value) => !value || value.length <= length,
  message: message || `Must be no more than ${length} characters`,
})

export const isEmail = (message = "Please enter a valid email address"): ValidationRule => ({
  test: (value) => !value || /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value),
  message,
})

export const isNumber = (message = "Please enter a valid number"): ValidationRule => ({
  test: (value) => !value || !isNaN(Number(value)),
  message,
})

export const isPositiveNumber = (message = "Please enter a positive number"): ValidationRule => ({
  test: (value) => !value || (!isNaN(Number(value)) && Number(value) > 0),
  message,
})

export const isWalletAddress = (message = "Please enter a valid wallet address"): ValidationRule => ({
  test: (value) => !value || /^0x[a-fA-F0-9]{40}$/.test(value),
  message,
})

export const isTokenSymbol = (
  message = "Please enter a valid token symbol (2-8 uppercase letters)",
): ValidationRule => ({
  test: (value) => !value || /^[A-Z]{2,8}$/.test(value),
  message,
})
