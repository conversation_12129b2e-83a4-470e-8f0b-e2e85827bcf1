// Types
export type FeedbackType = "bug" | "feature" | "ux" | "other"
export type FeedbackStatus = "new" | "reviewing" | "resolved" | "closed"

export interface Feedback {
  id: string
  type: FeedbackType
  message: string
  email?: string
  metadata: string
  status: FeedbackStatus
  createdAt: Date
  updatedAt: Date
}

interface FeedbackInput {
  type: FeedbackType
  message: string
  email?: string
  metadata: string
}

// Generate a unique ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 9)
}

// Save feedback to localStorage
export function saveFeedback(input: FeedbackInput): Feedback {
  // Create new feedback item
  const now = new Date()
  const newFeedback: Feedback = {
    id: generateId(),
    type: input.type,
    message: input.message,
    email: input.email,
    metadata: input.metadata,
    status: "new",
    createdAt: now,
    updatedAt: now,
  }

  // Get existing feedback
  const existingFeedback = getAllFeedback()

  // Add new feedback
  const updatedFeedback = [newFeedback, ...existingFeedback]

  // Save to localStorage
  if (typeof window !== "undefined") {
    localStorage.setItem("pawpumps_feedback", JSON.stringify(updatedFeedback))
    // Dispatch storage event to notify other tabs
    window.dispatchEvent(new Event("storage"))
  }

  return newFeedback
}

// Get all feedback from localStorage
export function getAllFeedback(): Feedback[] {
  if (typeof window === "undefined") {
    return []
  }

  const storedFeedback = localStorage.getItem("pawpumps_feedback")
  if (!storedFeedback) {
    return []
  }

  try {
    return JSON.parse(storedFeedback)
  } catch (error) {
    console.error("Error parsing feedback:", error)
    return []
  }
}

// Update feedback status
export function updateFeedbackStatus(id: string, status: FeedbackStatus): boolean {
  if (typeof window === "undefined") {
    return false
  }

  const feedback = getAllFeedback()
  const updatedFeedback = feedback.map((item) => {
    if (item.id === id) {
      return {
        ...item,
        status,
        updatedAt: new Date(),
      }
    }
    return item
  })

  localStorage.setItem("pawpumps_feedback", JSON.stringify(updatedFeedback))
  // Dispatch storage event to notify other tabs
  window.dispatchEvent(new Event("storage"))

  return true
}

// Delete feedback
export function deleteFeedback(id: string): boolean {
  if (typeof window === "undefined") {
    return false
  }

  const feedback = getAllFeedback()
  const updatedFeedback = feedback.filter((item) => item.id !== id)

  localStorage.setItem("pawpumps_feedback", JSON.stringify(updatedFeedback))
  // Dispatch storage event to notify other tabs
  window.dispatchEvent(new Event("storage"))

  return true
}

// Export feedback as CSV
export function exportFeedbackAsCsv(): string {
  const feedback = getAllFeedback()

  // Define CSV headers
  const headers = ["ID", "Type", "Message", "Email", "Status", "Created At", "Updated At", "Metadata"]

  // Create CSV rows
  const rows = feedback.map((item) => [
    item.id,
    item.type,
    `"${item.message.replace(/"/g, '""')}"`, // Escape quotes in message
    item.email || "",
    item.status,
    new Date(item.createdAt).toISOString(),
    new Date(item.updatedAt).toISOString(),
    `"${item.metadata.replace(/"/g, '""')}"`, // Escape quotes in metadata
  ])

  // Combine headers and rows
  const csvContent = [headers.join(","), ...rows.map((row) => row.join(","))].join("\n")

  return csvContent
}
