type AlertSeverity = "info" | "warning" | "critical"

type AlertThresholds = {
  warning: number
  critical: number
}

type MetricThresholds = {
  [key: string]: AlertThresholds
}

// Define alert thresholds for various metrics
const METRIC_THRESHOLDS: MetricThresholds = {
  "first-contentful-paint": { warning: 1500, critical: 3000 },
  "largest-contentful-paint": { warning: 2500, critical: 4000 },
  "first-input-delay": { warning: 100, critical: 300 },
  "cumulative-layout-shift": { warning: 0.1, critical: 0.25 },
  "time-to-interactive": { warning: 3500, critical: 5000 },
  "total-blocking-time": { warning: 300, critical: 600 },
  "server-response-time": { warning: 200, critical: 500 },
  "js-execution-time": { warning: 200, critical: 500 },
  "error-rate": { warning: 1, critical: 5 },
}

// Alert channels configuration
const ALERT_CHANNELS = {
  slack: {
    enabled: true,
    webhook: process.env.SLACK_WEBHOOK_URL,
    channel: "#pawpumps-alerts",
  },
  email: {
    enabled: true,
    recipients: ["<EMAIL>", "<EMAIL>"],
  },
  sms: {
    enabled: true,
    recipients: process.env.ONCALL_PHONE_NUMBERS?.split(",") || [],
  },
}

// Generate alert based on metric value
export function evaluateMetricForAlert(metricName: string, value: number): AlertSeverity | null {
  const thresholds = METRIC_THRESHOLDS[metricName]

  if (!thresholds) {
    console.warn(`No thresholds defined for metric: ${metricName}`)
    return null
  }

  if (value >= thresholds.critical) {
    return "critical"
  } else if (value >= thresholds.warning) {
    return "warning"
  }

  return null
}

// Send alert to configured channels
export async function sendAlert(
  metricName: string,
  value: number,
  severity: AlertSeverity,
  context: Record<string, any> = {},
): Promise<void> {
  const timestamp = new Date().toISOString()
  const alertId = `alert-${metricName}-${timestamp}`

  const alertData = {
    id: alertId,
    timestamp,
    metricName,
    value,
    severity,
    threshold: severity === 'info' ? undefined : METRIC_THRESHOLDS[metricName]?.[severity as keyof AlertThresholds],
    context,
  }

  console.log(`[ALERT] ${severity.toUpperCase()}: ${metricName} = ${value}`)

  try {
    // Send to Slack
    if (ALERT_CHANNELS.slack.enabled && ALERT_CHANNELS.slack.webhook) {
      await sendSlackAlert(alertData)
    }

    // Send email for critical alerts
    if (severity === "critical" && ALERT_CHANNELS.email.enabled) {
      await sendEmailAlert(alertData)
    }

    // Send SMS for critical alerts during off-hours
    if (severity === "critical" && ALERT_CHANNELS.sms.enabled && isOffHours()) {
      await sendSmsAlert(alertData)
    }

    // Record alert in monitoring system
    await recordAlertInMonitoringSystem(alertData)
  } catch (error) {
    console.error("Failed to send alert:", error)
  }
}

// Check if current time is outside business hours
function isOffHours(): boolean {
  const now = new Date()
  const hours = now.getHours()
  const day = now.getDay() // 0 = Sunday, 6 = Saturday

  // Consider off-hours: weekends or outside 9am-6pm
  return day === 0 || day === 6 || hours < 9 || hours >= 18
}

// Send alert to Slack
async function sendSlackAlert(alertData: any): Promise<void> {
  // Implementation would use fetch to post to Slack webhook
  console.log("Sending Slack alert:", alertData)
}

// Send alert via email
async function sendEmailAlert(alertData: any): Promise<void> {
  // Implementation would use email service
  console.log("Sending email alert:", alertData)
}

// Send alert via SMS
async function sendSmsAlert(alertData: any): Promise<void> {
  // Implementation would use SMS service
  console.log("Sending SMS alert:", alertData)
}

// Record alert in monitoring system
async function recordAlertInMonitoringSystem(alertData: any): Promise<void> {
  // Implementation would record in database or external system
  console.log("Recording alert in monitoring system:", alertData)
}

// Check a batch of metrics and trigger alerts as needed
export async function processMetricBatch(metrics: Record<string, number>): Promise<void> {
  const alertPromises = Object.entries(metrics).map(async ([metricName, value]) => {
    const severity = evaluateMetricForAlert(metricName, value)

    if (severity) {
      await sendAlert(metricName, value, severity)
    }
  })

  await Promise.all(alertPromises)
}

// Initialize real-time monitoring that triggers alerts
export function initAlertMonitoring(): void {
  if (typeof window === "undefined") return

  // Set up performance observers that trigger alerts
  setupPerformanceObservers()

  // Set up error monitoring
  setupErrorMonitoring()

  // Schedule regular checks
  scheduleHealthChecks()
}

// Set up performance observers
function setupPerformanceObservers(): void {
  // Similar implementation to performance-monitoring.ts but calls sendAlert
  console.log("Setting up performance observers for alerting")
}

// Set up error monitoring
function setupErrorMonitoring(): void {
  if (typeof window === "undefined") return

  // Monitor unhandled errors
  window.addEventListener("error", (event) => {
    const errorRate = calculateErrorRate()
    if (errorRate > 0) {
      const severity = evaluateMetricForAlert("error-rate", errorRate)
      if (severity) {
        sendAlert("error-rate", errorRate, severity, {
          errorMessage: event.message,
          source: event.filename,
          lineNumber: event.lineno,
        })
      }
    }
  })

  // Monitor unhandled promise rejections
  window.addEventListener("unhandledrejection", (event) => {
    const errorRate = calculateErrorRate()
    if (errorRate > 0) {
      const severity = evaluateMetricForAlert("error-rate", errorRate)
      if (severity) {
        sendAlert("error-rate", errorRate, severity, {
          errorMessage: event.reason?.message || "Unhandled Promise Rejection",
          reason: event.reason,
        })
      }
    }
  })
}

// Calculate current error rate (simplified implementation)
function calculateErrorRate(): number {
  // In a real implementation, this would track errors over a time window
  // For now, we'll just return a number for demonstration
  return Math.random() * 10 > 9 ? 5 : 0 // Occasionally trigger an alert
}

// Schedule regular health checks
function scheduleHealthChecks(): void {
  if (typeof window === "undefined") return

  // Check API health every minute
  setInterval(async () => {
    try {
      const start = performance.now()
      const response = await fetch("/api/health")
      const duration = performance.now() - start

      if (!response.ok) {
        sendAlert("api-health", 0, "critical", {
          status: response.status,
          statusText: response.statusText,
        })
      } else {
        // Check API response time
        const severity = evaluateMetricForAlert("server-response-time", duration)
        if (severity) {
          sendAlert("server-response-time", duration, severity)
        }
      }
    } catch (error) {
      sendAlert("api-health", 0, "critical", {
        error: error instanceof Error ? error.message : "Unknown error",
      })
    }
  }, 60000)
}
