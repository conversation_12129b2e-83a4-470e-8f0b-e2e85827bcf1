// Types for audit logging
export type AuditAction =
  | "login"
  | "logout"
  | "user:create"
  | "user:update"
  | "user:delete"
  | "user:suspend"
  | "user:unsuspend"
  | "user:bulk:update"
  | "user:bulk:delete"
  | "role:create"
  | "role:update"
  | "role:delete"
  | "proposal:create"
  | "proposal:vote"
  | "proposal:execute"
  | "proposal:cancel"
  | "treasury:withdraw"
  | "treasury:deposit"
  | "treasury:allocate"
  | "emergency:pause"
  | "emergency:resume"
  | "content:moderate"
  | "content:delete"
  | "settings:update"

export interface AuditLogEntry {
  id: string
  timestamp: string
  action: AuditAction
  actor: string // wallet address
  target?: string // affected entity ID
  details: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

// Add anomaly detection types and functions
export type AnomalyLevel = "low" | "medium" | "high"

export interface AnomalyDetectionRule {
  id: string
  name: string
  description: string
  condition: (log: AuditLogEntry, recentLogs: AuditLogEntry[]) => boolean
  level: AnomalyLevel
}

// Sample anomaly detection rules
const anomalyRules: AnomalyDetectionRule[] = [
  {
    id: "emergency-actions",
    name: "Emergency Actions",
    description: "Detects emergency actions that could indicate a security incident",
    condition: (log) => log.action.startsWith("emergency:"),
    level: "high",
  },
  {
    id: "treasury-withdrawals",
    name: "Large Treasury Withdrawals",
    description: "Detects unusually large withdrawals from the treasury",
    condition: (log) => {
      if (log.action !== "treasury:withdraw") return false
      // Check if withdrawal amount is large (example threshold)
      const amount = log.details.amount ? Number.parseFloat(log.details.amount) : 0
      return amount > 10000 // Example threshold
    },
    level: "high",
  },
  {
    id: "multiple-failed-logins",
    name: "Multiple Failed Logins",
    description: "Detects multiple failed login attempts that could indicate a brute force attack",
    condition: (log, recentLogs) => {
      if (log.action !== "login" || log.details.success !== false) return false

      // Count recent failed logins from the same IP
      const failedLogins = recentLogs.filter(
        (l) =>
          l.action === "login" &&
          l.details.success === false &&
          l.ipAddress === log.ipAddress &&
          new Date(l.timestamp).getTime() > new Date(log.timestamp).getTime() - 3600000, // Within last hour
      )

      return failedLogins.length >= 5 // Threshold for failed attempts
    },
    level: "medium",
  },
  {
    id: "unusual-admin-actions",
    name: "Unusual Admin Actions",
    description: "Detects admin actions at unusual times",
    condition: (log) => {
      if (!log.action.includes("admin")) return false

      // Check if action is performed during unusual hours (e.g., late night)
      const hour = new Date(log.timestamp).getHours()
      return hour >= 22 || hour <= 5 // Between 10 PM and 5 AM
    },
    level: "medium",
  },
  {
    id: "rapid-permission-changes",
    name: "Rapid Permission Changes",
    description: "Detects multiple permission changes in a short period",
    condition: (log, recentLogs) => {
      if (!log.action.includes("role:")) return false

      // Count recent role changes
      const recentRoleChanges = recentLogs.filter(
        (l) =>
          l.action.includes("role:") && new Date(l.timestamp).getTime() > new Date(log.timestamp).getTime() - 300000, // Within last 5 minutes
      )

      return recentRoleChanges.length >= 3 // Threshold for rapid changes
    },
    level: "medium",
  },
]

// Function to detect anomalies in audit logs
export function detectAnomalies(logs: AuditLogEntry[]): {
  anomalies: AuditLogEntry[]
  anomalyDetails: Map<string, { rule: AnomalyDetectionRule; level: AnomalyLevel }>
} {
  const anomalies: AuditLogEntry[] = []
  const anomalyDetails = new Map<string, { rule: AnomalyDetectionRule; level: AnomalyLevel }>()

  // Process each log entry
  logs.forEach((log) => {
    // Check each rule against the log
    for (const rule of anomalyRules) {
      if (rule.condition(log, logs)) {
        anomalies.push(log)
        anomalyDetails.set(log.id, { rule, level: rule.level })
        break // Stop after first matching rule
      }
    }
  })

  return { anomalies, anomalyDetails }
}

// In-memory storage for audit logs (in production, this would be a database)
let auditLogs: AuditLogEntry[] = []

// Function to log an audit event
export async function logAuditEvent(
  action: AuditAction,
  actor: string,
  details: Record<string, any>,
  target?: string,
): Promise<AuditLogEntry> {
  const logEntry: AuditLogEntry = {
    id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    timestamp: new Date().toISOString(),
    action,
    actor,
    target,
    details,
    ipAddress: "127.0.0.1", // In a real app, this would be the actual IP
    userAgent: typeof window !== "undefined" ? window.navigator.userAgent : "server",
  }

  // In production, this would save to a database
  auditLogs.unshift(logEntry)

  // Keep only the last 1000 logs in memory
  if (auditLogs.length > 1000) {
    auditLogs = auditLogs.slice(0, 1000)
  }

  // In production, you might also want to send critical logs to a monitoring service
  if (
    action.startsWith("emergency:") ||
    action === "treasury:withdraw" ||
    action === "user:delete" ||
    action === "role:delete"
  ) {
    await sendCriticalLogAlert(logEntry)
  }

  return logEntry
}

// Function to get audit logs with filtering
export function getAuditLogs(options?: {
  actions?: AuditAction[]
  actor?: string
  target?: string
  startDate?: Date
  endDate?: Date
  limit?: number
  offset?: number
}): AuditLogEntry[] {
  let filteredLogs = [...auditLogs]

  // Apply filters
  if (options) {
    if (options.actions && options.actions.length > 0) {
      filteredLogs = filteredLogs.filter((log) => options.actions!.includes(log.action))
    }

    if (options.actor) {
      filteredLogs = filteredLogs.filter((log) => log.actor.toLowerCase() === options.actor!.toLowerCase())
    }

    if (options.target) {
      filteredLogs = filteredLogs.filter((log) => log.target === options.target)
    }

    if (options.startDate) {
      filteredLogs = filteredLogs.filter((log) => new Date(log.timestamp) >= options.startDate!)
    }

    if (options.endDate) {
      filteredLogs = filteredLogs.filter((log) => new Date(log.timestamp) <= options.endDate!)
    }

    // Apply pagination
    if (options.offset !== undefined && options.limit !== undefined) {
      filteredLogs = filteredLogs.slice(options.offset, options.offset + options.limit)
    } else if (options.limit !== undefined) {
      filteredLogs = filteredLogs.slice(0, options.limit)
    }
  }

  return filteredLogs
}

// Function to send alerts for critical audit events
async function sendCriticalLogAlert(logEntry: AuditLogEntry): Promise<void> {
  // In production, this would send to a monitoring service, Slack, email, etc.
  console.log("CRITICAL AUDIT EVENT:", logEntry)

  // Example of sending to a webhook (e.g., Slack)
  if (process.env.SLACK_WEBHOOK_URL) {
    try {
      const response = await fetch(process.env.SLACK_WEBHOOK_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          text: `🚨 CRITICAL AUDIT EVENT: ${logEntry.action}`,
          blocks: [
            {
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*🚨 CRITICAL AUDIT EVENT*\n*Action:* ${logEntry.action}\n*Actor:* ${logEntry.actor}\n*Time:* ${new Date(
                  logEntry.timestamp,
                ).toLocaleString()}`,
              },
            },
            {
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*Details:*\n\`\`\`${JSON.stringify(logEntry.details, null, 2)}\`\`\``,
              },
            },
          ],
        }),
      })

      if (!response.ok) {
        console.error("Failed to send critical log alert to Slack")
      }
    } catch (error) {
      console.error("Error sending critical log alert:", error)
    }
  }
}

// Add a function to generate compliance reports
export async function generateComplianceReport(options: {
  startDate?: Date
  endDate?: Date
  actions?: AuditAction[]
  format?: "csv" | "json" | "pdf"
  includeDetails?: boolean
}): Promise<string> {
  const { startDate, endDate, actions, format = "csv", includeDetails = true } = options

  // Get filtered logs
  const logs = getAuditLogs({
    startDate,
    endDate,
    actions,
  })

  // Generate report based on format
  switch (format) {
    case "json":
      return JSON.stringify(logs, null, 2)

    case "pdf":
      // In a real app, this would generate a PDF
      return "PDF generation not implemented in this demo"

    case "csv":
    default:
      // Generate CSV
      const headers = ["Timestamp", "Action", "Actor", "Target"]
      if (includeDetails) headers.push("Details")

      const rows = logs.map((log) => {
        const row = [log.timestamp, log.action, log.actor, log.target || ""]

        if (includeDetails) {
          row.push(JSON.stringify(log.details).replace(/,/g, ";")) // Prevent CSV issues with commas
        }

        return row.join(",")
      })

      return [headers.join(","), ...rows].join("\n")
  }
}

// Add a function to get audit statistics
export function getAuditStatistics(logs: AuditLogEntry[]): {
  totalActions: number
  actionsByType: Record<string, number>
  actionsByUser: Record<string, number>
  actionsOverTime: Record<string, number> // Date string -> count
} {
  const stats = {
    totalActions: logs.length,
    actionsByType: {} as Record<string, number>,
    actionsByUser: {} as Record<string, number>,
    actionsOverTime: {} as Record<string, number>,
  }

  logs.forEach((log) => {
    // Count by action type
    const actionType = log.action.split(":")[0] // Get base action type
    stats.actionsByType[actionType] = (stats.actionsByType[actionType] || 0) + 1

    // Count by user
    stats.actionsByUser[log.actor] = (stats.actionsByUser[log.actor] || 0) + 1

    // Count by date
    const dateStr = new Date(log.timestamp).toISOString().split("T")[0] // YYYY-MM-DD
    stats.actionsOverTime[dateStr] = (stats.actionsOverTime[dateStr] || 0) + 1
  })

  return stats
}

// Hook for using audit logging in components
export function useAuditLog() {
  const logAction = async (action: string, user: string, details: Record<string, any> = {}): Promise<void> => {
    // In a real application, this would send the log to a server
    console.log(`[AUDIT] ${action} by ${user}`, details)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 100))

    return Promise.resolve()
  }

  return { logAction }
}
