export interface ChartDataPoint {
  timestamp: string
  time: string
  price: number
  volume: number
  high: number
  low: number
  open: number
  close: number
  name?: string
  value?: number
}

export interface TimeframeOption {
  value: string
  label: string
  duration: number // in milliseconds
}

export const TIMEFRAME_OPTIONS: TimeframeOption[] = [
  { value: '1s', label: '1S', duration: 1000 },
  { value: '15s', label: '15S', duration: 15000 },
  { value: '30s', label: '30S', duration: 30000 },
  { value: '1m', label: '1M', duration: 60000 },
  { value: '3m', label: '3M', duration: 180000 },
  { value: '5m', label: '5M', duration: 300000 },
  { value: '15m', label: '15M', duration: 900000 },
  { value: '30m', label: '30M', duration: 1800000 },
  { value: '1h', label: '1H', duration: 3600000 },
  { value: '2h', label: '2H', duration: 7200000 },
  { value: '4h', label: '4H', duration: 14400000 },
  { value: '8h', label: '8H', duration: 28800000 },
  { value: '12h', label: '12H', duration: 43200000 },
  { value: '1d', label: '1D', duration: 86400000 },
  { value: '3d', label: '3D', duration: 259200000 },
  { value: '1w', label: '1W', duration: 604800000 },
  { value: '1M', label: '1M', duration: 2592000000 },
]

// Generate realistic price data with trends
function generatePriceData(
  basePrice: number,
  points: number,
  timeframe: string,
  trend: 'up' | 'down' | 'sideways' = 'sideways'
): ChartDataPoint[] {
  const data: ChartDataPoint[] = []
  const timeframeOption = TIMEFRAME_OPTIONS.find(t => t.value === timeframe)
  const interval = timeframeOption?.duration || 3600000 // default 1 hour
  
  let currentPrice = basePrice
  const volatility = 0.02 // 2% volatility
  const trendStrength = trend === 'sideways' ? 0 : (trend === 'up' ? 0.001 : -0.001)
  
  for (let i = 0; i < points; i++) {
    const timestamp = new Date(Date.now() - (points - i) * interval)
    
    // Generate realistic OHLCV data
    const open = currentPrice
    const randomChange = (Math.random() - 0.5) * volatility + trendStrength
    const close = open * (1 + randomChange)
    
    const high = Math.max(open, close) * (1 + Math.random() * 0.01)
    const low = Math.min(open, close) * (1 - Math.random() * 0.01)
    const volume = Math.random() * 1000000 + 100000
    
    data.push({
      timestamp: timestamp.toISOString(),
      time: timestamp.toLocaleTimeString(),
      price: close,
      volume,
      high,
      low,
      open,
      close,
      name: formatTimeLabel(timestamp, timeframe),
      value: close
    })
    
    currentPrice = close
  }
  
  return data
}

function formatTimeLabel(date: Date, timeframe: string): string {
  switch (timeframe) {
    case '1s':
    case '15s':
    case '30s':
    case '1m':
    case '3m':
    case '5m':
      return date.toLocaleTimeString()
    case '15m':
    case '30m':
    case '1h':
    case '2h':
    case '4h':
    case '8h':
    case '12h':
      return date.toLocaleString(undefined, { 
        month: 'short', 
        day: 'numeric', 
        hour: 'numeric', 
        minute: '2-digit' 
      })
    case '1d':
    case '3d':
      return date.toLocaleDateString(undefined, { 
        month: 'short', 
        day: 'numeric' 
      })
    case '1w':
    case '1M':
      return date.toLocaleDateString(undefined, { 
        month: 'short', 
        day: 'numeric',
        year: '2-digit'
      })
    default:
      return date.toLocaleDateString()
  }
}

// Mock data for different tokens
export const MOCK_TOKEN_DATA = {
  'wDOGE': {
    basePrice: 0.08,
    trend: 'up' as const,
    name: 'Wrapped Dogecoin',
    symbol: 'wDOGE'
  },
  'PAW': {
    basePrice: 0.000045,
    trend: 'up' as const,
    name: 'PawPumps Token',
    symbol: 'PAW'
  },
  'SHIB': {
    basePrice: 0.000008,
    trend: 'sideways' as const,
    name: 'Shiba Inu',
    symbol: 'SHIB'
  },
  'PEPE': {
    basePrice: 0.000001,
    trend: 'down' as const,
    name: 'Pepe',
    symbol: 'PEPE'
  },
  'FLOKI': {
    basePrice: 0.00003,
    trend: 'up' as const,
    name: 'Floki',
    symbol: 'FLOKI'
  }
}

export function generateMockChartData(
  token: string = 'wDOGE',
  timeframe: string = '1d',
  points?: number
): ChartDataPoint[] {
  const tokenData = MOCK_TOKEN_DATA[token as keyof typeof MOCK_TOKEN_DATA] || MOCK_TOKEN_DATA.wDOGE
  
  // Determine number of points based on timeframe
  const defaultPoints = getDefaultPointsForTimeframe(timeframe)
  const numPoints = points || defaultPoints
  
  return generatePriceData(
    tokenData.basePrice,
    numPoints,
    timeframe,
    tokenData.trend
  )
}

function getDefaultPointsForTimeframe(timeframe: string): number {
  switch (timeframe) {
    case '1s':
    case '15s':
    case '30s':
      return 60 // 1 minute of data
    case '1m':
      return 60 // 1 hour of data
    case '3m':
    case '5m':
      return 48 // 4 hours of data
    case '15m':
    case '30m':
      return 48 // 12-24 hours of data
    case '1h':
      return 24 // 1 day of data
    case '2h':
    case '4h':
      return 24 // 2-4 days of data
    case '8h':
    case '12h':
      return 21 // 1 week of data
    case '1d':
      return 30 // 1 month of data
    case '3d':
      return 30 // 3 months of data
    case '1w':
      return 52 // 1 year of data
    case '1M':
      return 24 // 2 years of data
    default:
      return 30
  }
}

// Real-time data simulation
export function createRealtimeDataStream(
  token: string,
  timeframe: string,
  callback: (data: ChartDataPoint) => void
): () => void {
  const tokenData = MOCK_TOKEN_DATA[token as keyof typeof MOCK_TOKEN_DATA] || MOCK_TOKEN_DATA.wDOGE
  // Update every 100ms for smooth real-time flow with interpolation
  const interval = 100 // 100ms updates for ultra-smooth animation

  let currentPrice = tokenData.basePrice
  let targetPrice = currentPrice
  let lastPriceUpdate = Date.now()
  
  const intervalId = setInterval(() => {
    const now = Date.now()
    const timestamp = new Date()

    // Update target price every second (10 intervals)
    if (now - lastPriceUpdate >= 1000) {
      const randomChange = (Math.random() - 0.5) * 0.01 // 1% volatility
      targetPrice = currentPrice * (1 + randomChange)
      lastPriceUpdate = now
    }

    // Smooth interpolation towards target price
    const interpolationFactor = 0.1 // Smooth transition
    currentPrice += (targetPrice - currentPrice) * interpolationFactor

    const dataPoint: ChartDataPoint = {
      timestamp: timestamp.toISOString(),
      time: timestamp.toLocaleTimeString(),
      price: currentPrice,
      volume: Math.random() * 1000000 + 100000,
      high: currentPrice * 1.01,
      low: currentPrice * 0.99,
      open: currentPrice,
      close: currentPrice,
      name: formatTimeLabel(timestamp, timeframe),
      value: currentPrice
    }

    callback(dataPoint)
  }, interval)
  
  return () => clearInterval(intervalId)
}
