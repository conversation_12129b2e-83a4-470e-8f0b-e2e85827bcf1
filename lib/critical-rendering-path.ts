// Critical Rendering Path Optimizer
// Optimizes the critical rendering path for faster FCP and LCP

interface CriticalResource {
  type: 'css' | 'js' | 'font' | 'image'
  url: string
  priority: 'critical' | 'high' | 'low'
  blocking: boolean
  size?: number
}

interface RenderingMetrics {
  fcp: number
  lcp: number
  blockingTime: number
  criticalResourceCount: number
}

class CriticalRenderingPathOptimizer {
  private criticalResources: CriticalResource[] = []
  private metrics: RenderingMetrics | null = null
  private observer: PerformanceObserver | null = null

  constructor() {
    this.initializeMetricsCollection()
  }

  // Initialize performance metrics collection
  private initializeMetricsCollection(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return

    // Observe paint timing
    this.observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      
      entries.forEach((entry) => {
        if (entry.entryType === 'paint') {
          this.updatePaintMetrics(entry as PerformancePaintTiming)
        } else if (entry.entryType === 'largest-contentful-paint') {
          this.updateLCPMetrics(entry as any)
        }
      })
    })

    try {
      this.observer.observe({ entryTypes: ['paint', 'largest-contentful-paint'] })
    } catch (error) {
      console.warn('Performance Observer not supported:', error)
    }
  }

  // Update paint metrics
  private updatePaintMetrics(entry: PerformancePaintTiming): void {
    if (!this.metrics) {
      this.metrics = {
        fcp: 0,
        lcp: 0,
        blockingTime: 0,
        criticalResourceCount: 0
      }
    }

    if (entry.name === 'first-contentful-paint') {
      this.metrics.fcp = entry.startTime
    }
  }

  // Update LCP metrics
  private updateLCPMetrics(entry: any): void {
    if (!this.metrics) return
    this.metrics.lcp = entry.startTime
  }

  // Identify critical resources
  identifyCriticalResources(): void {
    if (typeof window === 'undefined') return

    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    
    resources.forEach((resource) => {
      const criticalResource = this.analyzeCriticalResource(resource)
      if (criticalResource) {
        this.criticalResources.push(criticalResource)
      }
    })

    this.optimizeCriticalResources()
  }

  // Analyze if a resource is critical
  private analyzeCriticalResource(resource: PerformanceResourceTiming): CriticalResource | null {
    const url = resource.name
    const isBlocking = (resource as any).renderBlockingStatus === 'blocking'
    
    // CSS files
    if (url.includes('.css')) {
      return {
        type: 'css',
        url,
        priority: isBlocking ? 'critical' : 'high',
        blocking: isBlocking,
        size: resource.transferSize
      }
    }

    // JavaScript files
    if (url.includes('.js')) {
      const isCritical = url.includes('main') || url.includes('runtime') || url.includes('polyfills')
      return {
        type: 'js',
        url,
        priority: isCritical ? 'critical' : 'low',
        blocking: isBlocking,
        size: resource.transferSize
      }
    }

    // Font files
    if (url.includes('.woff') || url.includes('.woff2') || url.includes('.ttf')) {
      return {
        type: 'font',
        url,
        priority: 'high',
        blocking: false,
        size: resource.transferSize
      }
    }

    // Images
    if (url.includes('.jpg') || url.includes('.png') || url.includes('.webp') || url.includes('.avif')) {
      const isAboveFold = this.isImageAboveFold(url)
      return {
        type: 'image',
        url,
        priority: isAboveFold ? 'high' : 'low',
        blocking: false,
        size: resource.transferSize
      }
    }

    return null
  }

  // Check if image is above the fold
  private isImageAboveFold(url: string): boolean {
    const images = document.querySelectorAll('img')
    for (const img of images) {
      if (img.src === url || img.srcset?.includes(url)) {
        const rect = img.getBoundingClientRect()
        return rect.top < window.innerHeight
      }
    }
    return false
  }

  // Optimize critical resources
  private optimizeCriticalResources(): void {
    const criticalCSS = this.criticalResources.filter(r => r.type === 'css' && r.priority === 'critical')
    const criticalJS = this.criticalResources.filter(r => r.type === 'js' && r.priority === 'critical')
    
    // Inline critical CSS if small enough
    criticalCSS.forEach(resource => {
      if (resource.size && resource.size < 14000) { // 14KB threshold
        this.inlineCriticalCSS(resource.url)
      }
    })

    // Defer non-critical JavaScript
    criticalJS.forEach(resource => {
      if (resource.priority !== 'critical') {
        this.deferJavaScript(resource.url)
      }
    })

    // Preload critical fonts
    const criticalFonts = this.criticalResources.filter(r => r.type === 'font' && r.priority === 'high')
    criticalFonts.forEach(font => {
      this.preloadFont(font.url)
    })
  }

  // Inline critical CSS
  private inlineCriticalCSS(url: string): void {
    fetch(url)
      .then(response => response.text())
      .then(css => {
        const style = document.createElement('style')
        style.textContent = css
        style.setAttribute('data-critical', 'true')
        document.head.insertBefore(style, document.head.firstChild)
        
        // Remove original link tag
        const link = document.querySelector(`link[href="${url}"]`)
        if (link) {
          link.remove()
        }
      })
      .catch(error => {
        console.warn('Failed to inline critical CSS:', error)
      })
  }

  // Defer JavaScript loading
  private deferJavaScript(url: string): void {
    const script = document.querySelector(`script[src="${url}"]`) as HTMLScriptElement
    if (script && !script.defer && !script.async) {
      script.defer = true
    }
  }

  // Preload critical fonts
  private preloadFont(url: string): void {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'font'
    link.type = 'font/woff2'
    link.crossOrigin = 'anonymous'
    link.href = url
    document.head.appendChild(link)
  }

  // Optimize images for LCP
  optimizeLCPImage(): void {
    // Find the LCP element
    const lcpElements = document.querySelectorAll('img, video, [style*="background-image"]')
    
    lcpElements.forEach(element => {
      if (element instanceof HTMLImageElement) {
        // Ensure LCP images have priority
        element.loading = 'eager'
        element.fetchPriority = 'high'
        
        // Add preload hint for LCP image
        const link = document.createElement('link')
        link.rel = 'preload'
        link.as = 'image'
        link.href = element.src
        document.head.appendChild(link)
      }
    })
  }

  // Remove render-blocking resources
  removeRenderBlocking(): void {
    // Move non-critical CSS to load asynchronously
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')
    
    stylesheets.forEach(stylesheet => {
      const link = stylesheet as HTMLLinkElement
      const href = link.href
      
      // Skip critical stylesheets
      if (this.isCriticalStylesheet(href)) return
      
      // Load non-critical CSS asynchronously
      link.rel = 'preload'
      link.as = 'style'
      link.onload = () => {
        link.rel = 'stylesheet'
      }
    })
  }

  // Check if stylesheet is critical
  private isCriticalStylesheet(href: string): boolean {
    return href.includes('critical') || 
           href.includes('above-fold') ||
           href.includes('layout')
  }

  // Get current metrics
  getMetrics(): RenderingMetrics | null {
    return this.metrics
  }

  // Get critical resources analysis
  getCriticalResourcesAnalysis(): {
    total: number
    byType: Record<string, number>
    blocking: number
    totalSize: number
  } {
    const analysis = {
      total: this.criticalResources.length,
      byType: {} as Record<string, number>,
      blocking: 0,
      totalSize: 0
    }

    this.criticalResources.forEach(resource => {
      analysis.byType[resource.type] = (analysis.byType[resource.type] || 0) + 1
      if (resource.blocking) analysis.blocking++
      if (resource.size) analysis.totalSize += resource.size
    })

    return analysis
  }

  // Cleanup
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
  }
}

// Global instance
export const criticalRenderingPathOptimizer = new CriticalRenderingPathOptimizer()

// Initialize optimization
export function initializeCriticalRenderingPath(): void {
  if (typeof window === 'undefined') return

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setupCriticalRenderingPath()
    })
  } else {
    setupCriticalRenderingPath()
  }
}

function setupCriticalRenderingPath(): void {
  // Identify and optimize critical resources
  criticalRenderingPathOptimizer.identifyCriticalResources()
  
  // Optimize LCP image
  criticalRenderingPathOptimizer.optimizeLCPImage()
  
  // Remove render-blocking resources
  criticalRenderingPathOptimizer.removeRenderBlocking()
}

// React hook for critical rendering path optimization
export function useCriticalRenderingPath() {
  const getMetrics = () => criticalRenderingPathOptimizer.getMetrics()
  const getAnalysis = () => criticalRenderingPathOptimizer.getCriticalResourcesAnalysis()
  
  return {
    getMetrics,
    getAnalysis,
    optimizeLCPImage: criticalRenderingPathOptimizer.optimizeLCPImage.bind(criticalRenderingPathOptimizer)
  }
}
