// Comprehensive error handling and logging system

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  NETWORK = 'network',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  BLOCKCHAIN = 'blockchain',
  UI = 'ui',
  PERFORMANCE = 'performance',
  SECURITY = 'security',
  SYSTEM = 'system'
}

export interface ErrorContext {
  userId?: string
  sessionId?: string
  userAgent?: string
  url?: string
  timestamp: number
  component?: string
  action?: string
  metadata?: Record<string, any>
}

export interface AppError {
  id: string
  message: string
  code?: string
  severity: ErrorSeverity
  category: ErrorCategory
  context: ErrorContext
  stack?: string
  originalError?: Error
  resolved?: boolean
  resolvedAt?: number
  resolvedBy?: string
}

// Error storage and management
class ErrorStore {
  private errors: Map<string, AppError> = new Map()
  private maxErrors = 1000

  add(error: AppError): void {
    this.errors.set(error.id, error)
    
    // Keep only the most recent errors
    if (this.errors.size > this.maxErrors) {
      const oldestKey = this.errors.keys().next().value
      this.errors.delete(oldestKey)
    }
  }

  get(id: string): AppError | undefined {
    return this.errors.get(id)
  }

  getAll(): AppError[] {
    return Array.from(this.errors.values())
  }

  getByCategory(category: ErrorCategory): AppError[] {
    return this.getAll().filter(error => error.category === category)
  }

  getBySeverity(severity: ErrorSeverity): AppError[] {
    return this.getAll().filter(error => error.severity === severity)
  }

  resolve(id: string, resolvedBy?: string): boolean {
    const error = this.errors.get(id)
    if (error) {
      error.resolved = true
      error.resolvedAt = Date.now()
      error.resolvedBy = resolvedBy
      return true
    }
    return false
  }

  clear(): void {
    this.errors.clear()
  }

  getStats() {
    const errors = this.getAll()
    return {
      total: errors.length,
      unresolved: errors.filter(e => !e.resolved).length,
      bySeverity: {
        critical: errors.filter(e => e.severity === ErrorSeverity.CRITICAL).length,
        high: errors.filter(e => e.severity === ErrorSeverity.HIGH).length,
        medium: errors.filter(e => e.severity === ErrorSeverity.MEDIUM).length,
        low: errors.filter(e => e.severity === ErrorSeverity.LOW).length,
      },
      byCategory: Object.values(ErrorCategory).reduce((acc, category) => {
        acc[category] = errors.filter(e => e.category === category).length
        return acc
      }, {} as Record<string, number>)
    }
  }
}

// Global error store
const errorStore = new ErrorStore()

// Error ID generation
function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Main error handler class
export class ErrorHandler {
  private static instance: ErrorHandler
  private listeners: Array<(error: AppError) => void> = []

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  // Handle and log errors
  handle(
    error: Error | string,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    category: ErrorCategory = ErrorCategory.SYSTEM,
    context: Partial<ErrorContext> = {}
  ): AppError {
    const appError: AppError = {
      id: generateErrorId(),
      message: typeof error === 'string' ? error : error.message,
      severity,
      category,
      context: {
        timestamp: Date.now(),
        url: typeof window !== 'undefined' ? window.location.href : undefined,
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
        ...context
      },
      stack: typeof error === 'object' ? error.stack : undefined,
      originalError: typeof error === 'object' ? error : undefined,
      resolved: false
    }

    // Store the error
    errorStore.add(appError)

    // Notify listeners
    this.listeners.forEach(listener => {
      try {
        listener(appError)
      } catch (listenerError) {
        console.error('Error in error listener:', listenerError)
      }
    })

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[${severity.toUpperCase()}] ${category}:`, appError)
    }

    // Send to external logging service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToLoggingService(appError)
    }

    return appError
  }

  // Add error listener
  addListener(listener: (error: AppError) => void): () => void {
    this.listeners.push(listener)
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  // Get error statistics
  getStats() {
    return errorStore.getStats()
  }

  // Get all errors
  getErrors(): AppError[] {
    return errorStore.getAll()
  }

  // Get error by ID
  getError(id: string): AppError | undefined {
    return errorStore.get(id)
  }

  // Resolve error
  resolveError(id: string, resolvedBy?: string): boolean {
    return errorStore.resolve(id, resolvedBy)
  }

  // Clear all errors
  clearErrors(): void {
    errorStore.clear()
  }

  // Send to external logging service
  private async sendToLoggingService(error: AppError): Promise<void> {
    try {
      // In a real application, you would send this to your logging service
      // For now, we'll just log it
      console.log('Sending error to logging service:', error.id)
      
      // Example: Send to external service
      // await fetch('/api/error-tracking', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(error)
      // })
    } catch (loggingError) {
      console.error('Failed to send error to logging service:', loggingError)
    }
  }
}

// Convenience functions
export const errorHandler = ErrorHandler.getInstance()

export function logError(
  error: Error | string,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
  category: ErrorCategory = ErrorCategory.SYSTEM,
  context?: Partial<ErrorContext>
): AppError {
  return errorHandler.handle(error, severity, category, context)
}

export function logNetworkError(error: Error | string, context?: Partial<ErrorContext>): AppError {
  return errorHandler.handle(error, ErrorSeverity.HIGH, ErrorCategory.NETWORK, context)
}

export function logValidationError(error: Error | string, context?: Partial<ErrorContext>): AppError {
  return errorHandler.handle(error, ErrorSeverity.MEDIUM, ErrorCategory.VALIDATION, context)
}

export function logSecurityError(error: Error | string, context?: Partial<ErrorContext>): AppError {
  return errorHandler.handle(error, ErrorSeverity.CRITICAL, ErrorCategory.SECURITY, context)
}

export function logBlockchainError(error: Error | string, context?: Partial<ErrorContext>): AppError {
  return errorHandler.handle(error, ErrorSeverity.HIGH, ErrorCategory.BLOCKCHAIN, context)
}

export function logUIError(error: Error | string, context?: Partial<ErrorContext>): AppError {
  return errorHandler.handle(error, ErrorSeverity.LOW, ErrorCategory.UI, context)
}

// React Error Boundary helper - removed JSX to avoid compilation issues
// Use React.ErrorBoundary directly in components instead

// Performance monitoring
export class PerformanceMonitor {
  private static measurements: Map<string, number[]> = new Map()

  static startMeasurement(name: string): void {
    if (typeof window !== 'undefined' && window.performance) {
      performance.mark(`${name}-start`)
    }
  }

  static endMeasurement(name: string): number | null {
    if (typeof window !== 'undefined' && window.performance) {
      try {
        performance.mark(`${name}-end`)
        performance.measure(name, `${name}-start`, `${name}-end`)
        
        const measure = performance.getEntriesByName(name, 'measure')[0]
        if (measure) {
          const duration = measure.duration
          
          // Store measurement
          if (!this.measurements.has(name)) {
            this.measurements.set(name, [])
          }
          this.measurements.get(name)!.push(duration)
          
          // Log slow operations
          if (duration > 1000) { // More than 1 second
            logError(
              `Slow operation detected: ${name} took ${duration.toFixed(2)}ms`,
              ErrorSeverity.MEDIUM,
              ErrorCategory.PERFORMANCE,
              { action: name, duration }
            )
          }
          
          // Clean up
          performance.clearMarks(`${name}-start`)
          performance.clearMarks(`${name}-end`)
          performance.clearMeasures(name)
          
          return duration
        }
      } catch (error) {
        logError(error as Error, ErrorSeverity.LOW, ErrorCategory.PERFORMANCE)
      }
    }
    return null
  }

  static getAverageTime(name: string): number | null {
    const times = this.measurements.get(name)
    if (times && times.length > 0) {
      return times.reduce((sum, time) => sum + time, 0) / times.length
    }
    return null
  }

  static getAllMeasurements(): Record<string, number[]> {
    return Object.fromEntries(this.measurements)
  }
}

// React import removed since JSX was removed
