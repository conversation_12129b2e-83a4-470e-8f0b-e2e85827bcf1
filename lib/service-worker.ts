// Service Worker registration and management
// Implements intelligent caching and offline functionality

interface ServiceWorkerConfig {
  enabled: boolean
  scope: string
  updateViaCache: 'imports' | 'all' | 'none'
  skipWaiting: boolean
}

interface CacheStats {
  staticCacheSize: number
  dynamicCacheSize: number
  apiCacheSize: number
  totalCacheSize: number
  lastUpdated: string
}

class ServiceWorkerManager {
  private registration: ServiceWorkerRegistration | null = null
  private config: ServiceWorkerConfig
  private updateAvailable = false

  constructor(config: Partial<ServiceWorkerConfig> = {}) {
    this.config = {
      enabled: true,
      scope: '/',
      updateViaCache: 'none',
      skipWaiting: false,
      ...config
    }
  }

  // Register service worker
  async register(): Promise<ServiceWorkerRegistration | null> {
    if (!this.isSupported() || !this.config.enabled) {
      console.log('Service Worker not supported or disabled')
      return null
    }

    try {
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: this.config.scope,
        updateViaCache: this.config.updateViaCache
      })

      console.log('Service Worker registered successfully:', this.registration.scope)

      // Set up event listeners
      this.setupEventListeners()

      // Check for updates
      this.checkForUpdates()

      return this.registration
    } catch (error) {
      console.error('Service Worker registration failed:', error)
      return null
    }
  }

  // Check if service workers are supported
  private isSupported(): boolean {
    return 'serviceWorker' in navigator
  }

  // Set up event listeners
  private setupEventListeners(): void {
    if (!this.registration) return

    // Listen for updates
    this.registration.addEventListener('updatefound', () => {
      const newWorker = this.registration!.installing
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            this.updateAvailable = true
            this.notifyUpdateAvailable()
          }
        })
      }
    })

    // Listen for messages from service worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      this.handleServiceWorkerMessage(event)
    })

    // Listen for controller changes
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('Service Worker controller changed')
      if (this.config.skipWaiting) {
        window.location.reload()
      }
    })
  }

  // Handle messages from service worker
  private handleServiceWorkerMessage(event: MessageEvent): void {
    const { type, payload } = event.data

    switch (type) {
      case 'CACHE_UPDATED':
        console.log('Cache updated:', payload)
        break
      case 'OFFLINE_READY':
        console.log('App ready for offline use')
        this.notifyOfflineReady()
        break
      case 'ERROR':
        console.error('Service Worker error:', payload)
        break
    }
  }

  // Check for service worker updates
  async checkForUpdates(): Promise<void> {
    if (!this.registration) return

    try {
      await this.registration.update()
    } catch (error) {
      console.error('Failed to check for updates:', error)
    }
  }

  // Apply pending update
  async applyUpdate(): Promise<void> {
    if (!this.registration || !this.updateAvailable) return

    const waitingWorker = this.registration.waiting
    if (waitingWorker) {
      waitingWorker.postMessage({ type: 'SKIP_WAITING' })
    }
  }

  // Get cache statistics
  async getCacheStats(): Promise<CacheStats | null> {
    if (!this.isSupported()) return null

    try {
      const cacheNames = await caches.keys()
      let totalSize = 0
      const stats: any = {
        staticCacheSize: 0,
        dynamicCacheSize: 0,
        apiCacheSize: 0,
        totalCacheSize: 0,
        lastUpdated: new Date().toISOString()
      }

      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName)
        const requests = await cache.keys()
        let cacheSize = 0

        for (const request of requests) {
          const response = await cache.match(request)
          if (response) {
            const blob = await response.blob()
            cacheSize += blob.size
          }
        }

        if (cacheName.includes('static')) {
          stats.staticCacheSize = cacheSize
        } else if (cacheName.includes('api')) {
          stats.apiCacheSize = cacheSize
        } else {
          stats.dynamicCacheSize += cacheSize
        }

        totalSize += cacheSize
      }

      stats.totalCacheSize = totalSize
      return stats
    } catch (error) {
      console.error('Failed to get cache stats:', error)
      return null
    }
  }

  // Clear all caches
  async clearCaches(): Promise<void> {
    if (!this.isSupported()) return

    try {
      const cacheNames = await caches.keys()
      await Promise.all(cacheNames.map(name => caches.delete(name)))
      console.log('All caches cleared')
    } catch (error) {
      console.error('Failed to clear caches:', error)
    }
  }

  // Preload critical resources
  async preloadCriticalResources(urls: string[]): Promise<void> {
    if (!this.registration) return

    try {
      const cache = await caches.open('pawpumps-static-v1')
      await cache.addAll(urls)
      console.log('Critical resources preloaded')
    } catch (error) {
      console.error('Failed to preload critical resources:', error)
    }
  }

  // Send message to service worker
  sendMessage(message: any): void {
    if (!this.registration || !this.registration.active) return

    this.registration.active.postMessage(message)
  }

  // Notify user about available update
  private notifyUpdateAvailable(): void {
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('sw-update-available', {
      detail: { registration: this.registration }
    }))
  }

  // Notify user that app is ready for offline use
  private notifyOfflineReady(): void {
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('sw-offline-ready'))
  }

  // Get registration status
  getRegistration(): ServiceWorkerRegistration | null {
    return this.registration
  }

  // Check if update is available
  isUpdateAvailable(): boolean {
    return this.updateAvailable
  }

  // Unregister service worker
  async unregister(): Promise<boolean> {
    if (!this.registration) return false

    try {
      const result = await this.registration.unregister()
      console.log('Service Worker unregistered:', result)
      return result
    } catch (error) {
      console.error('Failed to unregister Service Worker:', error)
      return false
    }
  }
}

// Global service worker manager instance
export const serviceWorkerManager = new ServiceWorkerManager({
  enabled: process.env.NODE_ENV === 'production',
  skipWaiting: false
})

// Initialize service worker
export async function initializeServiceWorker(): Promise<void> {
  if (typeof window === 'undefined') return

  try {
    await serviceWorkerManager.register()
  } catch (error) {
    console.error('Failed to initialize service worker:', error)
  }
}

// React hook for service worker functionality
export function useServiceWorker() {
  const register = () => serviceWorkerManager.register()
  const checkForUpdates = () => serviceWorkerManager.checkForUpdates()
  const applyUpdate = () => serviceWorkerManager.applyUpdate()
  const getCacheStats = () => serviceWorkerManager.getCacheStats()
  const clearCaches = () => serviceWorkerManager.clearCaches()
  const isUpdateAvailable = () => serviceWorkerManager.isUpdateAvailable()

  return {
    register,
    checkForUpdates,
    applyUpdate,
    getCacheStats,
    clearCaches,
    isUpdateAvailable,
    sendMessage: serviceWorkerManager.sendMessage.bind(serviceWorkerManager)
  }
}

// Utility functions for cache management
export const cacheUtils = {
  // Check if resource is cached
  async isCached(url: string): Promise<boolean> {
    if (!('caches' in window)) return false

    try {
      const cacheNames = await caches.keys()
      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName)
        const response = await cache.match(url)
        if (response) return true
      }
      return false
    } catch {
      return false
    }
  },

  // Get cached response
  async getCached(url: string): Promise<Response | null> {
    if (!('caches' in window)) return null

    try {
      const cacheNames = await caches.keys()
      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName)
        const response = await cache.match(url)
        if (response) return response
      }
      return null
    } catch {
      return null
    }
  },

  // Cache resource
  async cacheResource(url: string, cacheName = 'pawpumps-dynamic-v1'): Promise<void> {
    if (!('caches' in window)) return

    try {
      const cache = await caches.open(cacheName)
      await cache.add(url)
    } catch (error) {
      console.error('Failed to cache resource:', error)
    }
  }
}
