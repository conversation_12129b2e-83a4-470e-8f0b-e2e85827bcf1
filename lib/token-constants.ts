// Token constants for Dogechain Network
// These are placeholder addresses - replace with actual deployed contract addresses

export interface TokenInfo {
  address: string
  symbol: string
  name: string
  decimals: number
  icon?: string
}

// Native DOGE (wrapped DOGE on Dogechain)
export const WDOGE_TOKEN: TokenInfo = {
  address: '0x0000000000000000000000000000000000000000', // Native token placeholder
  symbol: '<PERSON>O<PERSON>',
  name: '<PERSON><PERSON><PERSON><PERSON>',
  decimals: 18,
  icon: '/icons/doge.svg'
}

// Dogechain governance token
export const DC_TOKEN: TokenInfo = {
  address: '0x7B4328c127B85369D9f82ca0503B000D09CF9180', // Placeholder - replace with actual DC token address
  symbol: 'DC',
  name: 'Dogechain Token',
  decimals: 18,
  icon: '/icons/dc.svg'
}

// PawPumps platform token
export const PAW_TOKEN: TokenInfo = {
  address: '0x0000000000000000000000000000000000000001', // Placeholder - replace with actual PAW token address
  symbol: 'PAW',
  name: '<PERSON>w<PERSON>umps Token',
  decimals: 18,
  icon: '/icons/paw.svg'
}

// All supported tokens
export const SUPPORTED_TOKENS: TokenInfo[] = [
  WDOGE_TOKEN,
  DC_TOKEN,
  PAW_TOKEN,
]

// Token addresses map for easy lookup
export const TOKEN_ADDRESSES = {
  DOGE: WDOGE_TOKEN.address,
  DC: DC_TOKEN.address,
  PAW: PAW_TOKEN.address,
} as const

// Get token info by address
export function getTokenByAddress(address: string): TokenInfo | undefined {
  return SUPPORTED_TOKENS.find(token => 
    token.address.toLowerCase() === address.toLowerCase()
  )
}

// Get token info by symbol
export function getTokenBySymbol(symbol: string): TokenInfo | undefined {
  return SUPPORTED_TOKENS.find(token => 
    token.symbol.toLowerCase() === symbol.toLowerCase()
  )
}
