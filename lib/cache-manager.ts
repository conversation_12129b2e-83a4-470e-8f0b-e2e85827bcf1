// Advanced caching system with multiple strategies

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
}

interface CacheOptions {
  ttl?: number // Time to live in milliseconds
  maxSize?: number // Maximum number of entries
  strategy?: 'lru' | 'lfu' | 'fifo' // Eviction strategy
}

class CacheManager<T = unknown> {
  private cache = new Map<string, CacheEntry<T>>()
  private accessOrder: string[] = [] // For LRU
  private readonly defaultTTL = 5 * 60 * 1000 // 5 minutes
  private readonly maxSize: number
  private readonly strategy: 'lru' | 'lfu' | 'fifo'

  constructor(options: CacheOptions = {}) {
    this.maxSize = options.maxSize || 100
    this.strategy = options.strategy || 'lru'
  }

  set(key: string, data: T, ttl?: number): void {
    const now = Date.now()
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      ttl: ttl || this.defaultTTL,
      accessCount: 0,
      lastAccessed: now,
    }

    // Remove existing entry if it exists
    if (this.cache.has(key)) {
      this.remove(key)
    }

    // Check if we need to evict entries
    if (this.cache.size >= this.maxSize) {
      this.evict()
    }

    this.cache.set(key, entry)
    this.updateAccessOrder(key)
  }

  get(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    const now = Date.now()

    // Check if entry has expired
    if (now - entry.timestamp > entry.ttl) {
      this.remove(key)
      return null
    }

    // Update access statistics
    entry.accessCount++
    entry.lastAccessed = now
    this.updateAccessOrder(key)

    return entry.data
  }

  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.remove(key)
      return false
    }

    return true
  }

  remove(key: string): boolean {
    const removed = this.cache.delete(key)
    if (removed) {
      this.accessOrder = this.accessOrder.filter(k => k !== key)
    }
    return removed
  }

  clear(): void {
    this.cache.clear()
    this.accessOrder = []
  }

  size(): number {
    return this.cache.size
  }

  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  // Get cache statistics
  getStats() {
    const entries = Array.from(this.cache.values())
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      strategy: this.strategy,
      totalAccesses: entries.reduce((sum, entry) => sum + entry.accessCount, 0),
      averageAge: entries.length > 0 
        ? entries.reduce((sum, entry) => sum + (Date.now() - entry.timestamp), 0) / entries.length 
        : 0,
    }
  }

  private updateAccessOrder(key: string): void {
    // Remove key from current position
    this.accessOrder = this.accessOrder.filter(k => k !== key)
    // Add to end (most recently used)
    this.accessOrder.push(key)
  }

  private evict(): void {
    if (this.cache.size === 0) return

    let keyToEvict: string

    switch (this.strategy) {
      case 'lru':
        keyToEvict = this.accessOrder[0] // Least recently used
        break
      case 'lfu':
        // Find least frequently used
        let minAccess = Infinity
        keyToEvict = ''
        for (const [key, entry] of this.cache) {
          if (entry.accessCount < minAccess) {
            minAccess = entry.accessCount
            keyToEvict = key
          }
        }
        break
      case 'fifo':
        // Find oldest entry
        let oldestTime = Infinity
        keyToEvict = ''
        for (const [key, entry] of this.cache) {
          if (entry.timestamp < oldestTime) {
            oldestTime = entry.timestamp
            keyToEvict = key
          }
        }
        break
      default:
        keyToEvict = this.accessOrder[0]
    }

    if (keyToEvict) {
      this.remove(keyToEvict)
    }
  }
}

// Specialized cache managers
export const apiCache = new CacheManager({
  ttl: 2 * 60 * 1000, // 2 minutes for API responses
  maxSize: 50,
  strategy: 'lru',
})

export const priceCache = new CacheManager({
  ttl: 30 * 1000, // 30 seconds for price data
  maxSize: 100,
  strategy: 'lru',
})

export const chartCache = new CacheManager({
  ttl: 5 * 60 * 1000, // 5 minutes for chart data
  maxSize: 20,
  strategy: 'lru',
})

export const userCache = new CacheManager({
  ttl: 10 * 60 * 1000, // 10 minutes for user data
  maxSize: 10,
  strategy: 'lfu',
})

// Cache-aware fetch function
export async function cachedFetch<T>(
  url: string,
  options: RequestInit = {},
  cacheOptions: { ttl?: number; cache?: CacheManager<T> } = {}
): Promise<T> {
  const cache = cacheOptions.cache || apiCache
  const cacheKey = `${url}:${JSON.stringify(options)}`

  // Try to get from cache first
  const cached = cache.get(cacheKey)
  if (cached) {
    return cached as T
  }

  // Fetch from network
  const response = await fetch(url, options)
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const data = await response.json()
  
  // Store in cache
  cache.set(cacheKey, data, cacheOptions.ttl)
  
  return data
}

// Memoization decorator for expensive computations
export function memoize<T extends (...args: unknown[]) => unknown>(
  fn: T,
  options: { ttl?: number; maxSize?: number } = {}
): T {
  const cache = new CacheManager({
    ttl: options.ttl || 5 * 60 * 1000,
    maxSize: options.maxSize || 50,
    strategy: 'lru',
  })

  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = JSON.stringify(args)
    
    const cached = cache.get(key)
    if (cached !== null) {
      return cached as ReturnType<T>
    }

    const result = fn(...args)
    cache.set(key, result)

    return result as ReturnType<T>
  }) as T
}

// React hook for cached data
export function useCachedData<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: { ttl?: number; cache?: CacheManager<T> } = {}
) {
  const cache = options.cache || apiCache
  
  // Check cache first
  const cached = cache.get(key)
  if (cached) {
    return { data: cached, loading: false, error: null }
  }

  // If not in cache, we need to fetch
  // This is a simplified version - in a real app you'd use a proper data fetching library
  return { data: null, loading: true, error: null }
}

// Cache warming utilities
export class CacheWarmer {
  private static instance: CacheWarmer
  private warmingQueue: Array<() => Promise<void>> = []
  private isWarming = false

  static getInstance(): CacheWarmer {
    if (!CacheWarmer.instance) {
      CacheWarmer.instance = new CacheWarmer()
    }
    return CacheWarmer.instance
  }

  addToQueue(warmer: () => Promise<void>): void {
    this.warmingQueue.push(warmer)
    this.processQueue()
  }

  async warmPriceData(): Promise<void> {
    const tokens = ['DOGE', 'SHIB', 'PEPE', 'FLOKI']
    const promises = tokens.map(async (token) => {
      try {
        const data = await cachedFetch(`/api/price/${token}`, {}, { cache: priceCache })
        console.log(`Warmed price cache for ${token}`)
      } catch (error) {
        console.warn(`Failed to warm price cache for ${token}:`, error)
      }
    })
    
    await Promise.allSettled(promises)
  }

  async warmChartData(): Promise<void> {
    const timeframes = ['1H', '4H', '1D', '1W']
    const promises = timeframes.map(async (timeframe) => {
      try {
        const data = await cachedFetch(`/api/chart/DOGE/${timeframe}`, {}, { cache: chartCache })
        console.log(`Warmed chart cache for ${timeframe}`)
      } catch (error) {
        console.warn(`Failed to warm chart cache for ${timeframe}:`, error)
      }
    })
    
    await Promise.allSettled(promises)
  }

  private async processQueue(): Promise<void> {
    if (this.isWarming || this.warmingQueue.length === 0) return

    this.isWarming = true
    
    while (this.warmingQueue.length > 0) {
      const warmer = this.warmingQueue.shift()
      if (warmer) {
        try {
          await warmer()
        } catch (error) {
          console.warn('Cache warming failed:', error)
        }
      }
    }
    
    this.isWarming = false
  }
}

// Initialize cache warming on app start
if (typeof window !== 'undefined') {
  const warmer = CacheWarmer.getInstance()
  
  // Warm critical caches on page load
  warmer.addToQueue(() => warmer.warmPriceData())
  warmer.addToQueue(() => warmer.warmChartData())
}

// Cache cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    // Clear sensitive caches
    userCache.clear()
  })
}

export { CacheManager }
