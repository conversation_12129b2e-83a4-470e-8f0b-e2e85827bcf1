// Real-time data integration with WebSocket connections

interface WebSocketMessage {
  type: string
  data: any
  timestamp: number
}

export interface PriceUpdate {
  symbol: string
  price: number
  change24h: number
  volume24h: number
  timestamp: number
}

export interface TradeUpdate {
  symbol: string
  price: number
  amount: number
  side: 'buy' | 'sell'
  timestamp: number
  txHash?: string
}

export interface OrderBookUpdate {
  symbol: string
  bids: Array<[number, number]> // [price, amount]
  asks: Array<[number, number]>
  timestamp: number
}

export interface NotificationUpdate {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: number
  userId?: string
}

type MessageHandler<T = any> = (data: T) => void

export class WebSocketManager {
  private static instance: WebSocketManager
  private connections: Map<string, WebSocket> = new Map()
  private handlers: Map<string, Set<MessageHandler>> = new Map()
  private reconnectAttempts: Map<string, number> = new Map()
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private heartbeatInterval = 30000
  private heartbeatTimers: Map<string, NodeJS.Timeout> = new Map()

  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager()
    }
    return WebSocketManager.instance
  }

  // Connect to a WebSocket endpoint
  connect(endpoint: string, protocols?: string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const ws = new WebSocket(endpoint, protocols)
        
        ws.onopen = () => {
          console.log(`WebSocket connected: ${endpoint}`)
          this.connections.set(endpoint, ws)
          this.reconnectAttempts.set(endpoint, 0)
          this.startHeartbeat(endpoint)
          resolve()
        }

        ws.onmessage = (event) => {
          this.handleMessage(endpoint, event.data)
        }

        ws.onclose = (event) => {
          console.log(`WebSocket closed: ${endpoint}`, event.code, event.reason)
          this.connections.delete(endpoint)
          this.stopHeartbeat(endpoint)
          
          if (!event.wasClean) {
            this.attemptReconnect(endpoint, protocols)
          }
        }

        ws.onerror = (error) => {
          console.error(`WebSocket error: ${endpoint}`, error)
          reject(error)
        }

      } catch (error) {
        reject(error)
      }
    })
  }

  // Disconnect from a WebSocket endpoint
  disconnect(endpoint: string): void {
    const ws = this.connections.get(endpoint)
    if (ws) {
      ws.close(1000, 'Client disconnect')
      this.connections.delete(endpoint)
      this.stopHeartbeat(endpoint)
    }
  }

  // Send message to WebSocket
  send(endpoint: string, message: any): boolean {
    const ws = this.connections.get(endpoint)
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message))
      return true
    }
    return false
  }

  // Subscribe to message type
  subscribe<T>(messageType: string, handler: MessageHandler<T>): () => void {
    if (!this.handlers.has(messageType)) {
      this.handlers.set(messageType, new Set())
    }
    
    this.handlers.get(messageType)!.add(handler)
    
    // Return unsubscribe function
    return () => {
      const handlers = this.handlers.get(messageType)
      if (handlers) {
        handlers.delete(handler)
        if (handlers.size === 0) {
          this.handlers.delete(messageType)
        }
      }
    }
  }

  // Handle incoming messages
  private handleMessage(endpoint: string, data: string): void {
    try {
      const message: WebSocketMessage = JSON.parse(data)
      
      // Handle heartbeat/pong messages
      if (message.type === 'pong') {
        return
      }
      
      // Dispatch to handlers
      const handlers = this.handlers.get(message.type)
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(message.data)
          } catch (error) {
            console.error('Error in message handler:', error)
          }
        })
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error)
    }
  }

  // Attempt to reconnect
  private attemptReconnect(endpoint: string, protocols?: string[]): void {
    const attempts = this.reconnectAttempts.get(endpoint) || 0
    
    if (attempts >= this.maxReconnectAttempts) {
      console.error(`Max reconnection attempts reached for ${endpoint}`)
      return
    }

    const delay = this.reconnectDelay * Math.pow(2, attempts) // Exponential backoff
    
    setTimeout(() => {
      console.log(`Attempting to reconnect to ${endpoint} (attempt ${attempts + 1})`)
      this.reconnectAttempts.set(endpoint, attempts + 1)
      this.connect(endpoint, protocols).catch(() => {
        this.attemptReconnect(endpoint, protocols)
      })
    }, delay)
  }

  // Start heartbeat for connection
  private startHeartbeat(endpoint: string): void {
    const timer = setInterval(() => {
      this.send(endpoint, { type: 'ping', timestamp: Date.now() })
    }, this.heartbeatInterval)
    
    this.heartbeatTimers.set(endpoint, timer)
  }

  // Stop heartbeat for connection
  private stopHeartbeat(endpoint: string): void {
    const timer = this.heartbeatTimers.get(endpoint)
    if (timer) {
      clearInterval(timer)
      this.heartbeatTimers.delete(endpoint)
    }
  }

  // Get connection status
  getConnectionStatus(endpoint: string): string {
    const ws = this.connections.get(endpoint)
    if (!ws) return 'disconnected'
    
    switch (ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting'
      case WebSocket.OPEN:
        return 'connected'
      case WebSocket.CLOSING:
        return 'closing'
      case WebSocket.CLOSED:
        return 'closed'
      default:
        return 'unknown'
    }
  }

  // Disconnect all connections
  disconnectAll(): void {
    this.connections.forEach((ws, endpoint) => {
      this.disconnect(endpoint)
    })
  }
}

// Specialized WebSocket services
export class PriceDataService {
  private wsManager = WebSocketManager.getInstance()
  private endpoint = process.env.NEXT_PUBLIC_PRICE_WS_URL || 'wss://api.pawpumps.com/ws/prices'
  private subscribedSymbols = new Set<string>()

  async connect(): Promise<void> {
    await this.wsManager.connect(this.endpoint)
  }

  subscribeToPrice(symbol: string, handler: MessageHandler<PriceUpdate>): () => void {
    // Subscribe to price updates
    const unsubscribe = this.wsManager.subscribe('price_update', (data: PriceUpdate) => {
      if (data.symbol === symbol) {
        handler(data)
      }
    })

    // Send subscription message
    if (!this.subscribedSymbols.has(symbol)) {
      this.wsManager.send(this.endpoint, {
        type: 'subscribe',
        channel: 'prices',
        symbol: symbol
      })
      this.subscribedSymbols.add(symbol)
    }

    return () => {
      unsubscribe()
      this.unsubscribeFromPrice(symbol)
    }
  }

  private unsubscribeFromPrice(symbol: string): void {
    this.subscribedSymbols.delete(symbol)
    this.wsManager.send(this.endpoint, {
      type: 'unsubscribe',
      channel: 'prices',
      symbol: symbol
    })
  }

  disconnect(): void {
    this.wsManager.disconnect(this.endpoint)
    this.subscribedSymbols.clear()
  }
}

export class TradeDataService {
  private wsManager = WebSocketManager.getInstance()
  private endpoint = process.env.NEXT_PUBLIC_TRADE_WS_URL || 'wss://api.pawpumps.com/ws/trades'

  async connect(): Promise<void> {
    await this.wsManager.connect(this.endpoint)
  }

  subscribeToTrades(symbol: string, handler: MessageHandler<TradeUpdate>): () => void {
    const unsubscribe = this.wsManager.subscribe('trade_update', (data: TradeUpdate) => {
      if (data.symbol === symbol) {
        handler(data)
      }
    })

    this.wsManager.send(this.endpoint, {
      type: 'subscribe',
      channel: 'trades',
      symbol: symbol
    })

    return unsubscribe
  }

  subscribeToOrderBook(symbol: string, handler: MessageHandler<OrderBookUpdate>): () => void {
    const unsubscribe = this.wsManager.subscribe('orderbook_update', (data: OrderBookUpdate) => {
      if (data.symbol === symbol) {
        handler(data)
      }
    })

    this.wsManager.send(this.endpoint, {
      type: 'subscribe',
      channel: 'orderbook',
      symbol: symbol
    })

    return unsubscribe
  }

  disconnect(): void {
    this.wsManager.disconnect(this.endpoint)
  }
}

export class NotificationService {
  private wsManager = WebSocketManager.getInstance()
  private endpoint = process.env.NEXT_PUBLIC_NOTIFICATION_WS_URL || 'wss://api.pawpumps.com/ws/notifications'

  async connect(userId?: string): Promise<void> {
    const url = userId ? `${this.endpoint}?userId=${userId}` : this.endpoint
    await this.wsManager.connect(url)
  }

  subscribeToNotifications(handler: MessageHandler<NotificationUpdate>): () => void {
    return this.wsManager.subscribe('notification', handler)
  }

  disconnect(): void {
    this.wsManager.disconnect(this.endpoint)
  }
}

// Export singleton instances
export const priceDataService = new PriceDataService()
export const tradeDataService = new TradeDataService()
export const notificationService = new NotificationService()

// Auto-cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    WebSocketManager.getInstance().disconnectAll()
  })
}
