// Tree-shaking optimization utilities

import React from 'react'

// Optimized icon imports - only import what we need
export const icons = {
  // Trading icons
  TrendingUp: () => import('lucide-react').then(mod => ({ default: mod.TrendingUp })),
  TrendingDown: () => import('lucide-react').then(mod => ({ default: mod.TrendingDown })),
  BarChart2: () => import('lucide-react').then(mod => ({ default: mod.BarChart2 })),
  LineChart: () => import('lucide-react').then(mod => ({ default: mod.LineChart })),
  
  // Navigation icons
  Home: () => import('lucide-react').then(mod => ({ default: mod.Home })),
  Settings: () => import('lucide-react').then(mod => ({ default: mod.Settings })),
  User: () => import('lucide-react').then(mod => ({ default: mod.User })),
  Menu: () => import('lucide-react').then(mod => ({ default: mod.Menu })),

  // Action icons
  Plus: () => import('lucide-react').then(mod => ({ default: mod.Plus })),
  Minus: () => import('lucide-react').then(mod => ({ default: mod.Minus })),
  X: () => import('lucide-react').then(mod => ({ default: mod.X })),
  Check: () => import('lucide-react').then(mod => ({ default: mod.Check })),

  // Wallet icons
  Wallet: () => import('lucide-react').then(mod => ({ default: mod.Wallet })),
  CreditCard: () => import('lucide-react').then(mod => ({ default: mod.CreditCard })),

  // Social icons
  Users: () => import('lucide-react').then(mod => ({ default: mod.Users })),
  MessageCircle: () => import('lucide-react').then(mod => ({ default: mod.MessageCircle })),
  Heart: () => import('lucide-react').then(mod => ({ default: mod.Heart })),
  Share: () => import('lucide-react').then(mod => ({ default: mod.Share })),
}

// Optimized utility imports
export const utils = {
  // Date utilities - only import specific functions
  formatDistance: () => import('date-fns/formatDistance'),
  format: () => import('date-fns/format'),
  parseISO: () => import('date-fns/parseISO'),
  
  // Note: Lodash utilities removed due to TypeScript declaration issues
  // Can be re-added when @types/lodash is properly installed
}

// Dynamic icon loader with caching
class IconLoader {
  private static cache = new Map<string, React.ComponentType>()
  
  static async load(iconName: keyof typeof icons): Promise<React.ComponentType> {
    if (this.cache.has(iconName)) {
      return this.cache.get(iconName)!
    }
    
    try {
      const iconLoader = icons[iconName]
      const iconModule = await iconLoader()
      const IconComponent = iconModule.default
      this.cache.set(iconName, IconComponent)
      return IconComponent
    } catch (error) {
      console.warn(`Failed to load icon: ${iconName}`, error)
      // Return a fallback icon
      return () => React.createElement('div', { className: 'w-4 h-4 bg-gray-400 rounded' })
    }
  }
  
  static preload(iconNames: (keyof typeof icons)[]) {
    iconNames.forEach(iconName => {
      this.load(iconName).catch(() => {
        // Silently fail for preloading
      })
    })
  }
}

// Dynamic utility loader
class UtilLoader {
  private static cache = new Map<string, any>()
  
  static async load<T>(utilName: keyof typeof utils): Promise<T> {
    if (this.cache.has(utilName)) {
      return this.cache.get(utilName)
    }
    
    try {
      const utilLoader = utils[utilName]
      const utilModule = await utilLoader()
      // For date-fns, the function is the default export of the module
      const util = (utilModule as any).default || utilModule
      this.cache.set(utilName, util)
      return util
    } catch (error) {
      console.warn(`Failed to load utility: ${utilName}`, error)
      throw error
    }
  }
}

// React component for dynamic icons
export function DynamicIcon({ 
  name, 
  className, 
  ...props 
}: { 
  name: keyof typeof icons
  className?: string
  [key: string]: any
}) {
  const [IconComponent, setIconComponent] = React.useState<React.ComponentType | null>(null)
  
  React.useEffect(() => {
    IconLoader.load(name).then(setIconComponent)
  }, [name])
  
  if (!IconComponent) {
    return React.createElement('div', {
      className: `animate-pulse bg-gray-400 rounded ${className}`
    })
  }
  
  return React.createElement(IconComponent as any, { className, ...props })
}

// Hook for dynamic utilities
export function useDynamicUtil<T>(utilName: keyof typeof utils) {
  const [util, setUtil] = React.useState<T | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<Error | null>(null)
  
  React.useEffect(() => {
    UtilLoader.load<T>(utilName)
      .then(loadedUtil => {
        setUtil(loadedUtil)
        setLoading(false)
      })
      .catch(err => {
        setError(err)
        setLoading(false)
      })
  }, [utilName])
  
  return { util, loading, error }
}

// Optimized chart imports
export const chartComponents = {
  LineChart: () => import('recharts').then(mod => ({ LineChart: mod.LineChart })),
  AreaChart: () => import('recharts').then(mod => ({ AreaChart: mod.AreaChart })),
  BarChart: () => import('recharts').then(mod => ({ BarChart: mod.BarChart })),
  PieChart: () => import('recharts').then(mod => ({ PieChart: mod.PieChart })),
  
  // Chart components
  XAxis: () => import('recharts').then(mod => ({ XAxis: mod.XAxis })),
  YAxis: () => import('recharts').then(mod => ({ YAxis: mod.YAxis })),
  CartesianGrid: () => import('recharts').then(mod => ({ CartesianGrid: mod.CartesianGrid })),
  Tooltip: () => import('recharts').then(mod => ({ Tooltip: mod.Tooltip })),
  Legend: () => import('recharts').then(mod => ({ Legend: mod.Legend })),
  ResponsiveContainer: () => import('recharts').then(mod => ({ ResponsiveContainer: mod.ResponsiveContainer })),
}

// Bundle size analyzer
export class TreeShakingAnalyzer {
  static analyzeImports() {
    if (typeof window === 'undefined') return
    
    const scripts = Array.from(document.querySelectorAll('script[src]'))
    const analysis = {
      totalScripts: scripts.length,
      estimatedSavings: 0,
      recommendations: [] as string[],
    }
    
    // Analyze for common tree-shaking opportunities
    scripts.forEach(script => {
      const src = (script as HTMLScriptElement).src
      
      if (src.includes('lucide-react') && !src.includes('esm')) {
        analysis.recommendations.push('Use ESM imports for Lucide React icons')
        analysis.estimatedSavings += 50 // KB
      }
      
      if (src.includes('lodash') && !src.includes('lodash-es')) {
        analysis.recommendations.push('Use lodash-es for better tree-shaking')
        analysis.estimatedSavings += 30 // KB
      }
      
      if (src.includes('date-fns') && src.includes('index')) {
        analysis.recommendations.push('Import specific date-fns functions')
        analysis.estimatedSavings += 20 // KB
      }
    })
    
    return analysis
  }
  
  static generateReport() {
    const analysis = this.analyzeImports()
    
    return {
      timestamp: new Date().toISOString(),
      ...analysis,
      iconCacheSize: IconLoader['cache'].size,
      utilCacheSize: UtilLoader['cache'].size,
    }
  }
}

// Preload critical icons and utilities
export function preloadCriticalAssets() {
  // Preload commonly used icons
  IconLoader.preload([
    'Home',
    'Menu',
    'User',
    'Wallet',
    'TrendingUp',
    'BarChart2',
  ])
  
  // Preload commonly used utilities
  Promise.allSettled([
    UtilLoader.load('format'),
    UtilLoader.load('formatDistance'),
    UtilLoader.load('parseISO'),
  ]).then(() => {
    console.log('Critical assets preloaded')
  })
}

// Webpack plugin helper for tree-shaking analysis
export function createTreeShakingPlugin() {
  return {
    name: 'tree-shaking-analyzer',
    buildStart() {
      console.log('🌳 Tree-shaking analysis started')
    },
    generateBundle(options: any, bundle: any) {
      const analysis = TreeShakingAnalyzer.generateReport()
      console.log('🌳 Tree-shaking analysis:', analysis)
      
      // Write analysis to file in development
      if (process.env.NODE_ENV === 'development') {
        const fs = require('fs')
        fs.writeFileSync(
          'tree-shaking-analysis.json',
          JSON.stringify(analysis, null, 2)
        )
      }
    },
  }
}

// Export everything
export {
  IconLoader,
  UtilLoader,
}

// Auto-preload in production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  // Delay preloading to not interfere with initial page load
  setTimeout(preloadCriticalAssets, 1000)
}
