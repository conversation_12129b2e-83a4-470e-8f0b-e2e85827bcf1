// Resource preloader for optimizing page load times
// Implements intelligent preloading strategies

interface PreloadOptions {
  priority?: 'high' | 'low'
  crossOrigin?: 'anonymous' | 'use-credentials'
  as?: 'script' | 'style' | 'font' | 'image' | 'fetch'
  type?: string
}

interface PreloadResource {
  href: string
  options: PreloadOptions
}

class ResourcePreloader {
  private preloadedResources = new Set<string>()
  private preloadQueue: PreloadResource[] = []
  private isProcessing = false

  // Preload a single resource
  preload(href: string, options: PreloadOptions = {}): void {
    if (this.preloadedResources.has(href)) return

    const resource: PreloadResource = { href, options }
    this.preloadQueue.push(resource)
    this.processQueue()
  }

  // Preload multiple resources
  preloadBatch(resources: Array<{ href: string; options?: PreloadOptions }>): void {
    resources.forEach(({ href, options = {} }) => {
      this.preload(href, options)
    })
  }

  // Process the preload queue
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.preloadQueue.length === 0) return

    this.isProcessing = true

    while (this.preloadQueue.length > 0) {
      const resource = this.preloadQueue.shift()!
      await this.preloadResource(resource)
    }

    this.isProcessing = false
  }

  // Preload a single resource
  private preloadResource(resource: PreloadResource): Promise<void> {
    return new Promise((resolve) => {
      if (typeof window === 'undefined') {
        resolve()
        return
      }

      const { href, options } = resource
      const { priority = 'low', crossOrigin, as = 'fetch', type } = options

      // Create link element for preloading
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = href
      link.as = as

      if (crossOrigin) {
        link.crossOrigin = crossOrigin
      }

      if (type) {
        link.type = type
      }

      // Set priority
      if (priority === 'high') {
        link.setAttribute('importance', 'high')
      }

      link.onload = () => {
        this.preloadedResources.add(href)
        resolve()
      }

      link.onerror = () => {
        console.warn(`Failed to preload resource: ${href}`)
        resolve()
      }

      document.head.appendChild(link)

      // Fallback timeout
      setTimeout(() => {
        this.preloadedResources.add(href)
        resolve()
      }, 5000)
    })
  }

  // Preload critical resources for the current page
  preloadCriticalResources(): void {
    const criticalResources = [
      {
        href: '/fonts/inter-var.woff2',
        options: { as: 'font' as const, type: 'font/woff2', crossOrigin: 'anonymous' as const, priority: 'high' as const }
      },
      {
        href: '/_next/static/css/app/layout.css',
        options: { as: 'style' as const, priority: 'high' as const }
      }
    ]

    this.preloadBatch(criticalResources)
  }

  // Preload resources based on user interaction
  preloadOnInteraction(): void {
    const interactionResources = [
      {
        href: '/_next/static/chunks/trading-interface.js',
        options: { as: 'script' as const, priority: 'low' as const }
      },
      {
        href: '/_next/static/chunks/wallet-connect-modal.js',
        options: { as: 'script' as const, priority: 'low' as const }
      }
    ]

    // Preload on first user interaction
    const preloadOnFirstInteraction = () => {
      this.preloadBatch(interactionResources)
    }

    ['mousedown', 'touchstart', 'keydown'].forEach(event => {
      document.addEventListener(event, preloadOnFirstInteraction, { once: true, passive: true })
    })
  }

  // Preload resources based on route
  preloadForRoute(route: string): void {
    const routeResources: Record<string, Array<{ href: string; options?: PreloadOptions }>> = {
      '/trade': [
        {
          href: '/_next/static/chunks/trading-chart.js',
          options: { as: 'script', priority: 'high' }
        },
        {
          href: '/_next/static/chunks/charts.js',
          options: { as: 'script', priority: 'high' }
        }
      ],
      '/analytics': [
        {
          href: '/_next/static/chunks/analytics-dashboard.js',
          options: { as: 'script', priority: 'high' }
        },
        {
          href: '/_next/static/chunks/charts.js',
          options: { as: 'script', priority: 'high' }
        }
      ],
      '/governance': [
        {
          href: '/_next/static/chunks/governance.js',
          options: { as: 'script', priority: 'high' }
        }
      ]
    }

    const resources = routeResources[route]
    if (resources) {
      this.preloadBatch(resources)
    }
  }

  // Intelligent preloading based on user behavior
  intelligentPreload(): void {
    // Preload based on hover over navigation links
    const navLinks = document.querySelectorAll('nav a[href]')
    
    navLinks.forEach(link => {
      const href = link.getAttribute('href')
      if (!href) return

      link.addEventListener('mouseenter', () => {
        this.preloadForRoute(href)
      }, { passive: true })
    })
  }

  // Preload images in viewport
  preloadImagesInViewport(): void {
    const images = document.querySelectorAll('img[data-src]')
    
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const src = img.getAttribute('data-src')
          
          if (src) {
            this.preload(src, { as: 'image', priority: 'low' })
          }
          
          imageObserver.unobserve(img)
        }
      })
    }, {
      threshold: 0.1,
      rootMargin: '50px'
    })

    images.forEach(img => imageObserver.observe(img))
  }

  // Check if resource is already preloaded
  isPreloaded(href: string): boolean {
    return this.preloadedResources.has(href)
  }

  // Get preload statistics
  getStats(): { preloadedCount: number; queueLength: number } {
    return {
      preloadedCount: this.preloadedResources.size,
      queueLength: this.preloadQueue.length
    }
  }
}

// Global instance
export const resourcePreloader = new ResourcePreloader()

// Initialize preloading strategies
export function initializeResourcePreloader(): void {
  if (typeof window === 'undefined') return

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setupPreloading()
    })
  } else {
    setupPreloading()
  }
}

function setupPreloading(): void {
  // Preload critical resources immediately
  resourcePreloader.preloadCriticalResources()

  // Set up interaction-based preloading
  resourcePreloader.preloadOnInteraction()

  // Set up intelligent preloading after a short delay
  setTimeout(() => {
    resourcePreloader.intelligentPreload()
    resourcePreloader.preloadImagesInViewport()
  }, 1000)
}

// React hook for component-level preloading
export function useResourcePreloader() {
  const preloadForComponent = (resources: Array<{ href: string; options?: PreloadOptions }>) => {
    resourcePreloader.preloadBatch(resources)
  }

  const preloadOnHover = (href: string, options?: PreloadOptions) => {
    return {
      onMouseEnter: () => resourcePreloader.preload(href, options)
    }
  }

  return {
    preloadForComponent,
    preloadOnHover,
    isPreloaded: resourcePreloader.isPreloaded.bind(resourcePreloader),
    getStats: resourcePreloader.getStats.bind(resourcePreloader)
  }
}
