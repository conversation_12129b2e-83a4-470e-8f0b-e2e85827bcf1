import {
  ErrorType,
  ErrorSeverity,
  createNetworkError,
  createValidationError,
  createBlockchainError,
  createWalletError,
  classifyError,
  withRetry,
  globalErrorHandler,
  ConsoleErrorReporter,
} from '../error-handling'

describe('Error Handling System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Reset error handler state by creating a new instance
    // Note: We can't directly clear the private errorCounts, but tests should be independent
  })

  describe('Error Factory Functions', () => {
    it('creates network errors correctly', () => {
      const error = createNetworkError('Connection failed', { status: 500 })
      
      expect(error.type).toBe(ErrorType.NETWORK)
      expect(error.severity).toBe(ErrorSeverity.MEDIUM)
      expect(error.message).toBe('Connection failed')
      expect(error.retryable).toBe(true)
      expect(error.maxRetries).toBe(3)
      expect(error.details).toEqual({ status: 500 })
    })

    it('creates validation errors correctly', () => {
      const error = createValidationError('Invalid input', { field: 'email' })
      
      expect(error.type).toBe(ErrorType.VALIDATION)
      expect(error.severity).toBe(ErrorSeverity.LOW)
      expect(error.message).toBe('Invalid input')
      expect(error.retryable).toBe(false)
      expect(error.details).toEqual({ field: 'email' })
    })

    it('creates blockchain errors correctly', () => {
      const error = createBlockchainError('Transaction failed', { gasUsed: 21000 })
      
      expect(error.type).toBe(ErrorType.BLOCKCHAIN)
      expect(error.severity).toBe(ErrorSeverity.HIGH)
      expect(error.message).toBe('Transaction failed')
      expect(error.retryable).toBe(true)
      expect(error.maxRetries).toBe(2)
    })

    it('creates wallet errors correctly', () => {
      const error = createWalletError('Wallet not connected')
      
      expect(error.type).toBe(ErrorType.WALLET)
      expect(error.severity).toBe(ErrorSeverity.HIGH)
      expect(error.message).toBe('Wallet not connected')
      expect(error.retryable).toBe(false)
    })
  })

  describe('Error Classification', () => {
    it('classifies network errors', () => {
      const networkError = new Error('fetch failed')
      const classified = classifyError(networkError)
      
      expect(classified.type).toBe(ErrorType.NETWORK)
      expect(classified.message).toBe('fetch failed')
      expect(classified.retryable).toBe(true)
    })

    it('classifies wallet errors', () => {
      const walletError = new Error('user rejected transaction')
      const classified = classifyError(walletError)
      
      expect(classified.type).toBe(ErrorType.WALLET)
      expect(classified.retryable).toBe(false)
    })

    it('classifies blockchain errors', () => {
      const blockchainError = new Error('transaction reverted')
      const classified = classifyError(blockchainError)
      
      expect(classified.type).toBe(ErrorType.BLOCKCHAIN)
      expect(classified.retryable).toBe(true)
    })

    it('classifies validation errors', () => {
      const validationError = new Error('invalid email format')
      const classified = classifyError(validationError)
      
      expect(classified.type).toBe(ErrorType.VALIDATION)
      expect(classified.retryable).toBe(false)
    })

    it('classifies unknown errors', () => {
      const unknownError = new Error('something went wrong')
      const classified = classifyError(unknownError)
      
      expect(classified.type).toBe(ErrorType.UNKNOWN)
      expect(classified.retryable).toBe(false)
    })

    it('preserves AppError instances', () => {
      const appError = createNetworkError('Network issue')
      const classified = classifyError(appError)
      
      expect(classified).toBe(appError)
    })

    it('adds context to classified errors', () => {
      const error = new Error('test error')
      const context = { component: 'TestComponent', action: 'testAction' }
      const classified = classifyError(error, context)
      
      expect(classified.details).toMatchObject(context)
    })
  })

  describe('Retry Mechanism', () => {
    it('retries failed operations', async () => {
      let attempts = 0
      const operation = jest.fn().mockImplementation(() => {
        attempts++
        if (attempts < 3) {
          throw new Error('temporary failure')
        }
        return 'success'
      })

      const result = await withRetry(operation, { maxRetries: 3, baseDelay: 10 })
      
      expect(result).toBe('success')
      expect(operation).toHaveBeenCalledTimes(3)
    })

    it('respects max retry limit', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('persistent failure'))

      await expect(withRetry(operation, { maxRetries: 2, baseDelay: 10 }))
        .rejects.toThrow('persistent failure')
      
      expect(operation).toHaveBeenCalledTimes(3) // Initial + 2 retries
    })

    it('does not retry non-retryable errors', async () => {
      const operation = jest.fn().mockRejectedValue(createValidationError('Invalid input'))

      await expect(withRetry(operation, { maxRetries: 3, baseDelay: 10 }))
        .rejects.toThrow('Invalid input')
      
      expect(operation).toHaveBeenCalledTimes(1) // No retries
    })

    it('applies exponential backoff', async () => {
      const delays: number[] = []
      const originalSetTimeout = global.setTimeout
      
      global.setTimeout = jest.fn().mockImplementation((callback, delay) => {
        delays.push(delay)
        return originalSetTimeout(callback, 0) // Execute immediately for test
      }) as any

      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('fail 1'))
        .mockRejectedValueOnce(new Error('fail 2'))
        .mockResolvedValueOnce('success')

      await withRetry(operation, { 
        maxRetries: 3, 
        baseDelay: 100, 
        backoffMultiplier: 2 
      })

      expect(delays).toHaveLength(2)
      expect(delays[0]).toBeGreaterThanOrEqual(100) // First retry delay
      expect(delays[1]).toBeGreaterThanOrEqual(200) // Second retry delay with backoff

      global.setTimeout = originalSetTimeout
    })

    it('adds jitter to prevent thundering herd', async () => {
      const delays: number[] = []
      const originalSetTimeout = global.setTimeout
      
      global.setTimeout = jest.fn().mockImplementation((callback, delay) => {
        delays.push(delay)
        return originalSetTimeout(callback, 0)
      }) as any

      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('fail'))
        .mockResolvedValueOnce('success')

      await withRetry(operation, { maxRetries: 1, baseDelay: 1000 })

      // Jitter should add up to 1000ms random delay
      expect(delays[0]).toBeGreaterThan(1000)
      expect(delays[0]).toBeLessThan(2000)

      global.setTimeout = originalSetTimeout
    })
  })

  describe('Error Reporter', () => {
    it('reports errors to console', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const reporter = new ConsoleErrorReporter()
      const error = createNetworkError('Test error')

      reporter.report(error)

      expect(consoleSpy).toHaveBeenCalledWith('Application Error:', expect.objectContaining({
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: 'Test error'
      }))

      consoleSpy.mockRestore()
    })

    it('uses appropriate log levels', () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation()
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation()
      const consoleInfoSpy = jest.spyOn(console, 'info').mockImplementation()
      
      const reporter = new ConsoleErrorReporter()

      // Critical error
      reporter.report({ ...createNetworkError('Critical'), severity: ErrorSeverity.CRITICAL })
      expect(consoleErrorSpy).toHaveBeenCalled()

      // Medium error
      reporter.report({ ...createNetworkError('Medium'), severity: ErrorSeverity.MEDIUM })
      expect(consoleWarnSpy).toHaveBeenCalled()

      // Low error
      reporter.report({ ...createValidationError('Low'), severity: ErrorSeverity.LOW })
      expect(consoleInfoSpy).toHaveBeenCalled()

      consoleErrorSpy.mockRestore()
      consoleWarnSpy.mockRestore()
      consoleInfoSpy.mockRestore()
    })
  })

  describe('Global Error Handler', () => {
    it('handles errors and reports them', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const error = new Error('Test error')

      globalErrorHandler.handleError(error)

      expect(consoleSpy).toHaveBeenCalled()
      consoleSpy.mockRestore()
    })

    it('tracks error frequency', () => {
      const error1 = createNetworkError('Network error')
      const error2 = createNetworkError('Network error') // Same error
      const error3 = createValidationError('Validation error') // Different error

      globalErrorHandler.handleError(error1)
      globalErrorHandler.handleError(error2)
      globalErrorHandler.handleError(error3)

      const stats = globalErrorHandler.getErrorStats()
      expect(stats['NETWORK:Network error']).toBe(2)
      expect(stats['VALIDATION:Validation error']).toBe(1)
    })

    it('handles unhandled promise rejections', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      
      // Simulate unhandled promise rejection
      const event = new Event('unhandledrejection') as any
      event.reason = 'Unhandled promise rejection'
      
      window.dispatchEvent(event)

      expect(consoleSpy).toHaveBeenCalled()
      consoleSpy.mockRestore()
    })

    it('handles uncaught errors', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      
      // Simulate uncaught error
      const event = new ErrorEvent('error', {
        message: 'Uncaught error',
        filename: 'test.js',
        lineno: 10,
        colno: 5,
        error: new Error('Uncaught error')
      })
      
      window.dispatchEvent(event)

      expect(consoleSpy).toHaveBeenCalled()
      consoleSpy.mockRestore()
    })
  })

  describe('Error Context', () => {
    it('includes context in error details', () => {
      const error = new Error('Test error')
      const context = {
        component: 'TestComponent',
        action: 'testAction',
        metadata: { userId: '123' }
      }

      const classified = classifyError(error, context)

      expect(classified.details).toMatchObject(context)
    })

    it('merges multiple context objects', () => {
      const error = new Error('Test error')
      const context1 = { component: 'TestComponent' }
      const context2 = { action: 'testAction' }

      const classified = classifyError(error, { ...context1, ...context2 })

      expect(classified.details).toMatchObject({
        component: 'TestComponent',
        action: 'testAction'
      })
    })
  })

  describe('Error Utilities', () => {
    it('handles async errors', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const failingPromise = Promise.reject(new Error('Async error'))

      await expect(async () => {
        const { handleAsyncError } = await import('../error-handling')
        await handleAsyncError(failingPromise)
      }).rejects.toThrow('Async error')

      expect(consoleSpy).toHaveBeenCalled()
      consoleSpy.mockRestore()
    })

    it('provides safe async execution with fallback', async () => {
      const { safeAsync } = await import('../error-handling')
      const failingOperation = () => Promise.reject(new Error('Operation failed'))
      const fallbackValue = 'fallback'

      const result = await safeAsync(failingOperation, fallbackValue)

      expect(result).toBe(fallbackValue)
    })
  })
})
