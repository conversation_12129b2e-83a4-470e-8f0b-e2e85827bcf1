// Comprehensive error handling utilities

export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  BLOCKCHAIN = 'BL<PERSON><PERSON>CHAIN',
  WALLET = 'WALLET',
  RATE_LIMIT = 'RATE_LIMIT',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN',
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface AppError {
  type: ErrorType
  severity: ErrorSeverity
  message: string
  code?: string
  details?: Record<string, any>
  timestamp: Date
  userId?: string
  sessionId?: string
  stack?: string
  retryable: boolean
  retryCount?: number
  maxRetries?: number
}

export interface ErrorContext {
  component?: string
  action?: string
  userId?: string
  sessionId?: string
  attempt?: number
  metadata?: Record<string, any>
}

// Error factory functions
export function createNetworkError(
  message: string,
  details?: Record<string, any>,
  context?: ErrorContext
): AppError {
  return {
    type: ErrorType.NETWORK,
    severity: ErrorSeverity.MEDIUM,
    message,
    details: { ...details, ...context },
    timestamp: new Date(),
    retryable: true,
    maxRetries: 3,
  }
}

export function createValidationError(
  message: string,
  details?: Record<string, any>,
  context?: ErrorContext
): AppError {
  return {
    type: ErrorType.VALIDATION,
    severity: ErrorSeverity.LOW,
    message,
    details: { ...details, ...context },
    timestamp: new Date(),
    retryable: false,
  }
}

export function createBlockchainError(
  message: string,
  details?: Record<string, any>,
  context?: ErrorContext
): AppError {
  return {
    type: ErrorType.BLOCKCHAIN,
    severity: ErrorSeverity.HIGH,
    message,
    details: { ...details, ...context },
    timestamp: new Date(),
    retryable: true,
    maxRetries: 2,
  }
}

export function createWalletError(
  message: string,
  details?: Record<string, any>,
  context?: ErrorContext
): AppError {
  return {
    type: ErrorType.WALLET,
    severity: ErrorSeverity.HIGH,
    message,
    details: { ...details, ...context },
    timestamp: new Date(),
    retryable: false,
  }
}

export function createAuthError(
  message: string,
  details?: Record<string, any>,
  context?: ErrorContext
): AppError {
  return {
    type: ErrorType.AUTHENTICATION,
    severity: ErrorSeverity.HIGH,
    message,
    details: { ...details, ...context },
    timestamp: new Date(),
    retryable: false,
  }
}

// Error classification from native errors
export function classifyError(error: Error | unknown, context?: ErrorContext): AppError {
  // Check if it's already an AppError by checking for required properties
  if (error && typeof error === 'object' && 'type' in error && 'severity' in error && 'message' in error) {
    return error as AppError
  }

  const errorMessage = error instanceof Error ? error.message : String(error)
  const errorStack = error instanceof Error ? error.stack : undefined

  // Network errors
  if (errorMessage.includes('fetch') || errorMessage.includes('network') || errorMessage.includes('timeout')) {
    return createNetworkError(errorMessage, { originalError: error, stack: errorStack }, context)
  }

  // Wallet errors
  if (errorMessage.includes('wallet') || errorMessage.includes('MetaMask') || errorMessage.includes('user rejected')) {
    return createWalletError(errorMessage, { originalError: error, stack: errorStack }, context)
  }

  // Blockchain errors
  if (errorMessage.includes('transaction') || errorMessage.includes('gas') || errorMessage.includes('revert')) {
    return createBlockchainError(errorMessage, { originalError: error, stack: errorStack }, context)
  }

  // Validation errors
  if (errorMessage.includes('validation') || errorMessage.includes('invalid') || errorMessage.includes('required')) {
    return createValidationError(errorMessage, { originalError: error, stack: errorStack }, context)
  }

  // Default to unknown error
  return {
    type: ErrorType.UNKNOWN,
    severity: ErrorSeverity.MEDIUM,
    message: errorMessage,
    details: { originalError: error, stack: errorStack, ...context },
    timestamp: new Date(),
    retryable: false,
  }
}

// Error recovery strategies
export interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  retryCondition?: (error: AppError) => boolean
}

export const defaultRetryConfig: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2,
  retryCondition: (error) => error.retryable,
}

export async function withRetry<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {},
  context?: ErrorContext
): Promise<T> {
  const finalConfig = { ...defaultRetryConfig, ...config }
  let lastError: AppError

  for (let attempt = 0; attempt <= finalConfig.maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = classifyError(error, { ...context, attempt })
      
      // Don't retry if not retryable or max attempts reached
      if (!finalConfig.retryCondition?.(lastError) || attempt === finalConfig.maxRetries) {
        throw lastError
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        finalConfig.baseDelay * Math.pow(finalConfig.backoffMultiplier, attempt),
        finalConfig.maxDelay
      )

      // Add jitter to prevent thundering herd
      const jitteredDelay = delay + Math.random() * 1000

      await new Promise(resolve => setTimeout(resolve, jitteredDelay))
    }
  }

  throw lastError!
}

// Error reporting
export interface ErrorReporter {
  report(error: AppError): void
}

export class ConsoleErrorReporter implements ErrorReporter {
  report(error: AppError): void {
    const logLevel = this.getLogLevel(error.severity)
    console[logLevel]('Application Error:', {
      type: error.type,
      severity: error.severity,
      message: error.message,
      details: error.details,
      timestamp: error.timestamp,
    })
  }

  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error'
      case ErrorSeverity.MEDIUM:
        return 'warn'
      case ErrorSeverity.LOW:
        return 'info'
      default:
        return 'error'
    }
  }
}

export class RemoteErrorReporter implements ErrorReporter {
  constructor(private endpoint: string, private apiKey?: string) {}

  async report(error: AppError): Promise<void> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      }

      if (this.apiKey) {
        headers['Authorization'] = `Bearer ${this.apiKey}`
      }

      await fetch(this.endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          ...error,
          userAgent: navigator.userAgent,
          url: window.location.href,
        }),
      })
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
    }
  }
}

// Global error handler
export class ErrorHandler {
  private reporters: ErrorReporter[] = []
  private errorCounts = new Map<string, number>()

  constructor() {
    // Only setup global handlers in browser environment
    if (typeof window !== 'undefined') {
      this.setupGlobalHandlers()
    }
  }

  addReporter(reporter: ErrorReporter): void {
    this.reporters.push(reporter)
  }

  handleError(error: Error | AppError, context?: ErrorContext): void {
    const appError = (error && typeof error === 'object' && 'type' in error && 'severity' in error && 'message' in error)
      ? error as AppError
      : classifyError(error, context)
    
    // Track error frequency
    const errorKey = `${appError.type}:${appError.message}`
    this.errorCounts.set(errorKey, (this.errorCounts.get(errorKey) || 0) + 1)

    // Report to all reporters
    this.reporters.forEach(reporter => {
      try {
        reporter.report(appError)
      } catch (reportingError) {
        console.error('Error reporter failed:', reportingError)
      }
    })
  }

  getErrorStats(): Record<string, number> {
    return Object.fromEntries(this.errorCounts)
  }

  private setupGlobalHandlers(): void {
    // Only setup global handlers in browser environment
    if (typeof window === 'undefined') {
      return
    }

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(new Error(event.reason), { component: 'global', action: 'unhandledrejection' })
    })

    // Handle uncaught errors
    window.addEventListener('error', (event) => {
      this.handleError(event.error || new Error(event.message), {
        component: 'global',
        action: 'uncaughterror',
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        }
      })
    })
  }
}

// Global error handler instance (lazy initialization)
let _globalErrorHandler: ErrorHandler | null = null

export function getGlobalErrorHandler(): ErrorHandler {
  if (!_globalErrorHandler) {
    _globalErrorHandler = new ErrorHandler()

    // Add default console reporter
    _globalErrorHandler.addReporter(new ConsoleErrorReporter())

    // Add remote reporter in production
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_ERROR_REPORTING_ENDPOINT) {
      _globalErrorHandler.addReporter(
        new RemoteErrorReporter(
          process.env.NEXT_PUBLIC_ERROR_REPORTING_ENDPOINT,
          process.env.NEXT_PUBLIC_ERROR_REPORTING_API_KEY
        )
      )
    }
  }
  return _globalErrorHandler
}

// Backward compatibility
export const globalErrorHandler = {
  get addReporter() { return getGlobalErrorHandler().addReporter.bind(getGlobalErrorHandler()) },
  get handleError() { return getGlobalErrorHandler().handleError.bind(getGlobalErrorHandler()) },
  get getErrorStats() { return getGlobalErrorHandler().getErrorStats.bind(getGlobalErrorHandler()) },
}

// Utility functions for common error scenarios
export function handleAsyncError<T>(
  promise: Promise<T>,
  context?: ErrorContext
): Promise<T> {
  return promise.catch(error => {
    const appError = classifyError(error, context)
    getGlobalErrorHandler().handleError(appError)
    throw appError
  })
}

export function safeAsync<T>(
  operation: () => Promise<T>,
  fallback: T,
  context?: ErrorContext
): Promise<T> {
  return operation().catch(error => {
    getGlobalErrorHandler().handleError(classifyError(error, context))
    return fallback
  })
}
