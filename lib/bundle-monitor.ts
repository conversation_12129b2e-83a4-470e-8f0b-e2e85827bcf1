// Bundle size monitoring and optimization utilities

import React from 'react'

interface BundleMetrics {
  componentName: string
  loadTime: number
  size?: number
  timestamp: number
  route: string
  userAgent: string
}

interface PerformanceMetrics {
  fcp: number // First Contentful Paint
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  ttfb: number // Time to First Byte
}

class BundleMonitor {
  private static instance: BundleMonitor
  private metrics: BundleMetrics[] = []
  private performanceMetrics: PerformanceMetrics | null = null
  private observer: PerformanceObserver | null = null

  static getInstance(): BundleMonitor {
    if (!BundleMonitor.instance) {
      BundleMonitor.instance = new BundleMonitor()
    }
    return BundleMonitor.instance
  }

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializePerformanceMonitoring()
    }
  }

  // Track component loading times
  trackComponentLoad(componentName: string, startTime: number) {
    const loadTime = performance.now() - startTime
    const metric: BundleMetrics = {
      componentName,
      loadTime,
      timestamp: Date.now(),
      route: window.location.pathname,
      userAgent: navigator.userAgent,
    }

    this.metrics.push(metric)
    
    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`📦 Component ${componentName} loaded in ${loadTime.toFixed(2)}ms`)
    }

    // Send to analytics in production
    if (process.env.NODE_ENV === 'production') {
      this.sendMetrics([metric])
    }
  }

  // Track bundle sizes using Resource Timing API
  trackBundleSize(bundleName: string) {
    if (typeof window === 'undefined') return

    const entries = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    const bundleEntry = entries.find(entry => 
      entry.name.includes(bundleName) && entry.name.includes('.js')
    )

    if (bundleEntry) {
      const size = bundleEntry.transferSize || bundleEntry.encodedBodySize
      console.log(`📊 Bundle ${bundleName}: ${(size / 1024).toFixed(2)}KB`)
      
      return {
        name: bundleName,
        size,
        compressed: bundleEntry.transferSize,
        uncompressed: bundleEntry.decodedBodySize,
      }
    }
  }

  // Initialize Web Vitals monitoring
  private initializePerformanceMonitoring() {
    // Monitor Core Web Vitals
    this.observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        switch (entry.entryType) {
          case 'paint':
            if (entry.name === 'first-contentful-paint') {
              this.updateMetric('fcp', entry.startTime)
            }
            break
          case 'largest-contentful-paint':
            this.updateMetric('lcp', entry.startTime)
            break
          case 'first-input':
            this.updateMetric('fid', (entry as any).processingStart - entry.startTime)
            break
          case 'layout-shift':
            if (!(entry as any).hadRecentInput) {
              this.updateMetric('cls', (entry as any).value)
            }
            break
        }
      }
    })

    // Observe different entry types
    try {
      this.observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] })
    } catch (e) {
      console.warn('Performance Observer not fully supported')
    }

    // Track TTFB
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        this.updateMetric('ttfb', navigation.responseStart - navigation.requestStart)
      }
    })
  }

  private updateMetric(metric: keyof PerformanceMetrics, value: number) {
    if (!this.performanceMetrics) {
      this.performanceMetrics = { fcp: 0, lcp: 0, fid: 0, cls: 0, ttfb: 0 }
    }

    this.performanceMetrics[metric] = value

    if (process.env.NODE_ENV === 'development') {
      console.log(`🎯 ${metric.toUpperCase()}: ${value.toFixed(2)}${metric === 'cls' ? '' : 'ms'}`)
    }
  }

  // Get performance summary
  getPerformanceSummary() {
    return {
      metrics: this.performanceMetrics,
      componentMetrics: this.metrics,
      bundleInfo: this.getBundleInfo(),
    }
  }

  // Analyze bundle composition
  private getBundleInfo() {
    if (typeof window === 'undefined') return null

    const scripts = Array.from(document.querySelectorAll('script[src]'))
    const bundles = scripts
      .map(script => {
        const src = (script as HTMLScriptElement).src
        const entries = performance.getEntriesByName(src) as PerformanceResourceTiming[]
        const entry = entries[0]
        
        if (entry) {
          return {
            name: src.split('/').pop() || 'unknown',
            size: entry.transferSize || entry.encodedBodySize,
            loadTime: entry.responseEnd - entry.requestStart,
          }
        }
        return null
      })
      .filter(Boolean)

    return bundles
  }

  // Send metrics to analytics service
  private async sendMetrics(metrics: BundleMetrics[]) {
    try {
      // In a real app, send to your analytics service
      await fetch('/api/analytics/bundle-metrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(metrics),
      })
    } catch (error) {
      console.warn('Failed to send bundle metrics:', error)
    }
  }

  // Generate performance report
  generateReport() {
    const summary = this.getPerformanceSummary()
    
    return {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      connection: (navigator as any).connection?.effectiveType || 'unknown',
      ...summary,
      recommendations: this.generateRecommendations(summary),
    }
  }

  // Generate optimization recommendations
  private generateRecommendations(summary: any) {
    const recommendations: string[] = []

    if (summary.metrics?.lcp > 2500) {
      recommendations.push('LCP is slow - consider optimizing images and critical resources')
    }

    if (summary.metrics?.fid > 100) {
      recommendations.push('FID is high - consider code splitting and reducing JavaScript execution time')
    }

    if (summary.metrics?.cls > 0.1) {
      recommendations.push('CLS is high - ensure proper sizing for dynamic content')
    }

    const slowComponents = summary.componentMetrics?.filter((m: BundleMetrics) => m.loadTime > 1000)
    if (slowComponents?.length > 0) {
      recommendations.push(`Slow components detected: ${slowComponents.map((c: BundleMetrics) => c.componentName).join(', ')}`)
    }

    const largeBundles = summary.bundleInfo?.filter((b: any) => b.size > 100000) // > 100KB
    if (largeBundles?.length > 0) {
      recommendations.push(`Large bundles detected: ${largeBundles.map((b: any) => b.name).join(', ')}`)
    }

    return recommendations
  }

  // Clear metrics (useful for SPA navigation)
  clearMetrics() {
    this.metrics = []
    this.performanceMetrics = null
  }
}

// HOC for tracking component performance
export function withPerformanceTracking<T extends object>(
  Component: React.ComponentType<T>,
  componentName: string
) {
  return function PerformanceTrackedComponent(props: T) {
    const startTime = performance.now()
    
    React.useEffect(() => {
      const monitor = BundleMonitor.getInstance()
      monitor.trackComponentLoad(componentName, startTime)
    }, [])

    return React.createElement(Component, props)
  }
}

// Hook for performance monitoring
export function usePerformanceMonitoring() {
  const monitor = BundleMonitor.getInstance()
  
  return {
    trackComponent: (name: string, startTime: number) => 
      monitor.trackComponentLoad(name, startTime),
    getReport: () => monitor.generateReport(),
    clearMetrics: () => monitor.clearMetrics(),
  }
}

// Bundle size budget checker
export class BundleBudget {
  private static budgets = {
    // Size budgets in KB
    main: 250,
    vendor: 300,
    ui: 100,
    charts: 150,
    icons: 50,
    common: 75,
  }

  static checkBudgets() {
    if (typeof window === 'undefined') return

    const monitor = BundleMonitor.getInstance()
    const violations: string[] = []

    Object.entries(this.budgets).forEach(([bundleName, budget]) => {
      const bundleInfo = monitor.trackBundleSize(bundleName)
      if (bundleInfo && bundleInfo.size > budget * 1024) {
        violations.push(
          `${bundleName} bundle (${(bundleInfo.size / 1024).toFixed(2)}KB) exceeds budget (${budget}KB)`
        )
      }
    })

    if (violations.length > 0) {
      console.warn('🚨 Bundle budget violations:', violations)
    }

    return violations
  }

  static updateBudget(bundleName: string, newBudget: number) {
    this.budgets[bundleName as keyof typeof this.budgets] = newBudget
  }
}

// Initialize monitoring
export const bundleMonitor = BundleMonitor.getInstance()

// Export for use in components
export default {
  BundleMonitor,
  BundleBudget,
  withPerformanceTracking,
  usePerformanceMonitoring,
  bundleMonitor,
}

// Auto-check budgets in development
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    setTimeout(() => {
      BundleBudget.checkBudgets()
    }, 2000) // Wait for bundles to load
  })
}
