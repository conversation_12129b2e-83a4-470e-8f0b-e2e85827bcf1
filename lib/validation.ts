import { z } from "zod"

// Token Launch Form Validation
export const tokenLaunchSchema = z.object({
  name: z
    .string()
    .min(1, "Token name is required")
    .min(2, "Token name must be at least 2 characters")
    .max(50, "Token name must be less than 50 characters")
    .regex(/^[a-zA-Z0-9\s]+$/, "Token name can only contain letters, numbers, and spaces"),
  
  symbol: z
    .string()
    .min(1, "Token symbol is required")
    .min(2, "Token symbol must be at least 2 characters")
    .max(10, "Token symbol must be less than 10 characters")
    .regex(/^[A-Z0-9]+$/, "Token symbol must be uppercase letters and numbers only"),
  
  description: z
    .string()
    .min(1, "Description is required")
    .min(10, "Description must be at least 10 characters")
    .max(500, "Description must be less than 500 characters"),
  
  totalSupply: z
    .string()
    .min(1, "Total supply is required")
    .refine((val) => {
      const num = parseFloat(val)
      return !isNaN(num) && num > 0
    }, "Total supply must be a positive number")
    .refine((val) => {
      const num = parseFloat(val)
      return num >= 1000000
    }, "Total supply must be at least 1,000,000 tokens")
    .refine((val) => {
      const num = parseFloat(val)
      return num <= 1000000000000
    }, "Total supply cannot exceed 1 trillion tokens"),
  
  initialPrice: z
    .string()
    .min(1, "Initial price is required")
    .refine((val) => {
      const num = parseFloat(val)
      return !isNaN(num) && num > 0
    }, "Initial price must be a positive number")
    .refine((val) => {
      const num = parseFloat(val)
      return num >= 0.000001
    }, "Initial price must be at least 0.000001 wDOGE"),
  
  bondingCurveType: z.enum(["linear", "exponential", "logarithmic"], {
    errorMap: () => ({ message: "Please select a valid bonding curve type" })
  }),
  
  liquidityPercentage: z
    .number()
    .min(10, "Liquidity percentage must be at least 10%")
    .max(90, "Liquidity percentage cannot exceed 90%"),
  
  website: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true
      try {
        new URL(val)
        return true
      } catch {
        return false
      }
    }, "Please enter a valid website URL"),
  
  twitter: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true
      return val.startsWith("@") || val.includes("twitter.com") || val.includes("x.com")
    }, "Please enter a valid Twitter handle or URL"),
  
  telegram: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true
      return val.startsWith("@") || val.includes("t.me")
    }, "Please enter a valid Telegram handle or URL"),
})

export type TokenLaunchFormData = z.infer<typeof tokenLaunchSchema>

// Trading Form Validation
export const tradingSchema = z.object({
  fromAmount: z
    .string()
    .min(1, "Amount is required")
    .refine((val) => {
      const num = parseFloat(val)
      return !isNaN(num) && num > 0
    }, "Amount must be a positive number")
    .refine((val) => {
      const num = parseFloat(val)
      return num >= 0.000001
    }, "Amount must be at least 0.000001"),
  
  toAmount: z
    .string()
    .min(1, "Expected amount is required"),
  
  fromToken: z
    .string()
    .min(1, "Please select a token to swap from"),
  
  toToken: z
    .string()
    .min(1, "Please select a token to swap to"),
  
  slippage: z
    .number()
    .min(0.1, "Slippage must be at least 0.1%")
    .max(50, "Slippage cannot exceed 50%"),
})

export type TradingFormData = z.infer<typeof tradingSchema>

// Governance Proposal Validation
export const proposalSchema = z.object({
  title: z
    .string()
    .min(1, "Proposal title is required")
    .min(5, "Title must be at least 5 characters")
    .max(100, "Title must be less than 100 characters"),
  
  description: z
    .string()
    .min(1, "Proposal description is required")
    .min(50, "Description must be at least 50 characters")
    .max(2000, "Description must be less than 2000 characters"),
  
  category: z.enum(["treasury", "protocol", "community", "development"], {
    errorMap: () => ({ message: "Please select a valid proposal category" })
  }),
  
  requestedAmount: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true
      const num = parseFloat(val)
      return !isNaN(num) && num > 0
    }, "Requested amount must be a positive number"),
  
  duration: z
    .number()
    .min(1, "Voting duration must be at least 1 day")
    .max(30, "Voting duration cannot exceed 30 days"),
})

export type ProposalFormData = z.infer<typeof proposalSchema>

// Validation Helper Functions
export function validateField<T>(
  schema: z.ZodSchema<T>,
  field: keyof T,
  value: any
): { isValid: boolean; error?: string } {
  try {
    // Create a partial object with just the field we want to validate
    const partialData = { [field]: value } as Partial<T>
    // For object schemas, we can use safeParse to avoid throwing
    const result = schema.safeParse(partialData as T)
    if (result.success) {
      return { isValid: true }
    } else {
      // Check if the error is for our specific field
      const fieldError = result.error.errors.find(err =>
        err.path.length > 0 && err.path[0] === field
      )
      if (fieldError) {
        return {
          isValid: false,
          error: fieldError.message
        }
      }
      // If no error for our field, consider it valid
      return { isValid: true }
    }
  } catch (error) {
    return { isValid: false, error: "Validation error" }
  }
}

export function getFieldError(
  errors: z.ZodError | undefined,
  field: string
): string | undefined {
  if (!errors) return undefined
  
  const fieldError = errors.errors.find(error => 
    error.path.includes(field)
  )
  
  return fieldError?.message
}

export function formatValidationErrors(error: z.ZodError): Record<string, string> {
  const formattedErrors: Record<string, string> = {}
  
  error.errors.forEach((err) => {
    const field = err.path.join('.')
    formattedErrors[field] = err.message
  })
  
  return formattedErrors
}

// Real-time validation debounce helper
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
