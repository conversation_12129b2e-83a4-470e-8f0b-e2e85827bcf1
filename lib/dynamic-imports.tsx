import React from 'react'
import dynamic from 'next/dynamic'
import { LoadingState } from '@/components/ui/loading-state'

// Dynamic imports for heavy components with loading states

// Trading components (heavy due to charts and calculations)
export const TradingChart = dynamic(
  () => import('@/components/trading-chart').then(mod => ({ default: mod.TradingChart })),
  {
    loading: () => <LoadingState type="chart" title="Loading Chart..." />,
    ssr: false, // Charts don't need SSR
  }
)

export const TradingInterface = dynamic(
  () => import('@/components/trading-interface').then(mod => ({ default: mod.TradingInterface })),
  {
    loading: () => <LoadingState type="data" title="Loading Trading Interface..." />,
    ssr: false, // Trading interface is interactive and heavy
  }
)

// Liquidity components
export const LiquidityInterface = dynamic(
  () => import('@/components/liquidity-interface').then(mod => ({ default: mod.LiquidityInterface })),
  {
    loading: () => <LoadingState type="data" title="Loading Liquidity Interface..." />,
    ssr: false,
  }
)

// Perpetual trading (very heavy)
export const PerpetualTrading = dynamic(
  () => import('@/components/perpetual-trading').then(mod => ({ default: mod.PerpetualTrading })),
  {
    loading: () => <LoadingState type="data" title="Loading Perpetual Trading..." />,
    ssr: false,
  }
)

// Token list (can be heavy with many tokens)
export const TokenList = dynamic(
  () => import('@/components/token-list').then(mod => ({ default: mod.TokenList })),
  {
    loading: () => <LoadingState type="data" title="Loading Tokens..." />,
    ssr: false,
  }
)

// Analytics components (heavy due to data processing)
export const AnalyticsDashboard = dynamic(
  () => import('@/components/analytics-dashboard').then(mod => ({ default: mod.AnalyticsDashboard })),
  {
    loading: () => <LoadingState type="chart" title="Loading Analytics..." />,
    ssr: false,
  }
)

// Governance components (complex forms and data)
export const GovernanceDashboard = dynamic(
  () => import('@/components/governance-dashboard').then(mod => ({ default: mod.GovernanceDashboard })),
  {
    loading: () => <LoadingState type="data" title="Loading Governance..." />,
  }
)

export const ProposalForm = dynamic(
  () => import('@/components/proposal-form').then(mod => ({ default: mod.ProposalForm })),
  {
    loading: () => <LoadingState type="form" title="Loading Proposal Form..." />,
  }
)

// Social components (potentially heavy with feeds)
export const SocialFeed = dynamic(
  () => import('@/components/social-feed').then(mod => ({ default: mod.SocialFeed })),
  {
    loading: () => <LoadingState type="data" title="Loading Social Feed..." />,
  }
)

// Token launch components (complex forms)
export const TokenLaunchForm = dynamic(
  () => import('@/components/token-launch-form').then(mod => ({ default: mod.TokenLaunchForm })),
  {
    loading: () => <LoadingState type="form" title="Loading Token Launch Form..." />,
  }
)

export const BondingCurveVisualization = dynamic(
  () => import('@/components/bonding-curve-visualization').then(mod => ({ default: mod.BondingCurveVisualization })),
  {
    loading: () => <LoadingState type="chart" title="Loading Visualization..." />,
    ssr: false,
  }
)

// Admin components (should only load for admin users)
export const AdminDashboard = dynamic(
  () => import('@/components/admin-dashboard').then(mod => ({ default: mod.AdminDashboard })),
  {
    loading: () => <LoadingState type="data" title="Loading Admin Dashboard..." />,
  }
)

export const ContentModeration = dynamic(
  () => import('@/components/content-moderation').then(mod => ({ default: mod.ContentModeration })),
  {
    loading: () => <LoadingState type="data" title="Loading Moderation Tools..." />,
  }
)

// Development components
export const DevelopmentTracker = dynamic(
  () => import('@/components/development-tracker').then(mod => ({ default: mod.DevelopmentTracker })),
  {
    loading: () => <LoadingState type="data" title="Loading Development Tracker..." />,
  }
)

// Utility function for conditional dynamic imports
export function createConditionalImport<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  condition: boolean,
  fallback?: React.ComponentType
) {
  if (!condition && fallback) {
    return fallback
  }
  
  return dynamic(importFn, {
    loading: () => <LoadingState title="Loading..." />,
  })
}

// Icon optimization - only import used icons
export const createIconImport = (iconName: string) => {
  return dynamic(
    () => import('lucide-react').then(mod => ({ default: mod[iconName as keyof typeof mod] as React.ComponentType<any> })),
    {
      loading: () => <div className="h-4 w-4 animate-pulse bg-white/20 rounded" />,
      ssr: false,
    }
  )
}

// Lazy load heavy libraries
export const loadChartLibrary = () => {
  return import('recharts')
}

export const loadDateLibrary = () => {
  return import('date-fns')
}

export const loadValidationLibrary = () => {
  return import('zod')
}

// Preload critical components
export const preloadCriticalComponents = () => {
  // Preload components that are likely to be needed soon
  if (typeof window !== 'undefined') {
    // Preload trading interface for homepage users
    import('@/components/trading-interface')
    
    // Preload token launch form
    import('@/components/token-launch-form')
    
    // Preload wallet components
    import('@/components/wallet-provider')
  }
}

// Component registry for dynamic loading based on route
export const routeComponentMap = {
  '/': ['TradingInterface'],
  '/trade': ['TradingInterface', 'TradingChart'],
  '/launch': ['TokenLaunchForm', 'BondingCurveVisualization'],
  '/governance': ['GovernanceDashboard', 'ProposalForm'],
  '/analytics': ['AnalyticsDashboard'],
  '/social': ['SocialFeed'],
  '/admin': ['AdminDashboard', 'ContentModeration'],
  '/development': ['DevelopmentTracker'],
} as const

// Function to preload components for a specific route
export const preloadRouteComponents = (route: keyof typeof routeComponentMap) => {
  const components = routeComponentMap[route]
  if (!components) return

  components.forEach(componentName => {
    switch (componentName) {
      case 'TradingInterface':
        import('@/components/trading-interface')
        break
      case 'TradingChart':
        import('@/components/trading-chart')
        break
      case 'TokenLaunchForm':
        import('@/components/token-launch-form')
        break
      case 'BondingCurveVisualization':
        import('@/components/bonding-curve-visualization')
        break
      case 'GovernanceDashboard':
        import('@/components/governance-dashboard')
        break
      case 'ProposalForm':
        import('@/components/proposal-form')
        break
      case 'AnalyticsDashboard':
        import('@/components/analytics-dashboard')
        break
      case 'SocialFeed':
        import('@/components/social-feed')
        break
      case 'AdminDashboard':
        import('@/components/admin-dashboard')
        break
      case 'ContentModeration':
        import('@/components/content-moderation')
        break
      case 'DevelopmentTracker':
        import('@/components/development-tracker')
        break
    }
  })
}
