// Security utilities and hardening measures

// Input sanitization
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') {
    return ''
  }
  
  // Remove potentially dangerous characters
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .replace(/vbscript:/gi, '') // Remove vbscript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
}

// HTML sanitization for display
export function sanitizeHTML(html: string): string {
  if (typeof html !== 'string') {
    return ''
  }
  
  // Basic HTML sanitization - remove script tags and dangerous attributes
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
    .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
    .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/data:/gi, '')
    .replace(/vbscript:/gi, '')
}

// URL validation
export function isValidURL(url: string): boolean {
  try {
    const urlObj = new URL(url)
    // Only allow http and https protocols
    return ['http:', 'https:'].includes(urlObj.protocol)
  } catch {
    return false
  }
}

// Ethereum address validation
export function isValidEthereumAddress(address: string): boolean {
  if (typeof address !== 'string') {
    return false
  }
  
  // Check if it's a valid hex string with 0x prefix and 40 characters
  const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/
  return ethAddressRegex.test(address)
}

// Rate limiting utilities
export class RateLimiter {
  private requests: Map<string, number[]> = new Map()
  private maxRequests: number
  private windowMs: number

  constructor(maxRequests: number = 100, windowMs: number = 60000) {
    this.maxRequests = maxRequests
    this.windowMs = windowMs
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now()
    const requests = this.requests.get(identifier) || []
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs)
    
    if (validRequests.length >= this.maxRequests) {
      return false
    }
    
    // Add current request
    validRequests.push(now)
    this.requests.set(identifier, validRequests)
    
    return true
  }

  reset(identifier?: string): void {
    if (identifier) {
      this.requests.delete(identifier)
    } else {
      this.requests.clear()
    }
  }
}

// CSRF token generation and validation
export function generateCSRFToken(): string {
  if (typeof window !== 'undefined' && window.crypto) {
    const array = new Uint8Array(32)
    window.crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }
  
  // Fallback for server-side or older browsers
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

// Content Security Policy helpers
export const CSP_DIRECTIVES = {
  defaultSrc: ["'self'"],
  scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
  styleSrc: ["'self'", "'unsafe-inline'"],
  imgSrc: ["'self'", "data:", "https:"],
  connectSrc: ["'self'", "https:"],
  fontSrc: ["'self'", "https:"],
  objectSrc: ["'none'"],
  mediaSrc: ["'self'"],
  frameSrc: ["'none'"],
}

export function generateCSPHeader(): string {
  return Object.entries(CSP_DIRECTIVES)
    .map(([directive, sources]) => `${directive.replace(/([A-Z])/g, '-$1').toLowerCase()} ${sources.join(' ')}`)
    .join('; ')
}

// Secure headers configuration
export const SECURITY_HEADERS = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
}

// Input validation schemas
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  username: /^[a-zA-Z0-9_-]{3,20}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  ethereumAddress: /^0x[a-fA-F0-9]{40}$/,
  tokenSymbol: /^[A-Z]{2,10}$/,
  amount: /^\d+(\.\d{1,18})?$/,
}

// Validation functions
export function validateEmail(email: string): boolean {
  return VALIDATION_PATTERNS.email.test(email)
}

export function validateUsername(username: string): boolean {
  return VALIDATION_PATTERNS.username.test(username)
}

export function validatePassword(password: string): boolean {
  return VALIDATION_PATTERNS.password.test(password)
}

export function validateTokenSymbol(symbol: string): boolean {
  return VALIDATION_PATTERNS.tokenSymbol.test(symbol)
}

export function validateAmount(amount: string): boolean {
  return VALIDATION_PATTERNS.amount.test(amount)
}

// Secure random number generation
export function secureRandom(): number {
  if (typeof window !== 'undefined' && window.crypto) {
    const array = new Uint32Array(1)
    window.crypto.getRandomValues(array)
    return array[0] / (0xffffffff + 1)
  }
  
  // Fallback to Math.random (less secure)
  return Math.random()
}

// Session management
export class SecureSession {
  private static readonly SESSION_KEY = 'paw_session'
  private static readonly EXPIRY_KEY = 'paw_session_expiry'
  
  static set(data: any, expiryMinutes: number = 60): void {
    if (typeof window === 'undefined') return
    
    const expiry = Date.now() + (expiryMinutes * 60 * 1000)
    
    try {
      localStorage.setItem(this.SESSION_KEY, JSON.stringify(data))
      localStorage.setItem(this.EXPIRY_KEY, expiry.toString())
    } catch (error) {
      console.warn('Failed to set session data:', error)
    }
  }
  
  static get(): any | null {
    if (typeof window === 'undefined') return null
    
    try {
      const expiry = localStorage.getItem(this.EXPIRY_KEY)
      if (!expiry || Date.now() > parseInt(expiry)) {
        this.clear()
        return null
      }
      
      const data = localStorage.getItem(this.SESSION_KEY)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.warn('Failed to get session data:', error)
      this.clear()
      return null
    }
  }
  
  static clear(): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.removeItem(this.SESSION_KEY)
      localStorage.removeItem(this.EXPIRY_KEY)
    } catch (error) {
      console.warn('Failed to clear session data:', error)
    }
  }
  
  static isValid(): boolean {
    return this.get() !== null
  }
}

// Error logging with security considerations
export function secureLog(message: string, data?: any): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[SECURITY] ${message}`, data)
  }
  
  // In production, you would send this to a secure logging service
  // without exposing sensitive information
}

// Audit trail
export interface AuditEvent {
  timestamp: number
  action: string
  userId?: string
  ip?: string
  userAgent?: string
  details?: Record<string, any>
}

export class AuditLogger {
  private static events: AuditEvent[] = []
  
  static log(action: string, details?: Record<string, any>): void {
    const event: AuditEvent = {
      timestamp: Date.now(),
      action,
      details: details ? { ...details } : undefined
    }
    
    this.events.push(event)
    
    // Keep only last 1000 events in memory
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000)
    }
    
    secureLog(`Audit: ${action}`, event)
  }
  
  static getEvents(): AuditEvent[] {
    return [...this.events]
  }
  
  static clear(): void {
    this.events = []
  }
}
