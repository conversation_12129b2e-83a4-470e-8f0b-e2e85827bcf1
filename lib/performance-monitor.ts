// Performance monitoring utilities for bundle size and runtime performance

interface PerformanceMetrics {
  bundleSize: number
  loadTime: number
  renderTime: number
  interactionTime: number
  memoryUsage: number
}

interface ComponentMetrics {
  name: string
  loadTime: number
  renderTime: number
  size: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = []
  private componentMetrics: Map<string, ComponentMetrics> = new Map()
  private observers: PerformanceObserver[] = []

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers()
    }
  }

  private initializeObservers() {
    // Observe navigation timing
    if ('PerformanceObserver' in window) {
      const navObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            this.recordNavigationMetrics(entry as PerformanceNavigationTiming)
          }
        })
      })
      navObserver.observe({ entryTypes: ['navigation'] })
      this.observers.push(navObserver)

      // Observe resource loading
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.entryType === 'resource') {
            this.recordResourceMetrics(entry as PerformanceResourceTiming)
          }
        })
      })
      resourceObserver.observe({ entryTypes: ['resource'] })
      this.observers.push(resourceObserver)

      // Observe largest contentful paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        this.recordLCP(lastEntry.startTime)
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      this.observers.push(lcpObserver)

      // Observe first input delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          const eventEntry = entry as any // PerformanceEventTiming
          this.recordFID(eventEntry.processingStart - entry.startTime)
        })
      })
      fidObserver.observe({ entryTypes: ['first-input'] })
      this.observers.push(fidObserver)
    }
  }

  private recordNavigationMetrics(entry: PerformanceNavigationTiming) {
    const metrics: PerformanceMetrics = {
      bundleSize: this.estimateBundleSize(),
      loadTime: entry.loadEventEnd - entry.fetchStart,
      renderTime: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      interactionTime: entry.domInteractive - entry.fetchStart,
      memoryUsage: this.getMemoryUsage(),
    }
    this.metrics.push(metrics)
    this.reportMetrics(metrics)
  }

  private recordResourceMetrics(entry: PerformanceResourceTiming) {
    // Track JavaScript bundle sizes
    if (entry.name.includes('.js') && entry.name.includes('/_next/static/')) {
      const size = entry.transferSize || entry.encodedBodySize || 0
      console.log(`Bundle: ${entry.name.split('/').pop()} - ${this.formatBytes(size)}`)
    }
  }

  private recordLCP(time: number) {
    console.log(`LCP: ${time.toFixed(2)}ms`)
    if (time > 2500) {
      console.warn('LCP is above recommended threshold (2.5s)')
    }
  }

  private recordFID(delay: number) {
    console.log(`FID: ${delay.toFixed(2)}ms`)
    if (delay > 100) {
      console.warn('FID is above recommended threshold (100ms)')
    }
  }

  private estimateBundleSize(): number {
    // Estimate based on loaded resources
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    return resources
      .filter(r => r.name.includes('.js'))
      .reduce((total, r) => total + (r.transferSize || r.encodedBodySize || 0), 0)
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize
    }
    return 0
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Component-level performance tracking
  startComponentTimer(componentName: string): () => void {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      const existing = this.componentMetrics.get(componentName)
      if (existing) {
        existing.renderTime = (existing.renderTime + renderTime) / 2 // Average
      } else {
        this.componentMetrics.set(componentName, {
          name: componentName,
          loadTime: 0,
          renderTime,
          size: 0,
        })
      }
    }
  }

  // Bundle analysis
  async analyzeBundleSize(): Promise<void> {
    if (typeof window === 'undefined') return

    try {
      const response = await fetch('/_next/static/chunks/pages/_app.js', { method: 'HEAD' })
      const size = response.headers.get('content-length')
      if (size) {
        console.log(`Main bundle size: ${this.formatBytes(parseInt(size))}`)
      }
    } catch (error) {
      console.warn('Could not analyze bundle size:', error)
    }
  }

  // Performance budget checking
  checkPerformanceBudget(): boolean {
    const budget = {
      maxBundleSize: 1024 * 1024, // 1MB
      maxLoadTime: 3000, // 3s
      maxRenderTime: 1000, // 1s
      maxLCP: 2500, // 2.5s
      maxFID: 100, // 100ms
    }

    const latest = this.metrics[this.metrics.length - 1]
    if (!latest) return true

    const violations: string[] = []

    if (latest.bundleSize > budget.maxBundleSize) {
      violations.push(`Bundle size: ${this.formatBytes(latest.bundleSize)} > ${this.formatBytes(budget.maxBundleSize)}`)
    }

    if (latest.loadTime > budget.maxLoadTime) {
      violations.push(`Load time: ${latest.loadTime}ms > ${budget.maxLoadTime}ms`)
    }

    if (latest.renderTime > budget.maxRenderTime) {
      violations.push(`Render time: ${latest.renderTime}ms > ${budget.maxRenderTime}ms`)
    }

    if (violations.length > 0) {
      console.warn('Performance budget violations:', violations)
      return false
    }

    return true
  }

  private reportMetrics(metrics: PerformanceMetrics) {
    if (process.env.NODE_ENV === 'development') {
      console.group('Performance Metrics')
      console.log(`Bundle Size: ${this.formatBytes(metrics.bundleSize)}`)
      console.log(`Load Time: ${metrics.loadTime.toFixed(2)}ms`)
      console.log(`Render Time: ${metrics.renderTime.toFixed(2)}ms`)
      console.log(`Interaction Time: ${metrics.interactionTime.toFixed(2)}ms`)
      console.log(`Memory Usage: ${this.formatBytes(metrics.memoryUsage)}`)
      console.groupEnd()
    }

    // In production, send to analytics service
    if (process.env.NODE_ENV === 'production') {
      // Example: send to analytics
      // analytics.track('performance_metrics', metrics)
    }
  }

  // Get performance report
  getPerformanceReport(): {
    metrics: PerformanceMetrics[]
    components: ComponentMetrics[]
    budgetStatus: boolean
  } {
    return {
      metrics: this.metrics,
      components: Array.from(this.componentMetrics.values()),
      budgetStatus: this.checkPerformanceBudget(),
    }
  }

  // Cleanup
  destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

// React hook for component performance tracking
export function usePerformanceTracking(componentName: string) {
  const startTimer = () => performanceMonitor.startComponentTimer(componentName)
  
  return { startTimer }
}

// Utility to measure async operations
export async function measureAsync<T>(
  name: string,
  operation: () => Promise<T>
): Promise<T> {
  const start = performance.now()
  try {
    const result = await operation()
    const end = performance.now()
    console.log(`${name}: ${(end - start).toFixed(2)}ms`)
    return result
  } catch (error) {
    const end = performance.now()
    console.log(`${name} (failed): ${(end - start).toFixed(2)}ms`)
    throw error
  }
}

// Bundle size estimation
export function estimateComponentSize(componentName: string): number {
  // This would need to be populated during build time
  const componentSizes: Record<string, number> = {
    'TradingInterface': 45000,
    'TokenLaunchForm': 32000,
    'AnalyticsDashboard': 38000,
    'GovernanceDashboard': 28000,
    // Add more as needed
  }
  
  return componentSizes[componentName] || 0
}
