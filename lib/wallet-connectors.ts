// Real wallet integration with MetaMask, WalletConnect, and other providers
// Optimized with lazy loading for better performance

import { ethers } from 'ethers'

export interface WalletConnector {
  id: string
  name: string
  icon: string
  connect(): Promise<WalletConnection>
  disconnect(): Promise<void>
  isAvailable(): boolean
  getProvider(): unknown
}

export interface WalletConnection {
  address: string
  chainId: number
  provider: unknown
  signer: ethers.Signer
}

export interface NetworkConfig {
  chainId: number
  chainName: string
  nativeCurrency: {
    name: string
    symbol: string
    decimals: number
  }
  rpcUrls: string[]
  blockExplorerUrls: string[]
}

// Dogechain Network Configuration
export const DOGECHAIN_NETWORK: NetworkConfig = {
  chainId: 2000,
  chainName: 'Dogechain Mainnet',
  nativeCurrency: {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    symbol: 'DOGE',
    decimals: 18,
  },
  rpcUrls: ['https://rpc.dogechain.dog'],
  blockExplorerUrls: ['https://explorer.dogechain.dog'],
}

export const DOGECHAIN_TESTNET: NetworkConfig = {
  chainId: 568,
  chainName: 'Dogechain Testnet',
  nativeCurrency: {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    symbol: 'DOGE',
    decimals: 18,
  },
  rpcUrls: ['https://rpc-testnet.dogechain.dog'],
  blockExplorerUrls: ['https://explorer-testnet.dogechain.dog'],
}

// MetaMask Connector
export class MetaMaskConnector implements WalletConnector {
  id = 'metamask'
  name = 'MetaMask'
  icon = '/icons/metamask.svg'

  isAvailable(): boolean {
    return typeof window !== 'undefined' &&
           typeof window.ethereum !== 'undefined' &&
           Boolean(window.ethereum.isMetaMask)
  }

  getProvider() {
    if (!this.isAvailable()) {
      throw new Error('MetaMask is not available')
    }
    return window.ethereum
  }

  async connect(): Promise<WalletConnection> {
    const provider = this.getProvider()

    if (!provider) {
      throw new Error('MetaMask not available')
    }

    try {
      // Request account access
      const accounts = await provider.request({
        method: 'eth_requestAccounts',
      })

      if (!accounts || accounts.length === 0) {
        throw new Error('No accounts found')
      }

      // Get chain ID
      const chainId = await provider.request({
        method: 'eth_chainId',
      })

      // Create ethers provider and signer
      const ethersProvider = new ethers.BrowserProvider(provider)
      const signer = await ethersProvider.getSigner()

      return {
        address: accounts[0],
        chainId: parseInt(chainId, 16),
        provider: ethersProvider,
        signer,
      }
    } catch (error) {
      console.error('MetaMask connection error:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    // MetaMask doesn't have a programmatic disconnect
    // Users need to disconnect from the extension
    console.log('Please disconnect from MetaMask extension')
  }

  async switchToDogechain(): Promise<void> {
    const provider = this.getProvider()

    if (!provider) {
      throw new Error('MetaMask not available')
    }

    try {
      // Try to switch to Dogechain
      await provider.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${DOGECHAIN_NETWORK.chainId.toString(16)}` }],
      })
    } catch (switchError: any) {
      // If the chain is not added, add it
      if (switchError.code === 4902) {
        await provider.request({
          method: 'wallet_addEthereumChain',
          params: [
            {
              chainId: `0x${DOGECHAIN_NETWORK.chainId.toString(16)}`,
              chainName: DOGECHAIN_NETWORK.chainName,
              nativeCurrency: DOGECHAIN_NETWORK.nativeCurrency,
              rpcUrls: DOGECHAIN_NETWORK.rpcUrls,
              blockExplorerUrls: DOGECHAIN_NETWORK.blockExplorerUrls,
            },
          ],
        })
      } else {
        throw switchError
      }
    }
  }
}

// WalletConnect Connector
export class WalletConnectConnector implements WalletConnector {
  id = 'walletconnect'
  name = 'WalletConnect'
  icon = '/icons/walletconnect.svg'
  private connector: any = null

  isAvailable(): boolean {
    return typeof window !== 'undefined'
  }

  getProvider() {
    return this.connector?.provider
  }

  async connect(): Promise<WalletConnection> {
    try {
      // Lazy load WalletConnect SDK only when needed
      const [{ EthereumProvider }] = await Promise.all([
        import('@walletconnect/ethereum-provider'),
        // Preload ethers if not already loaded
        typeof window !== 'undefined' && !(window as any).ethers ? import('ethers') : Promise.resolve(null)
      ])

      this.connector = await EthereumProvider.init({
        projectId: process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID!,
        chains: [DOGECHAIN_NETWORK.chainId],
        rpcMap: {
          [DOGECHAIN_NETWORK.chainId]: DOGECHAIN_NETWORK.rpcUrls[0],
        },
        showQrModal: true,
        metadata: {
          name: 'PawPumps',
          description: 'Memecoin Launchpad on Dogechain',
          url: 'https://pawpumps.com',
          icons: ['https://pawpumps.com/icon.png'],
        },
      })

      // Enable session (triggers QR Code modal)
      const accounts = await this.connector.enable()

      if (!accounts || accounts.length === 0) {
        throw new Error('No accounts found')
      }

      // Create ethers provider and signer
      const ethersProvider = new ethers.BrowserProvider(this.connector)
      const signer = await ethersProvider.getSigner()

      return {
        address: accounts[0],
        chainId: this.connector.chainId,
        provider: ethersProvider,
        signer,
      }
    } catch (error) {
      console.error('WalletConnect connection error:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.connector) {
      await this.connector.disconnect()
      this.connector = null
    }
  }
}

// Coinbase Wallet Connector
export class CoinbaseWalletConnector implements WalletConnector {
  id = 'coinbase'
  name = 'Coinbase Wallet'
  icon = '/icons/coinbase.svg'
  private connector: any = null

  isAvailable(): boolean {
    return typeof window !== 'undefined'
  }

  getProvider() {
    return this.connector?.provider
  }

  async connect(): Promise<WalletConnection> {
    try {
      // Lazy load Coinbase Wallet SDK only when needed
      const [{ CoinbaseWalletSDK }] = await Promise.all([
        import('@coinbase/wallet-sdk'),
        // Preload ethers if not already loaded
        typeof window !== 'undefined' && !(window as any).ethers ? import('ethers') : Promise.resolve(null)
      ])

      const coinbaseWallet = new CoinbaseWalletSDK({
        appName: 'PawPumps',
        appLogoUrl: 'https://pawpumps.com/icon.png',
      })

      this.connector = coinbaseWallet.makeWeb3Provider()

      // Request account access
      const accounts = await this.connector.request({
        method: 'eth_requestAccounts',
      })

      if (!accounts || accounts.length === 0) {
        throw new Error('No accounts found')
      }

      // Create ethers provider and signer
      const ethersProvider = new ethers.BrowserProvider(this.connector)
      const signer = await ethersProvider.getSigner()

      return {
        address: accounts[0],
        chainId: DOGECHAIN_NETWORK.chainId,
        provider: ethersProvider,
        signer,
      }
    } catch (error) {
      console.error('Coinbase Wallet connection error:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.connector) {
      await this.connector.disconnect()
      this.connector = null
    }
  }
}

// Trust Wallet Connector (uses injected provider)
export class TrustWalletConnector implements WalletConnector {
  id = 'trust'
  name = 'Trust Wallet'
  icon = '/icons/trust.svg'

  isAvailable(): boolean {
    return typeof window !== 'undefined' &&
           typeof window.ethereum !== 'undefined' &&
           Boolean(window.ethereum.isTrust)
  }

  getProvider() {
    if (!this.isAvailable()) {
      throw new Error('Trust Wallet is not available')
    }
    return window.ethereum
  }

  async connect(): Promise<WalletConnection> {
    const provider = this.getProvider()

    if (!provider) {
      throw new Error('Trust Wallet not available')
    }

    try {
      const accounts = await provider.request({
        method: 'eth_requestAccounts',
      })

      if (!accounts || accounts.length === 0) {
        throw new Error('No accounts found')
      }

      const chainId = await provider.request({
        method: 'eth_chainId',
      })

      const ethersProvider = new ethers.BrowserProvider(provider)
      const signer = await ethersProvider.getSigner()

      return {
        address: accounts[0],
        chainId: parseInt(chainId, 16),
        provider: ethersProvider,
        signer,
      }
    } catch (error) {
      console.error('Trust Wallet connection error:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    console.log('Please disconnect from Trust Wallet app')
  }
}

// Rabby Wallet Connector (uses injected provider)
export class RabbyWalletConnector implements WalletConnector {
  id = 'rabby'
  name = 'Rabby Wallet'
  icon = '/icons/rabby.svg'

  isAvailable(): boolean {
    return typeof window !== 'undefined' &&
           typeof window.ethereum !== 'undefined' &&
           Boolean(window.ethereum.isRabby)
  }

  getProvider() {
    if (!this.isAvailable()) {
      throw new Error('Rabby Wallet is not available')
    }
    return window.ethereum
  }

  async connect(): Promise<WalletConnection> {
    const provider = this.getProvider()

    if (!provider) {
      throw new Error('Rabby Wallet not available')
    }

    try {
      // Request account access
      const accounts = await provider.request({
        method: 'eth_requestAccounts'
      })

      if (!accounts || accounts.length === 0) {
        throw new Error('No accounts found')
      }

      // Get chain ID
      const chainId = await provider.request({ method: 'eth_chainId' })

      // Create ethers provider and signer
      const ethersProvider = new ethers.BrowserProvider(provider)
      const signer = await ethersProvider.getSigner()

      return {
        address: accounts[0],
        chainId: parseInt(chainId, 16),
        provider: ethersProvider,
        signer,
      }
    } catch (error) {
      console.error('Rabby Wallet connection error:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    // Rabby Wallet doesn't have a programmatic disconnect
    // Users need to disconnect from the extension
    console.log('Please disconnect from Rabby Wallet extension')
  }
}

// Available wallet connectors
export const walletConnectors: WalletConnector[] = [
  new MetaMaskConnector(),
  new RabbyWalletConnector(),
  new WalletConnectConnector(),
  new CoinbaseWalletConnector(),
  new TrustWalletConnector(),
]

// Utility functions
export function getAvailableWallets(): WalletConnector[] {
  return walletConnectors.filter(connector => connector.isAvailable())
}

export function getWalletConnector(id: string): WalletConnector | undefined {
  return walletConnectors.find(connector => connector.id === id)
}

export async function addTokenToWallet(
  provider: any,
  tokenAddress: string,
  tokenSymbol: string,
  tokenDecimals: number,
  tokenImage?: string
): Promise<boolean> {
  try {
    const wasAdded = await provider.request({
      method: 'wallet_watchAsset',
      params: {
        type: 'ERC20',
        options: {
          address: tokenAddress,
          symbol: tokenSymbol,
          decimals: tokenDecimals,
          image: tokenImage,
        },
      },
    })

    return wasAdded
  } catch (error) {
    console.error('Error adding token to wallet:', error)
    return false
  }
}

// Type declarations for window.ethereum
declare global {
  interface Window {
    ethereum?: {
      isMetaMask?: boolean
      isTrust?: boolean
      isRabby?: boolean
      request: (args: { method: string; params?: any[] }) => Promise<any>
      on: (event: string, callback: (...args: any[]) => void) => void
      removeListener: (event: string, callback: (...args: any[]) => void) => void
    }
  }
}
