import { ethers } from 'ethers'
import { SUPPORTED_TOKENS, TokenInfo, WDOGE_TOKEN } from './token-constants'

// Standard ERC-20 ABI for balance reading
const ERC20_ABI = [
  'function balanceOf(address owner) view returns (uint256)',
  'function decimals() view returns (uint8)',
  'function symbol() view returns (string)',
  'function name() view returns (string)',
]

export interface TokenBalance {
  token: TokenInfo
  balance: string
  balanceFormatted: string
  balanceUSD?: string
}

/**
 * Get token balance for a specific token
 */
export async function getTokenBalance(
  provider: ethers.BrowserProvider,
  tokenAddress: string,
  userAddress: string
): Promise<string> {
  try {
    // Handle native DOGE balance
    if (tokenAddress === WDOGE_TOKEN.address || tokenAddress === '******************************************') {
      const balance = await provider.getBalance(userAddress)
      return ethers.formatEther(balance)
    }

    // Handle ERC-20 tokens
    const contract = new ethers.Contract(tokenAddress, ERC20_ABI, provider)
    const balance = await contract.balanceOf(userAddress)
    const decimals = await contract.decimals()
    
    return ethers.formatUnits(balance, decimals)
  } catch (error) {
    console.error(`Error fetching balance for token ${tokenAddress}:`, error)
    return '0'
  }
}

/**
 * Get balances for all supported tokens
 */
export async function getAllTokenBalances(
  provider: ethers.BrowserProvider,
  userAddress: string
): Promise<TokenBalance[]> {
  const balances: TokenBalance[] = []

  for (const token of SUPPORTED_TOKENS) {
    try {
      const balance = await getTokenBalance(provider, token.address, userAddress)
      const balanceFormatted = parseFloat(balance).toFixed(4)

      balances.push({
        token,
        balance,
        balanceFormatted,
        // TODO: Add USD price conversion
        balanceUSD: undefined
      })
    } catch (error) {
      console.error(`Error fetching balance for ${token.symbol}:`, error)
      balances.push({
        token,
        balance: '0',
        balanceFormatted: '0.0000',
        balanceUSD: undefined
      })
    }
  }

  return balances
}

/**
 * Format token balance for display
 */
export function formatTokenBalance(balance: string, decimals: number = 4): string {
  const num = parseFloat(balance)
  if (num === 0) return '0'
  if (num < 0.0001) return '< 0.0001'
  return num.toFixed(decimals)
}

/**
 * Add token to wallet (MetaMask/Rabby)
 */
export async function addTokenToWallet(
  provider: any,
  token: TokenInfo
): Promise<boolean> {
  try {
    const wasAdded = await provider.request({
      method: 'wallet_watchAsset',
      params: {
        type: 'ERC20',
        options: {
          address: token.address,
          symbol: token.symbol,
          decimals: token.decimals,
          image: token.icon,
        },
      },
    })

    return wasAdded
  } catch (error) {
    console.error('Error adding token to wallet:', error)
    return false
  }
}
