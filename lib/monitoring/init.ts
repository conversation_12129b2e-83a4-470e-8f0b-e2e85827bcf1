/**
 * Monitoring System Initialization
 * Sets up all monitoring components for PawPumps
 */

import { logger } from './logger'
import { performanceMonitor } from './performance'
import { alertingSystem, requestNotificationPermission } from './alerting'
import { uptimeMonitor, startUptimeMonitoring } from './uptime'

export interface MonitoringConfig {
  enablePerformanceMonitoring: boolean
  enableErrorTracking: boolean
  enableUptimeMonitoring: boolean
  enableAlerting: boolean
  enableNotifications: boolean
  environment: 'development' | 'staging' | 'production'
  debug: boolean
}

class MonitoringSystem {
  private config: MonitoringConfig
  private isInitialized = false

  constructor() {
    this.config = this.getDefaultConfig()
  }

  private getDefaultConfig(): MonitoringConfig {
    const isDevelopment = process.env.NODE_ENV === 'development'
    const isProduction = process.env.NODE_ENV === 'production'

    return {
      enablePerformanceMonitoring: true,
      enableErrorTracking: true,
      enableUptimeMonitoring: isProduction, // Only in production by default
      enableAlerting: true,
      enableNotifications: isProduction, // Only in production by default
      environment: (process.env.NODE_ENV as any) || 'development',
      debug: isDevelopment,
    }
  }

  public async initialize(customConfig?: Partial<MonitoringConfig>): Promise<void> {
    if (this.isInitialized) {
      logger.warn('Monitoring system already initialized', {
        component: 'MonitoringSystem',
      })
      return
    }

    // Merge custom config with defaults
    this.config = { ...this.config, ...customConfig }

    logger.info('Initializing monitoring system', {
      component: 'MonitoringSystem',
      metadata: { config: this.config },
    })

    try {
      // Initialize error tracking and logging (always enabled)
      this.initializeErrorTracking()

      // Initialize performance monitoring
      if (this.config.enablePerformanceMonitoring) {
        this.initializePerformanceMonitoring()
      }

      // Initialize uptime monitoring
      if (this.config.enableUptimeMonitoring) {
        this.initializeUptimeMonitoring()
      }

      // Initialize alerting system
      if (this.config.enableAlerting) {
        await this.initializeAlerting()
      }

      // Setup global error handlers
      this.setupGlobalErrorHandlers()

      // Setup unload handlers
      this.setupUnloadHandlers()

      // Setup development tools
      if (this.config.debug) {
        this.setupDevelopmentTools()
      }

      this.isInitialized = true

      logger.info('Monitoring system initialized successfully', {
        component: 'MonitoringSystem',
        metadata: {
          performanceMonitoring: this.config.enablePerformanceMonitoring,
          uptimeMonitoring: this.config.enableUptimeMonitoring,
          alerting: this.config.enableAlerting,
          environment: this.config.environment,
        },
      })

    } catch (error) {
      logger.error('Failed to initialize monitoring system', error as Error, {
        component: 'MonitoringSystem',
      })
      throw error
    }
  }

  private initializeErrorTracking(): void {
    logger.info('Error tracking initialized', {
      component: 'MonitoringSystem',
    })

    // Error tracking is handled by the logger and global error handlers
    // In production, this would also initialize Sentry, Bugsnag, etc.
  }

  private initializePerformanceMonitoring(): void {
    logger.info('Performance monitoring initialized', {
      component: 'MonitoringSystem',
    })

    // Performance monitoring is automatically initialized when imported
    // Additional setup could be done here if needed
  }

  private initializeUptimeMonitoring(): void {
    logger.info('Uptime monitoring initialized', {
      component: 'MonitoringSystem',
    })

    // Start uptime monitoring
    startUptimeMonitoring()
  }

  private async initializeAlerting(): Promise<void> {
    logger.info('Alerting system initialized', {
      component: 'MonitoringSystem',
    })

    // Request notification permission if enabled
    if (this.config.enableNotifications && typeof window !== 'undefined') {
      try {
        const granted = await requestNotificationPermission()
        logger.info(`Notification permission: ${granted ? 'granted' : 'denied'}`, {
          component: 'MonitoringSystem',
        })
      } catch (error) {
        logger.warn('Failed to request notification permission', {
          component: 'MonitoringSystem',
          metadata: { error: (error as Error).message },
        })
      }
    }
  }

  private setupGlobalErrorHandlers(): void {
    if (typeof window === 'undefined') return

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      logger.error('Unhandled Promise Rejection', event.reason, {
        component: 'GlobalErrorHandler',
        metadata: {
          type: 'unhandledrejection',
          promise: event.promise,
        },
      })

      // Prevent the default browser behavior
      event.preventDefault()
    })

    // Handle uncaught errors
    window.addEventListener('error', (event) => {
      logger.error('Uncaught Error', event.error, {
        component: 'GlobalErrorHandler',
        metadata: {
          type: 'error',
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          message: event.message,
        },
      })
    })

    // Handle resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target && event.target !== window) {
        const target = event.target as any
        logger.error('Resource Loading Error', undefined, {
          component: 'GlobalErrorHandler',
          metadata: {
            type: 'resource',
            tagName: target.tagName,
            src: target.src || target.href,
            message: 'Failed to load resource',
          },
        })
      }
    }, true) // Use capture phase to catch resource errors

    logger.info('Global error handlers setup complete', {
      component: 'MonitoringSystem',
    })
  }

  private setupUnloadHandlers(): void {
    if (typeof window === 'undefined') return

    // Send final metrics before page unload
    window.addEventListener('beforeunload', () => {
      logger.info('Page unloading - sending final metrics', {
        component: 'MonitoringSystem',
      })

      // Performance monitor will handle sending metrics
      // Uptime monitor will handle final uptime data
      // Alerting system will handle any pending alerts
    })

    // Handle visibility changes (tab switching, minimizing)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        logger.debug('Page hidden - pausing non-critical monitoring', {
          component: 'MonitoringSystem',
        })
      } else {
        logger.debug('Page visible - resuming monitoring', {
          component: 'MonitoringSystem',
        })
      }
    })

    logger.info('Unload handlers setup complete', {
      component: 'MonitoringSystem',
    })
  }

  private setupDevelopmentTools(): void {
    if (typeof window === 'undefined') return

    // Add monitoring controls to window for debugging
    (window as any).pawpumpsMonitoring = {
      logger,
      performanceMonitor,
      alertingSystem,
      uptimeMonitor,
      config: this.config,
      
      // Utility functions for debugging
      triggerTestAlert: () => {
        alertingSystem.addCustomAlert(
          'system' as any,
          'medium' as any,
          'Test Alert',
          'This is a test alert for debugging purposes'
        )
      },
      
      getSystemStatus: () => ({
        initialized: this.isInitialized,
        config: this.config,
        alerts: alertingSystem.getActiveAlerts(),
        uptime: uptimeMonitor.getUptimeStats(),
        performance: performanceMonitor.getCoreWebVitals(),
      }),
      
      enablePerformanceDashboard: () => {
        localStorage.setItem('performance-dashboard', 'true')
        window.location.reload()
      },
      
      disablePerformanceDashboard: () => {
        localStorage.setItem('performance-dashboard', 'false')
        window.location.reload()
      },
    }

    // Log available debugging tools
    logger.info('Development tools available at window.pawpumpsMonitoring', {
      component: 'MonitoringSystem',
      metadata: {
        tools: [
          'logger',
          'performanceMonitor',
          'alertingSystem',
          'uptimeMonitor',
          'triggerTestAlert()',
          'getSystemStatus()',
          'enablePerformanceDashboard()',
          'disablePerformanceDashboard()',
        ],
      },
    })
  }

  public getConfig(): MonitoringConfig {
    return { ...this.config }
  }

  public isSystemInitialized(): boolean {
    return this.isInitialized
  }

  public async updateConfig(updates: Partial<MonitoringConfig>): Promise<void> {
    const oldConfig = { ...this.config }
    this.config = { ...this.config, ...updates }

    logger.info('Monitoring configuration updated', {
      component: 'MonitoringSystem',
      metadata: {
        oldConfig,
        newConfig: this.config,
        changes: updates,
      },
    })

    // Reinitialize if necessary
    if (updates.enableUptimeMonitoring !== undefined) {
      if (updates.enableUptimeMonitoring && !oldConfig.enableUptimeMonitoring) {
        this.initializeUptimeMonitoring()
      } else if (!updates.enableUptimeMonitoring && oldConfig.enableUptimeMonitoring) {
        uptimeMonitor.stop()
      }
    }
  }

  public getSystemHealth(): Record<string, any> {
    return {
      initialized: this.isInitialized,
      config: this.config,
      timestamp: new Date().toISOString(),
      uptime: this.config.enableUptimeMonitoring ? uptimeMonitor.getUptimeStats() : null,
      alerts: this.config.enableAlerting ? alertingSystem.getActiveAlerts().length : null,
      performance: this.config.enablePerformanceMonitoring ? performanceMonitor.getCoreWebVitals() : null,
    }
  }
}

// Export singleton instance
export const monitoringSystem = new MonitoringSystem()

// Auto-initialize in browser environment
if (typeof window !== 'undefined') {
  // Initialize after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      monitoringSystem.initialize().catch(console.error)
    })
  } else {
    // DOM is already ready
    monitoringSystem.initialize().catch(console.error)
  }
}

// Export convenience functions
export const initializeMonitoring = (config?: Partial<MonitoringConfig>) => 
  monitoringSystem.initialize(config)

export const getMonitoringConfig = () => monitoringSystem.getConfig()
export const getSystemHealth = () => monitoringSystem.getSystemHealth()
export const isMonitoringInitialized = () => monitoringSystem.isSystemInitialized()
