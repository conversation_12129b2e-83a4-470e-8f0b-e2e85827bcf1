/**
 * Real User Monitoring (RUM) for Core Web Vitals and Performance Tracking
 */

import { logger, logPerformanceMetric } from './logger'

export interface PerformanceMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  timestamp: number
  url: string
  userAgent?: string
}

export interface CoreWebVitals {
  LCP?: number // Largest Contentful Paint
  FID?: number // First Input Delay
  CLS?: number // Cumulative Layout Shift
  FCP?: number // First Contentful Paint
  TTFB?: number // Time to First Byte
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private isSupported = typeof window !== 'undefined' && 'performance' in window
  private observer?: PerformanceObserver

  constructor() {
    if (this.isSupported) {
      this.initializeObserver()
      this.trackNavigationTiming()
      this.trackResourceTiming()
      this.setupUnloadHandler()
    }
  }

  private initializeObserver(): void {
    try {
      // Observe Core Web Vitals
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.handlePerformanceEntry(entry)
        }
      })

      // Observe different types of performance entries
      const entryTypes = ['navigation', 'paint', 'largest-contentful-paint', 'first-input', 'layout-shift']
      
      entryTypes.forEach(type => {
        try {
          this.observer?.observe({ type, buffered: true })
        } catch (error) {
          // Some entry types might not be supported in all browsers
          console.debug(`Performance observer type '${type}' not supported`)
        }
      })
    } catch (error) {
      logger.warn('Failed to initialize performance observer', {
        component: 'PerformanceMonitor',
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      })
    }
  }

  private handlePerformanceEntry(entry: PerformanceEntry): void {
    switch (entry.entryType) {
      case 'navigation':
        this.handleNavigationEntry(entry as PerformanceNavigationTiming)
        break
      case 'paint':
        this.handlePaintEntry(entry as PerformancePaintTiming)
        break
      case 'largest-contentful-paint':
        this.handleLCPEntry(entry as any)
        break
      case 'first-input':
        this.handleFIDEntry(entry as any)
        break
      case 'layout-shift':
        this.handleCLSEntry(entry as any)
        break
    }
  }

  private handleNavigationEntry(entry: PerformanceNavigationTiming): void {
    const metrics = {
      'DNS Lookup': entry.domainLookupEnd - entry.domainLookupStart,
      'TCP Connection': entry.connectEnd - entry.connectStart,
      'TLS Handshake': entry.secureConnectionStart > 0 ? entry.connectEnd - entry.secureConnectionStart : 0,
      'Request': entry.responseStart - entry.requestStart,
      'Response': entry.responseEnd - entry.responseStart,
      'DOM Processing': entry.domComplete - entry.domInteractive,
      'Load Complete': entry.loadEventEnd - entry.loadEventStart,
    }

    Object.entries(metrics).forEach(([name, value]) => {
      if (value > 0) {
        this.recordMetric(name, value)
      }
    })
  }

  private handlePaintEntry(entry: PerformancePaintTiming): void {
    if (entry.name === 'first-contentful-paint') {
      this.recordMetric('FCP', entry.startTime, this.getFCPRating(entry.startTime))
    }
  }

  private handleLCPEntry(entry: any): void {
    this.recordMetric('LCP', entry.startTime, this.getLCPRating(entry.startTime))
  }

  private handleFIDEntry(entry: any): void {
    this.recordMetric('FID', entry.processingStart - entry.startTime, this.getFIDRating(entry.processingStart - entry.startTime))
  }

  private handleCLSEntry(entry: any): void {
    if (!entry.hadRecentInput) {
      this.recordMetric('CLS', entry.value, this.getCLSRating(entry.value))
    }
  }

  private recordMetric(name: string, value: number, rating?: 'good' | 'needs-improvement' | 'poor'): void {
    const metric: PerformanceMetric = {
      name,
      value: Math.round(value * 100) / 100, // Round to 2 decimal places
      rating: rating || this.getGenericRating(name, value),
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    }

    this.metrics.push(metric)
    
    // Log to our logging system
    logPerformanceMetric(name, metric.value, {
      component: 'PerformanceMonitor',
      metadata: {
        rating: metric.rating,
        url: metric.url,
      }
    })

    // Send to analytics in production
    if (process.env.NODE_ENV === 'production') {
      this.sendMetricToAnalytics(metric)
    }
  }

  private getLCPRating(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 2500) return 'good'
    if (value <= 4000) return 'needs-improvement'
    return 'poor'
  }

  private getFIDRating(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 100) return 'good'
    if (value <= 300) return 'needs-improvement'
    return 'poor'
  }

  private getCLSRating(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 0.1) return 'good'
    if (value <= 0.25) return 'needs-improvement'
    return 'poor'
  }

  private getFCPRating(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 1800) return 'good'
    if (value <= 3000) return 'needs-improvement'
    return 'poor'
  }

  private getGenericRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    // Generic thresholds for other metrics
    const thresholds: Record<string, [number, number]> = {
      'DNS Lookup': [50, 200],
      'TCP Connection': [100, 300],
      'Request': [100, 500],
      'Response': [200, 1000],
      'DOM Processing': [500, 2000],
    }

    const [good, poor] = thresholds[name] || [1000, 3000]
    
    if (value <= good) return 'good'
    if (value <= poor) return 'needs-improvement'
    return 'poor'
  }

  private trackNavigationTiming(): void {
    // Track page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        if (navigation) {
          const pageLoadTime = navigation.loadEventEnd - navigation.fetchStart
          this.recordMetric('Page Load Time', pageLoadTime)
        }
      }, 0)
    })
  }

  private trackResourceTiming(): void {
    // Track resource loading performance
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resource = entry as PerformanceResourceTiming
          const duration = resource.responseEnd - resource.fetchStart
          
          // Only track significant resources
          if (duration > 100) {
            this.recordMetric(`Resource: ${this.getResourceType(resource.name)}`, duration)
          }
        }
      }
    })

    try {
      observer.observe({ type: 'resource', buffered: true })
    } catch (error) {
      console.debug('Resource timing not supported')
    }
  }

  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'JavaScript'
    if (url.includes('.css')) return 'CSS'
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)/)) return 'Image'
    if (url.includes('/api/')) return 'API'
    return 'Other'
  }

  private setupUnloadHandler(): void {
    // Send metrics before page unload
    window.addEventListener('beforeunload', () => {
      this.sendAllMetrics()
    })

    // Also send metrics periodically
    setInterval(() => {
      this.sendAllMetrics()
    }, 30000) // Every 30 seconds
  }

  private async sendMetricToAnalytics(metric: PerformanceMetric): Promise<void> {
    try {
      await fetch('/api/analytics/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(metric),
        keepalive: true,
      })
    } catch (error) {
      // Silently fail to avoid affecting user experience
    }
  }

  private sendAllMetrics(): void {
    if (this.metrics.length === 0) return

    // Send all collected metrics
    const metricsToSend = [...this.metrics]
    this.metrics = [] // Clear sent metrics

    if (process.env.NODE_ENV === 'production') {
      fetch('/api/analytics/performance-batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(metricsToSend),
        keepalive: true,
      }).catch(() => {
        // Silently fail
      })
    }
  }

  // Public API
  public trackCustomMetric(name: string, value: number): void {
    this.recordMetric(`Custom: ${name}`, value)
  }

  public trackUserTiming(name: string, startTime: number): void {
    const duration = performance.now() - startTime
    this.recordMetric(`User Timing: ${name}`, duration)
  }

  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics]
  }

  public getCoreWebVitals(): CoreWebVitals {
    const vitals: CoreWebVitals = {}
    
    this.metrics.forEach(metric => {
      switch (metric.name) {
        case 'LCP':
          vitals.LCP = metric.value
          break
        case 'FID':
          vitals.FID = metric.value
          break
        case 'CLS':
          vitals.CLS = metric.value
          break
        case 'FCP':
          vitals.FCP = metric.value
          break
      }
    })

    return vitals
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor()

// Convenience functions
export const trackCustomMetric = (name: string, value: number) => performanceMonitor.trackCustomMetric(name, value)
export const trackUserTiming = (name: string, startTime: number) => performanceMonitor.trackUserTiming(name, startTime)
export const getCoreWebVitals = () => performanceMonitor.getCoreWebVitals()
export const getPerformanceMetrics = () => performanceMonitor.getMetrics()
