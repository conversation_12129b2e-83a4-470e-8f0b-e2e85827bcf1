/**
 * Uptime and Business Metrics Monitoring for PawPumps
 * Tracks service availability and critical business metrics
 */

import { logger } from './logger'
import { alertingSystem, AlertType, AlertSeverity } from './alerting'

export interface UptimeCheck {
  id: string
  name: string
  url: string
  method: 'GET' | 'POST' | 'HEAD'
  expectedStatus: number
  timeout: number
  interval: number // seconds
  enabled: boolean
  headers?: Record<string, string>
  body?: string
}

export interface UptimeResult {
  checkId: string
  timestamp: number
  success: boolean
  responseTime: number
  statusCode?: number
  error?: string
}

export interface BusinessMetric {
  name: string
  value: number
  timestamp: number
  metadata?: Record<string, any>
}

export interface UserJourney {
  id: string
  name: string
  steps: string[]
  successRate: number
  averageTime: number
  lastChecked: number
}

class UptimeMonitor {
  private checks: UptimeCheck[] = []
  private results: UptimeResult[] = []
  private businessMetrics: BusinessMetric[] = []
  private userJourneys: UserJourney[] = []
  private intervals: Map<string, NodeJS.Timeout> = new Map()
  private isRunning = false

  constructor() {
    this.initializeDefaultChecks()
    this.initializeUserJourneys()
  }

  private initializeDefaultChecks(): void {
    this.checks = [
      {
        id: 'homepage',
        name: 'Homepage Availability',
        url: '/',
        method: 'GET',
        expectedStatus: 200,
        timeout: 10000,
        interval: 60, // 1 minute
        enabled: true,
      },
      {
        id: 'trade-page',
        name: 'Trade Page Availability',
        url: '/trade',
        method: 'GET',
        expectedStatus: 200,
        timeout: 10000,
        interval: 120, // 2 minutes
        enabled: true,
      },
      {
        id: 'launch-page',
        name: 'Launch Page Availability',
        url: '/launch',
        method: 'GET',
        expectedStatus: 200,
        timeout: 10000,
        interval: 120, // 2 minutes
        enabled: true,
      },
      {
        id: 'price-api',
        name: 'Price API Health',
        url: '/api/price/DOGE',
        method: 'GET',
        expectedStatus: 200,
        timeout: 5000,
        interval: 30, // 30 seconds
        enabled: true,
      },
      {
        id: 'chart-api',
        name: 'Chart API Health',
        url: '/api/chart/DOGE/1H',
        method: 'GET',
        expectedStatus: 200,
        timeout: 5000,
        interval: 60, // 1 minute
        enabled: true,
      },
    ]
  }

  private initializeUserJourneys(): void {
    this.userJourneys = [
      {
        id: 'token-launch',
        name: 'Token Launch Journey',
        steps: ['Visit Launch Page', 'Fill Form', 'Connect Wallet', 'Submit Transaction'],
        successRate: 0.85, // 85% success rate
        averageTime: 180000, // 3 minutes
        lastChecked: Date.now(),
      },
      {
        id: 'trading',
        name: 'Trading Journey',
        steps: ['Visit Trade Page', 'View Charts', 'Connect Wallet', 'Execute Trade'],
        successRate: 0.92, // 92% success rate
        averageTime: 120000, // 2 minutes
        lastChecked: Date.now(),
      },
      {
        id: 'wallet-connection',
        name: 'Wallet Connection Journey',
        steps: ['Click Connect', 'Select Wallet', 'Approve Connection', 'Verify Connection'],
        successRate: 0.88, // 88% success rate
        averageTime: 30000, // 30 seconds
        lastChecked: Date.now(),
      },
    ]
  }

  public start(): void {
    if (this.isRunning) return

    this.isRunning = true
    logger.info('Starting uptime monitoring', {
      component: 'UptimeMonitor',
      metadata: { checksCount: this.checks.length },
    })

    // Start all enabled checks
    this.checks.forEach(check => {
      if (check.enabled) {
        this.startCheck(check)
      }
    })

    // Start business metrics collection
    this.startBusinessMetricsCollection()
  }

  public stop(): void {
    if (!this.isRunning) return

    this.isRunning = false
    logger.info('Stopping uptime monitoring', {
      component: 'UptimeMonitor',
    })

    // Clear all intervals
    this.intervals.forEach(interval => clearInterval(interval))
    this.intervals.clear()
  }

  private startCheck(check: UptimeCheck): void {
    // Run initial check
    this.performCheck(check)

    // Schedule recurring checks
    const interval = setInterval(() => {
      this.performCheck(check)
    }, check.interval * 1000)

    this.intervals.set(check.id, interval)
  }

  private async performCheck(check: UptimeCheck): Promise<void> {
    const startTime = Date.now()
    
    try {
      const baseUrl = typeof window !== 'undefined' 
        ? window.location.origin 
        : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
      
      const url = check.url.startsWith('http') ? check.url : `${baseUrl}${check.url}`
      
      const response = await fetch(url, {
        method: check.method,
        headers: check.headers,
        body: check.body,
        signal: AbortSignal.timeout(check.timeout),
      })

      const responseTime = Date.now() - startTime
      const success = response.status === check.expectedStatus

      const result: UptimeResult = {
        checkId: check.id,
        timestamp: Date.now(),
        success,
        responseTime,
        statusCode: response.status,
      }

      this.results.push(result)
      this.processUptimeResult(check, result)

      // Keep only last 1000 results
      if (this.results.length > 1000) {
        this.results = this.results.slice(-1000)
      }

    } catch (error) {
      const responseTime = Date.now() - startTime
      const result: UptimeResult = {
        checkId: check.id,
        timestamp: Date.now(),
        success: false,
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      }

      this.results.push(result)
      this.processUptimeResult(check, result)
    }
  }

  private processUptimeResult(check: UptimeCheck, result: UptimeResult): void {
    // Log result
    if (result.success) {
      logger.debug(`Uptime check passed: ${check.name}`, {
        component: 'UptimeMonitor',
        metadata: {
          checkId: check.id,
          responseTime: result.responseTime,
          statusCode: result.statusCode,
        },
      })
    } else {
      logger.error(`Uptime check failed: ${check.name}`, undefined, {
        component: 'UptimeMonitor',
        metadata: {
          checkId: check.id,
          responseTime: result.responseTime,
          statusCode: result.statusCode,
          error: result.error,
        },
      })

      // Trigger alert for failed checks
      alertingSystem.addCustomAlert(
        AlertType.SYSTEM,
        AlertSeverity.HIGH,
        `Service Down: ${check.name}`,
        `${check.name} is not responding. Status: ${result.statusCode || 'No response'}, Error: ${result.error || 'Unknown'}`,
        {
          checkId: check.id,
          url: check.url,
          responseTime: result.responseTime,
          statusCode: result.statusCode,
        }
      )
    }

    // Record business metric
    this.recordBusinessMetric('uptime_check_response_time', result.responseTime, {
      checkId: check.id,
      checkName: check.name,
      success: result.success,
    })
  }

  private startBusinessMetricsCollection(): void {
    // Collect business metrics every 5 minutes
    setInterval(() => {
      this.collectBusinessMetrics()
    }, 300000)

    // Update user journey metrics every 10 minutes
    setInterval(() => {
      this.updateUserJourneyMetrics()
    }, 600000)
  }

  private collectBusinessMetrics(): void {
    // Calculate uptime percentages
    const uptimeStats = this.calculateUptimeStats()
    
    Object.entries(uptimeStats).forEach(([checkId, stats]) => {
      this.recordBusinessMetric(`uptime_percentage_${checkId}`, stats.uptime, {
        checkId,
        totalChecks: stats.totalChecks,
        successfulChecks: stats.successfulChecks,
      })
    })

    // Record overall system health
    const overallUptime = Object.values(uptimeStats).reduce((sum, stats) => sum + stats.uptime, 0) / Object.keys(uptimeStats).length
    this.recordBusinessMetric('overall_uptime_percentage', overallUptime)

    // Simulate business metrics (in production, these would come from real data)
    this.recordBusinessMetric('active_users', Math.floor(Math.random() * 1000) + 500)
    this.recordBusinessMetric('daily_transactions', Math.floor(Math.random() * 100) + 50)
    this.recordBusinessMetric('wallet_connections', Math.floor(Math.random() * 200) + 100)
  }

  private calculateUptimeStats(): Record<string, { uptime: number; totalChecks: number; successfulChecks: number }> {
    const stats: Record<string, { uptime: number; totalChecks: number; successfulChecks: number }> = {}
    
    // Calculate stats for last 24 hours
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000)
    const recentResults = this.results.filter(result => result.timestamp > oneDayAgo)

    this.checks.forEach(check => {
      const checkResults = recentResults.filter(result => result.checkId === check.id)
      const successfulChecks = checkResults.filter(result => result.success).length
      const totalChecks = checkResults.length
      const uptime = totalChecks > 0 ? (successfulChecks / totalChecks) * 100 : 100

      stats[check.id] = {
        uptime,
        totalChecks,
        successfulChecks,
      }
    })

    return stats
  }

  private updateUserJourneyMetrics(): void {
    // In production, this would analyze real user journey data
    this.userJourneys.forEach(journey => {
      // Simulate journey metrics updates
      const successRate = 0.8 + (Math.random() * 0.2) // 80-100% success rate
      const averageTime = journey.averageTime * (0.8 + Math.random() * 0.4) // ±20% variation

      journey.successRate = successRate
      journey.averageTime = averageTime
      journey.lastChecked = Date.now()

      this.recordBusinessMetric(`journey_success_rate_${journey.id}`, successRate * 100, {
        journeyId: journey.id,
        journeyName: journey.name,
      })

      this.recordBusinessMetric(`journey_average_time_${journey.id}`, averageTime, {
        journeyId: journey.id,
        journeyName: journey.name,
      })

      // Alert on poor journey performance
      if (successRate < 0.8) { // Less than 80% success rate
        alertingSystem.addCustomAlert(
          AlertType.BUSINESS,
          AlertSeverity.HIGH,
          `Poor User Journey Performance: ${journey.name}`,
          `${journey.name} success rate is ${(successRate * 100).toFixed(1)}% (threshold: 80%)`,
          {
            journeyId: journey.id,
            successRate,
            averageTime,
          }
        )
      }
    })
  }

  private recordBusinessMetric(name: string, value: number, metadata?: Record<string, any>): void {
    const metric: BusinessMetric = {
      name,
      value,
      timestamp: Date.now(),
      metadata,
    }

    this.businessMetrics.push(metric)

    // Keep only last 10000 metrics
    if (this.businessMetrics.length > 10000) {
      this.businessMetrics = this.businessMetrics.slice(-10000)
    }

    // Send to analytics API
    if (typeof window !== 'undefined') {
      fetch('/api/analytics/business-metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(metric),
      }).catch(() => {
        // Silently fail
      })
    }
  }

  // Public API
  public getUptimeStats(): Record<string, any> {
    return this.calculateUptimeStats()
  }

  public getRecentResults(checkId?: string, limit = 100): UptimeResult[] {
    let results = this.results
    
    if (checkId) {
      results = results.filter(result => result.checkId === checkId)
    }
    
    return results.slice(-limit)
  }

  public getBusinessMetrics(name?: string, limit = 100): BusinessMetric[] {
    let metrics = this.businessMetrics
    
    if (name) {
      metrics = metrics.filter(metric => metric.name === name)
    }
    
    return metrics.slice(-limit)
  }

  public getUserJourneys(): UserJourney[] {
    return [...this.userJourneys]
  }

  public addCheck(check: UptimeCheck): void {
    this.checks.push(check)
    
    if (check.enabled && this.isRunning) {
      this.startCheck(check)
    }
  }

  public removeCheck(checkId: string): boolean {
    const index = this.checks.findIndex(check => check.id === checkId)
    
    if (index !== -1) {
      this.checks.splice(index, 1)
      
      const interval = this.intervals.get(checkId)
      if (interval) {
        clearInterval(interval)
        this.intervals.delete(checkId)
      }
      
      return true
    }
    
    return false
  }

  public getChecks(): UptimeCheck[] {
    return [...this.checks]
  }
}

// Export singleton instance
export const uptimeMonitor = new UptimeMonitor()

// Convenience functions
export const startUptimeMonitoring = () => uptimeMonitor.start()
export const stopUptimeMonitoring = () => uptimeMonitor.stop()
export const getUptimeStats = () => uptimeMonitor.getUptimeStats()
export const getBusinessMetrics = (name?: string, limit?: number) => uptimeMonitor.getBusinessMetrics(name, limit)
export const getUserJourneys = () => uptimeMonitor.getUserJourneys()
