/**
 * Alert Configuration and Thresholds for PawPumps
 * Centralized configuration for all monitoring alerts and escalation procedures
 */

import { AlertType, AlertSeverity, AlertRule } from './alerting'

export interface AlertThreshold {
  metric: string
  warning: number
  critical: number
  unit: string
  description: string
}

export interface EscalationLevel {
  level: number
  name: string
  delay: number // minutes
  channels: string[]
  recipients: string[]
}

export interface NotificationChannel {
  id: string
  name: string
  type: 'email' | 'slack' | 'sms' | 'webhook' | 'pagerduty'
  config: Record<string, any>
  enabled: boolean
}

export interface AlertConfiguration {
  thresholds: AlertThreshold[]
  rules: AlertRule[]
  escalation: EscalationLevel[]
  channels: NotificationChannel[]
  globalSettings: {
    enableAlerting: boolean
    defaultCooldown: number
    maxAlertsPerHour: number
    quietHours: { start: string; end: string }
  }
}

// Default alert thresholds based on industry best practices
export const DEFAULT_THRESHOLDS: AlertThreshold[] = [
  // Performance Thresholds
  {
    metric: 'lcp',
    warning: 2500,
    critical: 4000,
    unit: 'ms',
    description: 'Largest Contentful Paint - Core Web Vital',
  },
  {
    metric: 'fid',
    warning: 100,
    critical: 300,
    unit: 'ms',
    description: 'First Input Delay - Core Web Vital',
  },
  {
    metric: 'cls',
    warning: 0.1,
    critical: 0.25,
    unit: 'score',
    description: 'Cumulative Layout Shift - Core Web Vital',
  },
  {
    metric: 'fcp',
    warning: 1800,
    critical: 3000,
    unit: 'ms',
    description: 'First Contentful Paint',
  },
  {
    metric: 'page_load_time',
    warning: 3000,
    critical: 5000,
    unit: 'ms',
    description: 'Total page load time',
  },
  {
    metric: 'api_response_time',
    warning: 1000,
    critical: 3000,
    unit: 'ms',
    description: 'API response time',
  },

  // Error Rate Thresholds
  {
    metric: 'error_rate',
    warning: 0.02, // 2%
    critical: 0.05, // 5%
    unit: '%',
    description: 'Application error rate',
  },
  {
    metric: 'api_error_rate',
    warning: 0.03, // 3%
    critical: 0.08, // 8%
    unit: '%',
    description: 'API error rate',
  },

  // Availability Thresholds
  {
    metric: 'uptime',
    warning: 99.5,
    critical: 99.0,
    unit: '%',
    description: 'Service uptime percentage',
  },
  {
    metric: 'api_availability',
    warning: 99.0,
    critical: 98.0,
    unit: '%',
    description: 'API availability percentage',
  },

  // Business Metrics Thresholds
  {
    metric: 'user_journey_success_rate',
    warning: 90.0,
    critical: 85.0,
    unit: '%',
    description: 'Critical user journey success rate',
  },
  {
    metric: 'wallet_connection_success_rate',
    warning: 90.0,
    critical: 80.0,
    unit: '%',
    description: 'Wallet connection success rate',
  },
  {
    metric: 'token_launch_success_rate',
    warning: 85.0,
    critical: 75.0,
    unit: '%',
    description: 'Token launch completion rate',
  },
  {
    metric: 'trading_success_rate',
    warning: 95.0,
    critical: 90.0,
    unit: '%',
    description: 'Trading transaction success rate',
  },

  // Security Thresholds
  {
    metric: 'suspicious_requests_per_hour',
    warning: 50,
    critical: 100,
    unit: 'requests',
    description: 'Suspicious requests detected per hour',
  },
  {
    metric: 'failed_login_attempts',
    warning: 20,
    critical: 50,
    unit: 'attempts',
    description: 'Failed login attempts per hour',
  },

  // Resource Thresholds
  {
    metric: 'memory_usage',
    warning: 80,
    critical: 95,
    unit: '%',
    description: 'Memory usage percentage',
  },
  {
    metric: 'cpu_usage',
    warning: 70,
    critical: 90,
    unit: '%',
    description: 'CPU usage percentage',
  },
]

// Default escalation procedures
export const DEFAULT_ESCALATION: EscalationLevel[] = [
  {
    level: 1,
    name: 'Initial Alert',
    delay: 0,
    channels: ['slack', 'email'],
    recipients: ['engineering-team'],
  },
  {
    level: 2,
    name: 'Escalation to Senior Engineers',
    delay: 15, // 15 minutes
    channels: ['slack', 'email', 'sms'],
    recipients: ['senior-engineers', 'engineering-manager'],
  },
  {
    level: 3,
    name: 'Critical Escalation',
    delay: 30, // 30 minutes
    channels: ['pagerduty', 'slack', 'email', 'sms'],
    recipients: ['engineering-manager', 'cto', 'on-call-engineer'],
  },
  {
    level: 4,
    name: 'Executive Escalation',
    delay: 60, // 1 hour
    channels: ['pagerduty', 'email', 'sms'],
    recipients: ['cto', 'ceo', 'head-of-engineering'],
  },
]

// Default notification channels
export const DEFAULT_CHANNELS: NotificationChannel[] = [
  {
    id: 'slack',
    name: 'Slack Alerts',
    type: 'slack',
    config: {
      webhookUrl: process.env.SLACK_WEBHOOK_URL,
      channel: '#alerts',
      username: 'PawPumps Monitor',
    },
    enabled: !!process.env.SLACK_WEBHOOK_URL,
  },
  {
    id: 'email',
    name: 'Email Notifications',
    type: 'email',
    config: {
      smtpHost: process.env.SMTP_HOST,
      smtpPort: process.env.SMTP_PORT,
      smtpUser: process.env.SMTP_USER,
      smtpPass: process.env.SMTP_PASS,
      fromEmail: process.env.ALERT_FROM_EMAIL || '<EMAIL>',
    },
    enabled: !!process.env.SMTP_HOST,
  },
  {
    id: 'sms',
    name: 'SMS Alerts',
    type: 'sms',
    config: {
      provider: 'twilio',
      accountSid: process.env.TWILIO_ACCOUNT_SID,
      authToken: process.env.TWILIO_AUTH_TOKEN,
      fromNumber: process.env.TWILIO_FROM_NUMBER,
    },
    enabled: !!process.env.TWILIO_ACCOUNT_SID,
  },
  {
    id: 'pagerduty',
    name: 'PagerDuty',
    type: 'pagerduty',
    config: {
      integrationKey: process.env.PAGERDUTY_INTEGRATION_KEY,
      serviceId: process.env.PAGERDUTY_SERVICE_ID,
    },
    enabled: !!process.env.PAGERDUTY_INTEGRATION_KEY,
  },
  {
    id: 'webhook',
    name: 'Custom Webhook',
    type: 'webhook',
    config: {
      url: process.env.ALERT_WEBHOOK_URL,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': process.env.ALERT_WEBHOOK_TOKEN ? `Bearer ${process.env.ALERT_WEBHOOK_TOKEN}` : undefined,
      },
    },
    enabled: !!process.env.ALERT_WEBHOOK_URL,
  },
]

// Generate alert rules from thresholds
export function generateAlertRules(thresholds: AlertThreshold[]): AlertRule[] {
  const rules: AlertRule[] = []

  thresholds.forEach(threshold => {
    // Warning rule
    rules.push({
      id: `${threshold.metric}-warning`,
      name: `${threshold.metric.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} Warning`,
      type: getAlertType(threshold.metric),
      severity: AlertSeverity.MEDIUM,
      condition: (data: any) => {
        const value = data[threshold.metric]
        return value !== undefined && (
          threshold.metric.includes('rate') || threshold.metric.includes('uptime') 
            ? value < threshold.warning 
            : value > threshold.warning
        )
      },
      message: (data: any) => {
        const value = data[threshold.metric]
        return `${threshold.description}: ${value}${threshold.unit} (warning threshold: ${threshold.warning}${threshold.unit})`
      },
      cooldown: 10, // 10 minutes
      enabled: true,
    })

    // Critical rule
    rules.push({
      id: `${threshold.metric}-critical`,
      name: `${threshold.metric.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} Critical`,
      type: getAlertType(threshold.metric),
      severity: AlertSeverity.CRITICAL,
      condition: (data: any) => {
        const value = data[threshold.metric]
        return value !== undefined && (
          threshold.metric.includes('rate') || threshold.metric.includes('uptime')
            ? value < threshold.critical
            : value > threshold.critical
        )
      },
      message: (data: any) => {
        const value = data[threshold.metric]
        return `${threshold.description}: ${value}${threshold.unit} (critical threshold: ${threshold.critical}${threshold.unit})`
      },
      cooldown: 5, // 5 minutes
      enabled: true,
    })
  })

  return rules
}

function getAlertType(metric: string): AlertType {
  if (metric.includes('security') || metric.includes('suspicious') || metric.includes('failed_login')) {
    return AlertType.SECURITY
  }
  if (metric.includes('journey') || metric.includes('success_rate') || metric.includes('wallet') || metric.includes('trading')) {
    return AlertType.BUSINESS
  }
  if (metric.includes('error') || metric.includes('failure')) {
    return AlertType.ERROR
  }
  if (metric.includes('uptime') || metric.includes('availability') || metric.includes('memory') || metric.includes('cpu')) {
    return AlertType.SYSTEM
  }
  return AlertType.PERFORMANCE
}

// Default configuration
export const DEFAULT_ALERT_CONFIG: AlertConfiguration = {
  thresholds: DEFAULT_THRESHOLDS,
  rules: generateAlertRules(DEFAULT_THRESHOLDS),
  escalation: DEFAULT_ESCALATION,
  channels: DEFAULT_CHANNELS,
  globalSettings: {
    enableAlerting: true,
    defaultCooldown: 10, // 10 minutes
    maxAlertsPerHour: 50,
    quietHours: {
      start: '22:00', // 10 PM
      end: '08:00',   // 8 AM
    },
  },
}

// Configuration management functions
export function getAlertConfig(): AlertConfiguration {
  // In production, this would load from database or config service
  return DEFAULT_ALERT_CONFIG
}

export function updateAlertConfig(updates: Partial<AlertConfiguration>): AlertConfiguration {
  // In production, this would save to database or config service
  const config = { ...DEFAULT_ALERT_CONFIG, ...updates }
  
  // Regenerate rules if thresholds changed
  if (updates.thresholds) {
    config.rules = generateAlertRules(config.thresholds)
  }
  
  return config
}

export function getThreshold(metric: string): AlertThreshold | undefined {
  return DEFAULT_THRESHOLDS.find(t => t.metric === metric)
}

export function updateThreshold(metric: string, updates: Partial<AlertThreshold>): boolean {
  const threshold = DEFAULT_THRESHOLDS.find(t => t.metric === metric)
  if (threshold) {
    Object.assign(threshold, updates)
    return true
  }
  return false
}

export function addThreshold(threshold: AlertThreshold): void {
  DEFAULT_THRESHOLDS.push(threshold)
}

export function removeThreshold(metric: string): boolean {
  const index = DEFAULT_THRESHOLDS.findIndex(t => t.metric === metric)
  if (index !== -1) {
    DEFAULT_THRESHOLDS.splice(index, 1)
    return true
  }
  return false
}

// Validation functions
export function validateThreshold(threshold: AlertThreshold): string[] {
  const errors: string[] = []
  
  if (!threshold.metric) {
    errors.push('Metric name is required')
  }
  
  if (threshold.warning >= threshold.critical && !threshold.metric.includes('rate') && !threshold.metric.includes('uptime')) {
    errors.push('Warning threshold should be less than critical threshold')
  }
  
  if (threshold.warning <= 0 || threshold.critical <= 0) {
    errors.push('Thresholds must be positive numbers')
  }
  
  return errors
}

export function validateEscalationLevel(level: EscalationLevel): string[] {
  const errors: string[] = []
  
  if (level.level < 1) {
    errors.push('Escalation level must be at least 1')
  }
  
  if (level.delay < 0) {
    errors.push('Escalation delay cannot be negative')
  }
  
  if (level.channels.length === 0) {
    errors.push('At least one notification channel is required')
  }
  
  if (level.recipients.length === 0) {
    errors.push('At least one recipient is required')
  }
  
  return errors
}
