/**
 * Comprehensive alerting system for PawPumps
 * Monitors application health and sends alerts for critical issues
 */

import { logger, LogLevel } from './logger'
import { getCoreWebVitals, PerformanceMetric } from './performance'

export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum AlertType {
  PERFORMANCE = 'performance',
  ERROR = 'error',
  SECURITY = 'security',
  BUSINESS = 'business',
  SYSTEM = 'system',
}

export interface Alert {
  id: string
  type: AlertType
  severity: AlertSeverity
  title: string
  message: string
  timestamp: number
  metadata?: Record<string, any>
  resolved?: boolean
  resolvedAt?: number
}

export interface AlertRule {
  id: string
  name: string
  type: AlertType
  severity: AlertSeverity
  condition: (data: any) => boolean
  message: (data: any) => string
  cooldown: number // Minutes between alerts
  enabled: boolean
}

class AlertingSystem {
  private alerts: Alert[] = []
  private alertHistory: Alert[] = []
  private lastAlertTimes: Map<string, number> = new Map()
  private isEnabled = true

  // Default alert rules
  private rules: AlertRule[] = [
    // Performance alerts
    {
      id: 'lcp-poor',
      name: 'Poor Largest Contentful Paint',
      type: AlertType.PERFORMANCE,
      severity: AlertSeverity.HIGH,
      condition: (vitals) => vitals.LCP && vitals.LCP > 4000,
      message: (vitals) => `LCP is ${vitals.LCP}ms (threshold: 4000ms)`,
      cooldown: 5,
      enabled: true,
    },
    {
      id: 'fid-poor',
      name: 'Poor First Input Delay',
      type: AlertType.PERFORMANCE,
      severity: AlertSeverity.HIGH,
      condition: (vitals) => vitals.FID && vitals.FID > 300,
      message: (vitals) => `FID is ${vitals.FID}ms (threshold: 300ms)`,
      cooldown: 5,
      enabled: true,
    },
    {
      id: 'cls-poor',
      name: 'Poor Cumulative Layout Shift',
      type: AlertType.PERFORMANCE,
      severity: AlertSeverity.MEDIUM,
      condition: (vitals) => vitals.CLS && vitals.CLS > 0.25,
      message: (vitals) => `CLS is ${vitals.CLS} (threshold: 0.25)`,
      cooldown: 5,
      enabled: true,
    },
    
    // Error rate alerts
    {
      id: 'high-error-rate',
      name: 'High Error Rate',
      type: AlertType.ERROR,
      severity: AlertSeverity.CRITICAL,
      condition: (data) => data.errorRate && data.errorRate > 0.05, // 5%
      message: (data) => `Error rate is ${(data.errorRate * 100).toFixed(1)}% (threshold: 5%)`,
      cooldown: 2,
      enabled: true,
    },
    
    // API performance alerts
    {
      id: 'slow-api-response',
      name: 'Slow API Response',
      type: AlertType.PERFORMANCE,
      severity: AlertSeverity.MEDIUM,
      condition: (data) => data.apiResponseTime && data.apiResponseTime > 5000,
      message: (data) => `API response time is ${data.apiResponseTime}ms (threshold: 5000ms)`,
      cooldown: 3,
      enabled: true,
    },
    
    // Business logic alerts
    {
      id: 'wallet-connection-failure',
      name: 'Wallet Connection Failures',
      type: AlertType.BUSINESS,
      severity: AlertSeverity.HIGH,
      condition: (data) => data.walletFailureRate && data.walletFailureRate > 0.1, // 10%
      message: (data) => `Wallet connection failure rate is ${(data.walletFailureRate * 100).toFixed(1)}%`,
      cooldown: 5,
      enabled: true,
    },
    
    // Security alerts
    {
      id: 'suspicious-activity',
      name: 'Suspicious Activity Detected',
      type: AlertType.SECURITY,
      severity: AlertSeverity.CRITICAL,
      condition: (data) => data.suspiciousRequests && data.suspiciousRequests > 100,
      message: (data) => `${data.suspiciousRequests} suspicious requests detected in the last hour`,
      cooldown: 1,
      enabled: true,
    },
  ]

  constructor() {
    if (typeof window !== 'undefined') {
      this.startMonitoring()
    }
  }

  private startMonitoring(): void {
    // Monitor Core Web Vitals
    setInterval(() => {
      this.checkPerformanceAlerts()
    }, 30000) // Every 30 seconds

    // Monitor error rates
    setInterval(() => {
      this.checkErrorRates()
    }, 60000) // Every minute

    // Monitor business metrics
    setInterval(() => {
      this.checkBusinessMetrics()
    }, 120000) // Every 2 minutes

    // Clean up old alerts
    setInterval(() => {
      this.cleanupOldAlerts()
    }, 300000) // Every 5 minutes
  }

  private checkPerformanceAlerts(): void {
    const vitals = getCoreWebVitals()
    
    this.rules
      .filter(rule => rule.type === AlertType.PERFORMANCE && rule.enabled)
      .forEach(rule => {
        if (rule.condition(vitals)) {
          this.triggerAlert(rule, vitals)
        }
      })
  }

  private checkErrorRates(): void {
    // In a real implementation, this would fetch error metrics from your monitoring system
    const errorData = this.getErrorMetrics()
    
    this.rules
      .filter(rule => rule.type === AlertType.ERROR && rule.enabled)
      .forEach(rule => {
        if (rule.condition(errorData)) {
          this.triggerAlert(rule, errorData)
        }
      })
  }

  private checkBusinessMetrics(): void {
    // In a real implementation, this would fetch business metrics
    const businessData = this.getBusinessMetrics()
    
    this.rules
      .filter(rule => rule.type === AlertType.BUSINESS && rule.enabled)
      .forEach(rule => {
        if (rule.condition(businessData)) {
          this.triggerAlert(rule, businessData)
        }
      })
  }

  private getErrorMetrics(): any {
    // Mock error metrics - in production, this would come from your error tracking system
    return {
      errorRate: Math.random() * 0.1, // Random error rate for demo
      apiResponseTime: Math.random() * 10000, // Random response time
    }
  }

  private getBusinessMetrics(): any {
    // Mock business metrics - in production, this would come from your analytics
    return {
      walletFailureRate: Math.random() * 0.2, // Random failure rate for demo
      suspiciousRequests: Math.floor(Math.random() * 200), // Random suspicious activity
    }
  }

  private triggerAlert(rule: AlertRule, data: any): void {
    // Check cooldown
    const lastAlertTime = this.lastAlertTimes.get(rule.id) || 0
    const cooldownMs = rule.cooldown * 60 * 1000
    
    if (Date.now() - lastAlertTime < cooldownMs) {
      return // Still in cooldown period
    }

    const alert: Alert = {
      id: this.generateAlertId(),
      type: rule.type,
      severity: rule.severity,
      title: rule.name,
      message: rule.message(data),
      timestamp: Date.now(),
      metadata: {
        ruleId: rule.id,
        data,
      },
    }

    this.alerts.push(alert)
    this.alertHistory.push(alert)
    this.lastAlertTimes.set(rule.id, Date.now())

    // Log the alert
    logger.error(`ALERT: ${alert.title}`, undefined, {
      component: 'AlertingSystem',
      metadata: {
        alertId: alert.id,
        severity: alert.severity,
        type: alert.type,
        message: alert.message,
      },
    })

    // Send alert notifications
    this.sendAlertNotifications(alert)
  }

  private async sendAlertNotifications(alert: Alert): Promise<void> {
    try {
      // Send to monitoring API
      await fetch('/api/monitoring/alerts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(alert),
      })

      // In production, you would also send to:
      // - Slack: await slackClient.sendMessage(alert)
      // - Email: await emailService.sendAlert(alert)
      // - PagerDuty: await pagerDutyClient.triggerIncident(alert)
      // - Discord: await discordWebhook.send(alert)

      // Show browser notification for critical alerts
      if (alert.severity === AlertSeverity.CRITICAL && 'Notification' in window) {
        if (Notification.permission === 'granted') {
          new Notification(`PawPumps Alert: ${alert.title}`, {
            body: alert.message,
            icon: '/favicon.ico',
            tag: alert.id,
          })
        }
      }
    } catch (error) {
      logger.error('Failed to send alert notifications', error as Error, {
        component: 'AlertingSystem',
        metadata: { alertId: alert.id },
      })
    }
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private cleanupOldAlerts(): void {
    const oneHourAgo = Date.now() - (60 * 60 * 1000)
    
    // Remove resolved alerts older than 1 hour
    this.alerts = this.alerts.filter(alert => 
      !alert.resolved || alert.timestamp > oneHourAgo
    )

    // Keep only last 100 alerts in history
    if (this.alertHistory.length > 100) {
      this.alertHistory = this.alertHistory.slice(-100)
    }
  }

  // Public API
  public getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.resolved)
  }

  public getAllAlerts(): Alert[] {
    return [...this.alerts]
  }

  public getAlertHistory(): Alert[] {
    return [...this.alertHistory]
  }

  public resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert && !alert.resolved) {
      alert.resolved = true
      alert.resolvedAt = Date.now()
      
      logger.info(`Alert resolved: ${alert.title}`, {
        component: 'AlertingSystem',
        metadata: { alertId, resolvedAt: alert.resolvedAt },
      })
      
      return true
    }
    return false
  }

  public addCustomAlert(
    type: AlertType,
    severity: AlertSeverity,
    title: string,
    message: string,
    metadata?: Record<string, any>
  ): string {
    const alert: Alert = {
      id: this.generateAlertId(),
      type,
      severity,
      title,
      message,
      timestamp: Date.now(),
      metadata,
    }

    this.alerts.push(alert)
    this.alertHistory.push(alert)
    
    this.sendAlertNotifications(alert)
    
    return alert.id
  }

  public updateRule(ruleId: string, updates: Partial<AlertRule>): boolean {
    const rule = this.rules.find(r => r.id === ruleId)
    if (rule) {
      Object.assign(rule, updates)
      return true
    }
    return false
  }

  public addRule(rule: AlertRule): void {
    this.rules.push(rule)
  }

  public getRules(): AlertRule[] {
    return [...this.rules]
  }

  public enable(): void {
    this.isEnabled = true
  }

  public disable(): void {
    this.isEnabled = false
  }

  public isSystemEnabled(): boolean {
    return this.isEnabled
  }
}

// Export singleton instance
export const alertingSystem = new AlertingSystem()

// Convenience functions
export const getActiveAlerts = () => alertingSystem.getActiveAlerts()
export const resolveAlert = (alertId: string) => alertingSystem.resolveAlert(alertId)
export const addCustomAlert = (
  type: AlertType,
  severity: AlertSeverity,
  title: string,
  message: string,
  metadata?: Record<string, any>
) => alertingSystem.addCustomAlert(type, severity, title, message, metadata)

// Request browser notification permission
export const requestNotificationPermission = async (): Promise<boolean> => {
  if ('Notification' in window && Notification.permission === 'default') {
    const permission = await Notification.requestPermission()
    return permission === 'granted'
  }
  return Notification.permission === 'granted'
}
