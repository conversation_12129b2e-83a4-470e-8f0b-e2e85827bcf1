/**
 * Comprehensive logging utility for PawPumps
 * Supports multiple log levels and production error tracking
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4,
}

export interface LogContext {
  userId?: string
  sessionId?: string
  component?: string
  action?: string
  metadata?: Record<string, any>
  timestamp?: string
  userAgent?: string
  url?: string
}

export interface LogEntry {
  level: LogLevel
  message: string
  context?: LogContext
  error?: Error
  stack?: string
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development'
  private isProduction = process.env.NODE_ENV === 'production'
  private minLogLevel = this.isDevelopment ? LogLevel.DEBUG : LogLevel.INFO

  private formatMessage(entry: LogEntry): string {
    const timestamp = new Date().toISOString()
    const levelName = LogLevel[entry.level]
    const context = entry.context ? JSON.stringify(entry.context, null, 2) : ''
    
    return `[${timestamp}] ${levelName}: ${entry.message}${context ? '\nContext: ' + context : ''}`
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.minLogLevel
  }

  private getLogContext(): Partial<LogContext> {
    if (typeof window !== 'undefined') {
      return {
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
      }
    }
    return {
      timestamp: new Date().toISOString(),
    }
  }

  debug(message: string, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return

    const entry: LogEntry = {
      level: LogLevel.DEBUG,
      message,
      context: { ...this.getLogContext(), ...context },
    }

    if (this.isDevelopment) {
      console.debug(this.formatMessage(entry))
    }
  }

  info(message: string, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.INFO)) return

    const entry: LogEntry = {
      level: LogLevel.INFO,
      message,
      context: { ...this.getLogContext(), ...context },
    }

    console.info(this.formatMessage(entry))

    // Send to external logging service in production
    if (this.isProduction) {
      this.sendToExternalService(entry)
    }
  }

  warn(message: string, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.WARN)) return

    const entry: LogEntry = {
      level: LogLevel.WARN,
      message,
      context: { ...this.getLogContext(), ...context },
    }

    console.warn(this.formatMessage(entry))

    if (this.isProduction) {
      this.sendToExternalService(entry)
    }
  }

  error(message: string, error?: Error, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.ERROR)) return

    const entry: LogEntry = {
      level: LogLevel.ERROR,
      message,
      error,
      stack: error?.stack,
      context: { ...this.getLogContext(), ...context },
    }

    console.error(this.formatMessage(entry))

    if (this.isProduction) {
      this.sendToExternalService(entry)
      this.sendToErrorTracking(entry)
    }
  }

  fatal(message: string, error?: Error, context?: LogContext): void {
    const entry: LogEntry = {
      level: LogLevel.FATAL,
      message,
      error,
      stack: error?.stack,
      context: { ...this.getLogContext(), ...context },
    }

    console.error('FATAL:', this.formatMessage(entry))

    // Always send fatal errors regardless of environment
    this.sendToExternalService(entry)
    this.sendToErrorTracking(entry)
  }

  // Business metrics logging
  logUserAction(action: string, context?: LogContext): void {
    this.info(`User Action: ${action}`, {
      ...context,
      component: 'UserAction',
      action,
    })
  }

  logPerformanceMetric(metric: string, value: number, context?: LogContext): void {
    this.info(`Performance Metric: ${metric} = ${value}ms`, {
      ...context,
      component: 'Performance',
      action: 'metric',
      metadata: { metric, value },
    })
  }

  logAPICall(endpoint: string, method: string, duration: number, status: number, context?: LogContext): void {
    const level = status >= 400 ? LogLevel.ERROR : LogLevel.INFO
    const message = `API Call: ${method} ${endpoint} - ${status} (${duration}ms)`

    const entry: LogEntry = {
      level,
      message,
      context: {
        ...this.getLogContext(),
        ...context,
        component: 'API',
        action: 'call',
        metadata: { endpoint, method, duration, status },
      },
    }

    if (level === LogLevel.ERROR) {
      this.error(message, undefined, entry.context)
    } else {
      this.info(message, entry.context)
    }
  }

  private async sendToExternalService(entry: LogEntry): Promise<void> {
    try {
      // In production, this would send to a logging service like DataDog, LogRocket, etc.
      // For now, we'll use a simple endpoint
      if (typeof window !== 'undefined') {
        await fetch('/api/logging', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(entry),
        }).catch(() => {
          // Silently fail to avoid logging loops
        })
      }
    } catch (error) {
      // Silently fail to avoid logging loops
    }
  }

  private async sendToErrorTracking(entry: LogEntry): Promise<void> {
    try {
      // In production, this would integrate with Sentry, Bugsnag, etc.
      // For now, we'll use a simple error tracking endpoint
      if (typeof window !== 'undefined' && entry.error) {
        await fetch('/api/error-tracking', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: entry.message,
            stack: entry.stack,
            context: entry.context,
            timestamp: new Date().toISOString(),
          }),
        }).catch(() => {
          // Silently fail to avoid logging loops
        })
      }
    } catch (error) {
      // Silently fail to avoid logging loops
    }
  }
}

// Export singleton instance
export const logger = new Logger()

// Convenience functions
export const logUserAction = (action: string, context?: LogContext) => logger.logUserAction(action, context)
export const logPerformanceMetric = (metric: string, value: number, context?: LogContext) => logger.logPerformanceMetric(metric, value, context)
export const logAPICall = (endpoint: string, method: string, duration: number, status: number, context?: LogContext) => logger.logAPICall(endpoint, method, duration, status, context)

// Error tracking utilities
export const captureException = (error: Error, context?: LogContext) => {
  logger.error('Unhandled Exception', error, context)
}

export const captureMessage = (message: string, level: LogLevel = LogLevel.INFO, context?: LogContext) => {
  switch (level) {
    case LogLevel.DEBUG:
      logger.debug(message, context)
      break
    case LogLevel.INFO:
      logger.info(message, context)
      break
    case LogLevel.WARN:
      logger.warn(message, context)
      break
    case LogLevel.ERROR:
      logger.error(message, undefined, context)
      break
    case LogLevel.FATAL:
      logger.fatal(message, undefined, context)
      break
  }
}
