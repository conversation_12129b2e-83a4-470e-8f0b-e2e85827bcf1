# PawPumps Production Environment Configuration
# This file contains production-specific environment variables
# Copy to .env.production and update with actual production values

# =============================================================================
# PRODUCTION APPLICATION CONFIGURATION
# =============================================================================

# Application Configuration
NEXT_PUBLIC_APP_NAME=PawPumps
NEXT_PUBLIC_APP_DESCRIPTION="Decentralized Trading Platform"
NEXT_PUBLIC_APP_VERSION=1.0.0

# Production URLs
NEXT_PUBLIC_APP_URL=https://pawpumps.com
NEXT_PUBLIC_API_URL=https://pawpumps.com/api

# Environment
NODE_ENV=production
NEXT_PUBLIC_ENVIRONMENT=production

# =============================================================================
# PRODUCTION WEB3 CONFIGURATION
# =============================================================================

# Wallet Connect (Production Project)
NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID=production_wallet_connect_project_id

# Alchemy Production API
NEXT_PUBLIC_ALCHEMY_API_KEY=production_alchemy_api_key
NEXT_PUBLIC_ALCHEMY_NETWORK=eth-mainnet

# Production Network Configuration
NEXT_PUBLIC_CHAIN_ID=1
NEXT_PUBLIC_NETWORK_NAME=mainnet
NEXT_PUBLIC_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/production_api_key

# Production Contract Addresses
NEXT_PUBLIC_TOKEN_CONTRACT_ADDRESS=0x_production_token_contract_address
NEXT_PUBLIC_GOVERNANCE_CONTRACT_ADDRESS=0x_production_governance_contract_address
NEXT_PUBLIC_TRADING_CONTRACT_ADDRESS=0x_production_trading_contract_address

# =============================================================================
# PRODUCTION DATABASE CONFIGURATION
# =============================================================================

# Production PostgreSQL
DATABASE_URL=********************************************************/pawpumps_prod
DATABASE_DIRECT_URL=********************************************************/pawpumps_prod

# Production Redis
REDIS_URL=redis://prod-redis-host:6379

# Database Pool Configuration
DATABASE_POOL_MIN=5
DATABASE_POOL_MAX=20
DATABASE_TIMEOUT=30000

# =============================================================================
# PRODUCTION SECURITY CONFIGURATION
# =============================================================================

# NextAuth.js Production
NEXTAUTH_URL=https://pawpumps.com
NEXTAUTH_SECRET=production_nextauth_secret_minimum_64_characters_long

# JWT Production Secret
JWT_SECRET=production_jwt_secret_minimum_64_characters_long

# Admin Production Secret
ADMIN_SECRET_KEY=production_admin_secret_key_minimum_64_characters

# Session Configuration
SESSION_TIMEOUT=3600000
SESSION_SECURE=true
SESSION_SAME_SITE=strict

# =============================================================================
# PRODUCTION EXTERNAL SERVICES
# =============================================================================

# Production Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS=G-PRODUCTION_GA_ID
NEXT_PUBLIC_MIXPANEL_TOKEN=production_mixpanel_token

# Production Error Tracking
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project_id
SENTRY_AUTH_TOKEN=production_sentry_auth_token
SENTRY_ORG=your_sentry_org
SENTRY_PROJECT=pawpumps

# Production Email Service
EMAIL_FROM=<EMAIL>
SENDGRID_API_KEY=production_sendgrid_api_key
EMAIL_RATE_LIMIT=100

# Production SMS Service
TWILIO_ACCOUNT_SID=production_twilio_account_sid
TWILIO_AUTH_TOKEN=production_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# =============================================================================
# PRODUCTION API INTEGRATIONS
# =============================================================================

# Production CoinGecko API
COINGECKO_API_KEY=production_coingecko_api_key
COINGECKO_RATE_LIMIT=50

# Production CoinMarketCap API
COINMARKETCAP_API_KEY=production_coinmarketcap_api_key

# Production The Graph
NEXT_PUBLIC_GRAPH_API_URL=https://api.thegraph.com/subgraphs/name/pawpumps/production

# Production IPFS
NEXT_PUBLIC_IPFS_GATEWAY=https://pawpumps.mypinata.cloud/ipfs/
PINATA_API_KEY=production_pinata_api_key
PINATA_SECRET_API_KEY=production_pinata_secret_api_key

# =============================================================================
# PRODUCTION FEATURE FLAGS
# =============================================================================

# Production Features (All Enabled)
NEXT_PUBLIC_ENABLE_GOVERNANCE=true
NEXT_PUBLIC_ENABLE_TRADING=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_SOCIAL=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true

# Production Beta Features (Disabled)
NEXT_PUBLIC_ENABLE_BETA_FEATURES=false
NEXT_PUBLIC_ENABLE_EXPERIMENTAL_FEATURES=false
NEXT_PUBLIC_ENABLE_DEVTOOLS=false
NEXT_PUBLIC_ENABLE_MOCK_DATA=false
NEXT_PUBLIC_ENABLE_DEBUG_LOGS=false

# =============================================================================
# PRODUCTION DEPLOYMENT CONFIGURATION
# =============================================================================

# Vercel Production
VERCEL_URL=pawpumps.com
VERCEL_ENV=production

# Production CDN
NEXT_PUBLIC_CDN_URL=https://cdn.pawpumps.com
NEXT_PUBLIC_ASSETS_URL=https://assets.pawpumps.com

# SSL Configuration
FORCE_HTTPS=true
HSTS_MAX_AGE=31536000

# =============================================================================
# PRODUCTION MONITORING & LOGGING
# =============================================================================

# Production Monitoring
NEXT_PUBLIC_ENABLE_MONITORING=true
DATADOG_API_KEY=production_datadog_api_key
NEW_RELIC_LICENSE_KEY=production_new_relic_license_key

# Production Logging
LOG_LEVEL=warn
ENABLE_REQUEST_LOGGING=false
LOG_RETENTION_DAYS=30

# Health Check
HEALTH_CHECK_ENDPOINT=/api/health
HEALTH_CHECK_SECRET=production_health_check_secret

# =============================================================================
# PRODUCTION PERFORMANCE CONFIGURATION
# =============================================================================

# Performance Monitoring
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true
NEXT_PUBLIC_PERFORMANCE_BUDGET_JS=150000
NEXT_PUBLIC_PERFORMANCE_BUDGET_CSS=30000

# Cache Configuration
CACHE_TTL=3600
STATIC_CACHE_TTL=86400
API_CACHE_TTL=300

# =============================================================================
# PRODUCTION RATE LIMITING & SECURITY
# =============================================================================

# Production Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# Production CORS
CORS_ORIGIN=https://pawpumps.com
CORS_CREDENTIALS=true

# Security Headers
ENABLE_SECURITY_HEADERS=true
CSP_REPORT_URI=https://pawpumps.report-uri.com/r/d/csp/enforce

# Content Security Policy
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self' 'unsafe-inline' 'unsafe-eval' https://cdn.pawpumps.com
CSP_STYLE_SRC='self' 'unsafe-inline' https://fonts.googleapis.com
CSP_IMG_SRC='self' data: https: blob:
CSP_CONNECT_SRC='self' https://api.pawpumps.com wss://api.pawpumps.com

# =============================================================================
# PRODUCTION BACKUP & RECOVERY
# =============================================================================

# Database Backup
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=90
BACKUP_STORAGE_URL=s3://pawpumps-production-backups
BACKUP_ENCRYPTION_KEY=production_backup_encryption_key

# Disaster Recovery
DR_REGION=us-west-2
DR_DATABASE_URL=****************************************************/pawpumps_dr

# =============================================================================
# PRODUCTION SOCIAL MEDIA & MARKETING
# =============================================================================

# Official Social Media
NEXT_PUBLIC_TWITTER_URL=https://twitter.com/pawpumps
NEXT_PUBLIC_DISCORD_URL=https://discord.gg/pawpumps
NEXT_PUBLIC_TELEGRAM_URL=https://t.me/pawpumps
NEXT_PUBLIC_GITHUB_URL=https://github.com/pawpumps

# Production Marketing Tools
NEXT_PUBLIC_HOTJAR_ID=production_hotjar_id
NEXT_PUBLIC_FACEBOOK_PIXEL_ID=production_facebook_pixel_id

# =============================================================================
# PRODUCTION COMPLIANCE & LEGAL
# =============================================================================

# Legal Pages
NEXT_PUBLIC_TERMS_URL=https://pawpumps.com/terms
NEXT_PUBLIC_PRIVACY_URL=https://pawpumps.com/privacy
NEXT_PUBLIC_DISCLAIMER_URL=https://pawpumps.com/disclaimer

# Compliance
GDPR_COMPLIANCE=true
CCPA_COMPLIANCE=true
DATA_RETENTION_DAYS=2555

# =============================================================================
# PRODUCTION SCALING CONFIGURATION
# =============================================================================

# Auto-scaling
MIN_INSTANCES=2
MAX_INSTANCES=10
TARGET_CPU_UTILIZATION=70

# Load Balancing
LOAD_BALANCER_TIMEOUT=30000
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# PRODUCTION SECRETS MANAGEMENT
# =============================================================================

# Secrets Provider (AWS Secrets Manager, HashiCorp Vault, etc.)
SECRETS_PROVIDER=aws-secrets-manager
SECRETS_REGION=us-east-1
SECRETS_PREFIX=pawpumps/production/

# =============================================================================
# PRODUCTION DEPLOYMENT NOTES
# =============================================================================

# IMPORTANT PRODUCTION DEPLOYMENT CHECKLIST:
# 
# 1. ✅ Update all API keys to production values
# 2. ✅ Use strong, unique secrets (minimum 64 characters)
# 3. ✅ Enable HTTPS and security headers
# 4. ✅ Configure proper CORS origins
# 5. ✅ Set up monitoring and alerting
# 6. ✅ Configure database backups
# 7. ✅ Test disaster recovery procedures
# 8. ✅ Enable rate limiting
# 9. ✅ Configure CDN and caching
# 10. ✅ Set up log aggregation
# 11. ✅ Configure auto-scaling
# 12. ✅ Test all integrations
# 13. ✅ Verify SSL certificates
# 14. ✅ Set up health checks
# 15. ✅ Configure secrets management
