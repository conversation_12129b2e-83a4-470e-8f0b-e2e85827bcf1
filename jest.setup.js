// Set up polyfills FIRST before any other imports
const { TextEncoder, TextDecoder } = require('util')

// Set up global polyfills for Node.js environment
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Add Web Streams API polyfills for MSW compatibility
try {
  const { TransformStream, ReadableStream, WritableStream } = require('stream/web')
  global.TransformStream = TransformStream
  global.ReadableStream = ReadableStream
  global.WritableStream = WritableStream
} catch (error) {
  // Fallback for older Node.js versions
  console.warn('Web Streams API not available, using polyfills')
}

// Add additional polyfills for MSW
if (typeof global.structuredClone === 'undefined') {
  global.structuredClone = (obj) => JSON.parse(JSON.stringify(obj))
}

// Add BroadcastChannel polyfill for MSW
if (typeof global.BroadcastChannel === 'undefined') {
  global.BroadcastChannel = class BroadcastChannel {
    constructor(name) {
      this.name = name
    }
    postMessage() {}
    close() {}
    addEventListener() {}
    removeEventListener() {}
  }
}

// Now import other modules
import '@testing-library/jest-dom'
import 'whatwg-fetch'

// Add Response and Request polyfills
if (!global.Response) {
  global.Response = Response
}
if (!global.Request) {
  global.Request = Request
}

// Polyfill for TextEncoder/TextDecoder
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Suppress JSDOM navigation warnings
const originalConsoleError = console.error
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Not implemented: navigation') ||
       args[0].includes('Error: Not implemented: navigation'))
    ) {
      return // Suppress JSDOM navigation warnings
    }
    originalConsoleError.call(console, ...args)
  }
})

// Mock navigator clipboard if not already defined
if (!navigator.clipboard) {
  Object.defineProperty(navigator, 'clipboard', {
    value: {
      writeText: jest.fn().mockResolvedValue(undefined),
      readText: jest.fn().mockResolvedValue(''),
    },
    writable: true,
    configurable: true,
  })
}

// Mock crypto
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: () => 'test-uuid-' + Math.random().toString(36).substring(2, 11),
    getRandomValues: (arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    },
  },
})

// Mock fetch
global.fetch = jest.fn()

// Mock console methods to reduce noise in tests
const originalError = console.error
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return
    }
    originalError.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalError
})

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks()
})

// Mock next/router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn().mockResolvedValue(undefined),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
      isFallback: false,
    }
  },
}))

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// Mock Recharts
jest.mock('recharts', () => ({
  ResponsiveContainer: ({ children }) => children,
  LineChart: ({ children }) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  AreaChart: ({ children }) => <div data-testid="area-chart">{children}</div>,
  Area: () => <div data-testid="area" />,
  BarChart: ({ children }) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
}))

// Mock Lucide React icons
jest.mock('lucide-react', () => {
  const icons = [
    'AlertTriangle', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowUp',
    'BarChart2', 'Bell', 'CheckCircle', 'ChevronDown', 'ChevronUp',
    'Copy', 'Download', 'Edit', 'ExternalLink', 'Eye', 'EyeOff',
    'Heart', 'Home', 'Info', 'Loader2', 'Menu', 'RefreshCw',
    'Search', 'Settings', 'Share', 'Star', 'Trash2', 'TrendingDown',
    'TrendingUp', 'User', 'Wallet', 'X', 'Zap'
  ]
  
  const mockIcons = {}
  icons.forEach(icon => {
    mockIcons[icon] = ({ className, ...props }) => 
      <svg className={className} data-testid={`icon-${icon.toLowerCase()}`} {...props} />
  })
  
  return mockIcons
})

// Setup MSW server for API mocking - moved to synchronous setup
// MSW server will be set up in individual test files that need it

// Mock dynamic imports for testing
jest.mock('@/components/enhanced-trading-chart', () => ({
  EnhancedTradingChart: ({ token = 'wDOGE', height = 400 }) => (
    <div data-testid="enhanced-trading-chart" data-token={token} data-height={height}>
      Enhanced Trading Chart Mock
    </div>
  ),
}))

jest.mock('@/components/trading-chart', () => ({
  TradingChart: ({ height = 300, timeframe = '1D' }) => (
    <div data-testid="trading-chart" data-height={height} data-timeframe={timeframe}>
      Trading Chart Mock
    </div>
  ),
}))

jest.mock('@/components/token-list', () => ({
  TokenList: () => (
    <div data-testid="token-list">
      Token List Mock
    </div>
  ),
}))

jest.mock('@/components/user-feedback/feedback-button', () => ({
  FeedbackButton: () => (
    <button data-testid="feedback-button">
      Provide Feedback
    </button>
  ),
}))
