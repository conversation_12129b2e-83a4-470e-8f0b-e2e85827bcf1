react-dom-client.development.js:24915 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
development-optimizer.tsx:10 Development optimizer active - reducing overhead
Refused to connect to '<URL>' because it violates the following Content Security Policy directive: "connect-src 'self' https:".

Refused to connect to '<URL>' because it violates the following Content Security Policy directive: "connect-src 'self' https:".

Refused to connect to '<URL>' because it violates the following Content Security Policy directive: "connect-src 'self' https:".

Refused to connect to '<URL>' because it violates the following Content Security Policy directive: "connect-src 'self' https:".

Refused to connect to '<URL>' because it violates the following Content Security Policy directive: "connect-src 'self' https:".

Refused to connect to '<URL>' because it violates the following Content Security Policy directive: "connect-src 'self' https:".

Refused to connect to '<URL>' because it violates the following Content Security Policy directive: "connect-src 'self' https:".

Refused to connect to '<URL>' because it violates the following Content Security Policy directive: "connect-src 'self' https:".

Refused to connect to '<URL>' because it violates the following Content Security Policy directive: "connect-src 'self' https:".

Refused to connect to '<URL>' because it violates the following Content Security Policy directive: "connect-src 'self' https:".

Refused to connect to '<URL>' because it violates the following Content Security Policy directive: "connect-src 'self' https:".

stagewise-client.tsx:16 Fetch API cannot load http://localhost:5746/ping/stagewise. Refused to connect because it violates the document's Content Security Policy.
StagewiseClient.useEffect.initStagewise.testConnection @ stagewise-client.tsx:16
stagewise-client.tsx:16 Fetch API cannot load http://localhost:5747/ping/stagewise. Refused to connect because it violates the document's Content Security Policy.
StagewiseClient.useEffect.initStagewise.testConnection @ stagewise-client.tsx:16
stagewise-client.tsx:16 Fetch API cannot load http://localhost:5748/ping/stagewise. Refused to connect because it violates the document's Content Security Policy.
StagewiseClient.useEffect.initStagewise.testConnection @ stagewise-client.tsx:16
stagewise-client.tsx:16 Fetch API cannot load http://localhost:5749/ping/stagewise. Refused to connect because it violates the document's Content Security Policy.
StagewiseClient.useEffect.initStagewise.testConnection @ stagewise-client.tsx:16
stagewise-client.tsx:16 Fetch API cannot load http://localhost:5750/ping/stagewise. Refused to connect because it violates the document's Content Security Policy.
StagewiseClient.useEffect.initStagewise.testConnection @ stagewise-client.tsx:16
stagewise-client.tsx:16 Fetch API cannot load http://localhost:5751/ping/stagewise. Refused to connect because it violates the document's Content Security Policy.
StagewiseClient.useEffect.initStagewise.testConnection @ stagewise-client.tsx:16
stagewise-client.tsx:16 Fetch API cannot load http://localhost:5752/ping/stagewise. Refused to connect because it violates the document's Content Security Policy.
StagewiseClient.useEffect.initStagewise.testConnection @ stagewise-client.tsx:16
stagewise-client.tsx:16 Fetch API cannot load http://localhost:5753/ping/stagewise. Refused to connect because it violates the document's Content Security Policy.
StagewiseClient.useEffect.initStagewise.testConnection @ stagewise-client.tsx:16
stagewise-client.tsx:16 Fetch API cannot load http://localhost:5754/ping/stagewise. Refused to connect because it violates the document's Content Security Policy.
StagewiseClient.useEffect.initStagewise.testConnection @ stagewise-client.tsx:16
stagewise-client.tsx:16 Fetch API cannot load http://localhost:5755/ping/stagewise. Refused to connect because it violates the document's Content Security Policy.
StagewiseClient.useEffect.initStagewise.testConnection @ stagewise-client.tsx:16
stagewise-client.tsx:16 Fetch API cannot load http://localhost:5756/ping/stagewise. Refused to connect because it violates the document's Content Security Policy.
StagewiseClient.useEffect.initStagewise.testConnection @ stagewise-client.tsx:16
intercept-console-error.js:50 ❌ Failed to initialize Stagewise toolbar: Error: VS Code extension not found on ports 5746-5756
    at StagewiseClient.useEffect.initStagewise.testConnection (stagewise-client.tsx:31:17)
    at async StagewiseClient.useEffect.initStagewise (stagewise-client.tsx:35:31)
error @ intercept-console-error.js:50
development-optimizer.tsx:171 🛠️ Development Performance Tips:
