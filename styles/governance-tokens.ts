// Governance-specific design tokens
export const governanceTokens = {
  // Color palette
  colors: {
    proposal: {
      active: {
        text: "text-dogechain",
        bg: "bg-dogechain/10",
        border: "border-dogechain/20",
      },
      passed: {
        text: "text-green-500",
        bg: "bg-green-500/10",
        border: "border-green-500/20",
      },
      failed: {
        text: "text-red-500",
        bg: "bg-red-500/10",
        border: "border-red-500/20",
      },
      pending: {
        text: "text-white/70",
        bg: "bg-white/5",
        border: "border-white/10",
      },
      implemented: {
        text: "text-purple-500",
        bg: "bg-purple-500/10",
        border: "border-purple-500/20",
      },
    },
    task: {
      completed: {
        text: "text-green-500",
        bg: "bg-green-500/10",
        border: "border-green-500/20",
      },
      "in-progress": {
        text: "text-blue-500",
        bg: "bg-blue-500/10",
        border: "border-blue-500/20",
      },
      planned: {
        text: "text-white/70",
        bg: "bg-white/5",
        border: "border-white/10",
      },
      blocked: {
        text: "text-red-500",
        bg: "bg-red-500/10",
        border: "border-red-500/20",
      },
    },
    priority: {
      critical: {
        text: "text-red-500",
        bg: "bg-red-500/10",
        border: "border-red-500/20",
      },
      high: {
        text: "text-orange-500",
        bg: "bg-orange-500/10",
        border: "border-orange-500/20",
      },
      medium: {
        text: "text-yellow-500",
        bg: "bg-yellow-500/10",
        border: "border-yellow-500/20",
      },
      low: {
        text: "text-blue-400",
        bg: "bg-blue-400/10",
        border: "border-blue-400/20",
      },
    },
    category: {
      "core-functionality": {
        text: "text-purple-500",
        bg: "bg-purple-500/10",
        border: "border-purple-500/20",
      },
      "user-experience": {
        text: "text-blue-500",
        bg: "bg-blue-500/10",
        border: "border-blue-500/20",
      },
      "visual-design": {
        text: "text-pink-500",
        bg: "bg-pink-500/10",
        border: "border-pink-500/20",
      },
      mobile: {
        text: "text-green-500",
        bg: "bg-green-500/10",
        border: "border-green-500/20",
      },
      performance: {
        text: "text-yellow-500",
        bg: "bg-yellow-500/10",
        border: "border-yellow-500/20",
      },
      accessibility: {
        text: "text-teal-500",
        bg: "bg-teal-500/10",
        border: "border-teal-500/20",
      },
      content: {
        text: "text-indigo-500",
        bg: "bg-indigo-500/10",
        border: "border-indigo-500/20",
      },
      technical: {
        text: "text-gray-400",
        bg: "bg-gray-400/10",
        border: "border-gray-400/20",
      },
      security: {
        text: "text-red-500",
        bg: "bg-red-500/10",
        border: "border-red-500/20",
      },
      social: {
        text: "text-cyan-500",
        bg: "bg-cyan-500/10",
        border: "border-cyan-500/20",
      },
    },
  },

  // Spacing
  spacing: {
    card: {
      padding: "p-4 sm:p-5",
      gap: "gap-4 sm:gap-5",
      margin: "mb-4 sm:mb-5",
    },
    section: {
      padding: "py-4 sm:py-6",
      gap: "gap-6 sm:gap-8",
      margin: "mb-6 sm:mb-8",
    },
    modal: {
      padding: "p-4 sm:p-6",
      gap: "gap-4 sm:gap-6",
    },
  },

  // Typography
  typography: {
    title: {
      large: "text-2xl sm:text-3xl font-bold tracking-tight",
      medium: "text-xl sm:text-2xl font-bold",
      small: "text-lg sm:text-xl font-semibold",
    },
    body: {
      large: "text-base sm:text-lg",
      medium: "text-sm sm:text-base",
      small: "text-xs sm:text-sm",
    },
    metadata: "text-xs text-white/60",
    status: "text-xs font-medium",
  },

  // Animation
  animation: {
    transition: "transition-all duration-300 ease-in-out",
    hover: "hover:scale-[1.02] hover:shadow-lg",
    active: "active:scale-[0.98]",
  },

  // Shadows
  shadows: {
    card: "shadow-md hover:shadow-lg",
    modal: "shadow-xl",
    button: "shadow-md hover:shadow-lg",
  },

  // Border radius
  borderRadius: {
    card: "rounded-lg",
    button: "rounded-md",
    badge: "rounded-full",
    input: "rounded-md",
  },
}
