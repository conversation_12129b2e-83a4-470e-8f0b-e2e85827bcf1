#!/usr/bin/env node

// Development Performance Monitor
// Helps identify and resolve slow development builds

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🔍 PawPumps Development Performance Analyzer\n')

// Check Next.js cache size
function checkCacheSize() {
  const nextDir = path.join(process.cwd(), '.next')
  if (fs.existsSync(nextDir)) {
    try {
      const output = execSync(`du -sh ${nextDir}`, { encoding: 'utf8' })
      const size = output.trim().split('\t')[0]
      console.log(`📦 Next.js cache size: ${size}`)
      
      if (size.includes('G') || (size.includes('M') && parseInt(size) > 500)) {
        console.log('⚠️  Large cache detected. Consider clearing with: rm -rf .next')
      }
    } catch (error) {
      console.log('📦 Next.js cache: Unable to check size')
    }
  } else {
    console.log('📦 Next.js cache: Not found (first build)')
  }
}

// Check node_modules size
function checkNodeModulesSize() {
  const nodeModulesDir = path.join(process.cwd(), 'node_modules')
  if (fs.existsSync(nodeModulesDir)) {
    try {
      const output = execSync(`du -sh ${nodeModulesDir}`, { encoding: 'utf8' })
      const size = output.trim().split('\t')[0]
      console.log(`📚 node_modules size: ${size}`)
    } catch (error) {
      console.log('📚 node_modules: Unable to check size')
    }
  }
}

// Check for common performance issues
function checkPerformanceIssues() {
  console.log('\n🔍 Checking for common performance issues...\n')
  
  const issues = []
  
  // Check for large components
  const componentsDir = path.join(process.cwd(), 'components')
  if (fs.existsSync(componentsDir)) {
    const checkLargeFiles = (dir) => {
      const files = fs.readdirSync(dir)
      files.forEach(file => {
        const filePath = path.join(dir, file)
        const stat = fs.statSync(filePath)
        
        if (stat.isDirectory()) {
          checkLargeFiles(filePath)
        } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          if (stat.size > 50000) { // 50KB
            const relativePath = path.relative(process.cwd(), filePath)
            issues.push(`Large component: ${relativePath} (${Math.round(stat.size / 1024)}KB)`)
          }
        }
      })
    }
    
    checkLargeFiles(componentsDir)
  }
  
  // Check for potential import issues
  const packageJson = path.join(process.cwd(), 'package.json')
  if (fs.existsSync(packageJson)) {
    const pkg = JSON.parse(fs.readFileSync(packageJson, 'utf8'))
    const deps = { ...pkg.dependencies, ...pkg.devDependencies }
    
    // Check for heavy dependencies
    const heavyDeps = [
      'lodash', 'moment', 'rxjs', 'three', 'babylonjs', 
      'chart.js', 'plotly.js', 'ag-grid', 'antd'
    ]
    
    heavyDeps.forEach(dep => {
      if (deps[dep]) {
        issues.push(`Heavy dependency detected: ${dep} - consider tree-shaking or alternatives`)
      }
    })
  }
  
  if (issues.length > 0) {
    console.log('⚠️  Potential performance issues found:')
    issues.forEach(issue => console.log(`   • ${issue}`))
  } else {
    console.log('✅ No obvious performance issues detected')
  }
}

// Provide optimization suggestions
function showOptimizationSuggestions() {
  console.log('\n💡 Development Performance Tips:\n')
  
  const tips = [
    'Use dynamic imports for heavy components: const Heavy = dynamic(() => import("./Heavy"))',
    'Implement React.memo for components that re-render frequently',
    'Use useMemo and useCallback for expensive computations',
    'Consider lazy loading for routes: const Page = lazy(() => import("./Page"))',
    'Optimize images with next/image component',
    'Use Next.js built-in bundle analyzer: npm run build && npm run analyze',
    'Clear Next.js cache if builds are slow: rm -rf .next',
    'Use React DevTools Profiler to identify slow renders',
    'Consider using SWC instead of Babel for faster builds',
    'Reduce the number of dynamic imports in development'
  ]
  
  tips.forEach((tip, index) => {
    console.log(`${index + 1}. ${tip}`)
  })
}

// Check system resources
function checkSystemResources() {
  console.log('\n💻 System Resources:\n')
  
  try {
    // Check available memory
    const os = require('os')
    const totalMem = Math.round(os.totalmem() / 1024 / 1024 / 1024)
    const freeMem = Math.round(os.freemem() / 1024 / 1024 / 1024)
    const usedMem = totalMem - freeMem
    
    console.log(`Memory: ${usedMem}GB used / ${totalMem}GB total (${freeMem}GB free)`)
    
    if (freeMem < 2) {
      console.log('⚠️  Low memory detected. Consider closing other applications.')
    }
    
    // Check CPU info
    const cpus = os.cpus()
    console.log(`CPU: ${cpus[0].model} (${cpus.length} cores)`)
    
    // Check Node.js version
    console.log(`Node.js: ${process.version}`)
    
  } catch (error) {
    console.log('Unable to check system resources')
  }
}

// Main execution
async function main() {
  checkCacheSize()
  checkNodeModulesSize()
  checkSystemResources()
  checkPerformanceIssues()
  showOptimizationSuggestions()
  
  console.log('\n🚀 For immediate performance improvement:')
  console.log('   • Clear cache: rm -rf .next')
  console.log('   • Restart dev server: npm run dev')
  console.log('   • Use production build for testing: npm run build && npm run start')
  console.log('\n📊 Monitor performance: Open browser DevTools > Performance tab')
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { main }
