const puppeteer = require('puppeteer');
const fs = require('fs');

async function runPerformanceAudit() {
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Enable performance monitoring
  await page.setCacheEnabled(false);
  
  const performanceMetrics = {};
  const pages = [
    { name: 'Home', url: 'http://localhost:3003' },
    { name: 'Analytics', url: 'http://localhost:3003/analytics' },
    { name: 'Governance', url: 'http://localhost:3003/governance' },
    { name: 'Admin Governance', url: 'http://localhost:3003/admin/governance' },
    { name: 'Trade', url: 'http://localhost:3003/trade' }
  ];
  
  for (const pageInfo of pages) {
    console.log(`Testing ${pageInfo.name}...`);
    
    try {
      // Navigate to page
      const startTime = Date.now();
      await page.goto(pageInfo.url, { waitUntil: 'networkidle0', timeout: 30000 });
      const loadTime = Date.now() - startTime;
      
      // Get performance metrics
      const metrics = await page.metrics();
      
      // Get Core Web Vitals
      const webVitals = await page.evaluate(() => {
        return new Promise((resolve) => {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const vitals = {};
            
            entries.forEach((entry) => {
              if (entry.entryType === 'largest-contentful-paint') {
                vitals.LCP = entry.startTime;
              }
              if (entry.entryType === 'first-input') {
                vitals.FID = entry.processingStart - entry.startTime;
              }
              if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
                vitals.CLS = (vitals.CLS || 0) + entry.value;
              }
            });
            
            resolve(vitals);
          });
          
          observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
          
          // Fallback timeout
          setTimeout(() => resolve({}), 5000);
        });
      });
      
      // Get bundle size information
      const resourceSizes = await page.evaluate(() => {
        const resources = performance.getEntriesByType('resource');
        let totalSize = 0;
        let jsSize = 0;
        let cssSize = 0;
        
        resources.forEach(resource => {
          if (resource.transferSize) {
            totalSize += resource.transferSize;
            if (resource.name.includes('.js')) {
              jsSize += resource.transferSize;
            }
            if (resource.name.includes('.css')) {
              cssSize += resource.transferSize;
            }
          }
        });
        
        return { totalSize, jsSize, cssSize };
      });
      
      performanceMetrics[pageInfo.name] = {
        url: pageInfo.url,
        loadTime,
        metrics,
        webVitals,
        resourceSizes,
        timestamp: new Date().toISOString()
      };
      
      console.log(`✓ ${pageInfo.name} completed in ${loadTime}ms`);
      
    } catch (error) {
      console.error(`✗ ${pageInfo.name} failed:`, error.message);
      performanceMetrics[pageInfo.name] = {
        url: pageInfo.url,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  await browser.close();
  
  // Save results
  fs.writeFileSync('./performance-audit-results.json', JSON.stringify(performanceMetrics, null, 2));
  
  // Generate summary
  console.log('\n=== Performance Audit Summary ===');
  Object.entries(performanceMetrics).forEach(([name, data]) => {
    if (data.error) {
      console.log(`${name}: ERROR - ${data.error}`);
    } else {
      console.log(`${name}:`);
      console.log(`  Load Time: ${data.loadTime}ms`);
      console.log(`  Total Resources: ${(data.resourceSizes.totalSize / 1024).toFixed(2)} KB`);
      console.log(`  JavaScript: ${(data.resourceSizes.jsSize / 1024).toFixed(2)} KB`);
      console.log(`  CSS: ${(data.resourceSizes.cssSize / 1024).toFixed(2)} KB`);
      if (data.webVitals.LCP) console.log(`  LCP: ${data.webVitals.LCP.toFixed(2)}ms`);
      if (data.webVitals.CLS) console.log(`  CLS: ${data.webVitals.CLS.toFixed(4)}`);
    }
    console.log('');
  });
  
  return performanceMetrics;
}

if (require.main === module) {
  runPerformanceAudit().catch(console.error);
}

module.exports = { runPerformanceAudit };
