const { chromium, firefox, webkit } = require('playwright');
const fs = require('fs');

async function runCrossBrowserTest() {
  const browsers = [
    { name: 'Chromium', launcher: chromium },
    { name: 'Firefox', launcher: firefox },
    { name: 'WebKit', launcher: webkit }
  ];
  
  const testResults = {};
  
  const pages = [
    { name: 'Home', url: 'http://localhost:3004', timeout: 15000 },
    { name: 'Governance', url: 'http://localhost:3004/governance', timeout: 15000 },
    { name: 'Trade', url: 'http://localhost:3004/trade', timeout: 15000 },
    { name: 'Documentation', url: 'http://localhost:3004/documentation', timeout: 15000 }
  ];
  
  for (const browserInfo of browsers) {
    console.log(`\n=== Testing ${browserInfo.name} ===`);
    testResults[browserInfo.name] = {};
    
    let browser;
    try {
      browser = await browserInfo.launcher.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      const context = await browser.newContext({
        viewport: { width: 1920, height: 1080 },
        userAgent: `PawPumps-Test-${browserInfo.name}`
      });
      
      const page = await context.newPage();
      
      for (const pageInfo of pages) {
        console.log(`  Testing ${pageInfo.name}...`);
        
        try {
          const startTime = Date.now();
          
          // Navigate to page
          await page.goto(pageInfo.url, { 
            waitUntil: 'domcontentloaded', 
            timeout: pageInfo.timeout 
          });
          
          const loadTime = Date.now() - startTime;
          
          // Wait for page to stabilize
          await page.waitForTimeout(2000);
          
          // Basic functionality tests
          const basicTests = await page.evaluate(() => {
            const results = {
              hasTitle: !!document.title && document.title.trim() !== '',
              hasContent: document.body.children.length > 0,
              hasNavigation: !!document.querySelector('nav, [role="navigation"]'),
              hasMainContent: !!document.querySelector('main, [role="main"]'),
              hasFooter: !!document.querySelector('footer, [role="contentinfo"]'),
              scriptsLoaded: typeof window.React !== 'undefined' || document.querySelectorAll('script').length > 0,
              stylesLoaded: document.querySelectorAll('style, link[rel="stylesheet"]').length > 0,
              imagesCount: document.querySelectorAll('img').length,
              buttonsCount: document.querySelectorAll('button').length,
              linksCount: document.querySelectorAll('a').length,
              formsCount: document.querySelectorAll('form').length,
              hasErrors: !!document.querySelector('.error, [role="alert"]'),
              viewport: {
                width: window.innerWidth,
                height: window.innerHeight
              }
            };
            
            // Check for console errors (basic)
            results.consoleErrors = window.console && window.console.error ? 0 : 'unknown';
            
            return results;
          });
          
          // Test interactive elements
          const interactionTests = await page.evaluate(() => {
            const results = {
              clickableElements: 0,
              focusableElements: 0,
              interactionErrors: []
            };
            
            try {
              // Count clickable elements
              const clickable = document.querySelectorAll('button, a, [onclick], [role="button"]');
              results.clickableElements = clickable.length;
              
              // Count focusable elements
              const focusable = document.querySelectorAll(
                'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
              );
              results.focusableElements = focusable.length;
              
              // Test first few buttons for basic functionality
              const buttons = document.querySelectorAll('button');
              for (let i = 0; i < Math.min(3, buttons.length); i++) {
                try {
                  buttons[i].focus();
                  if (document.activeElement !== buttons[i]) {
                    results.interactionErrors.push(`Button ${i} not focusable`);
                  }
                } catch (e) {
                  results.interactionErrors.push(`Button ${i} focus error: ${e.message}`);
                }
              }
              
            } catch (e) {
              results.interactionErrors.push(`General interaction error: ${e.message}`);
            }
            
            return results;
          });
          
          // CSS and layout tests
          const layoutTests = await page.evaluate(() => {
            const results = {
              hasFlexbox: false,
              hasGrid: false,
              hasCustomProperties: false,
              responsiveElements: 0,
              hiddenElements: 0,
              overflowElements: 0
            };
            
            try {
              // Check for modern CSS features
              const testEl = document.createElement('div');
              testEl.style.display = 'flex';
              results.hasFlexbox = testEl.style.display === 'flex';
              
              testEl.style.display = 'grid';
              results.hasGrid = testEl.style.display === 'grid';
              
              testEl.style.setProperty('--test', 'value');
              results.hasCustomProperties = testEl.style.getPropertyValue('--test') === 'value';
              
              // Count responsive elements
              const mediaQueries = Array.from(document.styleSheets).reduce((count, sheet) => {
                try {
                  const rules = Array.from(sheet.cssRules || []);
                  return count + rules.filter(rule => rule.type === CSSRule.MEDIA_RULE).length;
                } catch (e) {
                  return count;
                }
              }, 0);
              results.responsiveElements = mediaQueries;
              
              // Count hidden elements
              const allElements = document.querySelectorAll('*');
              for (const el of allElements) {
                const style = window.getComputedStyle(el);
                if (style.display === 'none' || style.visibility === 'hidden') {
                  results.hiddenElements++;
                }
                if (style.overflow === 'hidden' || style.overflowX === 'hidden' || style.overflowY === 'hidden') {
                  results.overflowElements++;
                }
              }
              
            } catch (e) {
              results.error = e.message;
            }
            
            return results;
          });
          
          // Performance metrics
          const performanceMetrics = await page.evaluate(() => {
            const perf = performance.getEntriesByType('navigation')[0];
            return {
              domContentLoaded: perf ? perf.domContentLoadedEventEnd - perf.domContentLoadedEventStart : 0,
              loadComplete: perf ? perf.loadEventEnd - perf.loadEventStart : 0,
              firstPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-paint')?.startTime || 0,
              firstContentfulPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-contentful-paint')?.startTime || 0
            };
          });
          
          testResults[browserInfo.name][pageInfo.name] = {
            status: 'success',
            loadTime,
            basicTests,
            interactionTests,
            layoutTests,
            performanceMetrics,
            timestamp: new Date().toISOString()
          };
          
          console.log(`    ✓ ${pageInfo.name} - ${loadTime}ms`);
          
        } catch (error) {
          console.log(`    ✗ ${pageInfo.name} - ${error.message}`);
          testResults[browserInfo.name][pageInfo.name] = {
            status: 'error',
            error: error.message,
            timestamp: new Date().toISOString()
          };
        }
      }
      
      await browser.close();
      
    } catch (error) {
      console.error(`Failed to test ${browserInfo.name}:`, error.message);
      testResults[browserInfo.name] = {
        error: error.message,
        timestamp: new Date().toISOString()
      };
      
      if (browser) {
        await browser.close();
      }
    }
  }
  
  // Save results
  fs.writeFileSync('./cross-browser-test-results.json', JSON.stringify(testResults, null, 2));
  
  // Generate summary
  console.log('\n=== Cross-Browser Test Summary ===');
  
  const summary = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    browserSupport: {},
    pageCompatibility: {}
  };
  
  Object.entries(testResults).forEach(([browser, pages]) => {
    if (pages.error) {
      console.log(`${browser}: BROWSER ERROR - ${pages.error}`);
      summary.browserSupport[browser] = 'ERROR';
      return;
    }
    
    let browserPassed = 0;
    let browserTotal = 0;
    
    Object.entries(pages).forEach(([page, result]) => {
      browserTotal++;
      summary.totalTests++;
      
      if (result.status === 'success') {
        browserPassed++;
        summary.passedTests++;
        
        if (!summary.pageCompatibility[page]) {
          summary.pageCompatibility[page] = { passed: 0, total: 0 };
        }
        summary.pageCompatibility[page].passed++;
        summary.pageCompatibility[page].total++;
      } else {
        summary.failedTests++;
        
        if (!summary.pageCompatibility[page]) {
          summary.pageCompatibility[page] = { passed: 0, total: 0 };
        }
        summary.pageCompatibility[page].total++;
      }
    });
    
    const browserSuccessRate = browserTotal > 0 ? (browserPassed / browserTotal * 100).toFixed(1) : 0;
    summary.browserSupport[browser] = `${browserSuccessRate}%`;
    
    console.log(`${browser}: ${browserPassed}/${browserTotal} pages (${browserSuccessRate}%)`);
  });
  
  console.log(`\nOverall: ${summary.passedTests}/${summary.totalTests} tests passed (${(summary.passedTests / summary.totalTests * 100).toFixed(1)}%)`);
  
  console.log('\nPage Compatibility:');
  Object.entries(summary.pageCompatibility).forEach(([page, stats]) => {
    const rate = (stats.passed / stats.total * 100).toFixed(1);
    console.log(`  ${page}: ${stats.passed}/${stats.total} browsers (${rate}%)`);
  });
  
  return { testResults, summary };
}

if (require.main === module) {
  runCrossBrowserTest().catch(console.error);
}

module.exports = { runCrossBrowserTest };
