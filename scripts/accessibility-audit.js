const { chromium } = require('playwright');
const fs = require('fs');

async function runAccessibilityAudit() {
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Inject axe-core for accessibility testing
  await page.addScriptTag({
    url: 'https://unpkg.com/axe-core@4.8.2/axe.min.js'
  });
  
  const accessibilityResults = {};
  const pages = [
    { name: 'Home', url: 'http://localhost:3004' },
    { name: 'Analytics', url: 'http://localhost:3004/analytics' },
    { name: 'Governance', url: 'http://localhost:3004/governance' },
    { name: 'Trade', url: 'http://localhost:3004/trade' },
    { name: 'Documentation', url: 'http://localhost:3004/documentation' }
  ];
  
  for (const pageInfo of pages) {
    console.log(`Testing accessibility for ${pageInfo.name}...`);
    
    try {
      // Navigate to page
      await page.goto(pageInfo.url, { waitUntil: 'networkidle', timeout: 30000 });
      
      // Wait for page to be fully loaded
      await page.waitForTimeout(2000);
      
      // Run axe accessibility tests
      const axeResults = await page.evaluate(async () => {
        if (typeof axe !== 'undefined') {
          return await axe.run();
        } else {
          return { violations: [], passes: [], incomplete: [], inapplicable: [] };
        }
      });
      
      // Manual accessibility checks
      const manualChecks = await page.evaluate(() => {
        const checks = {
          hasTitle: !!document.title && document.title.trim() !== '',
          hasLang: !!document.documentElement.lang,
          hasMetaViewport: !!document.querySelector('meta[name="viewport"]'),
          hasSkipLinks: !!document.querySelector('a[href="#main"], a[href="#content"]'),
          hasHeadings: document.querySelectorAll('h1, h2, h3, h4, h5, h6').length > 0,
          hasMainLandmark: !!document.querySelector('main, [role="main"]'),
          hasNavLandmark: !!document.querySelector('nav, [role="navigation"]'),
          imagesHaveAlt: Array.from(document.querySelectorAll('img')).every(img => 
            img.hasAttribute('alt') || img.hasAttribute('aria-label') || img.getAttribute('role') === 'presentation'
          ),
          buttonsHaveLabels: Array.from(document.querySelectorAll('button')).every(btn => 
            btn.textContent.trim() !== '' || btn.hasAttribute('aria-label') || btn.hasAttribute('aria-labelledby')
          ),
          linksHaveText: Array.from(document.querySelectorAll('a')).every(link => 
            link.textContent.trim() !== '' || link.hasAttribute('aria-label') || link.hasAttribute('aria-labelledby')
          ),
          formsHaveLabels: Array.from(document.querySelectorAll('input, select, textarea')).every(input => {
            if (input.type === 'hidden') return true;
            return input.hasAttribute('aria-label') || 
                   input.hasAttribute('aria-labelledby') || 
                   document.querySelector(`label[for="${input.id}"]`) ||
                   input.closest('label');
          }),
          colorContrast: {
            // This would require more complex analysis
            note: 'Manual color contrast testing required'
          },
          keyboardNavigation: {
            focusableElements: document.querySelectorAll(
              'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
            ).length,
            note: 'Manual keyboard navigation testing required'
          }
        };
        
        return checks;
      });
      
      // Check for common accessibility patterns
      const commonPatterns = await page.evaluate(() => {
        return {
          hasAriaLive: document.querySelectorAll('[aria-live]').length,
          hasAriaExpanded: document.querySelectorAll('[aria-expanded]').length,
          hasAriaHidden: document.querySelectorAll('[aria-hidden]').length,
          hasRoleAttributes: document.querySelectorAll('[role]').length,
          hasTabIndex: document.querySelectorAll('[tabindex]').length,
          hasAriaDescribedBy: document.querySelectorAll('[aria-describedby]').length,
          hasAriaLabelledBy: document.querySelectorAll('[aria-labelledby]').length
        };
      });
      
      accessibilityResults[pageInfo.name] = {
        url: pageInfo.url,
        axeResults: {
          violations: axeResults.violations.length,
          passes: axeResults.passes.length,
          incomplete: axeResults.incomplete.length,
          violationDetails: axeResults.violations.map(v => ({
            id: v.id,
            impact: v.impact,
            description: v.description,
            help: v.help,
            nodes: v.nodes.length
          }))
        },
        manualChecks,
        commonPatterns,
        timestamp: new Date().toISOString()
      };
      
      console.log(`✓ ${pageInfo.name} completed - ${axeResults.violations.length} violations found`);
      
    } catch (error) {
      console.error(`✗ ${pageInfo.name} failed:`, error.message);
      accessibilityResults[pageInfo.name] = {
        url: pageInfo.url,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  await browser.close();
  
  // Save results
  fs.writeFileSync('./accessibility-audit-results.json', JSON.stringify(accessibilityResults, null, 2));
  
  // Generate summary
  console.log('\n=== Accessibility Audit Summary ===');
  let totalViolations = 0;
  let totalPasses = 0;
  
  Object.entries(accessibilityResults).forEach(([name, data]) => {
    if (data.error) {
      console.log(`${name}: ERROR - ${data.error}`);
    } else {
      const violations = data.axeResults.violations;
      const passes = data.axeResults.passes;
      totalViolations += violations;
      totalPasses += passes;
      
      console.log(`\n${name}:`);
      console.log(`  Violations: ${violations}`);
      console.log(`  Passes: ${passes}`);
      console.log(`  Manual Checks:`);
      console.log(`    - Has Title: ${data.manualChecks.hasTitle ? '✓' : '✗'}`);
      console.log(`    - Has Lang: ${data.manualChecks.hasLang ? '✓' : '✗'}`);
      console.log(`    - Has Viewport Meta: ${data.manualChecks.hasMetaViewport ? '✓' : '✗'}`);
      console.log(`    - Has Headings: ${data.manualChecks.hasHeadings ? '✓' : '✗'}`);
      console.log(`    - Has Main Landmark: ${data.manualChecks.hasMainLandmark ? '✓' : '✗'}`);
      console.log(`    - Images Have Alt: ${data.manualChecks.imagesHaveAlt ? '✓' : '✗'}`);
      console.log(`    - Buttons Have Labels: ${data.manualChecks.buttonsHaveLabels ? '✓' : '✗'}`);
      console.log(`    - Forms Have Labels: ${data.manualChecks.formsHaveLabels ? '✓' : '✗'}`);
      console.log(`  ARIA Usage:`);
      console.log(`    - Live Regions: ${data.commonPatterns.hasAriaLive}`);
      console.log(`    - Expandable Elements: ${data.commonPatterns.hasAriaExpanded}`);
      console.log(`    - Role Attributes: ${data.commonPatterns.hasRoleAttributes}`);
      console.log(`    - Focusable Elements: ${data.manualChecks.keyboardNavigation.focusableElements}`);
    }
  });
  
  console.log(`\n=== Overall Summary ===`);
  console.log(`Total Violations: ${totalViolations}`);
  console.log(`Total Passes: ${totalPasses}`);
  console.log(`Success Rate: ${totalPasses > 0 ? ((totalPasses / (totalPasses + totalViolations)) * 100).toFixed(1) : 0}%`);
  
  return accessibilityResults;
}

if (require.main === module) {
  runAccessibilityAudit().catch(console.error);
}

module.exports = { runAccessibilityAudit };
