const { chromium } = require('playwright');
const fs = require('fs');

async function runMobileResponsivenessTest() {
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const devices = [
    { name: 'iPhone 12', viewport: { width: 390, height: 844 }, userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15' },
    { name: 'iPhone SE', viewport: { width: 375, height: 667 }, userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15' },
    { name: 'iPad', viewport: { width: 768, height: 1024 }, userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15' },
    { name: 'Android Phone', viewport: { width: 360, height: 640 }, userAgent: 'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36' },
    { name: 'Android Tablet', viewport: { width: 800, height: 1280 }, userAgent: 'Mozilla/5.0 (Linux; Android 10; SM-T870) AppleWebKit/537.36' },
    { name: 'Desktop Small', viewport: { width: 1024, height: 768 }, userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' },
    { name: 'Desktop Large', viewport: { width: 1920, height: 1080 }, userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' }
  ];
  
  const pages = [
    { name: 'Home', url: 'http://localhost:3004' },
    { name: 'Governance', url: 'http://localhost:3004/governance' },
    { name: 'Trade', url: 'http://localhost:3004/trade' },
    { name: 'Documentation', url: 'http://localhost:3004/documentation' }
  ];
  
  const testResults = {};
  
  for (const device of devices) {
    console.log(`\n=== Testing ${device.name} (${device.viewport.width}x${device.viewport.height}) ===`);
    testResults[device.name] = {};
    
    const context = await browser.newContext({
      viewport: device.viewport,
      userAgent: device.userAgent,
      hasTouch: device.name.includes('iPhone') || device.name.includes('Android'),
      isMobile: device.name.includes('iPhone') || device.name.includes('Android')
    });
    
    const page = await context.newPage();
    
    for (const pageInfo of pages) {
      console.log(`  Testing ${pageInfo.name}...`);
      
      try {
        const startTime = Date.now();
        
        // Navigate to page
        await page.goto(pageInfo.url, { waitUntil: 'domcontentloaded', timeout: 15000 });
        
        const loadTime = Date.now() - startTime;
        
        // Wait for page to stabilize
        await page.waitForTimeout(2000);
        
        // Responsive design tests
        const responsiveTests = await page.evaluate((deviceInfo) => {
          const results = {
            viewport: {
              width: window.innerWidth,
              height: window.innerHeight,
              devicePixelRatio: window.devicePixelRatio
            },
            layout: {
              hasHorizontalScroll: document.documentElement.scrollWidth > window.innerWidth,
              hasVerticalScroll: document.documentElement.scrollHeight > window.innerHeight,
              contentWidth: document.documentElement.scrollWidth,
              contentHeight: document.documentElement.scrollHeight
            },
            navigation: {
              hasHamburgerMenu: !!document.querySelector('.hamburger, .menu-toggle, [aria-label*="menu"]'),
              hasMobileNav: !!document.querySelector('.mobile-nav, .nav-mobile, [class*="mobile"]'),
              navVisible: true,
              navElements: document.querySelectorAll('nav a, nav button').length
            },
            content: {
              textReadable: true, // Will be checked manually
              imagesResponsive: 0,
              totalImages: document.querySelectorAll('img').length,
              buttonsAccessible: 0,
              totalButtons: document.querySelectorAll('button').length,
              formsUsable: 0,
              totalForms: document.querySelectorAll('form').length
            },
            touch: {
              touchTargets: 0,
              minTouchSize: 44, // iOS/Android recommendation
              touchTargetIssues: []
            },
            performance: {
              renderTime: performance.now(),
              memoryUsage: performance.memory ? performance.memory.usedJSHeapSize : 'unknown'
            }
          };
          
          // Check navigation visibility
          const nav = document.querySelector('nav');
          if (nav) {
            const navStyle = window.getComputedStyle(nav);
            results.navigation.navVisible = navStyle.display !== 'none' && navStyle.visibility !== 'hidden';
          }
          
          // Check responsive images
          const images = document.querySelectorAll('img');
          images.forEach(img => {
            const style = window.getComputedStyle(img);
            if (style.maxWidth === '100%' || style.width === '100%' || img.hasAttribute('srcset')) {
              results.content.imagesResponsive++;
            }
          });
          
          // Check button accessibility (touch targets)
          const buttons = document.querySelectorAll('button, a, [role="button"]');
          buttons.forEach((btn, index) => {
            const rect = btn.getBoundingClientRect();
            const size = Math.min(rect.width, rect.height);
            
            if (size >= results.touch.minTouchSize) {
              results.content.buttonsAccessible++;
              results.touch.touchTargets++;
            } else if (rect.width > 0 && rect.height > 0) {
              results.touch.touchTargetIssues.push({
                element: btn.tagName.toLowerCase(),
                size: `${rect.width.toFixed(1)}x${rect.height.toFixed(1)}`,
                index
              });
            }
          });
          
          // Check form usability
          const forms = document.querySelectorAll('form');
          const inputs = document.querySelectorAll('input, select, textarea');
          inputs.forEach(input => {
            const rect = input.getBoundingClientRect();
            if (rect.height >= 44) { // Minimum touch target height
              results.content.formsUsable++;
            }
          });
          
          // Check for overflow issues
          const allElements = document.querySelectorAll('*');
          let overflowIssues = 0;
          for (const el of allElements) {
            const rect = el.getBoundingClientRect();
            if (rect.right > window.innerWidth + 10) { // 10px tolerance
              overflowIssues++;
            }
          }
          results.layout.overflowElements = overflowIssues;
          
          return results;
        }, device);
        
        // Test scrolling behavior
        const scrollTests = await page.evaluate(() => {
          const results = {
            canScrollVertically: document.documentElement.scrollHeight > window.innerHeight,
            canScrollHorizontally: document.documentElement.scrollWidth > window.innerWidth,
            smoothScrolling: false,
            scrollPosition: { x: window.scrollX, y: window.scrollY }
          };
          
          // Test smooth scrolling
          try {
            window.scrollTo({ top: 100, behavior: 'smooth' });
            results.smoothScrolling = true;
          } catch (e) {
            results.smoothScrolling = false;
          }
          
          return results;
        });
        
        // Test touch interactions (if mobile)
        let touchTests = { supported: false };
        if (device.name.includes('iPhone') || device.name.includes('Android')) {
          touchTests = await page.evaluate(() => {
            return {
              supported: 'ontouchstart' in window,
              touchEvents: ['touchstart', 'touchmove', 'touchend'].map(event => 
                typeof window[`on${event}`] !== 'undefined'
              ),
              gestureSupport: 'ongesturestart' in window
            };
          });
        }
        
        // Check CSS media queries
        const mediaQueryTests = await page.evaluate((deviceWidth) => {
          const results = {
            activeMediaQueries: [],
            breakpoints: {
              mobile: window.matchMedia('(max-width: 768px)').matches,
              tablet: window.matchMedia('(min-width: 769px) and (max-width: 1024px)').matches,
              desktop: window.matchMedia('(min-width: 1025px)').matches
            },
            orientation: window.matchMedia('(orientation: portrait)').matches ? 'portrait' : 'landscape'
          };
          
          // Test common breakpoints
          const commonBreakpoints = [320, 480, 768, 1024, 1200, 1440];
          commonBreakpoints.forEach(bp => {
            if (window.matchMedia(`(min-width: ${bp}px)`).matches) {
              results.activeMediaQueries.push(`min-width: ${bp}px`);
            }
          });
          
          return results;
        }, device.viewport.width);
        
        testResults[device.name][pageInfo.name] = {
          status: 'success',
          loadTime,
          responsiveTests,
          scrollTests,
          touchTests,
          mediaQueryTests,
          timestamp: new Date().toISOString()
        };
        
        console.log(`    ✓ ${pageInfo.name} - ${loadTime}ms`);
        
      } catch (error) {
        console.log(`    ✗ ${pageInfo.name} - ${error.message}`);
        testResults[device.name][pageInfo.name] = {
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    }
    
    await context.close();
  }
  
  await browser.close();
  
  // Save results
  fs.writeFileSync('./mobile-responsiveness-results.json', JSON.stringify(testResults, null, 2));
  
  // Generate summary
  console.log('\n=== Mobile Responsiveness Summary ===');
  
  const summary = {
    totalTests: 0,
    passedTests: 0,
    deviceSupport: {},
    responsiveIssues: [],
    touchTargetIssues: [],
    overallScore: 0
  };
  
  Object.entries(testResults).forEach(([device, pages]) => {
    let devicePassed = 0;
    let deviceTotal = 0;
    let deviceScore = 0;
    
    Object.entries(pages).forEach(([page, result]) => {
      deviceTotal++;
      summary.totalTests++;
      
      if (result.status === 'success') {
        devicePassed++;
        summary.passedTests++;
        
        const tests = result.responsiveTests;
        let pageScore = 0;
        let maxScore = 0;
        
        // Scoring criteria
        maxScore += 10; // No horizontal scroll
        if (!tests.layout.hasHorizontalScroll) pageScore += 10;
        
        maxScore += 10; // Navigation works
        if (tests.navigation.navVisible) pageScore += 10;
        
        maxScore += 10; // Images responsive
        if (tests.content.totalImages > 0) {
          pageScore += (tests.content.imagesResponsive / tests.content.totalImages) * 10;
        } else {
          pageScore += 10; // No images to test
        }
        
        maxScore += 10; // Touch targets adequate
        if (tests.content.totalButtons > 0) {
          pageScore += (tests.content.buttonsAccessible / tests.content.totalButtons) * 10;
        } else {
          pageScore += 10; // No buttons to test
        }
        
        maxScore += 10; // No overflow issues
        if (tests.layout.overflowElements === 0) pageScore += 10;
        
        deviceScore += (pageScore / maxScore) * 100;
        
        // Collect issues
        if (tests.layout.hasHorizontalScroll) {
          summary.responsiveIssues.push(`${device} - ${page}: Horizontal scroll detected`);
        }
        
        if (tests.touch.touchTargetIssues.length > 0) {
          summary.touchTargetIssues.push(`${device} - ${page}: ${tests.touch.touchTargetIssues.length} touch targets too small`);
        }
      }
    });
    
    const deviceSuccessRate = deviceTotal > 0 ? (devicePassed / deviceTotal * 100).toFixed(1) : 0;
    const avgDeviceScore = deviceTotal > 0 ? (deviceScore / deviceTotal).toFixed(1) : 0;
    
    summary.deviceSupport[device] = {
      successRate: `${deviceSuccessRate}%`,
      score: `${avgDeviceScore}/100`
    };
    
    console.log(`${device}: ${devicePassed}/${deviceTotal} pages (${deviceSuccessRate}%) - Score: ${avgDeviceScore}/100`);
  });
  
  summary.overallScore = summary.totalTests > 0 ? (summary.passedTests / summary.totalTests * 100).toFixed(1) : 0;
  
  console.log(`\nOverall: ${summary.passedTests}/${summary.totalTests} tests passed (${summary.overallScore}%)`);
  
  if (summary.responsiveIssues.length > 0) {
    console.log('\nResponsive Issues:');
    summary.responsiveIssues.forEach(issue => console.log(`  - ${issue}`));
  }
  
  if (summary.touchTargetIssues.length > 0) {
    console.log('\nTouch Target Issues:');
    summary.touchTargetIssues.forEach(issue => console.log(`  - ${issue}`));
  }
  
  return { testResults, summary };
}

if (require.main === module) {
  runMobileResponsivenessTest().catch(console.error);
}

module.exports = { runMobileResponsivenessTest };
