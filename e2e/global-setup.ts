import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup...')

  // Launch browser for setup
  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // Wait for the development server to be ready
    console.log('⏳ Waiting for development server...')
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' })
    
    // Verify the app is loaded
    await page.waitForSelector('[data-testid="app-loaded"]', { timeout: 30000 })
    console.log('✅ Development server is ready')

    // Setup test data or authentication if needed
    // For example, you might want to:
    // - Create test accounts
    // - Setup mock blockchain data
    // - Initialize test wallets
    
    // Mock wallet setup for testing
    await page.addInitScript(() => {
      // Mock MetaMask or other wallet providers
      window.ethereum = {
        isMetaMask: true,
        request: async ({ method, params }) => {
          switch (method) {
            case 'eth_requestAccounts':
              return ['******************************************']
            case 'eth_accounts':
              return ['******************************************']
            case 'eth_chainId':
              return '0x7d0' // Dogechain testnet
            case 'wallet_switchEthereumChain':
              return null
            case 'eth_sendTransaction':
              return '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890'
            default:
              throw new Error(`Unsupported method: ${method}`)
          }
        },
        on: () => {},
        removeListener: () => {},
      }
    })

    console.log('✅ Global setup completed successfully')
  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
}

export default globalSetup
