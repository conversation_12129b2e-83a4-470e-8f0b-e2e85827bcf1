import { test, expect } from '@playwright/test'

test.describe('Basic Application Functionality', () => {
  test('should load homepage correctly', async ({ page }) => {
    await page.goto('/')

    // Check page title
    await expect(page).toHaveTitle(/PawPumps/i)

    // Check main navigation is visible
    await expect(page.getByRole('navigation')).toBeVisible()

    // Check main content areas (use first occurrence)
    await expect(page.getByText('PawPumps').first()).toBeVisible()

    // Check footer is present
    await expect(page.locator('footer')).toBeVisible()
  })

  test('should navigate between pages', async ({ page }) => {
    await page.goto('/')

    // Navigate to Trade page (use first link to avoid footer link)
    await page.getByRole('navigation').getByRole('link', { name: /trade/i }).click()
    await expect(page).toHaveURL(/.*trade/)
    await expect(page).toHaveTitle(/.*Trade/i)

    // Navigate to Launch page
    await page.getByRole('navigation').getByRole('link', { name: /launch/i }).click()
    await expect(page).toHaveURL(/.*launch/)
    await expect(page).toHaveTitle(/.*Launch/i)

    // Navigate to Social page
    await page.getByRole('navigation').getByRole('link', { name: /social/i }).click()
    await expect(page).toHaveURL(/.*social/)
    await expect(page).toHaveTitle(/.*Social/i)

    // Navigate to Governance page
    await page.getByRole('navigation').getByRole('link', { name: /governance/i }).click()
    await expect(page).toHaveURL(/.*governance/)
    await expect(page).toHaveTitle(/.*Governance/i)
  })

  test('should load trade page with basic elements', async ({ page }) => {
    await page.goto('/trade')

    // Wait for page to load
    await page.waitForLoadState('domcontentloaded')

    // Check page title
    await expect(page).toHaveTitle(/.*Trade/i)

    // Check main sections are present (use navigation link to avoid footer)
    await expect(page.getByRole('navigation').getByText('Trade')).toBeVisible()

    // Check for trading interface elements
    await expect(page.getByText('Swap')).toBeVisible()
    await expect(page.getByText('Chart').first()).toBeVisible()
  })

  test('should load launch page with form elements', async ({ page }) => {
    await page.goto('/launch')

    // Wait for page to load
    await page.waitForLoadState('domcontentloaded')

    // Check page title
    await expect(page).toHaveTitle(/.*Launch/i)

    // Check main heading
    await expect(page.getByText('Launch Your Memecoin')).toBeVisible()

    // Check description text
    await expect(page.getByText('Create and deploy your own memecoin')).toBeVisible()
  })

  test('should handle responsive design', async ({ page, isMobile }) => {
    await page.goto('/')
    
    if (isMobile) {
      // Check mobile navigation
      const mobileMenu = page.getByRole('button', { name: /menu/i })
      if (await mobileMenu.isVisible()) {
        await mobileMenu.click()
        await expect(page.getByRole('navigation')).toBeVisible()
      }
    } else {
      // Check desktop navigation
      await expect(page.getByRole('navigation')).toBeVisible()
      await expect(page.getByRole('navigation').getByRole('link', { name: /trade/i })).toBeVisible()
    }
  })

  test('should handle page refresh gracefully', async ({ page }) => {
    await page.goto('/trade')
    await page.waitForLoadState('domcontentloaded')
    
    // Refresh page
    await page.reload()
    await page.waitForLoadState('domcontentloaded')
    
    // Should still load correctly
    await expect(page).toHaveTitle(/.*Trade/i)
    await expect(page.getByRole('navigation').getByText('Trade')).toBeVisible()
  })

  test('should have working wallet connect button', async ({ page }) => {
    await page.goto('/trade')
    await page.waitForLoadState('domcontentloaded')
    
    // Look for connect wallet button (there might be multiple)
    const connectButtons = page.getByRole('button', { name: /connect wallet/i })
    const firstConnectButton = connectButtons.first()
    
    // Check if button exists and is visible
    await expect(firstConnectButton).toBeVisible()
    
    // Click the button (should trigger wallet connection flow)
    await firstConnectButton.click()
    
    // Note: In a real test, you'd mock the wallet connection
    // For now, just verify the button interaction works
  })

  test('should display price data when available', async ({ page }) => {
    await page.goto('/trade')
    await page.waitForLoadState('domcontentloaded')
    
    // Wait a bit for API calls to complete
    await page.waitForTimeout(3000)
    
    // Check if price data is displayed (look for currency symbols or price patterns)
    const priceElements = page.locator('text=/\\$[0-9]/').or(page.locator('text=/[0-9]+\\.[0-9]+/'))
    
    // Should have at least some price data visible
    await expect(priceElements.first()).toBeVisible({ timeout: 10000 })
  })

  test('should handle keyboard navigation', async ({ page }) => {
    await page.goto('/')
    
    // Tab through navigation elements
    await page.keyboard.press('Tab')
    
    // Check that focus is visible on interactive elements
    const focusedElement = page.locator(':focus')
    await expect(focusedElement).toBeVisible()
    
    // Continue tabbing
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    
    // Should be able to activate links with Enter
    await page.keyboard.press('Enter')
    
    // Should navigate somewhere (URL should change or focus should move)
    await page.waitForTimeout(1000)
    const currentUrl = page.url()
    // Either URL changed or we're still on homepage but focus moved
    const isStillOnHomepage = currentUrl === 'http://localhost:3000/'
    if (isStillOnHomepage) {
      // Check that focus moved to a different element
      const focusedElement = page.locator(':focus')
      await expect(focusedElement).toBeVisible()
    } else {
      // URL changed, which is also valid
      expect(currentUrl).not.toBe('http://localhost:3000/')
    }
  })

  test('should load without JavaScript errors', async ({ page }) => {
    const errors: string[] = []
    
    // Listen for console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })
    
    await page.goto('/')
    await page.waitForLoadState('domcontentloaded')
    
    // Navigate to other pages
    await page.goto('/trade')
    await page.waitForLoadState('domcontentloaded')
    
    await page.goto('/launch')
    await page.waitForLoadState('domcontentloaded')
    
    // Filter out known non-critical errors
    const criticalErrors = errors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('manifest') &&
      !error.includes('MetaMask') &&
      !error.includes('wallet')
    )
    
    // Should have no critical JavaScript errors
    expect(criticalErrors).toHaveLength(0)
  })

  test('should have proper meta tags for SEO', async ({ page }) => {
    await page.goto('/')
    
    // Check for essential meta tags
    const title = await page.locator('title').textContent()
    expect(title).toContain('PawPumps')
    
    const description = await page.locator('meta[name="description"]').getAttribute('content')
    expect(description).toBeTruthy()
    
    const ogTitle = await page.locator('meta[property="og:title"]').getAttribute('content')
    expect(ogTitle).toBeTruthy()
    
    const ogDescription = await page.locator('meta[property="og:description"]').getAttribute('content')
    expect(ogDescription).toBeTruthy()
  })
})
