import { test, expect } from '@playwright/test'
import AxeBuilder from '@axe-core/playwright'

test.describe('Accessibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
  })

  test('should not have any automatically detectable accessibility issues', async ({ page }) => {
    const accessibilityScanResults = await new AxeBuilder({ page }).analyze()
    expect(accessibilityScanResults.violations).toEqual([])
  })

  test('should have proper heading hierarchy', async ({ page }) => {
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all()
    
    // Should have at least one h1
    const h1Count = await page.locator('h1').count()
    expect(h1Count).toBeGreaterThanOrEqual(1)
    
    // Check heading order
    let previousLevel = 0
    for (const heading of headings) {
      const tagName = await heading.evaluate(el => el.tagName.toLowerCase())
      const currentLevel = parseInt(tagName.charAt(1))
      
      if (previousLevel > 0) {
        // Heading levels should not skip (e.g., h1 -> h3)
        expect(currentLevel - previousLevel).toBeLessThanOrEqual(1)
      }
      
      previousLevel = currentLevel
    }
  })

  test('should have proper focus management', async ({ page }) => {
    // Test tab navigation
    await page.keyboard.press('Tab')
    
    // First focusable element should be focused
    const focusedElement = await page.locator(':focus').first()
    await expect(focusedElement).toBeVisible()
    
    // Continue tabbing through elements
    for (let i = 0; i < 5; i++) {
      await page.keyboard.press('Tab')
      const currentFocus = await page.locator(':focus').first()
      await expect(currentFocus).toBeVisible()
    }
  })

  test('should have proper ARIA labels and roles', async ({ page }) => {
    // Check for buttons without accessible names
    const buttons = await page.locator('button').all()
    for (const button of buttons) {
      const accessibleName = await button.evaluate(el => {
        return el.getAttribute('aria-label') || 
               el.getAttribute('aria-labelledby') || 
               el.textContent?.trim()
      })
      expect(accessibleName).toBeTruthy()
    }
    
    // Check for images without alt text
    const images = await page.locator('img').all()
    for (const image of images) {
      const altText = await image.getAttribute('alt')
      const role = await image.getAttribute('role')
      
      // Images should have alt text or be decorative
      expect(altText !== null || role === 'presentation').toBeTruthy()
    }
  })

  test('should have sufficient color contrast', async ({ page }) => {
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze()
    
    const contrastViolations = accessibilityScanResults.violations.filter(
      violation => violation.id === 'color-contrast'
    )
    
    expect(contrastViolations).toHaveLength(0)
  })

  test('should be keyboard navigable', async ({ page }) => {
    // Test keyboard navigation on trading page
    await page.goto('/trade')
    await page.waitForLoadState('networkidle')
    
    // Tab through form elements
    await page.keyboard.press('Tab')
    let focusedElement = await page.locator(':focus').first()
    
    // Should be able to reach all interactive elements
    const interactiveElements = await page.locator('button, input, select, textarea, a[href]').count()
    let tabCount = 0
    
    while (tabCount < interactiveElements && tabCount < 20) {
      await page.keyboard.press('Tab')
      focusedElement = await page.locator(':focus').first()
      
      if (await focusedElement.isVisible()) {
        tabCount++
      }
    }
    
    expect(tabCount).toBeGreaterThan(0)
  })

  test('should have proper form labels', async ({ page }) => {
    await page.goto('/launch')
    await page.waitForLoadState('networkidle')
    
    // All form inputs should have labels
    const inputs = await page.locator('input, textarea, select').all()
    
    for (const input of inputs) {
      const id = await input.getAttribute('id')
      const ariaLabel = await input.getAttribute('aria-label')
      const ariaLabelledby = await input.getAttribute('aria-labelledby')
      
      if (id) {
        // Check for associated label
        const label = await page.locator(`label[for="${id}"]`).count()
        expect(label > 0 || ariaLabel || ariaLabelledby).toBeTruthy()
      } else {
        // Should have aria-label or aria-labelledby
        expect(ariaLabel || ariaLabelledby).toBeTruthy()
      }
    }
  })

  test('should handle screen reader announcements', async ({ page }) => {
    // Test live regions
    const liveRegions = await page.locator('[aria-live]').all()
    
    for (const region of liveRegions) {
      const ariaLive = await region.getAttribute('aria-live')
      expect(['polite', 'assertive', 'off']).toContain(ariaLive)
    }
    
    // Test status messages
    await page.goto('/trade')
    await page.waitForLoadState('networkidle')
    
    // Trigger a status update
    const statusRegion = page.locator('[role="status"], [aria-live="polite"]').first()
    if (await statusRegion.isVisible()) {
      await expect(statusRegion).toBeVisible()
    }
  })

  test('should have proper modal accessibility', async ({ page }) => {
    // Look for modal triggers
    const modalTriggers = await page.locator('[data-testid*="modal"], [aria-haspopup="dialog"]').all()
    
    for (const trigger of modalTriggers) {
      if (await trigger.isVisible()) {
        await trigger.click()
        
        // Check for modal
        const modal = page.locator('[role="dialog"], [aria-modal="true"]').first()
        if (await modal.isVisible()) {
          // Modal should have proper attributes
          const ariaModal = await modal.getAttribute('aria-modal')
          const role = await modal.getAttribute('role')
          
          expect(ariaModal === 'true' || role === 'dialog').toBeTruthy()
          
          // Focus should be trapped in modal
          await page.keyboard.press('Tab')
          const focusedElement = await page.locator(':focus').first()
          
          // Focused element should be within modal
          const isWithinModal = await focusedElement.evaluate((el, modalEl) => {
            return modalEl.contains(el)
          }, await modal.elementHandle())
          
          expect(isWithinModal).toBeTruthy()
          
          // Close modal
          await page.keyboard.press('Escape')
          await expect(modal).not.toBeVisible()
        }
      }
    }
  })

  test('should have proper error handling accessibility', async ({ page }) => {
    await page.goto('/launch')
    await page.waitForLoadState('networkidle')
    
    // Try to submit form without required fields
    const submitButton = page.getByRole('button', { name: /create|launch|submit/i })
    if (await submitButton.isVisible()) {
      await submitButton.click()
      
      // Check for error messages
      const errorMessages = await page.locator('[role="alert"], .error, [aria-invalid="true"]').all()
      
      for (const error of errorMessages) {
        if (await error.isVisible()) {
          // Error should be associated with form field
          const ariaDescribedby = await error.getAttribute('aria-describedby')
          const id = await error.getAttribute('id')
          
          if (id) {
            // Check if any input references this error
            const referencingInput = await page.locator(`[aria-describedby*="${id}"]`).count()
            expect(referencingInput).toBeGreaterThan(0)
          }
        }
      }
    }
  })

  test('should be usable with high contrast mode', async ({ page }) => {
    // Simulate high contrast mode
    await page.addStyleTag({
      content: `
        @media (prefers-contrast: high) {
          * {
            background-color: black !important;
            color: white !important;
            border-color: white !important;
          }
        }
      `
    })
    
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // Check that content is still visible and usable
    const mainContent = page.locator('main, [role="main"]').first()
    await expect(mainContent).toBeVisible()
    
    // Interactive elements should still be focusable
    await page.keyboard.press('Tab')
    const focusedElement = await page.locator(':focus').first()
    await expect(focusedElement).toBeVisible()
  })

  test('should work with reduced motion preferences', async ({ page }) => {
    // Simulate reduced motion preference
    await page.emulateMedia({ reducedMotion: 'reduce' })
    
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // Animations should be reduced or disabled
    const animatedElements = await page.locator('[class*="animate"], [style*="animation"]').all()
    
    for (const element of animatedElements) {
      const computedStyle = await element.evaluate(el => {
        return window.getComputedStyle(el).animationDuration
      })
      
      // Animation duration should be very short or none
      expect(['0s', '0.01s', 'none'].some(value => computedStyle.includes(value))).toBeTruthy()
    }
  })

  test('should have proper landmark regions', async ({ page }) => {
    // Check for proper landmark structure
    const landmarks = {
      main: await page.locator('main, [role="main"]').count(),
      navigation: await page.locator('nav, [role="navigation"]').count(),
      banner: await page.locator('header, [role="banner"]').count(),
      contentinfo: await page.locator('footer, [role="contentinfo"]').count(),
    }
    
    // Should have at least one main landmark
    expect(landmarks.main).toBeGreaterThanOrEqual(1)
    
    // Should have navigation
    expect(landmarks.navigation).toBeGreaterThanOrEqual(1)
    
    // Check that landmarks have proper labels if multiple exist
    if (landmarks.navigation > 1) {
      const navElements = await page.locator('nav, [role="navigation"]').all()
      for (const nav of navElements) {
        const ariaLabel = await nav.getAttribute('aria-label')
        const ariaLabelledby = await nav.getAttribute('aria-labelledby')
        expect(ariaLabel || ariaLabelledby).toBeTruthy()
      }
    }
  })
})
