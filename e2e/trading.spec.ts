import { test, expect } from '@playwright/test'

test.describe('Trading Interface', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/trade')
    await page.waitForLoadState('networkidle')
  })

  test('should load trading interface correctly', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/PawPumps.*Trade/i)

    // Check main sections are visible
    await expect(page.getByText('Chart')).toBeVisible()
    await expect(page.getByText('Swap')).toBeVisible()
    await expect(page.getByText('Liquidity')).toBeVisible()

    // Check trading form elements
    await expect(page.getByPlaceholder('0.0').first()).toBeVisible()
    await expect(page.getByRole('button', { name: /swap tokens/i })).toBeVisible()
  })

  test('should connect wallet successfully', async ({ page }) => {
    // Click connect wallet button
    await page.getByRole('button', { name: /connect wallet/i }).click()

    // Wait for wallet connection
    await expect(page.getByText(/0x1234/)).toBeVisible({ timeout: 10000 })

    // Verify wallet is connected
    await expect(page.getByRole('button', { name: /disconnect/i })).toBeVisible()
  })

  test('should perform token swap', async ({ page }) => {
    // Connect wallet first
    await page.getByRole('button', { name: /connect wallet/i }).click()
    await page.waitForTimeout(1000)

    // Fill in swap amount
    const fromAmountInput = page.getByPlaceholder('0.0').first()
    await fromAmountInput.fill('100')

    // Verify estimated output is calculated
    const toAmountInput = page.getByPlaceholder('0.0').nth(1)
    await expect(toAmountInput).not.toHaveValue('')

    // Click swap button
    await page.getByRole('button', { name: /swap tokens/i }).click()

    // Wait for transaction to complete
    await expect(page.getByText(/swap successful/i)).toBeVisible({ timeout: 15000 })

    // Verify form is reset
    await expect(fromAmountInput).toHaveValue('')
  })

  test('should handle swap validation errors', async ({ page }) => {
    // Try to swap without connecting wallet
    await page.getByRole('button', { name: /swap tokens/i }).click()

    // Should show wallet connection error
    await expect(page.getByText(/wallet not connected/i)).toBeVisible()

    // Connect wallet
    await page.getByRole('button', { name: /connect wallet/i }).click()
    await page.waitForTimeout(1000)

    // Try to swap without amount
    await page.getByRole('button', { name: /swap tokens/i }).click()

    // Should show amount validation error
    await expect(page.getByText(/invalid amount/i)).toBeVisible()
  })

  test('should switch token selection', async ({ page }) => {
    // Click on from token selector
    await page.getByTestId('from-token-selector').click()

    // Should show token selection modal
    await expect(page.getByText(/select token/i)).toBeVisible()

    // Select a different token
    await page.getByText('SHIB').click()

    // Verify token is selected
    await expect(page.getByTestId('from-token-selector')).toContainText('SHIB')
  })

  test('should reverse swap direction', async ({ page }) => {
    // Fill in from amount
    await page.getByPlaceholder('0.0').first().fill('100')

    // Click swap direction button
    await page.getByRole('button', { name: /swap direction/i }).click()

    // Verify amounts are swapped
    await expect(page.getByPlaceholder('0.0').nth(1)).toHaveValue('100')
    await expect(page.getByPlaceholder('0.0').first()).toHaveValue('')
  })

  test('should show transaction details', async ({ page }) => {
    // Fill in swap amount
    await page.getByPlaceholder('0.0').first().fill('1000')

    // Check transaction details are shown
    await expect(page.getByText(/estimated gas/i)).toBeVisible()
    await expect(page.getByText(/minimum received/i)).toBeVisible()
    await expect(page.getByText(/price impact/i)).toBeVisible()
  })

  test('should handle high price impact warning', async ({ page }) => {
    // Fill in large amount that causes high price impact
    await page.getByPlaceholder('0.0').first().fill('1000000')

    // Should show price impact warning
    await expect(page.getByText(/high price impact/i)).toBeVisible()
    await expect(page.getByText(/warning/i)).toBeVisible()
  })

  test('should open and configure settings', async ({ page }) => {
    // Click settings button
    await page.getByRole('button', { name: /settings/i }).click()

    // Should show settings modal
    await expect(page.getByText(/slippage tolerance/i)).toBeVisible()

    // Change slippage tolerance
    const slippageInput = page.getByDisplayValue('0.5')
    await slippageInput.fill('1.0')

    // Save settings
    await page.getByRole('button', { name: /save/i }).click()

    // Verify settings are applied
    await page.getByRole('button', { name: /settings/i }).click()
    await expect(page.getByDisplayValue('1.0')).toBeVisible()
  })

  test('should switch between chart timeframes', async ({ page }) => {
    // Click timeframe selector
    await page.getByText('1D').click()

    // Should show timeframe options
    await expect(page.getByText('1H')).toBeVisible()
    await expect(page.getByText('4H')).toBeVisible()
    await expect(page.getByText('1W')).toBeVisible()

    // Select different timeframe
    await page.getByText('1H').click()

    // Verify timeframe is selected
    await expect(page.getByText('1H')).toHaveClass(/selected/)
  })

  test('should handle network errors gracefully', async ({ page }) => {
    // Simulate network failure
    await page.route('**/api/**', route => route.abort())

    // Connect wallet
    await page.getByRole('button', { name: /connect wallet/i }).click()
    await page.waitForTimeout(1000)

    // Fill in swap amount
    await page.getByPlaceholder('0.0').first().fill('100')

    // Try to swap
    await page.getByRole('button', { name: /swap tokens/i }).click()

    // Should show error message
    await expect(page.getByText(/network error/i)).toBeVisible()

    // Should show retry button
    await expect(page.getByRole('button', { name: /retry/i })).toBeVisible()
  })

  test('should be accessible via keyboard navigation', async ({ page }) => {
    // Tab through form elements
    await page.keyboard.press('Tab')
    await expect(page.getByPlaceholder('0.0').first()).toBeFocused()

    await page.keyboard.press('Tab')
    await expect(page.getByTestId('from-token-selector')).toBeFocused()

    await page.keyboard.press('Tab')
    await expect(page.getByPlaceholder('0.0').nth(1)).toBeFocused()

    // Enter key should activate buttons
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    await expect(page.getByRole('button', { name: /swap tokens/i })).toBeFocused()
  })

  test('should work on mobile devices', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip('This test is only for mobile devices')
    }

    // Check mobile layout
    await expect(page.getByTestId('mobile-trading-interface')).toBeVisible()

    // Check mobile navigation
    await page.getByRole('button', { name: /menu/i }).click()
    await expect(page.getByRole('navigation')).toBeVisible()

    // Test mobile swap functionality
    await page.getByPlaceholder('0.0').first().fill('100')
    await page.getByRole('button', { name: /swap tokens/i }).click()

    // Should work the same as desktop
    await expect(page.getByText(/wallet not connected/i)).toBeVisible()
  })

  test('should handle page refresh gracefully', async ({ page }) => {
    // Fill in some data
    await page.getByPlaceholder('0.0').first().fill('100')

    // Refresh page
    await page.reload()
    await page.waitForLoadState('networkidle')

    // Should load correctly
    await expect(page.getByText('Chart')).toBeVisible()
    await expect(page.getByRole('button', { name: /swap tokens/i })).toBeVisible()

    // Form should be reset
    await expect(page.getByPlaceholder('0.0').first()).toHaveValue('')
  })

  test('should maintain state across tab switches', async ({ page }) => {
    // Fill in swap amount
    await page.getByPlaceholder('0.0').first().fill('100')

    // Switch to liquidity tab
    await page.getByRole('tab', { name: /liquidity/i }).click()
    await expect(page.getByText(/add liquidity/i)).toBeVisible()

    // Switch back to swap tab
    await page.getByRole('tab', { name: /swap/i }).click()

    // Amount should be preserved
    await expect(page.getByPlaceholder('0.0').first()).toHaveValue('100')
  })

  test('should show loading states appropriately', async ({ page }) => {
    // Connect wallet
    await page.getByRole('button', { name: /connect wallet/i }).click()
    await page.waitForTimeout(1000)

    // Fill in swap amount
    await page.getByPlaceholder('0.0').first().fill('100')

    // Click swap button
    await page.getByRole('button', { name: /swap tokens/i }).click()

    // Should show loading state
    await expect(page.getByText(/swapping/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /swap tokens/i })).toBeDisabled()

    // Wait for completion
    await expect(page.getByText(/swap successful/i)).toBeVisible({ timeout: 15000 })
  })
})
