import { test, expect } from '@playwright/test'

test.describe('Performance Tests', () => {
  test('should meet Core Web Vitals thresholds', async ({ page }) => {
    await page.goto('/')
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle')
    
    // Measure Core Web Vitals
    const vitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals = {
          fcp: 0,
          lcp: 0,
          fid: 0,
          cls: 0,
          ttfb: 0,
        }
        
        // First Contentful Paint
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              vitals.fcp = entry.startTime
            }
          }
        }).observe({ entryTypes: ['paint'] })
        
        // Largest Contentful Paint
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          vitals.lcp = lastEntry.startTime
        }).observe({ entryTypes: ['largest-contentful-paint'] })
        
        // First Input Delay
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            vitals.fid = entry.processingStart - entry.startTime
          }
        }).observe({ entryTypes: ['first-input'] })
        
        // Cumulative Layout Shift
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              vitals.cls += entry.value
            }
          }
        }).observe({ entryTypes: ['layout-shift'] })
        
        // Time to First Byte
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        if (navigation) {
          vitals.ttfb = navigation.responseStart - navigation.requestStart
        }
        
        setTimeout(() => resolve(vitals), 3000)
      })
    })
    
    // Assert Core Web Vitals thresholds
    expect(vitals.fcp).toBeLessThan(1800) // Good: < 1.8s
    expect(vitals.lcp).toBeLessThan(2500) // Good: < 2.5s
    expect(vitals.fid).toBeLessThan(100)  // Good: < 100ms
    expect(vitals.cls).toBeLessThan(0.1)  // Good: < 0.1
    expect(vitals.ttfb).toBeLessThan(800) // Good: < 800ms
  })

  test('should load critical resources quickly', async ({ page }) => {
    const startTime = Date.now()
    
    await page.goto('/')
    await page.waitForSelector('[data-testid="app-loaded"]', { timeout: 5000 })
    
    const loadTime = Date.now() - startTime
    
    // Page should load within 3 seconds
    expect(loadTime).toBeLessThan(3000)
  })

  test('should have efficient bundle sizes', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Get resource timing data
    const resources = await page.evaluate(() => {
      return performance.getEntriesByType('resource').map(entry => ({
        name: entry.name,
        size: entry.transferSize || entry.encodedBodySize,
        duration: entry.duration,
      }))
    })
    
    // Check JavaScript bundle sizes
    const jsResources = resources.filter(r => r.name.includes('.js'))
    const totalJSSize = jsResources.reduce((sum, r) => sum + r.size, 0)
    
    // Total JS should be under 500KB
    expect(totalJSSize).toBeLessThan(500 * 1024)
    
    // Individual bundles should be reasonable
    for (const resource of jsResources) {
      if (resource.name.includes('main') || resource.name.includes('index')) {
        expect(resource.size).toBeLessThan(250 * 1024) // Main bundle < 250KB
      }
    }
  })

  test('should handle large datasets efficiently', async ({ page }) => {
    await page.goto('/analytics')
    await page.waitForLoadState('networkidle')
    
    // Measure rendering performance with large data
    const renderTime = await page.evaluate(() => {
      const start = performance.now()
      
      // Simulate large dataset rendering
      const container = document.createElement('div')
      for (let i = 0; i < 1000; i++) {
        const item = document.createElement('div')
        item.textContent = `Item ${i}`
        container.appendChild(item)
      }
      document.body.appendChild(container)
      
      const end = performance.now()
      document.body.removeChild(container)
      
      return end - start
    })
    
    // Rendering 1000 items should take less than 100ms
    expect(renderTime).toBeLessThan(100)
  })

  test('should have smooth animations', async ({ page }) => {
    await page.goto('/trade')
    await page.waitForLoadState('networkidle')
    
    // Test animation performance
    const animationPerformance = await page.evaluate(() => {
      return new Promise((resolve) => {
        let frameCount = 0
        let startTime = performance.now()
        
        const measureFrames = () => {
          frameCount++
          if (frameCount < 60) { // Measure for ~1 second at 60fps
            requestAnimationFrame(measureFrames)
          } else {
            const endTime = performance.now()
            const duration = endTime - startTime
            const fps = (frameCount / duration) * 1000
            resolve(fps)
          }
        }
        
        requestAnimationFrame(measureFrames)
      })
    })
    
    // Should maintain close to 60fps
    expect(animationPerformance).toBeGreaterThan(55)
  })

  test('should handle memory efficiently', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Get initial memory usage
    const initialMemory = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0
    })
    
    // Navigate through several pages
    const pages = ['/trade', '/launch', '/analytics', '/governance']
    
    for (const pagePath of pages) {
      await page.goto(pagePath)
      await page.waitForLoadState('networkidle')
      await page.waitForTimeout(1000)
    }
    
    // Return to home
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Force garbage collection if available
    await page.evaluate(() => {
      if ((window as any).gc) {
        (window as any).gc()
      }
    })
    
    await page.waitForTimeout(2000)
    
    // Get final memory usage
    const finalMemory = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0
    })
    
    // Memory growth should be reasonable (less than 50MB increase)
    const memoryGrowth = finalMemory - initialMemory
    expect(memoryGrowth).toBeLessThan(50 * 1024 * 1024)
  })

  test('should load images efficiently', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Check image loading performance
    const imageMetrics = await page.evaluate(() => {
      const images = Array.from(document.querySelectorAll('img'))
      return images.map(img => ({
        src: img.src,
        naturalWidth: img.naturalWidth,
        naturalHeight: img.naturalHeight,
        displayWidth: img.offsetWidth,
        displayHeight: img.offsetHeight,
        loaded: img.complete,
      }))
    })
    
    // All images should be loaded
    for (const image of imageMetrics) {
      expect(image.loaded).toBeTruthy()
      
      // Images shouldn't be unnecessarily large
      if (image.displayWidth > 0 && image.displayHeight > 0) {
        const oversizeRatio = Math.max(
          image.naturalWidth / image.displayWidth,
          image.naturalHeight / image.displayHeight
        )
        expect(oversizeRatio).toBeLessThan(2) // Not more than 2x larger than display size
      }
    }
  })

  test('should handle network conditions gracefully', async ({ page, context }) => {
    // Simulate slow 3G connection
    await context.route('**/*', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 100)) // Add 100ms delay
      await route.continue()
    })
    
    const startTime = Date.now()
    await page.goto('/')
    await page.waitForSelector('[data-testid="app-loaded"]', { timeout: 10000 })
    const loadTime = Date.now() - startTime
    
    // Should still load within reasonable time on slow connection
    expect(loadTime).toBeLessThan(8000)
    
    // Critical content should be visible
    await expect(page.getByRole('main')).toBeVisible()
  })

  test('should have efficient scroll performance', async ({ page }) => {
    await page.goto('/analytics')
    await page.waitForLoadState('networkidle')
    
    // Measure scroll performance
    const scrollPerformance = await page.evaluate(() => {
      return new Promise((resolve) => {
        let frameCount = 0
        let startTime = performance.now()
        
        const measureScroll = () => {
          frameCount++
          window.scrollBy(0, 10)
          
          if (frameCount < 100) {
            requestAnimationFrame(measureScroll)
          } else {
            const endTime = performance.now()
            const duration = endTime - startTime
            resolve(duration)
          }
        }
        
        requestAnimationFrame(measureScroll)
      })
    })
    
    // Scrolling should be smooth (100 frames in reasonable time)
    expect(scrollPerformance).toBeLessThan(2000)
  })

  test('should handle concurrent operations efficiently', async ({ page }) => {
    await page.goto('/trade')
    await page.waitForLoadState('networkidle')
    
    // Simulate multiple concurrent operations
    const operations = []
    
    for (let i = 0; i < 5; i++) {
      operations.push(
        page.evaluate(() => {
          return new Promise(resolve => {
            // Simulate API call
            setTimeout(() => {
              resolve(Math.random())
            }, Math.random() * 1000)
          })
        })
      )
    }
    
    const startTime = Date.now()
    await Promise.all(operations)
    const duration = Date.now() - startTime
    
    // Concurrent operations should complete efficiently
    expect(duration).toBeLessThan(2000)
  })

  test('should maintain performance during user interactions', async ({ page }) => {
    await page.goto('/trade')
    await page.waitForLoadState('networkidle')
    
    // Measure interaction responsiveness
    const interactions = []
    
    for (let i = 0; i < 10; i++) {
      const startTime = Date.now()
      
      // Simulate user interaction
      await page.getByPlaceholder('0.0').first().fill(`${i * 10}`)
      await page.waitForTimeout(50)
      
      const endTime = Date.now()
      interactions.push(endTime - startTime)
    }
    
    // Average interaction time should be reasonable
    const averageTime = interactions.reduce((sum, time) => sum + time, 0) / interactions.length
    expect(averageTime).toBeLessThan(200)
  })
})
