import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Security middleware
export function middleware(request: NextRequest) {
  const response = NextResponse.next()
  
  // Get client IP
  const ip = request.headers.get('x-forwarded-for') ||
             request.headers.get('x-real-ip') ||
             'unknown'
  
  // Rate limiting for API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    const rateLimitKey = `${ip}:${request.nextUrl.pathname}`
    const now = Date.now()
    const windowMs = 60 * 1000 // 1 minute
    const maxRequests = 100 // Max requests per window
    
    const current = rateLimitStore.get(rateLimitKey)
    
    if (current) {
      if (now < current.resetTime) {
        if (current.count >= maxRequests) {
          return new NextResponse('Too Many Requests', { status: 429 })
        }
        current.count++
      } else {
        // Reset window
        rateLimitStore.set(rateLimitKey, { count: 1, resetTime: now + windowMs })
      }
    } else {
      rateLimitStore.set(rateLimitKey, { count: 1, resetTime: now + windowMs })
    }
  }
  
  // Security headers (additional to next.config.js)
  response.headers.set('X-Robots-Tag', 'noindex, nofollow, nosnippet, noarchive')
  
  // CSRF protection for state-changing requests
  if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {
    const origin = request.headers.get('origin')
    const host = request.headers.get('host')
    
    // Check if origin matches host (simple CSRF protection)
    if (origin && host && !origin.includes(host)) {
      return new NextResponse('Forbidden', { status: 403 })
    }
  }
  
  // Block suspicious user agents
  const userAgent = request.headers.get('user-agent') || ''
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
  ]
  
  // Allow legitimate bots but block others on sensitive routes
  if (request.nextUrl.pathname.startsWith('/admin') || 
      request.nextUrl.pathname.startsWith('/governance/admin')) {
    if (suspiciousPatterns.some(pattern => pattern.test(userAgent))) {
      return new NextResponse('Forbidden', { status: 403 })
    }
  }
  
  // Content type validation for API routes
  if (request.nextUrl.pathname.startsWith('/api/') && 
      ['POST', 'PUT', 'PATCH'].includes(request.method)) {
    const contentType = request.headers.get('content-type')
    if (contentType && !contentType.includes('application/json')) {
      return new NextResponse('Unsupported Media Type', { status: 415 })
    }
  }
  
  return response
}

// Configure which routes the middleware runs on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
