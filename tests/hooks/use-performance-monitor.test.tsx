import { renderHook, act } from "@testing-library/react"
import { usePerformanceMonitor } from "@/hooks/use-performance-monitor"
import { jest } from "@jest/globals"

describe("usePerformanceMonitor", () => {
  beforeEach(() => {
    // Clear any mocked functions
    jest.clearAllMocks()

    // Reset localStorage
    if (typeof window !== "undefined") {
      localStorage.clear()
    }
  })

  it("initializes with default metrics", () => {
    const { result } = renderHook(() => usePerformanceMonitor())

    expect(result.current.metrics).toEqual({
      fcp: null,
      lcp: null,
      cls: null,
      fid: null,
      ttfb: null,
    })
  })

  it("starts monitoring when called", () => {
    const mockAddEventListener = jest.spyOn(window, "addEventListener")

    const { result } = renderHook(() => usePerformanceMonitor())

    act(() => {
      result.current.startMonitoring()
    })

    // Check that event listeners were added for performance metrics
    expect(mockAddEventListener).toHaveBeenCalled()
  })

  it("stops monitoring when called", () => {
    const mockRemoveEventListener = jest.spyOn(window, "removeEventListener")

    const { result } = renderHook(() => usePerformanceMonitor())

    act(() => {
      result.current.startMonitoring()
      result.current.stopMonitoring()
    })

    // Check that event listeners were removed
    expect(mockRemoveEventListener).toHaveBeenCalled()
  })
})
