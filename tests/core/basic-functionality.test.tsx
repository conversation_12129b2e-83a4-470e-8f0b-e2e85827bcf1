import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'

// Test basic React functionality
describe('Core Functionality Tests', () => {
  it('renders basic React components', () => {
    const TestComponent = () => <div data-testid="test">Hello World</div>
    render(<TestComponent />)
    expect(screen.getByTestId('test')).toBeInTheDocument()
    expect(screen.getByText('Hello World')).toBeInTheDocument()
  })

  it('handles basic props', () => {
    const TestComponent = ({ message }: { message: string }) => (
      <div data-testid="test">{message}</div>
    )
    render(<TestComponent message="Test Message" />)
    expect(screen.getByText('Test Message')).toBeInTheDocument()
  })

  it('handles basic state', () => {
    const { useState } = require('react')
    const TestComponent = () => {
      const [count, setCount] = useState(0)
      return (
        <div>
          <span data-testid="count">{count}</span>
          <button onClick={() => setCount(count + 1)}>Increment</button>
        </div>
      )
    }
    render(<TestComponent />)
    expect(screen.getByTestId('count')).toHaveTextContent('0')
  })
})

// Test environment setup
describe('Test Environment', () => {
  it('has proper Jest configuration', () => {
    expect(jest).toBeDefined()
    expect(expect).toBeDefined()
  })

  it('has proper DOM testing setup', () => {
    expect(document).toBeDefined()
    expect(window).toBeDefined()
  })

  it('has proper module resolution', () => {
    // Test that we can import from @/ paths
    expect(() => {
      // This will throw if module resolution is broken
      require('@/components/ui/button')
    }).not.toThrow()
  })
})
