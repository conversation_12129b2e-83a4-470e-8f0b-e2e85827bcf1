import { logPerformanceMetric, getPerformanceBudget, isMetricWithinBudget } from "@/utils/performance-monitoring"

// Mock console.log to avoid cluttering test output
const originalConsoleLog = console.log
const mockConsoleLog = jest.fn()

describe("Performance Monitoring Utilities", () => {
  beforeEach(() => {
    console.log = mockConsoleLog
    mockConsoleLog.mockClear()
  })

  afterAll(() => {
    console.log = originalConsoleLog
  })

  describe("logPerformanceMetric", () => {
    it("logs performance metrics correctly", () => {
      logPerformanceMetric("FCP", 1200)

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining("Performance Metric"),
        expect.stringContaining("FCP"),
        expect.stringContaining("1200ms"),
      )
    })

    it("logs warning if metric exceeds budget", () => {
      // FCP budget typically < 1000ms
      logPerformanceMetric("FCP", 2500)

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining("Performance Metric"),
        expect.stringContaining("FCP"),
        expect.stringContaining("2500ms"),
        expect.stringContaining("exceeds budget"),
      )
    })
  })

  describe("getPerformanceBudget", () => {
    it("returns correct budget for known metrics", () => {
      expect(getPerformanceBudget("FCP")).toBeLessThanOrEqual(2000)
      expect(getPerformanceBudget("LCP")).toBeLessThanOrEqual(2500)
      expect(getPerformanceBudget("CLS")).toBeLessThanOrEqual(0.1)
      expect(getPerformanceBudget("FID")).toBeLessThanOrEqual(100)
      expect(getPerformanceBudget("TTFB")).toBeLessThanOrEqual(800)
    })

    it("returns null for unknown metrics", () => {
      expect(getPerformanceBudget("UNKNOWN_METRIC")).toBeNull()
    })
  })

  describe("isMetricWithinBudget", () => {
    it("correctly determines if metrics are within budget", () => {
      // These should be within budget
      expect(isMetricWithinBudget("FCP", 1000)).toBe(true)
      expect(isMetricWithinBudget("LCP", 2000)).toBe(true)
      expect(isMetricWithinBudget("CLS", 0.05)).toBe(true)

      // These should exceed budget
      expect(isMetricWithinBudget("FCP", 3000)).toBe(false)
      expect(isMetricWithinBudget("LCP", 4000)).toBe(false)
      expect(isMetricWithinBudget("CLS", 0.2)).toBe(false)
    })

    it("handles unknown metrics correctly", () => {
      expect(isMetricWithinBudget("UNKNOWN_METRIC", 100)).toBe(true)
    })
  })
})
