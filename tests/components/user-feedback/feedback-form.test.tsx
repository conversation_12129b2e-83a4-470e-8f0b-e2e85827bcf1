"use client"

import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import { FeedbackForm } from "@/components/user-feedback/feedback-form"
import { saveFeedback } from "@/utils/feedback-storage"

// Mock the feedback storage
jest.mock("@/utils/feedback-storage", () => ({
  saveFeedback: jest.fn().mockResolvedValue(undefined),
}))

describe("FeedbackForm", () => {
  const mockOnClose = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it("renders the form correctly", () => {
    render(<FeedbackForm onClose={mockOnClose} />)

    expect(screen.getByText("Share Your Feedback")).toBeInTheDocument()
    expect(screen.getByLabelText(/feedback type/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/your feedback/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByRole("button", { name: /submit feedback/i })).toBeInTheDocument()
  })

  it("closes when the close button is clicked", () => {
    render(<FeedbackForm onClose={mockOnClose} />)

    fireEvent.click(screen.getByRole("button", { name: /close feedback form/i }))

    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it("submits the form with correct data", async () => {
    // Mock Date.now for consistent ID generation
    const dateSpy = jest.spyOn(Date, "now").mockReturnValue(12345)

    render(<FeedbackForm onClose={mockOnClose} />)

    // Fill out the form
    fireEvent.change(screen.getByLabelText(/feedback type/i), {
      target: { value: "bug" },
    })

    fireEvent.change(screen.getByLabelText(/your feedback/i), {
      target: { value: "Test feedback message" },
    })

    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: "<EMAIL>" },
    })

    // Submit the form
    fireEvent.click(screen.getByRole("button", { name: /submit feedback/i }))

    // Check that saveFeedback was called with the correct data
    await waitFor(() => {
      expect(saveFeedback).toHaveBeenCalledWith(
        expect.objectContaining({
          id: "feedback_12345",
          type: "bug",
          message: "Test feedback message",
          email: "<EMAIL>",
          status: "new",
        }),
      )
    })

    // Check that success message is displayed
    await waitFor(() => {
      expect(screen.getByText("Thank you for your feedback!")).toBeInTheDocument()
    })

    // Form should automatically close after success
    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled()
    })

    dateSpy.mockRestore()
  })

  it("handles form validation", async () => {
    render(<FeedbackForm onClose={mockOnClose} />)

    // Try to submit with empty required fields
    fireEvent.click(screen.getByRole("button", { name: /submit feedback/i }))

    // Check that saveFeedback was not called
    expect(saveFeedback).not.toHaveBeenCalled()
  })
})
