"use client"

import { render, screen, fireEvent } from "@testing-library/react"
import { FeedbackButton } from "@/components/user-feedback/feedback-button"

// Mock the feedback form component
jest.mock("@/components/user-feedback/feedback-form", () => ({
  FeedbackForm: ({ onClose }: { onClose: () => void }) => (
    <div data-testid="feedback-form">
      <button onClick={onClose}>Close</button>
    </div>
  ),
}))

describe("FeedbackButton", () => {
  it("renders correctly", () => {
    render(<FeedbackButton />)
    expect(screen.getByRole("button", { name: /provide feedback/i })).toBeInTheDocument()
  })

  it("opens feedback form when clicked", () => {
    render(<FeedbackButton />)

    // Initially, form should not be visible
    expect(screen.queryByTestId("feedback-form")).not.toBeInTheDocument()

    // Click the button
    fireEvent.click(screen.getByRole("button", { name: /provide feedback/i }))

    // Form should be visible now
    expect(screen.getByTestId("feedback-form")).toBeInTheDocument()
  })

  it("closes feedback form when close is triggered", () => {
    render(<FeedbackButton />)

    // Open the form
    fireEvent.click(screen.getByRole("button", { name: /provide feedback/i }))

    // Form should be visible
    expect(screen.getByTestId("feedback-form")).toBeInTheDocument()

    // Click close button
    fireEvent.click(screen.getByRole("button", { name: /close/i }))

    // Form should be closed
    expect(screen.queryByTestId("feedback-form")).not.toBeInTheDocument()
  })
})
