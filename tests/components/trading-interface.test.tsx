import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'

// Mock all the dependencies
jest.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}))

jest.mock('@/components/wallet-provider', () => ({
  useWallet: () => ({
    isConnected: true,
    address: '0x123...',
    connect: jest.fn(),
    disconnect: jest.fn(),
  }),
}))

jest.mock('@/hooks/use-notification', () => ({
  useNotification: () => ({
    showNotification: jest.fn(),
  }),
}))

jest.mock('@/hooks/use-error-handler', () => ({
  useTradingErrorHandler: () => ({
    handleError: jest.fn(),
    clearError: jest.fn(),
    error: null,
  }),
}))

jest.mock('@/hooks/use-cached-data', () => ({
  useCachedPriceData: () => ({
    data: { price: 100, change: 5.2 },
    loading: false,
    error: null,
  }),
  useCachedChartData: () => ({
    data: [{ time: '2023-01-01', price: 100 }],
    loading: false,
    error: null,
  }),
}))

// Mock child components
jest.mock('@/components/trading-chart', () => ({
  TradingChart: ({ token }: { token: string }) => (
    <div data-testid="trading-chart">Chart for {token}</div>
  ),
}))

jest.mock('@/components/token-list', () => ({
  TokenList: () => <div data-testid="token-list">Token List</div>,
}))

jest.mock('@/components/liquidity-interface', () => ({
  LiquidityInterface: () => <div data-testid="liquidity-interface">Liquidity Interface</div>,
}))

jest.mock('@/components/ui/loading-state', () => ({
  LoadingSpinner: () => <div data-testid="loading-spinner">Loading...</div>,
  ChartSkeleton: () => <div data-testid="chart-skeleton">Chart Loading...</div>,
}))

jest.mock('@/components/ui/live-region', () => ({
  TradingStatusAnnouncer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="status-announcer">{children}</div>
  ),
}))

jest.mock('@/components/ui/error-recovery', () => ({
  InlineErrorRecovery: ({ onRetry }: { onRetry: () => void }) => (
    <div data-testid="error-recovery">
      <button onClick={onRetry}>Retry</button>
    </div>
  ),
}))

import { TradingInterface } from '@/components/trading-interface'

describe('TradingInterface Component', () => {
  it('renders the trading interface correctly', () => {
    render(<TradingInterface />)
    
    // Check for main trading elements
    expect(screen.getByText('Trade')).toBeInTheDocument()
    expect(screen.getByTestId('trading-chart')).toBeInTheDocument()
    expect(screen.getByTestId('token-list')).toBeInTheDocument()
  })

  it('shows swap interface by default', () => {
    render(<TradingInterface />)
    
    // Check for swap-related elements
    expect(screen.getByText('From')).toBeInTheDocument()
    expect(screen.getByText('To')).toBeInTheDocument()
  })

  it('allows switching between tabs', () => {
    render(<TradingInterface />)
    
    // Check for tab navigation
    const swapTab = screen.getByRole('tab', { name: /swap/i })
    const limitTab = screen.getByRole('tab', { name: /limit/i })
    
    expect(swapTab).toBeInTheDocument()
    expect(limitTab).toBeInTheDocument()
    
    // Click on limit tab
    fireEvent.click(limitTab)
    expect(limitTab).toHaveAttribute('aria-selected', 'true')
  })

  it('handles token selection', async () => {
    render(<TradingInterface />)
    
    // Find token selection buttons
    const tokenSelectors = screen.getAllByText(/select token/i)
    expect(tokenSelectors.length).toBeGreaterThan(0)
  })

  it('displays wallet connection status', () => {
    render(<TradingInterface />)
    
    // Since wallet is mocked as connected, should show trading interface
    expect(screen.getByText('Trade')).toBeInTheDocument()
  })
})
