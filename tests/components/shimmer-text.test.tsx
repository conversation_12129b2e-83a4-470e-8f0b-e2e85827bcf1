import { render, screen } from "@testing-library/react"
import { ShimmerText } from "@/components/shimmer-text"

describe("ShimmerText Component", () => {
  it("renders the text content correctly", () => {
    render(<ShimmerText>Test Content</ShimmerText>)
    expect(screen.getByText("Test Content")).toBeInTheDocument()
  })

  it("applies the shimmer effect class", () => {
    render(<ShimmerText>Test Content</ShimmerText>)
    const element = screen.getByText("Test Content")
    expect(element).toHaveClass("shimmer-text")
  })

  it("passes additional className to the component", () => {
    render(<ShimmerText className="custom-class">Test Content</ShimmerText>)
    const element = screen.getByText("Test Content")
    expect(element).toHaveClass("custom-class")
    expect(element).toHaveClass("shimmer-text")
  })

  it("renders with the correct tag when as prop is provided", () => {
    render(<ShimmerText as="h2">Heading Text</ShimmerText>)
    const heading = screen.getByRole("heading", { level: 2, name: "Heading Text" })
    expect(heading).toBeInTheDocument()
    expect(heading).toHaveClass("shimmer-text")
  })
})
