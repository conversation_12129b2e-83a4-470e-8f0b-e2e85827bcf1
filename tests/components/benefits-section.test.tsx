import { render, screen } from "@testing-library/react"
import { BenefitsSection } from "@/components/benefits-section"

describe("BenefitsSection", () => {
  it("renders the section correctly", () => {
    render(<BenefitsSection />)

    // Check for section title
    expect(screen.getByRole("heading", { level: 2 })).toBeInTheDocument()

    // Check for benefit cards
    const cards = screen.getAllByRole("article")
    expect(cards.length).toBeGreaterThan(0)

    // Each card should have a title and description
    cards.forEach((card) => {
      expect(card.querySelector("h3")).toBeInTheDocument()
      expect(card.querySelector("p")).toBeInTheDocument()
    })
  })

  it("renders the correct number of benefits", () => {
    render(<BenefitsSection />)

    // Count the number of benefit cards
    const cards = screen.getAllByRole("article")

    // Adjust this number based on your actual implementation
    expect(cards.length).toBe(3)
  })
})
