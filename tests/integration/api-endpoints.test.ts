import { setupMSW } from '../mocks/server'
import { http, HttpResponse } from 'msw'

// Setup MSW for this test file
setupMSW()

// Test API endpoints with MSW
describe('API Integration Tests', () => {
  describe('Price API', () => {
    it('fetches price data for DOGE', async () => {
      const response = await fetch('/api/price/DOGE')
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('price')
      expect(data).toHaveProperty('change24h')
      expect(data).toHaveProperty('volume24h')
      expect(data).toHaveProperty('marketCap')
      expect(data).toHaveProperty('timestamp')
      expect(typeof data.price).toBe('number')
    })

    it('fetches price data for PEPE', async () => {
      const response = await fetch('/api/price/PEPE')
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.price).toBe(0.00000123)
      expect(data.change24h).toBe(-2.1)
    })

    it('returns 404 for unknown token', async () => {
      const response = await fetch('/api/price/UNKNOWN')
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data).toHaveProperty('error')
      expect(data.error).toBe('Token not found')
    })

    it('fetches price data for all supported tokens', async () => {
      const tokens = ['DOGE', 'PEPE', 'FLOKI', 'SHIB', 'wDOGE', 'PAW']
      
      for (const token of tokens) {
        const response = await fetch(`/api/price/${token}`)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data).toHaveProperty('price')
        expect(data).toHaveProperty('change24h')
        expect(typeof data.price).toBe('number')
        expect(typeof data.change24h).toBe('number')
      }
    })
  })

  describe('Chart API', () => {
    it('fetches chart data for DOGE 1H timeframe', async () => {
      const response = await fetch('/api/chart/DOGE/1H')
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('token')
      expect(data).toHaveProperty('timeframe')
      expect(data).toHaveProperty('data')
      expect(data.token).toBe('DOGE')
      expect(data.timeframe).toBe('1H')
      expect(Array.isArray(data.data)).toBe(true)
      expect(data.data.length).toBeGreaterThan(0)
    })

    it('fetches chart data for different timeframes', async () => {
      const timeframes = ['1H', '4H', '1D', '1W']
      
      for (const timeframe of timeframes) {
        const response = await fetch(`/api/chart/DOGE/${timeframe}`)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.timeframe).toBe(timeframe)
        expect(Array.isArray(data.data)).toBe(true)
        
        // Check data structure
        if (data.data.length > 0) {
          const dataPoint = data.data[0]
          expect(dataPoint).toHaveProperty('timestamp')
          expect(dataPoint).toHaveProperty('price')
          expect(dataPoint).toHaveProperty('volume')
        }
      }
    })
  })

  describe('Token Launch API', () => {
    it('successfully launches a token with valid data', async () => {
      const tokenData = {
        name: 'Test Token',
        symbol: 'TEST',
        totalSupply: '1000000000',
        description: 'A test token'
      }

      const response = await fetch('/api/token/launch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tokenData),
      })

      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('success')
      expect(data.success).toBe(true)
      expect(data).toHaveProperty('tokenAddress')
      expect(data).toHaveProperty('transactionHash')
      expect(data.name).toBe(tokenData.name)
      expect(data.symbol).toBe(tokenData.symbol)
    })

    it('returns 400 for missing required fields', async () => {
      const invalidData = {
        description: 'Missing name and symbol'
      }

      const response = await fetch('/api/token/launch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invalidData),
      })

      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data).toHaveProperty('error')
      expect(data.error).toBe('Missing required fields')
    })
  })

  describe('Trading API', () => {
    it('successfully executes a swap', async () => {
      const swapData = {
        fromToken: 'DOGE',
        toToken: 'PEPE',
        amount: '100'
      }

      const response = await fetch('/api/trade/swap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(swapData),
      })

      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('success')
      expect(data.success).toBe(true)
      expect(data).toHaveProperty('outputAmount')
      expect(data).toHaveProperty('exchangeRate')
      expect(data).toHaveProperty('transactionHash')
      expect(data.fromToken).toBe(swapData.fromToken)
      expect(data.toToken).toBe(swapData.toToken)
    })

    it('returns 400 for invalid swap data', async () => {
      const invalidSwapData = {
        fromToken: 'DOGE'
        // Missing toToken and amount
      }

      const response = await fetch('/api/trade/swap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invalidSwapData),
      })

      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data).toHaveProperty('error')
    })
  })

  describe('Error Handling', () => {
    it('handles 500 server errors', async () => {
      const response = await fetch('/api/error/500')
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data).toHaveProperty('error')
      expect(data.error).toBe('Internal Server Error')
    })

    it('handles network errors gracefully', async () => {
      // Override handler to simulate network error
      server.use(
        http.get('/api/price/NETWORK_ERROR', () => {
          return HttpResponse.error()
        })
      )

      try {
        await fetch('/api/price/NETWORK_ERROR')
      } catch (error) {
        expect(error).toBeDefined()
      }
    })
  })
})
