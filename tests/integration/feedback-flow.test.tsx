import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import { FeedbackButton } from "@/components/user-feedback/feedback-button"
import { getAllFeedback } from "@/utils/feedback-storage"

// Unmock the modules for integration testing
jest.unmock("@/components/user-feedback/feedback-form")
jest.unmock("@/utils/feedback-storage")

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {}

  return {
    getItem: jest.fn((key: string) => {
      return store[key] || null
    }),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value
    }),
    clear: jest.fn(() => {
      store = {}
    }),
  }
})()

Object.defineProperty(window, "localStorage", {
  value: mockLocalStorage,
})

describe("Feedback Flow Integration", () => {
  beforeEach(() => {
    mockLocalStorage.clear.clear()
    jest.clearAllMocks()
  })

  afterEach(() => {
    mockLocalStorage.clear()
  })

  it("completes a full feedback submission flow", async () => {
    render(<FeedbackButton />)

    // Open the feedback form
    fireEvent.click(screen.getByRole("button", { name: /provide feedback/i }))

    // Form should be visible
    expect(screen.getByText("Share Your Feedback")).toBeInTheDocument()

    // Fill out the form
    fireEvent.change(screen.getByLabelText(/feedback type/i), {
      target: { value: "feature" },
    })

    fireEvent.change(screen.getByLabelText(/your feedback/i), {
      target: { value: "I would like to see a dark mode option" },
    })

    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: "<EMAIL>" },
    })

    // Submit the form
    fireEvent.click(screen.getByRole("button", { name: /submit feedback/i }))

    // Wait for the success message
    await waitFor(() => {
      expect(screen.getByText("Thank you for your feedback!")).toBeInTheDocument()
    })

    // Verify the feedback was saved
    expect(mockLocalStorage.setItem).toHaveBeenCalled()

    // Check that the correct data was saved
    const savedFeedback = getAllFeedback()
    expect(savedFeedback.length).toBe(1)
    expect(savedFeedback[0].type).toBe("feature")
    expect(savedFeedback[0].message).toBe("I would like to see a dark mode option")
    expect(savedFeedback[0].email).toBe("<EMAIL>")
    expect(savedFeedback[0].status).toBe("new")
  })
})
