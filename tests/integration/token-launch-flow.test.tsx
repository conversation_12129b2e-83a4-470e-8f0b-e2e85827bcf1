import type React from "react"
import { render, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { OptimizedTokenLaunchForm } from "@/components/optimized-token-launch-form"
import { WalletProvider } from "@/components/wallet-provider"
import { NotificationProvider } from "@/components/notification-provider"

// Mock the hooks and providers
jest.mock("@/hooks/use-notification", () => ({
  useNotification: () => ({
    showNotification: jest.fn(),
  }),
}))

jest.mock("@/components/ui/use-toast", () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}))

jest.mock("@/components/wallet-provider", () => ({
  WalletProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useWallet: () => ({
    isConnected: true,
    connect: jest.fn(),
    disconnect: jest.fn(),
    address: "0x1234567890abcdef",
  }),
}))

describe("Token Launch Flow", () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks()
  })

  it("allows users to fill out the form and submit", async () => {
    const user = userEvent.setup()

    render(
      <NotificationProvider>
        <WalletProvider>
          <OptimizedTokenLaunchForm />
        </WalletProvider>
      </NotificationProvider>,
    )

    // Fill out the form
    await user.type(screen.getByLabelText(/Token Name/i), "Test Token")
    await user.type(screen.getByLabelText(/Token Symbol/i), "TEST")
    await user.type(screen.getByLabelText(/Description/i), "This is a test token")

    // Select a bonding curve
    await user.click(screen.getByLabelText(/Exponential/i))

    // Submit the form
    await user.click(screen.getByRole("button", { name: /Launch Token/i }))

    // Wait for the submission to complete
    await waitFor(() => {
      expect(screen.queryByText(/Deploying Token/i)).not.toBeInTheDocument()
    })
  })

  it("shows validation errors for invalid inputs", async () => {
    const user = userEvent.setup()

    render(
      <NotificationProvider>
        <WalletProvider>
          <OptimizedTokenLaunchForm />
        </WalletProvider>
      </NotificationProvider>,
    )

    // Fill out the form with invalid data
    await user.type(screen.getByLabelText(/Token Name/i), "T") // Too short
    await user.type(screen.getByLabelText(/Token Symbol/i), "t") // Not uppercase

    // Submit the form
    await user.click(screen.getByRole("button", { name: /Launch Token/i }))

    // Check for validation errors
    expect(screen.getByText(/Token name must be at least 3 characters/i)).toBeInTheDocument()
    expect(screen.getByText(/Please enter a valid token symbol/i)).toBeInTheDocument()
  })
})
