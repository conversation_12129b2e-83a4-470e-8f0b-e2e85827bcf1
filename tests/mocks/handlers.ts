import { http, HttpResponse } from 'msw'

// Mock API responses
export const handlers = [
  // Price API endpoints
  http.get('/api/price/:token', ({ params }) => {
    const { token } = params
    
    const mockPrices: Record<string, any> = {
      DOGE: {
        price: 0.08234,
        change24h: 5.2,
        volume24h: 1234567890,
        marketCap: 11234567890,
        timestamp: Date.now()
      },
      PEPE: {
        price: 0.00000123,
        change24h: -2.1,
        volume24h: 987654321,
        marketCap: 5234567890,
        timestamp: Date.now()
      },
      FLOKI: {
        price: 0.000045,
        change24h: 8.7,
        volume24h: 456789123,
        marketCap: 2345678901,
        timestamp: Date.now()
      },
      SHIB: {
        price: 0.0000089,
        change24h: -1.3,
        volume24h: 2345678901,
        marketCap: 5678901234,
        timestamp: Date.now()
      },
      wDOGE: {
        price: 0.08234,
        change24h: 5.2,
        volume24h: 1234567890,
        marketCap: 11234567890,
        timestamp: Date.now()
      },
      PAW: {
        price: 0.00012,
        change24h: 12.5,
        volume24h: 345678901,
        marketCap: 1234567890,
        timestamp: Date.now()
      }
    }

    const priceData = mockPrices[token as string]
    
    if (!priceData) {
      return HttpResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      )
    }

    return HttpResponse.json(priceData)
  }),

  // Chart API endpoints
  http.get('/api/chart/:token/:timeframe', ({ params }) => {
    const { token, timeframe } = params
    
    // Generate mock chart data
    const generateChartData = (points: number) => {
      const data = []
      const basePrice = 0.08234
      const now = Date.now()
      
      for (let i = points; i >= 0; i--) {
        const timestamp = now - (i * 3600000) // 1 hour intervals
        const price = basePrice * (1 + (Math.random() - 0.5) * 0.1)
        data.push({
          timestamp,
          price: parseFloat(price.toFixed(8)),
          volume: Math.floor(Math.random() * 1000000)
        })
      }
      
      return data
    }

    const timeframePoints: Record<string, number> = {
      '1H': 24,
      '4H': 24,
      '1D': 30,
      '1W': 7
    }

    const points = timeframePoints[timeframe as string] || 24
    const chartData = generateChartData(points)

    return HttpResponse.json({
      token,
      timeframe,
      data: chartData
    })
  }),

  // Token launch API
  http.post('/api/token/launch', async ({ request }) => {
    const body = await request.json() as any

    // Simulate validation
    if (!body || !body.name || !body.symbol) {
      return HttpResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Simulate successful token launch
    return HttpResponse.json({
      success: true,
      tokenAddress: '0x1234567890abcdef1234567890abcdef12345678',
      transactionHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
      name: body.name,
      symbol: body.symbol,
      totalSupply: body.totalSupply || '1000000000',
      timestamp: Date.now()
    })
  }),

  // Trading API
  http.post('/api/trade/swap', async ({ request }) => {
    const body = await request.json() as any

    if (!body || !body.fromToken || !body.toToken || !body.amount) {
      return HttpResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Simulate swap calculation
    const exchangeRate = 0.95 // 5% slippage
    const outputAmount = parseFloat(body.amount) * exchangeRate

    return HttpResponse.json({
      success: true,
      fromToken: body.fromToken,
      toToken: body.toToken,
      inputAmount: body.amount,
      outputAmount: outputAmount.toString(),
      exchangeRate,
      transactionHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
      timestamp: Date.now()
    })
  }),

  // Error simulation endpoints
  http.get('/api/error/500', () => {
    return HttpResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }),

  http.get('/api/error/timeout', () => {
    // Simulate timeout by delaying response
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(HttpResponse.json({ error: 'Request timeout' }, { status: 408 }))
      }, 5000)
    })
  })
]
