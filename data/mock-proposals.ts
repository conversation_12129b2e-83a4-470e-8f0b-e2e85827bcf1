export type ProposalStatus = "active" | "passed" | "failed" | "pending" | "implemented"

export type CategoryType =
  | "core-functionality"
  | "user-experience"
  | "visual-design"
  | "mobile"
  | "performance"
  | "accessibility"
  | "content"
  | "technical"
  | "security"
  | "social"

export interface ProposalAction {
  type: string
  params: Record<string, any>
}

export interface Comment {
  id: string
  author: string
  authorAddress: string
  content: string
  timestamp: string
  replies?: Comment[]
}

export interface Proposal {
  id: string
  title: string
  description: string
  creator: string
  createdAt: string
  endTime: string
  status: ProposalStatus
  category: CategoryType
  votesFor: number
  votesAgainst: number
  quorum: number
  actions?: ProposalAction[]
  comments?: Comment[]
}

// Mock data
export const mockProposals: Proposal[] = [
  {
    id: "PROP-001",
    title: "Add Perpetual Trading",
    description:
      "Integrate perpetual trading functionality to allow leveraged trading of memecoin pairs.\n\nThis proposal aims to expand our trading capabilities by adding perpetual futures contracts. This will allow users to trade with leverage and take both long and short positions on various memecoin pairs.\n\nThe implementation will include:\n- Initial support for up to 10x leverage\n- Liquidation mechanisms to protect the platform\n- Funding rate calculations to balance long and short positions\n- Risk management tools for users",
    creator: "0x1234...5678",
    createdAt: "2025-05-01",
    endTime: "2025-05-08",
    status: "active",
    category: "core-functionality",
    votesFor: 1250000,
    votesAgainst: 450000,
    quorum: 2000000,
    comments: [
      {
        id: "comment-1",
        author: "0xabcd...1234",
        authorAddress: "0xabcd1234567890abcdef1234567890abcdef1234",
        content:
          "I strongly support this proposal. Adding perpetual trading will bring more users to the platform and increase trading volume.",
        timestamp: "2025-05-02T14:23:45Z",
        replies: [
          {
            id: "reply-1",
            author: "0x9876...5432",
            authorAddress: "0x9876543210abcdef9876543210abcdef98765432",
            content: "Agreed! This is a must-have feature for any modern trading platform.",
            timestamp: "2025-05-02T15:12:30Z",
          },
        ],
      },
      {
        id: "comment-2",
        author: "0xdef0...7890",
        authorAddress: "0xdef0123456789abcdef0123456789abcdef0123",
        content:
          "I'm concerned about the risk management. How will liquidations be handled to prevent cascading failures?",
        timestamp: "2025-05-03T09:45:12Z",
        replies: [],
      },
    ],
  },
  {
    id: "PROP-002",
    title: "Reduce Trading Fees",
    description:
      "Reduce trading fees from 0.5% to 0.3% to increase volume and attract more traders.\n\nBy lowering our trading fees, we aim to:\n- Increase overall trading volume\n- Attract more traders from competing platforms\n- Improve liquidity across all trading pairs\n\nThe proposed fee reduction would apply to all trading pairs and would be implemented immediately upon proposal approval.",
    creator: "0x8765...4321",
    createdAt: "2025-04-28",
    endTime: "2025-05-05",
    status: "passed",
    category: "core-functionality",
    votesFor: 1800000,
    votesAgainst: 200000,
    quorum: 1500000,
    actions: [
      {
        type: "updateFee",
        params: {
          feeType: "trading",
          oldValue: "0.5%",
          newValue: "0.3%",
        },
      },
    ],
    comments: [
      {
        id: "comment-3",
        author: "0x1111...2222",
        authorAddress: "0x1111222233334444555566667777888899990000",
        content: "This is a great move! Lower fees will definitely attract more traders.",
        timestamp: "2025-04-29T10:30:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "PROP-003",
    title: "Add New Token Pair",
    description:
      "Add SHIB/wDOGE as a new trading pair with initial liquidity incentives.\n\nThis proposal suggests adding SHIB/wDOGE as a new trading pair on our platform. To bootstrap liquidity, we would allocate 100,000 $PAW tokens as liquidity mining rewards over a 3-month period.\n\nThe addition of this popular trading pair would attract new users and increase overall platform activity.",
    creator: "0x5678...1234",
    createdAt: "2025-04-25",
    endTime: "2025-05-02",
    status: "failed",
    category: "core-functionality",
    votesFor: 800000,
    votesAgainst: 1200000,
    quorum: 1500000,
    comments: [],
  },
  {
    id: "PROP-004",
    title: "Implement Token Burning Mechanism",
    description:
      "Implement a 0.1% token burn on each transaction to create deflationary pressure on $PAW.\n\nThis proposal aims to create a deflationary mechanism for the $PAW token by implementing a 0.1% burn on every transaction. This would reduce the total supply over time, potentially increasing the value of remaining tokens.\n\nThe burn would apply to all transactions involving $PAW tokens, including trading, staking, and governance actions.",
    creator: "0x4321...8765",
    createdAt: "2025-05-02",
    endTime: "2025-05-09",
    status: "pending",
    category: "core-functionality",
    votesFor: 0,
    votesAgainst: 0,
    quorum: 2000000,
    comments: [],
  },
  {
    id: "PROP-005",
    title: "Add Multi-Chain Support",
    description:
      "Expand beyond Dogechain to support Ethereum, Binance Smart Chain, and other EVM chains.\n\nThis proposal suggests expanding our platform to support multiple blockchains, including Ethereum, Binance Smart Chain, and other EVM-compatible chains.\n\nThe implementation would involve:\n- Developing cross-chain bridges for seamless asset transfers\n- Deploying our contracts on multiple chains\n- Creating a unified interface for users to interact with any supported chain\n- Implementing chain-specific optimizations for gas fees and transaction speeds",
    creator: "0x1234...5678",
    createdAt: "2025-05-03",
    endTime: "2025-05-10",
    status: "active",
    category: "technical",
    votesFor: 950000,
    votesAgainst: 350000,
    quorum: 2000000,
    comments: [],
  },
  {
    id: "PROP-006",
    title: "Implement Dark Mode",
    description:
      "Add a dark mode option for the entire platform to reduce eye strain and save battery.\n\nThis proposal aims to implement a comprehensive dark mode option across the entire platform. Users would be able to toggle between light and dark modes based on their preferences.\n\nThe dark mode would follow accessibility best practices to ensure proper contrast and readability for all users.",
    creator: "0x8765...4321",
    createdAt: "2025-05-02",
    endTime: "2025-05-09",
    status: "active",
    category: "user-experience",
    votesFor: 1200000,
    votesAgainst: 100000,
    quorum: 2000000,
    comments: [
      {
        id: "comment-4",
        author: "0x3333...4444",
        authorAddress: "0x3333444455556666777788889999000011112222",
        content: "Finally! Dark mode is essential for reducing eye strain during night trading.",
        timestamp: "2025-05-03T22:15:00Z",
        replies: [
          {
            id: "reply-2",
            author: "0x5555...6666",
            authorAddress: "0x5555666677778888999900001111222233334444",
            content: "Agreed! I've been waiting for this feature for a long time.",
            timestamp: "2025-05-03T23:05:30Z",
          },
        ],
      },
    ],
  },
]
