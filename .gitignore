# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# Testing
/coverage
/test-results
/playwright-report
/lighthouse-report
*.lcov
*.xml
results.json
results.xml

# Next.js
/.next/
/out/
next-env.d.ts

# Production
/build
/dist

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production

# Vercel
.vercel

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Turbo
.turbo

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Audit and test results
accessibility-audit-results.json
cross-browser-test-results.json
mobile-responsiveness-results.json
performance-audit-results.json
performance-budget.json
lighthouse-*.json

# Bundle analysis
.next/analyze/

# Database
*.db
*.sqlite
*.sqlite3

# Cache
.cache/
.parcel-cache/

# Backup files
*.backup
*.bak
*.tmp

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Local development files
.env.local
.env.development.local
.env.test.local

# Production files (should not be in repo)
.env.production

# Certificates
*.pem
*.key
*.crt

# Docker
.dockerignore
Dockerfile.local

# Kubernetes
k8s/secrets/

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# AWS
.aws/

# Monitoring
.lighthouseci/

# Sentry
.sentryclirc

# Chromatic
chromatic.log

# Playwright
test-results/
playwright-report/
playwright/.cache/
