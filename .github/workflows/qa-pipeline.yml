name: QA Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  CACHE_KEY: node-modules-${{ hashFiles('**/package-lock.json') }}

jobs:
  # Job 1: Code Quality & Linting
  code-quality:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
      
    - name: Run ESLint
      run: npm run lint
      
    - name: Run TypeScript check
      run: npm run type-check
      
    - name: Check code formatting
      run: npm run format:check

  # Job 2: Unit & Integration Tests
  unit-tests:
    name: Unit & Integration Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
      
    - name: Run unit tests
      run: npm run test:ci
      
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        
    - name: Archive test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: unit-test-results
        path: |
          coverage/
          test-results/

  # Job 3: Build & Bundle Analysis
  build-test:
    name: Build & Bundle Analysis
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
      
    - name: Build application
      run: npm run build
      
    - name: Analyze bundle size
      run: npm run size-check
      
    - name: Archive build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-artifacts
        path: |
          .next/
          out/

  # Job 4: E2E Tests
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, build-test]
    
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
      
    - name: Install Playwright browsers
      run: npx playwright install --with-deps ${{ matrix.browser }}
      
    - name: Run E2E tests
      run: npx playwright test --project=${{ matrix.browser }}
      env:
        CI: true
        
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results-${{ matrix.browser }}
        path: |
          test-results/
          playwright-report/

  # Job 5: Performance Testing
  performance-tests:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: build-test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
      
    - name: Build application
      run: npm run build
      
    - name: Start application
      run: npm start &
      env:
        PORT: 3000
        
    - name: Wait for application
      run: npx wait-on http://localhost:3000 --timeout 60000
      
    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # Job 6: Security Scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
      
    - name: Run npm audit
      run: npm audit --audit-level=moderate
      
    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

  # Job 7: Accessibility Testing
  accessibility-tests:
    name: Accessibility Testing
    runs-on: ubuntu-latest
    needs: build-test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
      
    - name: Build application
      run: npm run build
      
    - name: Start application
      run: npm start &
      env:
        PORT: 3000
        
    - name: Wait for application
      run: npx wait-on http://localhost:3000 --timeout 60000
      
    - name: Run accessibility tests
      run: |
        npm install -g @axe-core/cli
        axe http://localhost:3000 --exit
        axe http://localhost:3000/trade --exit
        axe http://localhost:3000/launch --exit

  # Job 8: QA Report Generation
  qa-report:
    name: Generate QA Report
    runs-on: ubuntu-latest
    needs: [unit-tests, e2e-tests, performance-tests, security-scan, accessibility-tests]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download all artifacts
      uses: actions/download-artifact@v3
      
    - name: Generate QA report
      run: |
        echo "# QA Pipeline Report" > qa-report.md
        echo "**Date**: $(date)" >> qa-report.md
        echo "**Commit**: ${{ github.sha }}" >> qa-report.md
        echo "**Branch**: ${{ github.ref_name }}" >> qa-report.md
        echo "" >> qa-report.md
        
        # Add test results summary
        echo "## Test Results Summary" >> qa-report.md
        echo "- Unit Tests: ${{ needs.unit-tests.result }}" >> qa-report.md
        echo "- E2E Tests: ${{ needs.e2e-tests.result }}" >> qa-report.md
        echo "- Performance: ${{ needs.performance-tests.result }}" >> qa-report.md
        echo "- Security: ${{ needs.security-scan.result }}" >> qa-report.md
        echo "- Accessibility: ${{ needs.accessibility-tests.result }}" >> qa-report.md
        
    - name: Upload QA report
      uses: actions/upload-artifact@v3
      with:
        name: qa-report
        path: qa-report.md
        
    - name: Comment PR with results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('qa-report.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: report
          });

  # Job 9: Deployment Gate
  deployment-gate:
    name: Deployment Gate
    runs-on: ubuntu-latest
    needs: [unit-tests, e2e-tests, performance-tests, security-scan, accessibility-tests]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Check all tests passed
      run: |
        if [[ "${{ needs.unit-tests.result }}" != "success" || 
              "${{ needs.e2e-tests.result }}" != "success" || 
              "${{ needs.performance-tests.result }}" != "success" || 
              "${{ needs.security-scan.result }}" != "success" || 
              "${{ needs.accessibility-tests.result }}" != "success" ]]; then
          echo "❌ Quality gates failed. Deployment blocked."
          exit 1
        else
          echo "✅ All quality gates passed. Ready for deployment."
        fi
        
    - name: Trigger deployment
      if: success()
      run: echo "🚀 Triggering production deployment..."
      # Add your deployment trigger here
