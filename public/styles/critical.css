/* Critical CSS for above-the-fold content */
/* This CSS is inlined to prevent render-blocking */

/* CSS Variables for dark theme */
:root {
  --background: 240 10% 4%;
  --foreground: 0 0% 88%;
  --card: 240 10% 6%;
  --card-foreground: 0 0% 88%;
  --popover: 240 10% 6%;
  --popover-foreground: 0 0% 88%;
  --primary: 0 0% 98%;
  --primary-foreground: 240 5.9% 10%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 4.9% 83.9%;
  --radius: 0.5rem;
}

/* Base styles */
* {
  border-color: hsl(var(--border));
}

html {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: Inter, system-ui, -apple-system, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

/* Layout containers */
.min-h-screen {
  min-height: 100vh;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-1 {
  flex: 1 1 0%;
}

.relative {
  position: relative;
}

/* Navigation styles */
nav {
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Hero section critical styles */
.hero-section {
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.1) 0%, 
    rgba(59, 130, 246, 0.1) 50%, 
    rgba(16, 185, 129, 0.1) 100%);
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem 1rem;
}

.hero-title {
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #8B5CF6, #3B82F6, #10B981);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hero-subtitle {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, #8B5CF6, #3B82F6);
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

/* Glass effect utilities */
.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
}

.glass-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  transition: all 0.2s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
}

/* Loading states */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .hero-section {
    min-height: 50vh;
    padding: 1rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
}

/* Arc Browser specific fixes */
html[data-arc-browser="true"] {
  background-color: hsl(240, 10%, 4%) !important;
  background: hsl(240, 10%, 4%) !important;
}

html[data-arc-browser="true"] body {
  background-color: hsl(240, 10%, 4%) !important;
  background: hsl(240, 10%, 4%) !important;
}

/* Arc Browser text effects fixes - preserve original text-shadow effects */
html[data-arc-browser="true"] .doge-text-glow {
  text-shadow: 0 0 10px rgba(255, 193, 7, 0.5) !important;
}

html[data-arc-browser="true"] .dogechain-text-glow {
  text-shadow: 0 0 10px rgba(138, 43, 226, 0.5) !important;
}

/* Only apply gradient to elements that should have gradients */
html[data-arc-browser="true"] .hero-title {
  background: linear-gradient(135deg, #8B5CF6, #3B82F6, #10B981) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
}

/* Arc Browser background fixes */
html[data-arc-browser="true"] .hero-section {
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.1) 0%,
    rgba(59, 130, 246, 0.1) 50%,
    rgba(16, 185, 129, 0.1) 100%) !important;
}

/* Arc Browser glass effects fixes */
html[data-arc-browser="true"] .glass-card {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

html[data-arc-browser="true"] .glass-button {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Prevent layout shift */
.prevent-layout-shift {
  min-height: 1px;
  contain: layout style paint;
}

/* Critical font loading */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/fonts/inter-var.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Hide non-critical content initially */
.defer-load {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.defer-load.loaded {
  opacity: 1;
}
