// Service Worker for PawPumps
// Implements intelligent caching strategies for optimal performance

const CACHE_NAME = 'pawpumps-v1'
const STATIC_CACHE = 'pawpumps-static-v1'
const DYNAMIC_CACHE = 'pawpumps-dynamic-v1'
const API_CACHE = 'pawpumps-api-v1'

// Cache strategies
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  NETWORK_ONLY: 'network-only',
  CACHE_ONLY: 'cache-only'
}

// Resources to cache immediately
const STATIC_ASSETS = [
  '/',
  '/styles/critical.css',
  '/fonts/inter-var.woff2',
  '/_next/static/css/app/layout.css',
  '/_next/static/chunks/main.js',
  '/_next/static/chunks/webpack.js',
  '/_next/static/chunks/polyfills.js'
]

// Cache configuration for different resource types
const CACHE_CONFIG = {
  // Static assets - cache first, long TTL
  static: {
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    maxEntries: 100
  },
  // JavaScript bundles - stale while revalidate
  js: {
    strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE,
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    maxEntries: 50
  },
  // CSS files - cache first
  css: {
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    maxEntries: 20
  },
  // Images - cache first, long TTL
  images: {
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    maxEntries: 200
  },
  // API responses - network first, short TTL
  api: {
    strategy: CACHE_STRATEGIES.NETWORK_FIRST,
    maxAge: 5 * 60 * 1000, // 5 minutes
    maxEntries: 100
  },
  // Fonts - cache first, very long TTL
  fonts: {
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
    maxEntries: 10
  }
}

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Caching static assets...')
        return cache.addAll(STATIC_ASSETS)
      })
      .then(() => {
        console.log('Static assets cached successfully')
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error('Failed to cache static assets:', error)
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== API_CACHE) {
              console.log('Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('Service Worker activated')
        return self.clients.claim()
      })
  )
})

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return
  }
  
  event.respondWith(handleRequest(request))
})

// Handle different types of requests
async function handleRequest(request) {
  const url = new URL(request.url)
  const pathname = url.pathname
  
  try {
    // API requests
    if (pathname.startsWith('/api/')) {
      return handleAPIRequest(request)
    }
    
    // Static assets
    if (pathname.startsWith('/_next/static/')) {
      return handleStaticAsset(request)
    }
    
    // Images
    if (isImageRequest(pathname)) {
      return handleImageRequest(request)
    }
    
    // Fonts
    if (isFontRequest(pathname)) {
      return handleFontRequest(request)
    }
    
    // CSS files
    if (pathname.endsWith('.css')) {
      return handleCSSRequest(request)
    }
    
    // JavaScript files
    if (pathname.endsWith('.js')) {
      return handleJSRequest(request)
    }
    
    // HTML pages
    return handlePageRequest(request)
    
  } catch (error) {
    console.error('Error handling request:', error)
    return fetch(request)
  }
}

// Handle API requests with network-first strategy
async function handleAPIRequest(request) {
  const cache = await caches.open(API_CACHE)
  
  try {
    // Try network first
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      // Cache successful responses
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    // Fallback to cache
    const cachedResponse = await cache.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    throw error
  }
}

// Handle static assets with cache-first strategy
async function handleStaticAsset(request) {
  const cache = await caches.open(STATIC_CACHE)
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse) {
    return cachedResponse
  }
  
  const networkResponse = await fetch(request)
  if (networkResponse.ok) {
    cache.put(request, networkResponse.clone())
  }
  
  return networkResponse
}

// Handle images with cache-first strategy
async function handleImageRequest(request) {
  const cache = await caches.open(DYNAMIC_CACHE)
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse) {
    return cachedResponse
  }
  
  const networkResponse = await fetch(request)
  if (networkResponse.ok) {
    cache.put(request, networkResponse.clone())
  }
  
  return networkResponse
}

// Handle fonts with cache-first strategy
async function handleFontRequest(request) {
  const cache = await caches.open(STATIC_CACHE)
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse) {
    return cachedResponse
  }
  
  const networkResponse = await fetch(request)
  if (networkResponse.ok) {
    cache.put(request, networkResponse.clone())
  }
  
  return networkResponse
}

// Handle CSS with cache-first strategy
async function handleCSSRequest(request) {
  const cache = await caches.open(STATIC_CACHE)
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse) {
    return cachedResponse
  }
  
  const networkResponse = await fetch(request)
  if (networkResponse.ok) {
    cache.put(request, networkResponse.clone())
  }
  
  return networkResponse
}

// Handle JavaScript with stale-while-revalidate strategy
async function handleJSRequest(request) {
  const cache = await caches.open(DYNAMIC_CACHE)
  const cachedResponse = await cache.match(request)
  
  // Return cached version immediately if available
  if (cachedResponse) {
    // Update cache in background
    fetch(request).then((networkResponse) => {
      if (networkResponse.ok) {
        cache.put(request, networkResponse.clone())
      }
    }).catch(() => {
      // Ignore network errors for background updates
    })
    
    return cachedResponse
  }
  
  // No cached version, fetch from network
  const networkResponse = await fetch(request)
  if (networkResponse.ok) {
    cache.put(request, networkResponse.clone())
  }
  
  return networkResponse
}

// Handle page requests with network-first strategy
async function handlePageRequest(request) {
  try {
    return await fetch(request)
  } catch (error) {
    // Fallback to cached version or offline page
    const cache = await caches.open(DYNAMIC_CACHE)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    // Return cached homepage as fallback
    return cache.match('/')
  }
}

// Utility functions
function isImageRequest(pathname) {
  return /\.(jpg|jpeg|png|gif|webp|avif|svg)$/i.test(pathname)
}

function isFontRequest(pathname) {
  return /\.(woff|woff2|ttf|otf|eot)$/i.test(pathname)
}

// Clean up old cache entries
async function cleanupCache(cacheName, maxEntries, maxAge) {
  const cache = await caches.open(cacheName)
  const requests = await cache.keys()
  
  // Remove old entries
  const now = Date.now()
  const expiredRequests = []
  
  for (const request of requests) {
    const response = await cache.match(request)
    const dateHeader = response.headers.get('date')
    
    if (dateHeader) {
      const responseDate = new Date(dateHeader).getTime()
      if (now - responseDate > maxAge) {
        expiredRequests.push(request)
      }
    }
  }
  
  // Remove expired entries
  await Promise.all(expiredRequests.map(request => cache.delete(request)))
  
  // Remove excess entries if over limit
  if (requests.length > maxEntries) {
    const excessRequests = requests.slice(maxEntries)
    await Promise.all(excessRequests.map(request => cache.delete(request)))
  }
}

// Periodic cache cleanup
setInterval(() => {
  cleanupCache(DYNAMIC_CACHE, 100, 7 * 24 * 60 * 60 * 1000) // 7 days
  cleanupCache(API_CACHE, 50, 60 * 60 * 1000) // 1 hour
}, 60 * 60 * 1000) // Run every hour
