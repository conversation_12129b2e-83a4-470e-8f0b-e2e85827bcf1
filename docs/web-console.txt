evmAsk.js:5 Uncaught TypeError: Cannot redefine property: ethereum
    at Object.defineProperty (<anonymous>)
    at r.inject (evmAsk.js:5:5093)
    at window.addEventListener.once (evmAsk.js:5:9013)
inject @ evmAsk.js:5
window.addEventListener.once @ evmAsk.js:5
await in window.addEventListener.once
(anonymous) @ contentScript.js:9
rR @ evmAsk.js:5
(anonymous) @ evmAsk.js:5
await in (anonymous)
(anonymous) @ evmAsk.js:5
(anonymous) @ evmAsk.js:5
inpage.js:1 MetaMask encountered an error setting the global Ethereum provider - this is likely due to another Ethereum wallet extension also setting the global Ethereum provider: TypeError: Cannot set property ethereum of #<Window> which has only a getter
    at a (inpage.js:1:49400)
    at r.initializeProvider (inpage.js:1:49171)
    at Object.<anonymous> (inpage.js:1:2312)
    at Object.<anonymous> (inpage.js:1:6320)
    at 2.../../shared/modules/provider-injection (inpage.js:1:6333)
    at s (inpage.js:1:254)
    at e (inpage.js:1:414)
    at inpage.js:1:431
a @ inpage.js:1
r.initializeProvider @ inpage.js:1
(anonymous) @ inpage.js:1
(anonymous) @ inpage.js:1
2.../../shared/modules/provider-injection @ inpage.js:1
s @ inpage.js:1
e @ inpage.js:1
(anonymous) @ inpage.js:1
solanaActionsContentScript.js:27 Uncaught (in promise) TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at solanaActionsContentScript.js:27:41730
(anonymous) @ solanaActionsContentScript.js:27
Promise.then
mI @ solanaActionsContentScript.js:27
pv @ solanaActionsContentScript.js:27
E4 @ solanaActionsContentScript.js:1214
await in E4
(anonymous) @ solanaActionsContentScript.js:1214
(anonymous) @ solanaActionsContentScript.js:1214
main-app.js?v=1751130160720:2271 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
logger.ts:86 [2025-06-28T17:02:43.105Z] INFO: Performance Metric: TCP Connection = 0.1ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.105Z",
  "component": "Performance",
  "metadata": {
    "metric": "TCP Connection",
    "value": 0.1
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.105Z] INFO: Performance Metric: Request = 112.5ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.105Z",
  "component": "Performance",
  "metadata": {
    "metric": "Request",
    "value": 112.5
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.105Z] INFO: Performance Metric: Response = 19.7ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.105Z",
  "component": "Performance",
  "metadata": {
    "metric": "Response",
    "value": 19.7
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.105Z] INFO: Performance Metric: FCP = 840ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.105Z",
  "component": "Performance",
  "metadata": {
    "metric": "FCP",
    "value": 840
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.105Z] INFO: Performance Metric: LCP = 840ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.105Z",
  "component": "Performance",
  "metadata": {
    "metric": "LCP",
    "value": 840
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.105Z] INFO: Performance Metric: Resource: JavaScript = 1029.5ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.105Z",
  "component": "Performance",
  "metadata": {
    "metric": "Resource: JavaScript",
    "value": 1029.5
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.105Z] INFO: Performance Metric: Resource: JavaScript = 2042.7ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.105Z",
  "component": "Performance",
  "metadata": {
    "metric": "Resource: JavaScript",
    "value": 2042.7
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.105Z] INFO: Performance Metric: Resource: JavaScript = 1054.3ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.105Z",
  "component": "Performance",
  "metadata": {
    "metric": "Resource: JavaScript",
    "value": 1054.3
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.105Z] INFO: Performance Metric: Resource: JavaScript = 1125.7ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.105Z",
  "component": "Performance",
  "metadata": {
    "metric": "Resource: JavaScript",
    "value": 1125.7
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.105Z] INFO: Performance Metric: Resource: JavaScript = 1200.4ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.105Z",
  "component": "Performance",
  "metadata": {
    "metric": "Resource: JavaScript",
    "value": 1200.4
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.156Z] INFO: Performance Metric: Page Load Time = 2425ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.156Z",
  "component": "Performance",
  "metadata": {
    "metric": "Page Load Time",
    "value": 2425
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.156Z] INFO: Performance Metric: TCP Connection = 0.1ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.156Z",
  "component": "Performance",
  "metadata": {
    "metric": "TCP Connection",
    "value": 0.1
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.156Z] INFO: Performance Metric: Request = 112.5ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.156Z",
  "component": "Performance",
  "metadata": {
    "metric": "Request",
    "value": 112.5
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.156Z] INFO: Performance Metric: Response = 19.7ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.156Z",
  "component": "Performance",
  "metadata": {
    "metric": "Response",
    "value": 19.7
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.156Z] INFO: Performance Metric: DOM Processing = 1623.9ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.156Z",
  "component": "Performance",
  "metadata": {
    "metric": "DOM Processing",
    "value": 1623.9
  },
  "action": "metric"
}
logger.ts:86 [2025-06-28T17:02:43.156Z] INFO: Performance Metric: Load Complete = 0.1ms
Context: {
  "url": "http://localhost:3000/",
  "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "timestamp": "2025-06-28T17:02:43.156Z",
  "component": "Performance",
  "metadata": {
    "metric": "Load Complete",
    "value": 0.1
  },
  "action": "metric"
}
arc-browser-detector.tsx:19 Arc Browser detected - applying theme fixes
arc-browser-detector.tsx:111 Uncaught ReferenceError: html is not defined
    at ArcBrowserDetector.useEffect.detectAndFixArcBrowser (arc-browser-detector.tsx:111:11)
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
V.useCallback[S].L @ index.mjs:67
V.useCallback[S] @ index.mjs:70
V.useEffect @ index.mjs:121
react-stack-bottom-frame @ react-dom-client.development.js:24036
runWithFiberInDEV @ react-dom-client.development.js:1511
commitHookEffectListMount @ react-dom-client.development.js:10515
commitHookPassiveMountEffects @ react-dom-client.development.js:10636
commitPassiveMountOnFiber @ react-dom-client.development.js:12442
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12454
flushPassiveEffects @ react-dom-client.development.js:15796
flushPendingEffects @ react-dom-client.development.js:15761
performSyncWorkOnRoot @ react-dom-client.development.js:16287
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
flushSpawnedWork @ react-dom-client.development.js:15665
commitRoot @ react-dom-client.development.js:15391
commitRootWhenReady @ react-dom-client.development.js:14644
performWorkOnRoot @ react-dom-client.development.js:14567
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16275
performWorkUntilDeadline @ scheduler.development.js:45
<V>
exports.createElement @ react.development.js:1022
J @ index.mjs:47
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooksAgain @ react-dom-client.development.js:5179
renderWithHooks @ react-dom-client.development.js:5091
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopConcurrentByScheduler @ react-dom-client.development.js:15114
renderRootConcurrent @ react-dom-client.development.js:15089
performWorkOnRoot @ react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16275
performWorkUntilDeadline @ scheduler.development.js:45
<J>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
ThemeProvider @ theme-provider.tsx:113
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooksAgain @ react-dom-client.development.js:5179
renderWithHooks @ react-dom-client.development.js:5091
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9894
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopConcurrentByScheduler @ react-dom-client.development.js:15114
renderRootConcurrent @ react-dom-client.development.js:15089
performWorkOnRoot @ react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16275
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
RootLayout @ layout.tsx:105
eval @ react-server-dom-webpack-client.browser.development.js:2335
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
getOutlinedModel @ react-server-dom-webpack-client.browser.development.js:1307
parseModelString @ react-server-dom-webpack-client.browser.development.js:1520
eval @ react-server-dom-webpack-client.browser.development.js:2274
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1011
resolveModel @ react-server-dom-webpack-client.browser.development.js:1579
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2268
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
<RootLayout>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2020
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2007
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2043
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2241
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1567
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2376
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2696
eval @ app-index.js:133
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1751130160720:149
options.factory @ webpack.js?v=1751130160720:700
__webpack_require__ @ webpack.js?v=1751130160720:37
fn @ webpack.js?v=1751130160720:357
eval @ app-next-dev.js:10
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:9
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1751130160720:171
options.factory @ webpack.js?v=1751130160720:700
__webpack_require__ @ webpack.js?v=1751130160720:37
__webpack_exec__ @ main-app.js?v=1751130160720:2781
(anonymous) @ main-app.js?v=1751130160720:2782
webpackJsonpCallback @ webpack.js?v=1751130160720:1376
(anonymous) @ main-app.js?v=1751130160720:9
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ArcBrowserDetector.useEffect.detectAndFixArcBrowser.applyArcFix @ arc-browser-detector.tsx:31
setTimeout
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:104
ArcBrowserDetector.useEffect @ arc-browser-detector.tsx:132
react-stack-bottom-frame @ react-dom-client.development.js:24036
runWithFiberInDEV @ react-dom-client.development.js:1511
commitHookEffectListMount @ react-dom-client.development.js:10515
commitHookPassiveMountEffects @ react-dom-client.development.js:10636
commitPassiveMountOnFiber @ react-dom-client.development.js:12442
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12454
flushPassiveEffects @ react-dom-client.development.js:15796
flushPendingEffects @ react-dom-client.development.js:15761
performSyncWorkOnRoot @ react-dom-client.development.js:16287
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
flushSpawnedWork @ react-dom-client.development.js:15665
commitRoot @ react-dom-client.development.js:15391
commitRootWhenReady @ react-dom-client.development.js:14644
performWorkOnRoot @ react-dom-client.development.js:14567
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16275
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
RootLayout @ layout.tsx:106
eval @ react-server-dom-webpack-client.browser.development.js:2335
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
getOutlinedModel @ react-server-dom-webpack-client.browser.development.js:1307
parseModelString @ react-server-dom-webpack-client.browser.development.js:1520
eval @ react-server-dom-webpack-client.browser.development.js:2274
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1011
resolveModel @ react-server-dom-webpack-client.browser.development.js:1579
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2268
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
<RootLayout>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2020
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2007
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2043
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2241
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1567
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2376
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2696
eval @ app-index.js:133
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1751130160720:149
options.factory @ webpack.js?v=1751130160720:700
__webpack_require__ @ webpack.js?v=1751130160720:37
fn @ webpack.js?v=1751130160720:357
eval @ app-next-dev.js:10
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:9
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1751130160720:171
options.factory @ webpack.js?v=1751130160720:700
__webpack_require__ @ webpack.js?v=1751130160720:37
__webpack_exec__ @ main-app.js?v=1751130160720:2781
(anonymous) @ main-app.js?v=1751130160720:2782
webpackJsonpCallback @ webpack.js?v=1751130160720:1376
(anonymous) @ main-app.js?v=1751130160720:9
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
attributes
ArcBrowserDetector.useEffect.detectAndFixArcBrowser.applyArcFix @ arc-browser-detector.tsx:31
setTimeout
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:104
ArcBrowserDetector.useEffect @ arc-browser-detector.tsx:132
react-stack-bottom-frame @ react-dom-client.development.js:24036
runWithFiberInDEV @ react-dom-client.development.js:1511
commitHookEffectListMount @ react-dom-client.development.js:10515
commitHookPassiveMountEffects @ react-dom-client.development.js:10636
commitPassiveMountOnFiber @ react-dom-client.development.js:12442
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12454
flushPassiveEffects @ react-dom-client.development.js:15796
flushPendingEffects @ react-dom-client.development.js:15761
performSyncWorkOnRoot @ react-dom-client.development.js:16287
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
flushSpawnedWork @ react-dom-client.development.js:15665
commitRoot @ react-dom-client.development.js:15391
commitRootWhenReady @ react-dom-client.development.js:14644
performWorkOnRoot @ react-dom-client.development.js:14567
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16275
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
RootLayout @ layout.tsx:106
eval @ react-server-dom-webpack-client.browser.development.js:2335
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
getOutlinedModel @ react-server-dom-webpack-client.browser.development.js:1307
parseModelString @ react-server-dom-webpack-client.browser.development.js:1520
eval @ react-server-dom-webpack-client.browser.development.js:2274
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1034
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1011
resolveModel @ react-server-dom-webpack-client.browser.development.js:1579
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2268
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
<RootLayout>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2020
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2007
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2043
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2241
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2213
progress @ react-server-dom-webpack-client.browser.development.js:2459
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1567
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2376
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2696
eval @ app-index.js:133
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1751130160720:149
options.factory @ webpack.js?v=1751130160720:700
__webpack_require__ @ webpack.js?v=1751130160720:37
fn @ webpack.js?v=1751130160720:357
eval @ app-next-dev.js:10
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:9
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1751130160720:171
options.factory @ webpack.js?v=1751130160720:700
__webpack_require__ @ webpack.js?v=1751130160720:37
__webpack_exec__ @ main-app.js?v=1751130160720:2781
(anonymous) @ main-app.js?v=1751130160720:2782
webpackJsonpCallback @ webpack.js?v=1751130160720:1376
(anonymous) @ main-app.js?v=1751130160720:9
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
ThemeProvider.useEffect.forceThemeApplication @ theme-provider.tsx:46
ThemeProvider.useEffect @ theme-provider.tsx:88
arc-browser-detector.tsx:111 Uncaught 
ArcBrowserDetector.useEffect.detectAndFixArcBrowser @ arc-browser-detector.tsx:111
attributes
