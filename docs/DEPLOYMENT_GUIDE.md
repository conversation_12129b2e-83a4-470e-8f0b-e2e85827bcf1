# PAW PUMPS Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the PAW PUMPS platform to production environments. The platform is optimized for deployment on Vercel, but can be adapted for other hosting providers.

## Prerequisites

### System Requirements
- Node.js 18.x or higher
- npm 9.x or higher
- Git 2.x or higher

### Environment Setup
- Production domain configured
- SSL certificate ready
- Environment variables prepared
- Database connections configured (if applicable)

## Environment Variables

Create a `.env.production` file with the following variables:

```bash
# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME="PAW PUMPS"

# Security
NEXT_PUBLIC_CSP_NONCE=your-csp-nonce
SECURITY_KEY=your-security-key

# Analytics (Optional)
NEXT_PUBLIC_GA_ID=your-google-analytics-id
NEXT_PUBLIC_HOTJAR_ID=your-hotjar-id

# Error Tracking (Optional)
SENTRY_DSN=your-sentry-dsn
BUGSNAG_API_KEY=your-bugsnag-key

# Performance Monitoring (Optional)
DATADOG_API_KEY=your-datadog-key
NEW_RELIC_LICENSE_KEY=your-newrelic-key

# Blockchain (if applicable)
NEXT_PUBLIC_INFURA_PROJECT_ID=your-infura-id
NEXT_PUBLIC_ALCHEMY_API_KEY=your-alchemy-key
```

## Deployment Options

### Option 1: Vercel Deployment (Recommended)

#### 1. Install Vercel CLI
```bash
npm install -g vercel
```

#### 2. Login to Vercel
```bash
vercel login
```

#### 3. Deploy to Production
```bash
# Build and deploy
vercel --prod

# Or deploy with environment variables
vercel --prod --env NODE_ENV=production
```

#### 4. Configure Domain
```bash
vercel domains add your-domain.com
vercel alias your-deployment-url.vercel.app your-domain.com
```

### Option 2: Docker Deployment

#### 1. Create Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### 2. Build and Run Docker Container
```bash
# Build the image
docker build -t pawpumps .

# Run the container
docker run -p 3000:3000 pawpumps
```

### Option 3: Traditional Server Deployment

#### 1. Build the Application
```bash
npm install
npm run build
```

#### 2. Start the Production Server
```bash
npm start
```

#### 3. Configure Reverse Proxy (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Security Configuration

### 1. SSL/TLS Setup
- Ensure SSL certificate is properly configured
- Enable HSTS (HTTP Strict Transport Security)
- Configure secure headers

### 2. Firewall Configuration
```bash
# Allow HTTP and HTTPS traffic
ufw allow 80
ufw allow 443

# Allow SSH (if needed)
ufw allow 22

# Enable firewall
ufw enable
```

### 3. Security Headers Verification
Verify that security headers are properly set:
```bash
curl -I https://your-domain.com
```

Expected headers:
- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Content-Security-Policy: ...`
- `Strict-Transport-Security: ...`

## Performance Optimization

### 1. CDN Configuration
- Configure CDN for static assets
- Enable gzip/brotli compression
- Set appropriate cache headers

### 2. Database Optimization (if applicable)
- Configure connection pooling
- Set up read replicas
- Enable query optimization

### 3. Monitoring Setup
- Configure performance monitoring
- Set up error tracking
- Enable real-time alerts

## Post-Deployment Checklist

### Immediate Actions (First 24 hours)
- [ ] Verify all pages load correctly
- [ ] Test critical user flows
- [ ] Check error rates in monitoring
- [ ] Verify security headers
- [ ] Test performance metrics
- [ ] Confirm SSL certificate validity

### Short-term Actions (First week)
- [ ] Monitor error logs daily
- [ ] Review performance metrics
- [ ] Check security audit logs
- [ ] Verify backup procedures
- [ ] Test disaster recovery
- [ ] Update documentation

### Ongoing Maintenance
- [ ] Weekly performance reviews
- [ ] Monthly security audits
- [ ] Quarterly dependency updates
- [ ] Regular backup verification
- [ ] Continuous monitoring setup

## Monitoring and Alerting

### Error Monitoring
```javascript
// Example Sentry configuration
import * as Sentry from "@sentry/nextjs"

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0,
})
```

### Performance Monitoring
```javascript
// Example DataDog configuration
import { datadogRum } from '@datadog/browser-rum'

datadogRum.init({
  applicationId: process.env.NEXT_PUBLIC_DATADOG_APP_ID,
  clientToken: process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN,
  site: 'datadoghq.com',
  service: 'pawpumps',
  env: process.env.NODE_ENV,
  sampleRate: 100,
  trackInteractions: true,
})
```

### Health Check Endpoint
The application includes a health check endpoint at `/api/health`:

```bash
curl https://your-domain.com/api/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-06T12:00:00.000Z",
  "version": "1.0.0"
}
```

## Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear cache and rebuild
rm -rf .next
npm run build
```

#### Memory Issues
```bash
# Increase Node.js memory limit
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

#### SSL Certificate Issues
```bash
# Verify certificate
openssl x509 -in certificate.crt -text -noout
```

### Log Analysis
```bash
# View application logs
tail -f /var/log/pawpumps/app.log

# View error logs
tail -f /var/log/pawpumps/error.log

# View access logs
tail -f /var/log/nginx/access.log
```

## Rollback Procedures

### Vercel Rollback
```bash
# List deployments
vercel ls

# Rollback to previous deployment
vercel rollback [deployment-url]
```

### Docker Rollback
```bash
# Stop current container
docker stop pawpumps

# Run previous version
docker run -p 3000:3000 pawpumps:previous-tag
```

### Traditional Server Rollback
```bash
# Switch to previous build
mv /app/current /app/backup
mv /app/previous /app/current

# Restart application
pm2 restart pawpumps
```

## Support and Maintenance

### Regular Maintenance Tasks
- Weekly dependency updates
- Monthly security patches
- Quarterly performance reviews
- Annual security audits

### Emergency Contacts
- Development Team: <EMAIL>
- Security Team: <EMAIL>
- Operations Team: <EMAIL>

### Documentation Updates
Keep this deployment guide updated with:
- New environment variables
- Configuration changes
- Security updates
- Performance optimizations

---

*Last updated: 2025-01-06*  
*Version: 1.0*
