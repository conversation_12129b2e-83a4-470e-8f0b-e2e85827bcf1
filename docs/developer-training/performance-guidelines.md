# Performance Guidelines for PawPumps Developers

This document outlines our approach to building high-performance web applications, with specific guidance for PawPumps developers.

## Performance Budgets

We maintain strict performance budgets to ensure our application remains fast and responsive:

| Metric | Budget |
|--------|--------|
| First Contentful Paint (FCP) | < 1.5s |
| Largest Contentful Paint (LCP) | < 2.5s |
| First Input Delay (FID) | < 100ms |
| Cumulative Layout Shift (CLS) | < 0.1 |
| Time to Interactive (TTI) | < 3.5s |
| Total Blocking Time (TBT) | < 300ms |
| JavaScript Bundle Size | < 300KB (initial) |
| CSS Size | < 100KB |
| Image Size | < 250KB (above-the-fold) |
| Total Resources | < 1MB |

## Performance Optimization Techniques

### Component Optimization

1. **Memoization**
    \`\`\`tsx
    // Do use React.memo for expensive components
    const ExpensiveComponent = React.memo(function ExpensiveComponent(props) {
      // Component logic
    });

    // Do use useMemo for expensive calculations
    const expensiveValue = useMemo(() => computeExpensiveValue(a, b), [a, b]);
    \`\`\`

2. **Code Splitting**
    \`\`\`tsx
    // Do use dynamic imports for code splitting
    const DynamicComponent = dynamic(() => import('@/components/heavy-component'), {
      loading: () => <Spinner />,
      ssr: false // if not needed for SEO
    });
    \`\`\`

3. **Virtualization for Long Lists**
    \`\`\`tsx
    // Use virtualization for long lists
    import { useVirtualizer } from '@tanstack/react-virtual';
    
    function VirtualList({ items }) {
      const virtualizer = useVirtualizer({
        count: items.length,
        getScrollElement: () => parentRef.current,
        estimateSize: () => 50,
      });
      
      // Implementation
    }
    \`\`\`

### Image Optimization

1. **Use Next.js Image component**
    \`\`\`tsx
    // Always use the Next.js Image component
    import Image from 'next/image';
    
    <Image
      src="/image.jpg"
      alt="Description"
      width={800}
      height={600}
      loading="lazy" // Use 'eager' for above the fold
      priority={false} // Use true for LCP images
    />
    \`\`\`

2. **Proper image formats**
   - Use WebP or AVIF for photos
   - Use SVG for icons and logos
   - Use appropriate sizes for different viewports

### JavaScript Optimization

1. **Avoid blocking the main thread**
    \`\`\`tsx
    // Use web workers for CPU-intensive tasks
    const worker = new Worker(new URL('../workers/heavy-calculation.js', import.meta.url));
    
    // Or use time slicing
    const result = useTimeSlice(() => heavyCalculation(data), [data]);
    \`\`\`

2. **Debounce and throttle event handlers**
    \`\`\`tsx
    // Use the debounce utility
    import { useDebounce } from '@/hooks/use-debounce';
    
    const debouncedHandleSearch = useDebounce(handleSearch, 300);
    \`\`\`

### CSS Optimization

1. **Minimize CSS-in-JS runtime overhead**
    \`\`\`tsx
    // Prefer Tailwind CSS classes
    <div className="flex items-center justify-between p-4" />
    
    // Avoid large css prop objects
    <div css={{ display: 'flex', alignItems: 'center', /* many more props */ }} />
    \`\`\`

2. **Reduce render-blocking CSS**
   - Use `media="print"` for print styles
   - Use `media="(min-width: 768px)"` for desktop-only styles

### Network Optimization

1. **Implement proper caching strategies**
    \`\`\`tsx
    // Use SWR with appropriate cache configuration
    const { data } = useSWR('/api/data', fetcher, {
      revalidateOnFocus: false,
      dedupingInterval: 60000,
    });
    \`\`\`

2. **Prefetch critical resources**
    \`\`\`tsx
    // Use Next.js prefetching for pages
    <Link href="/dashboard" prefetch>Dashboard</Link>
    
    // Or manually prefetch data
    useEffect(() => {
      if (isVisible) {
        void prefetchData();
      }
    }, [isVisible]);
    \`\`\`

## Performance Testing

1. **Local Testing**
   - Use Lighthouse in Chrome DevTools
   - Use the Performance panel to profile JavaScript execution
   - Test with throttled CPU and network

2. **CI/CD Testing**
   - All PRs are automatically tested with Lighthouse CI
   - Performance budgets are enforced in CI

3. **Real User Monitoring**
   - We collect Core Web Vitals from real users
   - Performance alerts are triggered when metrics degrade

## Common Performance Issues to Avoid

1. **Large Third-Party Scripts**
   - Always evaluate the performance impact of third-party scripts
   - Load non-critical third-party scripts after the main app has loaded

2. **Unoptimized Images**
   - Never use images without proper optimization
   - Always specify width and height to avoid layout shifts

3. **Excessive Re-Renders**
   - Use React DevTools Profiler to identify unnecessary re-renders
   - Fix by using memoization and proper state management

4. **Memory Leaks**
   - Always clean up effects, subscriptions, and event listeners
   - Watch for growing memory usage in Chrome DevTools

## Performance Debugging

1. **Identifying Jank**
   - Use Performance panel in Chrome DevTools
   - Look for long tasks and layout shifts

2. **Bundle Analysis**
   - Use `@next/bundle-analyzer` to analyze bundle size
   - Look for duplicate dependencies and large packages

3. **Render Performance**
   - Use React DevTools Profiler
   - Identify components that render too often or take too long to render

## Resources

- [Web Vitals](https://web.dev/vitals/)
- [React Performance Optimization](https://reactjs.org/docs/optimizing-performance.html)
- [Next.js Performance Optimization](https://nextjs.org/docs/advanced-features/measuring-performance)
\`\`\`

Let's create a monitoring and alerting document:
