# QA Reference Guide - PawPumps

## 📚 Quick Reference

This guide provides quick access to essential QA information, commands, and resources for the PawPumps development team.

---

## 🚀 Quick Start Commands

### **Development Setup**
```bash
# Clone repository
git clone https://github.com/pawpumps/pawpumps.git
cd pawpumps

# Install dependencies
npm install --legacy-peer-deps

# Start development server
npm run dev

# Run all tests
npm run test:all
```

### **Testing Commands**
```bash
# Unit tests
npm run test                    # Run once
npm run test:watch             # Watch mode
npm run test:coverage          # With coverage

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e               # Headless
npm run test:e2e:ui            # With UI
npm run test:e2e:debug         # Debug mode

# Performance tests
npm run lighthouse:ci
```

### **Quality Checks**
```bash
# Code quality
npm run lint                   # ESLint check
npm run lint:fix              # Fix auto-fixable issues
npm run type-check             # TypeScript check
npm run format                 # Format code
npm run format:check           # Check formatting

# Build verification
npm run build                  # Production build
npm run start                  # Start production server
```

---

## 🧪 Testing Framework Reference

### **Jest Configuration**
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
  },
  collectCoverageFrom: [
    'components/**/*.{ts,tsx}',
    'lib/**/*.{ts,tsx}',
    'app/**/*.{ts,tsx}',
    '!**/*.d.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
}
```

### **React Testing Library Queries**
```typescript
// By Role (Preferred)
screen.getByRole('button', { name: /submit/i })
screen.getByRole('textbox', { name: /email/i })
screen.getByRole('heading', { level: 1 })

// By Label Text
screen.getByLabelText(/password/i)
screen.getByLabelText('Confirm Password')

// By Placeholder
screen.getByPlaceholderText(/enter email/i)

// By Text Content
screen.getByText(/welcome/i)
screen.getByText('Exact text match')

// By Test ID (Last resort)
screen.getByTestId('submit-button')

// Query variants
getBy*     // Throws error if not found
queryBy*   // Returns null if not found
findBy*    // Returns promise, waits for element
```

### **Playwright Selectors**
```typescript
// By Role
page.getByRole('button', { name: 'Submit' })
page.getByRole('textbox', { name: 'Email' })

// By Text
page.getByText('Welcome')
page.getByText(/welcome/i)

// By Label
page.getByLabel('Password')

// By Placeholder
page.getByPlaceholder('Enter email')

// By Test ID
page.getByTestId('submit-button')

// CSS Selectors
page.locator('.class-name')
page.locator('#element-id')
page.locator('[data-testid="element"]')

// XPath
page.locator('xpath=//button[contains(text(), "Submit")]')
```

---

## 🔧 Debugging Tools

### **Browser DevTools**
```javascript
// Console debugging
console.log('Debug info:', data)
console.table(arrayData)
console.group('Group name')
console.groupEnd()

// Performance debugging
console.time('operation')
// ... code to measure
console.timeEnd('operation')

// React DevTools
// Install React Developer Tools extension
// Use Components and Profiler tabs
```

### **Testing Debugging**
```typescript
// React Testing Library debugging
import { screen } from '@testing-library/react'

// Print current DOM
screen.debug()

// Print specific element
screen.debug(screen.getByRole('button'))

// Playwright debugging
await page.pause() // Pause execution
await page.screenshot({ path: 'debug.png' })
await page.locator('button').highlight()
```

### **Monitoring Tools**
```javascript
// Access monitoring tools in console
window.pawpumpsMonitoring.getSystemStatus()
window.pawpumpsMonitoring.triggerTestAlert()
window.pawpumpsMonitoring.logger.info('Debug message')

// Performance monitoring
window.pawpumpsMonitoring.performanceMonitor.getCoreWebVitals()
window.pawpumpsMonitoring.performanceMonitor.getMetrics()
```

---

## 📊 Quality Metrics Targets

### **Code Quality**
- **Test Coverage**: >80% (Critical: >95%)
- **TypeScript Strict**: Enabled
- **ESLint Errors**: 0
- **Build Warnings**: <5

### **Performance**
- **LCP**: <2.5s (Good: <1.8s)
- **FID**: <100ms (Good: <50ms)
- **CLS**: <0.1 (Good: <0.05)
- **Lighthouse Score**: >90

### **Accessibility**
- **WCAG 2.1 AA**: 100% compliance
- **Color Contrast**: 4.5:1 minimum
- **Keyboard Navigation**: 100% functional
- **Screen Reader**: Compatible

### **Production**
- **Uptime**: >99.9%
- **Error Rate**: <1%
- **API Response**: <1s (95th percentile)
- **Page Load**: <3s

---

## 🔍 Common Issues & Solutions

### **Test Failures**
```bash
# Module resolution issues
# Solution: Check jest.config.js moduleNameMapping

# React Testing Library queries fail
# Solution: Use screen.debug() to see current DOM

# Async operations not waiting
# Solution: Use waitFor() or findBy* queries

# Playwright timeouts
# Solution: Increase timeout or add explicit waits
```

### **Build Issues**
```bash
# TypeScript errors
# Solution: Run npm run type-check for details

# Import path issues
# Solution: Check tsconfig.json paths configuration

# Dependency conflicts
# Solution: Delete node_modules and reinstall
```

### **Performance Issues**
```bash
# Large bundle size
# Solution: Use dynamic imports and code splitting

# Slow page loads
# Solution: Check network tab, optimize images

# Memory leaks
# Solution: Check for uncleaned event listeners
```

---

## 📋 Workflow Checklists

### **Feature Development**
1. [ ] Create feature branch from main
2. [ ] Implement feature with tests
3. [ ] Run quality checks locally
4. [ ] Create pull request
5. [ ] Address code review feedback
6. [ ] Merge after approval

### **Bug Fix Process**
1. [ ] Reproduce bug locally
2. [ ] Write failing test
3. [ ] Implement fix
4. [ ] Verify test passes
5. [ ] Test related functionality
6. [ ] Create pull request

### **Release Process**
1. [ ] All tests passing
2. [ ] Performance benchmarks met
3. [ ] Accessibility verified
4. [ ] Security scan clean
5. [ ] Staging deployment successful
6. [ ] Production deployment

---

## 🛠️ Tool Configuration

### **VS Code Extensions**
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-playwright.playwright",
    "ms-vscode.test-adapter-converter"
  ]
}
```

### **Git Hooks**
```bash
# Pre-commit hook
#!/bin/sh
npm run lint
npm run type-check
npm run test

# Pre-push hook
#!/bin/sh
npm run test:all
npm run build
```

---

## 📞 Support & Resources

### **Internal Resources**
- **QA Documentation**: `/docs/qa/`
- **Troubleshooting**: `/docs/qa/TROUBLESHOOTING_GUIDE.md`
- **Developer Guidelines**: `/docs/qa/DEVELOPER_GUIDELINES.md`
- **Training Materials**: `/docs/qa/TRAINING_SESSIONS.md`

### **External Resources**
- **Jest**: https://jestjs.io/docs/getting-started
- **React Testing Library**: https://testing-library.com/docs/react-testing-library/intro/
- **Playwright**: https://playwright.dev/docs/intro
- **Next.js**: https://nextjs.org/docs
- **TypeScript**: https://www.typescriptlang.org/docs/
- **Tailwind CSS**: https://tailwindcss.com/docs

### **Community Resources**
- **React**: https://react.dev/
- **Testing Best Practices**: https://kentcdodds.com/blog/common-mistakes-with-react-testing-library
- **Accessibility**: https://www.w3.org/WAI/WCAG21/quickref/
- **Performance**: https://web.dev/performance/

---

## 🎯 Key Contacts

### **Team Roles**
- **QA Lead**: Augment Agent
- **Engineering Manager**: [To be assigned]
- **DevOps Lead**: [To be assigned]
- **Security Lead**: [To be assigned]

### **Escalation Process**
1. **Level 1**: Team discussion in Slack
2. **Level 2**: QA Lead consultation
3. **Level 3**: Engineering Manager involvement
4. **Level 4**: Technical leadership escalation

---

## 📈 Continuous Improvement

### **Regular Reviews**
- **Daily**: Standup quality discussion
- **Weekly**: Metrics review and trend analysis
- **Monthly**: Process improvement session
- **Quarterly**: Strategic quality planning

### **Feedback Channels**
- **Slack**: #qa-discussion
- **GitHub**: Issues and discussions
- **Retrospectives**: Sprint-based improvements
- **Surveys**: Anonymous feedback collection

---

## 🎉 Success Metrics

### **Team Success Indicators**
- Test coverage trending upward
- Bug escape rate decreasing
- Development velocity maintained
- Team satisfaction with QA processes

### **Quality Achievements**
- Zero critical production bugs
- Performance targets consistently met
- Accessibility compliance maintained
- Security vulnerabilities addressed promptly

---

*QA Reference Guide maintained by PawPumps QA Team*  
*Last updated: 2025-01-27*
