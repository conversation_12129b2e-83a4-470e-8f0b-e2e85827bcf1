# Troubleshooting Guide - PawPumps QA & Development

## 🔧 Common Issues & Solutions

### **Build & Compilation Issues**

#### **TypeScript Errors**
```bash
# Error: Cannot find module '@/components/...'
# Solution: Check tsconfig.json path mapping
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./*"]
    }
  }
}
```

#### **Next.js Build Failures**
```bash
# Error: Module not found
npm run build

# Solutions:
1. Clear .next directory: rm -rf .next
2. Reinstall dependencies: rm -rf node_modules && npm install
3. Check import paths and file extensions
4. Verify all dependencies are installed
```

#### **ESLint Configuration Issues**
```bash
# Error: ESLint configuration conflicts
# Solution: Check .eslintrc.json for conflicting rules
npm run lint -- --debug
```

---

### **Testing Issues**

#### **Jest Test Failures**
```bash
# Error: Cannot resolve module in tests
# Solution: Update jest.config.js moduleNameMapper
module.exports = {
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
  },
}

# Error: React Testing Library queries not working
# Solution: Ensure proper setup in jest.setup.js
import '@testing-library/jest-dom'
```

#### **Playwright E2E Test Issues**
```bash
# Error: Browser not installed
npx playwright install

# Error: Test timeout
# Solution: Increase timeout in playwright.config.ts
use: {
  timeout: 30000, // 30 seconds
}

# Error: Element not found
# Solution: Use proper selectors and wait conditions
await page.waitForSelector('[data-testid="element"]')
await expect(page.locator('[data-testid="element"]')).toBeVisible()
```

#### **Test Coverage Issues**
```bash
# Low coverage warnings
# Solutions:
1. Add tests for uncovered branches
2. Remove dead code
3. Update coverage thresholds in jest.config.js
4. Use --coverage flag to see detailed report
npm run test:coverage
```

---

### **Performance Issues**

#### **Slow Page Load Times**
```typescript
// Check Core Web Vitals in browser console
window.pawpumpsMonitoring.getSystemStatus()

// Solutions:
1. Optimize images and assets
2. Implement code splitting
3. Use React.lazy for component loading
4. Check network requests in DevTools
5. Enable caching strategies
```

#### **Memory Leaks**
```typescript
// Check for memory leaks
// Solutions:
1. Clean up event listeners in useEffect
useEffect(() => {
  const handler = () => { /* ... */ }
  window.addEventListener('resize', handler)
  
  return () => {
    window.removeEventListener('resize', handler)
  }
}, [])

2. Cancel pending requests on unmount
useEffect(() => {
  const controller = new AbortController()
  
  fetch('/api/data', { signal: controller.signal })
  
  return () => controller.abort()
}, [])
```

#### **Bundle Size Issues**
```bash
# Analyze bundle size
npm run build:analyze

# Solutions:
1. Use dynamic imports for large components
2. Remove unused dependencies
3. Use tree shaking
4. Optimize third-party libraries
```

---

### **API & Network Issues**

#### **API Call Failures**
```typescript
// Debug API calls
import { logger } from '@/lib/monitoring/logger'

const debugAPICall = async (url: string) => {
  try {
    const response = await fetch(url)
    logger.info(`API call successful: ${url}`, {
      status: response.status,
      headers: Object.fromEntries(response.headers.entries())
    })
  } catch (error) {
    logger.error(`API call failed: ${url}`, error)
  }
}

// Common solutions:
1. Check network connectivity
2. Verify API endpoint URLs
3. Check CORS configuration
4. Validate request headers and body
5. Check rate limiting
```

#### **CORS Issues**
```javascript
// Next.js API route CORS setup
export async function GET(request) {
  const response = NextResponse.json(data)
  
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  
  return response
}
```

---

### **Monitoring & Alerting Issues**

#### **Alerts Not Triggering**
```typescript
// Debug alerting system
window.pawpumpsMonitoring.triggerTestAlert()

// Check alert configuration
const config = window.pawpumpsMonitoring.alertingSystem.getRules()
console.log('Alert rules:', config)

// Solutions:
1. Verify alert thresholds are correct
2. Check if alerting is enabled
3. Verify notification channels are configured
4. Check cooldown periods
```

#### **Performance Monitoring Not Working**
```typescript
// Check if performance monitoring is active
const vitals = window.pawpumpsMonitoring.performanceMonitor.getCoreWebVitals()
console.log('Core Web Vitals:', vitals)

// Solutions:
1. Ensure browser supports Performance Observer API
2. Check if monitoring is enabled in configuration
3. Verify network requests to analytics endpoints
```

#### **Logging Issues**
```typescript
// Test logging system
import { logger } from '@/lib/monitoring/logger'

logger.info('Test log message', {
  component: 'Troubleshooting',
  metadata: { test: true }
})

// Check browser console and network tab
// Solutions:
1. Verify log level configuration
2. Check if logging endpoints are accessible
3. Ensure proper error handling in logging system
```

---

### **Development Environment Issues**

#### **Node.js Version Conflicts**
```bash
# Check Node.js version
node --version

# Use Node Version Manager (nvm)
nvm install 18
nvm use 18

# Or use specific version in package.json
"engines": {
  "node": ">=18.0.0"
}
```

#### **Package Installation Issues**
```bash
# Clear npm cache
npm cache clean --force

# Remove node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Use legacy peer deps if needed
npm install --legacy-peer-deps
```

#### **Port Conflicts**
```bash
# Error: Port 3000 already in use
# Solutions:
1. Kill process using port: lsof -ti:3000 | xargs kill -9
2. Use different port: npm run dev -- -p 3001
3. Set PORT environment variable: PORT=3001 npm run dev
```

---

### **Browser-Specific Issues**

#### **Safari Compatibility**
```typescript
// Safari-specific fixes
// 1. Use proper polyfills for newer APIs
// 2. Test with Safari Technology Preview
// 3. Check for webkit-specific CSS properties

// Example: Safari date input fix
const isWebkit = /webkit/i.test(navigator.userAgent)
if (isWebkit) {
  // Apply Safari-specific handling
}
```

#### **Mobile Browser Issues**
```css
/* Mobile viewport fixes */
@media (max-width: 768px) {
  /* Prevent zoom on input focus */
  input, select, textarea {
    font-size: 16px;
  }
  
  /* Fix viewport height on mobile */
  .full-height {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height */
  }
}
```

---

### **Accessibility Issues**

#### **Screen Reader Problems**
```typescript
// Add proper ARIA labels
<button aria-label="Close dialog" onClick={onClose}>
  <X aria-hidden="true" />
</button>

// Use semantic HTML
<main role="main">
  <section aria-labelledby="trading-heading">
    <h2 id="trading-heading">Trading Interface</h2>
  </section>
</main>
```

#### **Keyboard Navigation Issues**
```typescript
// Ensure proper focus management
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    onClose()
  }
  
  if (event.key === 'Tab') {
    // Handle tab navigation
  }
}

// Use proper tabindex
<div tabIndex={0} onKeyDown={handleKeyDown}>
  Focusable content
</div>
```

---

### **Production Deployment Issues**

#### **Environment Variables**
```bash
# Check environment variables
echo $NODE_ENV
echo $NEXT_PUBLIC_APP_URL

# Verify .env files are properly configured
# .env.local (local development)
# .env.production (production)
```

#### **Build Optimization**
```javascript
// next.config.js optimizations
module.exports = {
  experimental: {
    optimizeCss: true,
  },
  images: {
    domains: ['example.com'],
    formats: ['image/webp', 'image/avif'],
  },
  compress: true,
}
```

---

## 🆘 Getting Help

### **Escalation Process**
1. **Self-Service**: Check this troubleshooting guide
2. **Team Chat**: Ask in development Slack channel
3. **Code Review**: Request help during code review
4. **Technical Lead**: Escalate complex technical issues
5. **External Support**: Contact vendor support if needed

### **Useful Commands**
```bash
# System diagnostics
npm run test:all          # Run all tests
npm run lint              # Check code quality
npm run type-check        # TypeScript validation
npm run build             # Production build test
npm run size-check        # Bundle size analysis

# Monitoring diagnostics
window.pawpumpsMonitoring.getSystemStatus()
window.pawpumpsMonitoring.triggerTestAlert()
```

### **Log Analysis**
```bash
# Check application logs
tail -f logs/application.log

# Check error logs
grep "ERROR" logs/application.log

# Check performance logs
grep "PERFORMANCE" logs/application.log
```

---

*Troubleshooting Guide maintained by PawPumps QA Team*  
*Last updated: 2025-01-27*
