# PawPumps Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying PawPumps to production environments. Follow these steps carefully to ensure a secure and reliable deployment.

## 🚨 Pre-Deployment Requirements

### Critical Issues to Resolve First

Before deploying to production, the following critical issues **MUST** be resolved:

1. **Test Infrastructure** 🚨 BLOCKING
   - Fix MSW compatibility issues
   - Resolve React component import failures
   - Achieve >90% test pass rate

2. **Performance Optimization** ⚠️ HIGH PRIORITY
   - Reduce admin/governance bundle from 381 kB to <200 kB
   - Optimize delegation page from 351 kB to <200 kB
   - Implement code splitting for heavy components

3. **Code Quality** 📝 RECOMMENDED
   - Fix 400+ ESLint issues
   - Replace TypeScript `any` types
   - Remove unused imports and variables

## 🌐 Deployment Platforms

### Recommended: Vercel (Next.js Optimized)

**Advantages:**
- Native Next.js support
- Automatic deployments
- Edge functions
- Built-in analytics
- Zero-config SSL

**Setup:**
```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

### Alternative: Netlify

**Setup:**
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy
netlify deploy --prod
```

### Self-Hosted: Docker

**Dockerfile:**
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

## 🔧 Environment Configuration

### 1. Copy Environment Files

```bash
# For production
cp .env.production.example .env.production

# For local development
cp .env.example .env.local
```

### 2. Required Environment Variables

**Critical Variables (Must be set):**
```env
# Application
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production

# Security
NEXTAUTH_SECRET=your-64-character-secret
JWT_SECRET=your-64-character-secret

# Database
DATABASE_URL=********************************/db

# Web3
NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID=your-project-id
NEXT_PUBLIC_ALCHEMY_API_KEY=your-api-key
```

### 3. Security Configuration

**SSL/HTTPS:**
```env
FORCE_HTTPS=true
HSTS_MAX_AGE=31536000
```

**CORS:**
```env
CORS_ORIGIN=https://your-domain.com
CORS_CREDENTIALS=true
```

**Rate Limiting:**
```env
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_WINDOW_MS=60000
```

## 🗄️ Database Setup

### PostgreSQL Production Setup

1. **Create Production Database:**
```sql
CREATE DATABASE pawpumps_prod;
CREATE USER pawpumps_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE pawpumps_prod TO pawpumps_user;
```

2. **Run Migrations:**
```bash
npm run db:migrate:prod
```

3. **Seed Initial Data:**
```bash
npm run db:seed:prod
```

### Redis Cache Setup

```bash
# Install Redis
sudo apt-get install redis-server

# Configure Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

## 🔐 Security Hardening

### 1. SSL Certificate Setup

**Let's Encrypt (Free):**
```bash
sudo apt-get install certbot
sudo certbot --nginx -d your-domain.com
```

### 2. Security Headers

Add to your web server configuration:
```nginx
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
```

### 3. Content Security Policy

```env
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self' 'unsafe-inline' https://cdn.your-domain.com
CSP_STYLE_SRC='self' 'unsafe-inline' https://fonts.googleapis.com
```

## 📊 Monitoring Setup

### 1. Application Monitoring

**Sentry (Error Tracking):**
```env
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn
SENTRY_AUTH_TOKEN=your-auth-token
```

**Google Analytics:**
```env
NEXT_PUBLIC_GOOGLE_ANALYTICS=G-XXXXXXXXXX
```

### 2. Performance Monitoring

**Core Web Vitals:**
```javascript
// Add to _app.tsx
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric) {
  // Send to your analytics service
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

### 3. Health Checks

Create `/api/health` endpoint:
```typescript
export default function handler(req, res) {
  // Check database connection
  // Check external services
  // Return health status
  res.status(200).json({ status: 'healthy' });
}
```

## 🚀 Deployment Process

### 1. Pre-Deployment Checklist

- [ ] All tests passing (>90% success rate)
- [ ] Bundle sizes optimized (<200 kB per page)
- [ ] Environment variables configured
- [ ] Database migrations ready
- [ ] SSL certificates installed
- [ ] Monitoring configured
- [ ] Backup strategy implemented

### 2. Deployment Steps

```bash
# 1. Build the application
npm run build

# 2. Run production tests
npm run test:prod

# 3. Deploy to staging
npm run deploy:staging

# 4. Run smoke tests
npm run test:smoke

# 5. Deploy to production
npm run deploy:prod

# 6. Verify deployment
npm run verify:prod
```

### 3. Post-Deployment Verification

1. **Functionality Tests:**
   - [ ] Homepage loads correctly
   - [ ] Trading interface functional
   - [ ] Governance system accessible
   - [ ] Analytics dashboard working
   - [ ] Wallet connection working

2. **Performance Tests:**
   - [ ] Page load times <3 seconds
   - [ ] Core Web Vitals passing
   - [ ] Bundle sizes within budget
   - [ ] API response times <500ms

3. **Security Tests:**
   - [ ] HTTPS enforced
   - [ ] Security headers present
   - [ ] No exposed secrets
   - [ ] Rate limiting active

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 🆘 Troubleshooting

### Common Issues

1. **Build Failures:**
   - Check Node.js version (18+)
   - Clear node_modules and reinstall
   - Verify environment variables

2. **Performance Issues:**
   - Enable bundle analyzer
   - Check for large dependencies
   - Implement code splitting

3. **Database Connection:**
   - Verify connection string
   - Check firewall settings
   - Test database connectivity

### Emergency Procedures

1. **Rollback:**
```bash
# Vercel
vercel rollback

# Manual
git revert HEAD
npm run deploy:prod
```

2. **Health Check:**
```bash
curl https://your-domain.com/api/health
```

## 📋 Maintenance

### Regular Tasks

- **Daily:** Monitor error rates and performance
- **Weekly:** Review security logs and updates
- **Monthly:** Update dependencies and security patches
- **Quarterly:** Performance optimization review

### Backup Strategy

```bash
# Database backup
pg_dump pawpumps_prod > backup_$(date +%Y%m%d).sql

# File backup
tar -czf files_backup_$(date +%Y%m%d).tar.gz /app/uploads
```

## 📞 Support

For deployment issues:
1. Check the troubleshooting section
2. Review deployment logs
3. Contact the development team
4. Create a GitHub issue

---

**⚠️ Important:** Do not deploy to production until all critical issues are resolved and comprehensive testing is completed.
