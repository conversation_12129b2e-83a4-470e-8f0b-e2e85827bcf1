# GitHub Repository Setup Guide

## Overview

This guide provides step-by-step instructions for setting up the PawPumps GitHub repository and configuring it for production deployment.

## 🚀 Repository Creation

### Step 1: Create GitHub Repository

1. **Go to GitHub** and sign in to your account
2. **Click "New repository"** or visit https://github.com/new
3. **Configure repository settings:**
   - **Repository name:** `pawpumps`
   - **Description:** `Decentralized Trading Platform - Next.js 15 + React 19 + Web3`
   - **Visibility:** ✅ **Private** (Recommended for production)
   - **Initialize:** ❌ Do not initialize (we have existing code)

4. **Click "Create repository"**

### Step 2: Connect Local Repository

```bash
# Add GitHub remote
git remote add origin https://github.com/YOUR_USERNAME/pawpumps.git

# Verify remote
git remote -v

# Push initial commit
git branch -M main
git push -u origin main
```

## 🔧 Repository Configuration

### Branch Protection Rules

1. **Go to Settings > Branches**
2. **Add rule for `main` branch:**
   - ✅ Require a pull request before merging
   - ✅ Require approvals (minimum 1)
   - ✅ Dismiss stale PR approvals when new commits are pushed
   - ✅ Require status checks to pass before merging
   - ✅ Require branches to be up to date before merging
   - ✅ Require conversation resolution before merging
   - ✅ Restrict pushes that create files larger than 100 MB

### Required Status Checks

Configure these checks to be required:
- `build` - Build process
- `test` - Test suite
- `lint` - Code linting
- `type-check` - TypeScript validation

### Repository Settings

1. **General Settings:**
   - ✅ Allow merge commits
   - ✅ Allow squash merging
   - ❌ Allow rebase merging
   - ✅ Automatically delete head branches

2. **Security Settings:**
   - ✅ Enable vulnerability alerts
   - ✅ Enable automated security updates
   - ✅ Enable private vulnerability reporting

## 🔐 Secrets Configuration

### Required Secrets

Go to **Settings > Secrets and variables > Actions** and add:

#### Deployment Secrets
```
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_vercel_org_id
VERCEL_PROJECT_ID=your_vercel_project_id
```

#### Database Secrets
```
DATABASE_URL=********************************/db
REDIS_URL=redis://host:6379
```

#### Web3 Secrets
```
WALLET_CONNECT_PROJECT_ID=your_project_id
ALCHEMY_API_KEY=your_api_key
```

#### Monitoring Secrets
```
SENTRY_DSN=your_sentry_dsn
SENTRY_AUTH_TOKEN=your_auth_token
GOOGLE_ANALYTICS=your_ga_id
```

#### Security Secrets
```
NEXTAUTH_SECRET=your_64_character_secret
JWT_SECRET=your_64_character_secret
ADMIN_SECRET_KEY=your_admin_secret
```

### Environment Variables

Add these as **Variables** (not secrets):
```
NODE_ENV=production
NEXT_PUBLIC_APP_NAME=PawPumps
NEXT_PUBLIC_APP_URL=https://pawpumps.com
NEXT_PUBLIC_ENVIRONMENT=production
```

## 🔄 GitHub Actions Setup

### Create Workflow Files

Create `.github/workflows/` directory and add these workflows:

#### 1. CI/CD Pipeline (`.github/workflows/ci-cd.yml`)

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    name: Test & Build
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run linting
        run: npm run lint
        
      - name: Run type checking
        run: npm run type-check
        
      - name: Run tests
        run: npm run test:ci
        
      - name: Build application
        run: npm run build
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: .next/

  deploy-staging:
    name: Deploy to Staging
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Deploy to Vercel (Staging)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          scope: ${{ secrets.VERCEL_ORG_ID }}

  deploy-production:
    name: Deploy to Production
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Deploy to Vercel (Production)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          scope: ${{ secrets.VERCEL_ORG_ID }}
```

#### 2. Security Audit (`.github/workflows/security.yml`)

```yaml
name: Security Audit

on:
  schedule:
    - cron: '0 2 * * 1' # Weekly on Monday at 2 AM
  push:
    branches: [main]

jobs:
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run security audit
        run: npm audit --audit-level=high
        
      - name: Run dependency check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'PawPumps'
          path: '.'
          format: 'JSON'
```

#### 3. Performance Testing (`.github/workflows/performance.yml`)

```yaml
name: Performance Testing

on:
  pull_request:
    branches: [main]
  schedule:
    - cron: '0 4 * * *' # Daily at 4 AM

jobs:
  lighthouse:
    name: Lighthouse CI
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build application
        run: npm run build
        
      - name: Start application
        run: npm start &
        
      - name: Wait for server
        run: npx wait-on http://localhost:3000
        
      - name: Run Lighthouse CI
        run: npx lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
```

## 📊 Issue Templates

### Create Issue Templates

Create `.github/ISSUE_TEMPLATE/` directory with these templates:

#### Bug Report (`bug_report.yml`)
```yaml
name: Bug Report
description: File a bug report
title: "[Bug]: "
labels: ["bug", "triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
        
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: Tell us what you see!
    validations:
      required: true
      
  - type: textarea
    id: steps
    attributes:
      label: Steps to reproduce
      description: How can we reproduce this issue?
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true
```

#### Feature Request (`feature_request.yml`)
```yaml
name: Feature Request
description: Suggest an idea for this project
title: "[Feature]: "
labels: ["enhancement", "triage"]
body:
  - type: textarea
    id: problem
    attributes:
      label: Is your feature request related to a problem?
      description: A clear and concise description of what the problem is.
      placeholder: I'm always frustrated when [...]
    validations:
      required: true
      
  - type: textarea
    id: solution
    attributes:
      label: Describe the solution you'd like
      description: A clear and concise description of what you want to happen.
    validations:
      required: true
```

## 📋 Pull Request Template

Create `.github/pull_request_template.md`:

```markdown
## Description
Brief description of the changes

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] This change requires a documentation update

## How Has This Been Tested?
- [ ] Unit tests
- [ ] Integration tests
- [ ] Manual testing

## Checklist:
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
```

## 🏷️ Labels Configuration

Create these labels in **Issues > Labels**:

### Type Labels
- `bug` (red) - Something isn't working
- `enhancement` (blue) - New feature or request
- `documentation` (green) - Improvements or additions to documentation
- `performance` (orange) - Performance improvements

### Priority Labels
- `priority: critical` (dark red) - Critical issues
- `priority: high` (red) - High priority
- `priority: medium` (yellow) - Medium priority
- `priority: low` (green) - Low priority

### Status Labels
- `status: triage` (gray) - Needs triage
- `status: in-progress` (yellow) - Currently being worked on
- `status: blocked` (red) - Blocked by dependencies
- `status: ready-for-review` (green) - Ready for code review

## 📈 Project Management

### GitHub Projects Setup

1. **Create new project** (Projects tab)
2. **Choose "Board" layout**
3. **Add columns:**
   - Backlog
   - Todo
   - In Progress
   - In Review
   - Done

4. **Configure automation:**
   - Auto-add new issues to Backlog
   - Move to "In Progress" when PR is opened
   - Move to "Done" when PR is merged

## 🔍 Repository Insights

### Enable Insights

1. **Go to Insights tab**
2. **Configure:**
   - Dependency graph
   - Security advisories
   - Code scanning alerts
   - Secret scanning alerts

## 📞 Support

For GitHub setup issues:
1. Check GitHub documentation
2. Contact repository administrators
3. Create a support ticket

---

**Next Steps:**
1. Create the GitHub repository
2. Configure all settings as described
3. Set up CI/CD workflows
4. Configure branch protection
5. Add team members and permissions
