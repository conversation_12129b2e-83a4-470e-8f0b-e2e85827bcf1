import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { WalletProvider, WalletContext } from '@/components/wallet-provider'
import { NotificationProvider, NotificationContext } from '@/components/notification-provider'
import { Toaster } from '@/components/ui/toaster'

// Mock wallet context for testing
const mockWalletContext = {
  isConnected: false,
  address: null,
  balance: null,
  chainId: 2000, // Dogechain
  connector: null,
  provider: null,
  signer: null,
  tokenBalances: [],
  isLoadingBalances: false,
  connect: jest.fn(),
  disconnect: jest.fn(),
  switchNetwork: jest.fn(),
  addToken: jest.fn().mockResolvedValue(false),
  getAvailableWallets: jest.fn().mockReturnValue([]),
  refreshBalances: jest.fn(),
  isConnecting: false,
  error: null,
}

// Mock notification context for testing
const mockNotificationContext = {
  notifications: [],
  unreadCount: 0,
  addNotification: jest.fn(),
  markAsRead: jest.fn(),
  markAllAsRead: jest.fn(),
  removeNotification: jest.fn(),
  clearAllNotifications: jest.fn(),
  getNotificationsByCategory: jest.fn().mockReturnValue([]),
  getNotificationsByType: jest.fn().mockReturnValue([]),
  getUnreadNotificationsByCategory: jest.fn().mockReturnValue([]),
}

// Mock providers for testing
const MockWalletProvider = ({ children }: { children: React.ReactNode }) => (
  <WalletContext.Provider value={mockWalletContext}>
    {children}
  </WalletContext.Provider>
)

const MockNotificationProvider = ({ children }: { children: React.ReactNode }) => (
  <NotificationContext.Provider value={mockNotificationContext}>
    {children}
  </NotificationContext.Provider>
)

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <MockWalletProvider>
      <MockNotificationProvider>
        {children}
        <Toaster />
      </MockNotificationProvider>
    </MockWalletProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }

// Test utilities
export const createMockWallet = (overrides = {}) => ({
  ...mockWalletContext,
  ...overrides,
})

export const createMockNotification = (overrides = {}) => ({
  id: 'test-notification',
  title: 'Test Notification',
  message: 'This is a test notification',
  type: 'info' as const,
  timestamp: new Date(),
  read: false,
  ...overrides,
})

// Mock data generators
export const generateMockPriceData = (days = 30) => {
  const data = []
  let basePrice = 0.000001
  
  for (let i = 0; i < days; i++) {
    const date = new Date()
    date.setDate(date.getDate() - (days - i))
    
    const change = (Math.random() - 0.5) * 0.1
    basePrice = Math.max(basePrice * (1 + change), 0.0000001)
    
    data.push({
      timestamp: date.toISOString(),
      price: basePrice,
      volume: Math.random() * 1000000 + 100000,
      high: basePrice * (1 + Math.random() * 0.05),
      low: basePrice * (1 - Math.random() * 0.05),
      open: basePrice * (1 + (Math.random() - 0.5) * 0.02),
      close: basePrice,
    })
  }
  
  return data
}

export const generateMockTokenData = () => ({
  address: '0x1234567890123456789012345678901234567890',
  name: 'Test Token',
  symbol: 'TEST',
  decimals: 18,
  totalSupply: '1000000000000000000000000000',
  price: 0.000001,
  priceChange24h: 5.2,
  volume24h: 1500000,
  marketCap: 1000000,
  holders: 15000,
})

export const generateMockTransactionData = () => ({
  hash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
  from: '0x1234567890123456789012345678901234567890',
  to: '0x0987654321098765432109876543210987654321',
  value: '1000000000000000000',
  gasUsed: '21000',
  gasPrice: '20000000000',
  timestamp: new Date().toISOString(),
  status: 'success' as const,
})

// Form testing utilities
export const fillForm = async (form: HTMLFormElement, data: Record<string, string>) => {
  const { fireEvent } = await import('@testing-library/react')
  
  Object.entries(data).forEach(([name, value]) => {
    const input = form.querySelector(`[name="${name}"]`) as HTMLInputElement
    if (input) {
      fireEvent.change(input, { target: { value } })
    }
  })
}

export const submitForm = async (form: HTMLFormElement) => {
  const { fireEvent } = await import('@testing-library/react')
  fireEvent.submit(form)
}

// Async testing utilities
export const waitForAsyncOperation = async (operation: () => Promise<any>, timeout = 5000) => {
  const { waitFor } = await import('@testing-library/react')
  
  return waitFor(operation, { timeout })
}

// Error testing utilities
export const mockConsoleError = () => {
  const originalError = console.error
  const mockError = jest.fn()
  console.error = mockError
  
  return {
    mockError,
    restore: () => {
      console.error = originalError
    },
  }
}

// Network mocking utilities
export const mockFetch = (response: any, options: { status?: number; ok?: boolean } = {}) => {
  const mockResponse = {
    ok: options.ok ?? true,
    status: options.status ?? 200,
    json: jest.fn().mockResolvedValue(response),
    text: jest.fn().mockResolvedValue(JSON.stringify(response)),
  }
  
  global.fetch = jest.fn().mockResolvedValue(mockResponse)
  
  return mockResponse
}

export const mockFailedFetch = (error = new Error('Network error')) => {
  global.fetch = jest.fn().mockRejectedValue(error)
}

// Local storage mocking
export const mockLocalStorage = () => {
  const store: Record<string, string> = {}
  
  const mockStorage = {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key]
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key])
    }),
  }
  
  Object.defineProperty(window, 'localStorage', {
    value: mockStorage,
  })
  
  return mockStorage
}

// Intersection Observer mock
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = jest.fn()
  mockIntersectionObserver.mockReturnValue({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  })
  
  window.IntersectionObserver = mockIntersectionObserver
  
  return mockIntersectionObserver
}

// Resize Observer mock
export const mockResizeObserver = () => {
  const mockResizeObserver = jest.fn()
  mockResizeObserver.mockReturnValue({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  })
  
  window.ResizeObserver = mockResizeObserver
  
  return mockResizeObserver
}

// Custom matchers
export const customMatchers = {
  toBeValidEmail: (received: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const pass = emailRegex.test(received)
    
    return {
      message: () =>
        pass
          ? `expected ${received} not to be a valid email`
          : `expected ${received} to be a valid email`,
      pass,
    }
  },
  
  toBeValidAddress: (received: string) => {
    const addressRegex = /^0x[a-fA-F0-9]{40}$/
    const pass = addressRegex.test(received)
    
    return {
      message: () =>
        pass
          ? `expected ${received} not to be a valid Ethereum address`
          : `expected ${received} to be a valid Ethereum address`,
      pass,
    }
  },
}

// Setup function for tests
export const setupTest = () => {
  // Clear all mocks
  jest.clearAllMocks()
  
  // Reset DOM
  document.body.innerHTML = ''
  
  // Mock common APIs
  mockLocalStorage()
  mockIntersectionObserver()
  mockResizeObserver()
  
  // Reset fetch
  global.fetch = jest.fn()
}

// Cleanup function for tests
export const cleanupTest = () => {
  // Clear all mocks
  jest.clearAllMocks()
  
  // Reset DOM
  document.body.innerHTML = ''
  
  // Clear timers
  jest.clearAllTimers()
}
