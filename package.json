{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "build:analyze": "ANALYZE=true next build", "dev": "next dev -p 3003", "lint": "next lint", "start": "next start", "bundle-analyzer": "npx webpack-bundle-analyzer .next/static/chunks/", "size-check": "npm run build && npx bundlesize", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "dev:perf": "node scripts/dev-performance.js", "dev:clean": "rm -rf .next && npm run dev", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:all": "npm run test:ci && npm run test:e2e", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "npx chromatic --project-token=your-project-token"}, "dependencies": {"@coinbase/wallet-sdk": "^4.3.4", "@hookform/resolvers": "^3.9.1", "@jest/globals": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@stagewise/toolbar-next": "^0.5.2", "@testing-library/dom": "latest", "@testing-library/jest-dom": "latest", "@testing-library/react": "latest", "@testing-library/user-event": "latest", "@walletconnect/ethereum-provider": "^2.21.4", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "embla-carousel-react": "8.5.1", "ethers": "^6.14.4", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "pino-pretty": "^13.0.0", "react": "^19", "react-day-picker": "^9.8.0", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "latest", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@chromatic-com/storybook": "^4.0.1", "@next/bundle-analyzer": "^15.3.5", "@playwright/test": "^1.53.1", "@storybook/addon-docs": "^9.0.13", "@storybook/addon-onboarding": "^9.0.13", "@storybook/nextjs": "^9.0.13", "@types/jest": "^30.0.0", "@types/node": "^24.0.10", "@types/react": "^19", "@types/react-dom": "^19", "babel-jest": "^30.0.2", "eslint": "^9.30.1", "eslint-config-next": "15.3.5", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "lighthouse": "^12.7.1", "msw": "^2.10.3", "postcss": "^8.5", "puppeteer": "^24.11.2", "storybook": "^9.0.13", "tailwindcss": "^3.4.17", "typescript": "^5", "whatwg-fetch": "^3.6.20"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}