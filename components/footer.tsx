"use client"

import Link from "next/link"
import Image from "next/image"
import { Gith<PERSON>, MessageSquare } from "lucide-react"
import { X } from "lucide-react"
import { Send } from "lucide-react"
import { ShimmerText } from "@/components/shimmer-text"

export function Footer() {
  const handleFeedbackClick = () => {
    window.dispatchEvent(new CustomEvent("open-feedback"))
  }

  return (
    <footer className="border-t border-white/5 py-12">
      <div className="container">
        <div className="grid gap-8 md:grid-cols-4">
          <div className="md:col-span-1">
            <Link href="/" className="flex items-center gap-2">
              <Image src="/images/pawpumps-icon.png" alt="PawPumps" width={32} height={32} className="h-8 w-8" />
              <span className="text-2xl font-bold">
                <ShimmerText>PawPumps</ShimmerText>
              </span>
            </Link>
            <p className="mt-4 text-sm text-white/60">
              The premier memecoin launchpad and DEX on the Dogechain Network.
            </p>
            <div className="mt-6 flex items-center gap-4">
              <Link
                href="https://x.com/pawpumps"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/60 hover:text-white transition-colors"
              >
                <X className="h-5 w-5" />
                <span className="sr-only">X</span>
              </Link>
              <Link
                href="https://t.me/pawpumps"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/60 hover:text-white transition-colors"
              >
                <Send className="h-5 w-5" />
                <span className="sr-only">Telegram</span>
              </Link>
              <Link
                href="https://github.com/pawpumps"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/60 hover:text-white transition-colors"
              >
                <Github className="h-5 w-5" />
                <span className="sr-only">GitHub</span>
              </Link>
            </div>
          </div>
          <div className="grid grid-cols-3 gap-8 md:col-span-3">
            <div>
              <h3 className="mb-4 text-sm font-medium text-white">Platform</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/launch" className="text-white/60 hover:text-white transition-colors">
                    Launch
                  </Link>
                </li>
                <li>
                  <Link href="/trade" className="text-white/60 hover:text-white transition-colors">
                    Trade
                  </Link>
                </li>
                <li>
                  <Link href="/social" className="text-white/60 hover:text-white transition-colors">
                    Social
                  </Link>
                </li>
                <li>
                  <Link href="/governance" className="text-white/60 hover:text-white transition-colors">
                    Governance
                  </Link>
                </li>
                <li>
                  <Link href="/analytics" className="text-white/60 hover:text-white transition-colors">
                    Analytics
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="mb-4 text-sm font-medium text-white">Resources</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/docs" className="text-white/60 hover:text-white transition-colors">
                    Documentation
                  </Link>
                </li>
                <li>
                  <Link href="/faq" className="text-white/60 hover:text-white transition-colors">
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link href="/blog" className="text-white/60 hover:text-white transition-colors">
                    Blog
                  </Link>
                </li>
                <li>
                  <Link href="/tutorials" className="text-white/60 hover:text-white transition-colors">
                    Tutorials
                  </Link>
                </li>
                <li>
                  <button
                    onClick={handleFeedbackClick}
                    className="text-white/60 hover:text-white transition-colors cursor-pointer flex items-center"
                  >
                    <MessageSquare className="h-4 w-4 mr-1" />
                    Feedback
                  </button>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="mb-4 text-sm font-medium text-white">Legal</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/terms" className="text-white/60 hover:text-white transition-colors">
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="text-white/60 hover:text-white transition-colors">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/disclaimer" className="text-white/60 hover:text-white transition-colors">
                    Disclaimer
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div className="mt-12 border-t border-white/5 pt-8 text-center text-sm text-white/60">
          <p>© 2025 PawPumps. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
