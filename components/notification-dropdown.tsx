"use client"

import { useState, useRef } from "react"
import { Bell, Check, Calendar, AlertCircle, Zap, Coins, Setting<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useNotifications } from "@/components/notification-provider"
import { useOnClickOutside } from "@/hooks/use-click-outside"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import Link from "next/link"

// Update the component to include categories and tabs
export function NotificationDropdown() {
  const { notifications, unreadCount, markAllAsRead, markAsRead } = useNotifications()
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<"all" | "governance" | "token" | "system">("all")
  const dropdownRef = useRef<HTMLDivElement>(null)

  useOnClickOutside(dropdownRef, () => setIsOpen(false))

  const toggleDropdown = () => {
    setIsOpen(!isOpen)
  }

  const getFilteredNotifications = () => {
    if (activeTab === "all") return notifications
    return notifications.filter((notification) => notification.category === activeTab)
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "governance":
        return (
          <Badge className="bg-blue-500/20 text-blue-500 h-8 w-8 rounded-full flex items-center justify-center">
            <Calendar className="h-4 w-4" />
          </Badge>
        )
      case "token":
        return (
          <Badge className="bg-purple-500/20 text-purple-500 h-8 w-8 rounded-full flex items-center justify-center">
            <Coins className="h-4 w-4" />
          </Badge>
        )
      case "price":
        return (
          <Badge className="bg-green-500/20 text-green-500 h-8 w-8 rounded-full flex items-center justify-center">
            <Zap className="h-4 w-4" />
          </Badge>
        )
      case "system":
        return (
          <Badge className="bg-yellow-500/20 text-yellow-500 h-8 w-8 rounded-full flex items-center justify-center">
            <AlertCircle className="h-4 w-4" />
          </Badge>
        )
      default:
        return (
          <Badge className="bg-white/20 text-white h-8 w-8 rounded-full flex items-center justify-center">
            <Bell className="h-4 w-4" />
          </Badge>
        )
    }
  }

  const getPriorityClass = (priority?: "low" | "medium" | "high") => {
    switch (priority) {
      case "high":
        return "border-l-2 border-red-500"
      case "medium":
        return "border-l-2 border-yellow-500"
      case "low":
        return "border-l-2 border-blue-500"
      default:
        return ""
    }
  }

  const formatTime = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return "just now"
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    return date.toLocaleDateString()
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        variant="ghost"
        size="icon"
        className="relative text-white/80 hover:text-white"
        onClick={toggleDropdown}
        aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ""}`}
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-doge" aria-hidden="true" />}
      </Button>

      {isOpen && (
        <div
          className="absolute right-0 mt-2 w-96 glass-card rounded-md shadow-lg ring-1 ring-white/10 z-50"
          style={{ maxHeight: "calc(100vh - 80px)", overflowY: "auto" }}
        >
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-white">Notifications</h3>
              <div className="flex items-center gap-2">
                {notifications.length > 0 && (
                  <button onClick={() => markAllAsRead()} className="text-xs text-white/70 hover:text-white">
                    Mark all as read
                  </button>
                )}
                <Link href="/notifications/settings">
                  <Button variant="ghost" size="icon" className="h-6 w-6 text-white/70 hover:text-white">
                    <Settings className="h-4 w-4" />
                    <span className="sr-only">Notification Settings</span>
                  </Button>
                </Link>
              </div>
            </div>

            <Tabs defaultValue="all" value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
              <TabsList className="grid w-full grid-cols-4 glass mb-4">
                <TabsTrigger value="all" className="text-xs">
                  All
                </TabsTrigger>
                <TabsTrigger value="governance" className="text-xs">
                  Governance
                </TabsTrigger>
                <TabsTrigger value="token" className="text-xs">
                  Tokens
                </TabsTrigger>
                <TabsTrigger value="system" className="text-xs">
                  System
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-0">
                <ScrollArea className="h-[400px] pr-3">
                  {getFilteredNotifications().length === 0 ? (
                    <p className="text-white/70 text-center py-4">No notifications</p>
                  ) : (
                    <div className="space-y-3">
                      {getFilteredNotifications().map((notification) => (
                        <div
                          key={notification.id}
                          className={`p-3 rounded-md flex items-start gap-3 ${
                            notification.read ? "bg-black/20" : "bg-black/40"
                          } ${getPriorityClass(notification.priority)}`}
                          onClick={() => {
                            if (!notification.read) markAsRead(notification.id)
                          }}
                        >
                          {getNotificationIcon(notification.type)}
                          <div className="flex-1">
                            <div className="flex items-start justify-between">
                              <p className="text-sm font-medium text-white">{notification.title}</p>
                              <span className="text-xs text-white/60 whitespace-nowrap ml-2">
                                {formatTime(notification.timestamp)}
                              </span>
                            </div>
                            <p className="text-sm text-white/80 mt-1">{notification.message}</p>
                            {notification.link && (
                              <Link
                                href={notification.link}
                                className="text-xs text-doge hover:underline mt-1 inline-block"
                                onClick={(e) => e.stopPropagation()}
                              >
                                View details
                              </Link>
                            )}
                          </div>
                          {!notification.read && (
                            <button
                              className="text-white/60 hover:text-white"
                              onClick={(e) => {
                                e.stopPropagation()
                                markAsRead(notification.id)
                              }}
                              aria-label="Mark as read"
                            >
                              <Check className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      )}
    </div>
  )
}
