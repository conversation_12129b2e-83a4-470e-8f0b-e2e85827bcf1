"use client"

import { useState, useEffect, useRef, Suspense } from 'react'
import dynamic from 'next/dynamic'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { BarChart3, TrendingUp, Loader2 } from 'lucide-react'

// Lazy load chart components only when needed
const TradingChart = dynamic(
  () => import('@/components/trading-chart').then(mod => ({ default: mod.TradingChart })),
  {
    ssr: false,
    loading: () => (
      <div className="h-64 bg-white/5 rounded-lg flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-white/50" />
      </div>
    )
  }
)

const RechartsChart = dynamic(
  () => import('./recharts-wrapper').then(mod => ({ default: mod.RechartsWrapper })),
  {
    ssr: false,
    loading: () => (
      <div className="h-64 bg-white/5 rounded-lg flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-white/50" />
      </div>
    )
  }
)

interface ConditionalChartLoaderProps {
  chartType?: 'trading' | 'analytics' | 'performance'
  data?: any[]
  height?: number
  autoLoad?: boolean
  className?: string
}

export function ConditionalChartLoader({
  chartType = 'trading',
  data,
  height = 300,
  autoLoad = false,
  className = ''
}: ConditionalChartLoaderProps) {
  const [isLoaded, setIsLoaded] = useState(autoLoad)
  const [isVisible, setIsVisible] = useState(false)
  const [hasUserInteracted, setHasUserInteracted] = useState(false)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!containerRef.current || autoLoad) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        if (entry.isIntersecting) {
          setIsVisible(true)
          // Auto-load if user has scrolled to the chart
          if (!hasUserInteracted) {
            setTimeout(() => setIsLoaded(true), 500)
          }
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    )

    observerRef.current.observe(containerRef.current)

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [autoLoad, hasUserInteracted])

  const handleLoadChart = () => {
    setHasUserInteracted(true)
    setIsLoaded(true)
  }

  const getChartIcon = () => {
    switch (chartType) {
      case 'analytics':
        return <BarChart3 className="h-6 w-6" />
      case 'performance':
        return <TrendingUp className="h-6 w-6" />
      default:
        return <BarChart3 className="h-6 w-6" />
    }
  }

  const getChartTitle = () => {
    switch (chartType) {
      case 'analytics':
        return 'Analytics Chart'
      case 'performance':
        return 'Performance Chart'
      default:
        return 'Trading Chart'
    }
  }

  const renderChart = () => {
    switch (chartType) {
      case 'analytics':
      case 'performance':
        return <RechartsChart data={data} height={height} type={chartType} />
      default:
        return <TradingChart data={data} height={height} />
    }
  }

  if (!isLoaded) {
    return (
      <div ref={containerRef} className={className}>
        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div 
              className="flex flex-col items-center justify-center space-y-4"
              style={{ height: `${height}px` }}
            >
              <div className="flex items-center space-x-2 text-white/70">
                {getChartIcon()}
                <span className="text-lg font-medium">{getChartTitle()}</span>
              </div>
              
              <p className="text-white/50 text-center max-w-md">
                {isVisible 
                  ? "Click to load the chart and start analyzing data"
                  : "Chart will be available when you scroll to this section"
                }
              </p>
              
              {isVisible && (
                <Button 
                  onClick={handleLoadChart}
                  variant="outline"
                  className="glass-button border-white/20 hover:border-white/40"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Load Chart
                </Button>
              )}
              
              <div className="text-xs text-white/30 text-center">
                This saves ~{chartType === 'trading' ? '25KB' : '45KB'} of initial bundle size
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div ref={containerRef} className={className}>
      <Suspense fallback={
        <div className="h-64 bg-white/5 rounded-lg flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-white/50" />
        </div>
      }>
        {renderChart()}
      </Suspense>
    </div>
  )
}

// Hook for preloading charts when user is likely to need them
export function useChartPreloader() {
  const preloadCharts = () => {
    // Preload chart components when user shows intent
    Promise.allSettled([
      import('@/components/trading-chart'),
      import('./recharts-wrapper')
    ]).then(() => {
      console.log('Chart components preloaded')
    })
  }

  return { preloadCharts }
}

// Performance monitoring for chart loading
export function trackChartPerformance(chartType: string, loadTime: number) {
  if (process.env.NODE_ENV === 'development') {
    console.log(`Chart Performance: ${chartType} loaded in ${loadTime}ms`)
  }
  
  // In production, send to analytics
  if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
    // Example: analytics.track('chart_load_time', { chartType, loadTime })
  }
}
