"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip } from "@/components/ui/chart"

interface DelegationStatsData {
  month: string
  delegatedPower: number
  activeDelegators: number
}

interface DelegationChartProps {
  data: DelegationStatsData[]
}

export function DelegationChart({ data }: DelegationChartProps) {
  return (
    <ChartContainer
      config={{
        delegatedPower: {
          label: "Delegated Power",
          color: "hsl(var(--chart-1))",
        },
        activeDelegators: {
          label: "Active Delegators",
          color: "hsl(var(--chart-2))",
        },
      }}
      className="h-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
          <XAxis
            dataKey="month"
            stroke="rgba(255,255,255,0.5)"
            tick={{ fill: "rgba(255,255,255,0.5)" }}
          />
          <YAxis
            yAxisId="left"
            orientation="left"
            stroke="rgba(255,255,255,0.5)"
            tick={{ fill: "rgba(255,255,255,0.5)" }}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            stroke="rgba(255,255,255,0.5)"
            tick={{ fill: "rgba(255,255,255,0.5)" }}
          />
          <ChartTooltip />
          <Bar
            yAxisId="left"
            dataKey="delegatedPower"
            fill="var(--color-delegatedPower)"
            radius={[4, 4, 0, 0]}
          />
          <Bar
            yAxisId="right"
            dataKey="activeDelegators"
            fill="var(--color-activeDelegators)"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
