"use client"

import React, { useMemo, useEffect, useState, useCallback } from 'react';

// Lazy load recharts components
let RechartsComponents: any = null

const loadRechartsComponents = async () => {
  if (RechartsComponents) return RechartsComponents

  const [
    { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer },
    { BarChart, Bar },
    { AreaChart, Area }
  ] = await Promise.all([
    import('recharts'),
    import('recharts'),
    import('recharts')
  ])

  RechartsComponents = {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    BarChart,
    Bar,
    AreaChart,
    Area
  }

  return RechartsComponents
}

interface RechartsWrapperProps {
  data?: any[]
  height?: number
  type?: 'trading' | 'analytics' | 'performance'
}

export function RechartsWrapper({ data, height = 300, type = 'trading' }: RechartsWrapperProps) {
  const [components, setComponents] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadRechartsComponents().then((comps) => {
      setComponents(comps)
      setIsLoading(false)
    })
  }, [])

  const chartData = useMemo(() => {
    if (data) return data

    // Generate sample data based on chart type
    const sampleData = []
    const points = type === 'performance' ? 30 : 50
    
    for (let i = 0; i < points; i++) {
      const baseValue = type === 'performance' ? 85 : 100
      const variance = type === 'performance' ? 10 : 20
      
      sampleData.push({
        name: `Point ${i + 1}`,
        value: baseValue + Math.random() * variance - variance / 2,
        volume: Math.random() * 1000000,
        timestamp: new Date(Date.now() - (points - i) * 24 * 60 * 60 * 1000).toISOString()
      })
    }
    
    return sampleData
  }, [data, type])

  if (isLoading || !components) {
    return (
      <div 
        className="bg-white/5 rounded-lg animate-pulse flex items-center justify-center"
        style={{ height: `${height}px` }}
      >
        <div className="text-white/50">Loading chart...</div>
      </div>
    )
  }

  const {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    BarChart,
    Bar,
    AreaChart,
    Area
  } = components

  // Define props type for CustomBar
  interface CustomBarProps {
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    index?: number;
    payload?: any;
    [key: string]: any;
  }

  // Memoize the CustomBar to prevent unnecessary re-renders
  const CustomBar = React.memo(({ x = 0, y = 0, width = 0, height = 0, index = 0 }: CustomBarProps) => {
    // Create a unique ID for each bar's gradient
    const gradientId = `volumeBarGradient-${index}`;
    
    // Don't render if dimensions are invalid
    if (width <= 0 || height <= 0) return null;
    
    return (
      <g>
        <defs>
          <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor="#8B5CF6" stopOpacity={0.8} />
            <stop offset="100%" stopColor="#3B82F6" stopOpacity={0.3} />
          </linearGradient>
        </defs>
        <rect
          x={x}
          y={y}
          width={width}
          height={height}
          fill={`url(#${gradientId})`}
          rx={2}
          ry={2}
          style={{
            cursor: 'pointer',
            shapeRendering: 'crispEdges',
            WebkitTapHighlightColor: 'transparent',
            transition: 'opacity 0.1s ease',
            opacity: 0.8
          }}
          className="volume-bar-rect hover:opacity-100"
        />
      </g>
    );
  });

  // Custom tooltip formatter - simplified to match the smaller tooltip
  const renderTooltip = (props: any) => {
    const { active, payload, label } = props;
    if (!active || !payload || !payload.length) return null;
    
    // Format date for display
    const date = new Date(label);
    const formattedDate = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
    
    // Format volume with appropriate units
    const volume = payload[0]?.value || 0;
    let formattedVolume = '';
    
    if (volume >= 1000000) {
      formattedVolume = `${(volume / 1000000).toFixed(1)}M`;
    } else if (volume >= 1000) {
      formattedVolume = `${(volume / 1000).toFixed(1)}K`;
    } else {
      formattedVolume = volume.toString();
    }
    
    return (
      <div className="custom-tooltip bg-black/90 border border-white/10 rounded px-3 py-2 text-xs text-white shadow-lg">
        <div className="font-medium">{formattedDate}</div>
        <div className="text-white/80">Volume: {formattedVolume}</div>
      </div>
    );
  };

  const renderChart = () => {
    switch (type) {
      case 'analytics':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
              <XAxis 
                dataKey="name" 
                stroke="rgba(255,255,255,0.5)"
                fontSize={12}
              />
              <YAxis 
                stroke="rgba(255,255,255,0.5)"
                fontSize={12}
              />
              <Bar
                dataKey="volume"
                name="Volume"
                shape={(props: any, index: number) => (
                  <CustomBar {...props} index={index} />
                )}
                className="volume-bar"
                isAnimationActive={false}
                radius={[2, 2, 0, 0]}
              />
              <Tooltip 
                content={renderTooltip}
                cursor={{ fill: 'rgba(255, 255, 255, 0.1)' }}
                wrapperStyle={{
                  zIndex: 1000,
                  pointerEvents: 'none'
                }}
                contentStyle={{
                  backgroundColor: 'rgba(0, 0, 0, 0.9)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: '6px',
                  padding: '6px 12px',
                  fontSize: '12px',
                  color: 'white',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }}
                itemStyle={{
                  color: 'white',
                  padding: '2px 0',
                  textTransform: 'capitalize'
                }}
                labelStyle={{
                  color: 'rgba(255, 255, 255, 0.7)',
                  marginBottom: '4px',
                  fontWeight: 500
                }}
                formatter={(value: any, name: any, props: any) => {
                  // Format the value with appropriate units
                  let formattedValue = value;
                  if (name === 'Volume') {
                    if (value >= 1000000) {
                      formattedValue = `${(value / 1000000).toFixed(1)}M`;
                    } else if (value >= 1000) {
                      formattedValue = `${(value / 1000).toFixed(1)}K`;
                    }
                  } else if (typeof value === 'number') {
                    formattedValue = value.toLocaleString();
                  }
                  return [formattedValue, name];
                }}
              />
            </BarChart>
          </ResponsiveContainer>
        )

      case 'performance':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <AreaChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
              <XAxis 
                dataKey="name" 
                stroke="rgba(255,255,255,0.5)"
                fontSize={12}
              />
              <YAxis 
                stroke="rgba(255,255,255,0.5)"
                fontSize={12}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'rgba(0,0,0,0.8)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
              <Area
                type="monotone"
                dataKey="value"
                stroke="#10B981"
                fill="url(#areaGradient)"
                strokeWidth={2}
                isAnimationActive={false}
              />
              <defs>
                <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#10B981" stopOpacity={0.3} />
                  <stop offset="100%" stopColor="#10B981" stopOpacity={0.05} />
                </linearGradient>
              </defs>
            </AreaChart>
          </ResponsiveContainer>
        )

      default: // trading
        return (
          <ResponsiveContainer width="100%" height={height}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
              <XAxis 
                dataKey="name" 
                stroke="rgba(255,255,255,0.5)"
                fontSize={12}
              />
              <YAxis 
                stroke="rgba(255,255,255,0.5)"
                fontSize={12}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'rgba(0,0,0,0.8)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#F59E0B"
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 4, fill: '#F59E0B' }}
                isAnimationActive={false}
              />
            </LineChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <div className="w-full">
      {renderChart()}
    </div>
  )
}
