"use client"

import type React from "react"
import Link from "next/link"

import { useState } from "react"
import { Penci<PERSON>, Save, User, Wallet, Clock, Trophy, Settings, Heart, MessageSquare, Share2 } from "lucide-react"
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useWallet } from "@/components/wallet-provider"
import { useToast } from "@/components/ui/use-toast"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"

export function UserProfile() {
  const { isConnected, address } = useWallet()
  const { toast } = useToast()
  const [isEditing, setIsEditing] = useState(false)
  const [profileData, setProfileData] = useState({
    username: "DogeWhale",
    bio: "Memecoin enthusiast and early adopter of PawPumps. Always looking for the next 100x gem!",
    avatar: "/placeholder.svg?key=jg7nh",
    twitter: "@dogewhale",
    website: "https://dogewhale.xyz",
  })

  const [editForm, setEditForm] = useState({ ...profileData })

  const handleEditSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setProfileData(editForm)
    setIsEditing(false)

    toast({
      title: "Profile updated",
      description: "Your profile has been updated successfully.",
    })
  }

  // Mock activity data
  const activities = [
    {
      id: "1",
      type: "trade",
      description: "Swapped 100 wDOGE for 42,000 PAW",
      timestamp: "2 hours ago",
      icon: Wallet,
      iconColor: "text-doge",
    },
    {
      id: "2",
      type: "social",
      description: "Posted a comment on RocketDoge",
      timestamp: "5 hours ago",
      icon: MessageSquare,
      iconColor: "text-dogechain",
    },
    {
      id: "3",
      type: "reward",
      description: "Earned 50 PAW for First Trade achievement",
      timestamp: "1 day ago",
      icon: Trophy,
      iconColor: "text-doge",
    },
    {
      id: "4",
      type: "governance",
      description: "Voted on 'Add Perpetual Trading' proposal",
      timestamp: "2 days ago",
      icon: Settings,
      iconColor: "text-dogechain",
    },
    {
      id: "5",
      type: "trade",
      description: "Provided liquidity to PAW/wDOGE pool",
      timestamp: "3 days ago",
      icon: Wallet,
      iconColor: "text-doge",
    },
  ]

  // Mock tokens data
  const tokens = [
    {
      name: "PawPumps",
      symbol: "PAW",
      balance: "42,000",
      value: "$176.40",
      change24h: 12.5,
    },
    {
      name: "DogeChain",
      symbol: "DC",
      balance: "1,500",
      value: "$15.75",
      change24h: 3.2,
    },
    {
      name: "RocketDoge",
      symbol: "RDOGE",
      balance: "100,000",
      value: "$78.00",
      change24h: 28.4,
    },
    {
      name: "DogeKing",
      symbol: "DGK",
      balance: "250,000",
      value: "$57.50",
      change24h: -5.7,
    },
  ]

  // Mock achievements data
  const achievements = [
    {
      name: "Early Adopter",
      description: "Joined PawPumps in the first month",
      progress: 100,
      icon: Clock,
      completed: true,
    },
    {
      name: "Trading Volume",
      description: "Reach $1,000 in trading volume",
      progress: 65,
      icon: Wallet,
      completed: false,
    },
    {
      name: "Social Butterfly",
      description: "Make 10 posts or comments",
      progress: 40,
      icon: MessageSquare,
      completed: false,
    },
    {
      name: "Governance Participant",
      description: "Vote on 5 governance proposals",
      progress: 20,
      icon: Settings,
      completed: false,
    },
  ]

  if (!isConnected) {
    return (
      <Card className="glass-card border-white/5 liquid-glow">
        <CardContent className="flex flex-col items-center justify-center py-12 text-center">
          <User className="h-16 w-16 text-white/20 mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">Connect Wallet</h2>
          <p className="text-white/70 mb-6 max-w-md">
            Connect your wallet to view and manage your profile, track your activity, and see your token holdings.
          </p>
          <Button className="doge-button doge-shine">Connect Wallet</Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid gap-8 lg:grid-cols-3">
      <div className="lg:col-span-2">
        <Card className="glass-card border-white/5 liquid-glow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white">Profile</CardTitle>
              {!isEditing ? (
                <Button variant="outline" size="sm" className="glass-button" onClick={() => setIsEditing(true)}>
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit Profile
                </Button>
              ) : (
                <Button variant="outline" size="sm" className="glass-button" onClick={() => setIsEditing(false)}>
                  Cancel
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {!isEditing ? (
              <div className="flex flex-col md:flex-row gap-6">
                <Avatar className="h-24 w-24">
                  <AvatarImage src={profileData.avatar || "/placeholder.svg"} alt={profileData.username} />
                  <AvatarFallback>{profileData.username.slice(0, 2)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h2 className="text-2xl font-bold text-white">{profileData.username}</h2>
                  <p className="text-sm text-white/60 mb-4">
                    {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : ""}
                  </p>
                  <p className="text-white/80 mb-4">{profileData.bio}</p>
                  <div className="flex flex-wrap gap-4">
                    {profileData.twitter && (
                      <div className="flex items-center gap-2 text-white/60">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                        </svg>
                        <span>{profileData.twitter}</span>
                      </div>
                    )}
                    {profileData.website && (
                      <div className="flex items-center gap-2 text-white/60">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <circle cx="12" cy="12" r="10" />
                          <line x1="2" y1="12" x2="22" y2="12" />
                          <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
                        </svg>
                        <span>{profileData.website}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <form onSubmit={handleEditSubmit} className="space-y-6">
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="flex flex-col items-center gap-2">
                    <Avatar className="h-24 w-24">
                      <AvatarImage src={editForm.avatar || "/placeholder.svg"} alt={editForm.username} />
                      <AvatarFallback>{editForm.username.slice(0, 2)}</AvatarFallback>
                    </Avatar>
                    <Button variant="outline" size="sm" className="glass-button w-full">
                      Change Avatar
                    </Button>
                  </div>
                  <div className="flex-1 space-y-4">
                    <div>
                      <Label htmlFor="username" className="text-white/80">
                        Username
                      </Label>
                      <Input
                        id="username"
                        value={editForm.username}
                        onChange={(e) => setEditForm({ ...editForm, username: e.target.value })}
                        className="glass-input border-white/10 focus:border-white/20"
                      />
                    </div>
                    <div>
                      <Label htmlFor="bio" className="text-white/80">
                        Bio
                      </Label>
                      <Textarea
                        id="bio"
                        value={editForm.bio}
                        onChange={(e) => setEditForm({ ...editForm, bio: e.target.value })}
                        className="glass-input border-white/10 focus:border-white/20 min-h-[100px]"
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="twitter" className="text-white/80">
                          Twitter
                        </Label>
                        <Input
                          id="twitter"
                          value={editForm.twitter}
                          onChange={(e) => setEditForm({ ...editForm, twitter: e.target.value })}
                          className="glass-input border-white/10 focus:border-white/20"
                        />
                      </div>
                      <div>
                        <Label htmlFor="website" className="text-white/80">
                          Website
                        </Label>
                        <Input
                          id="website"
                          value={editForm.website}
                          onChange={(e) => setEditForm({ ...editForm, website: e.target.value })}
                          className="glass-input border-white/10 focus:border-white/20"
                        />
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <Button type="submit" className="doge-button doge-shine">
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </Button>
                    </div>
                  </div>
                </div>
              </form>
            )}
          </CardContent>
        </Card>

        <div className="mt-8">
          <Tabs defaultValue="activity" className="w-full">
            <TabsList className="grid w-full grid-cols-3 glass">
              <TabsTrigger value="activity">Activity</TabsTrigger>
              <TabsTrigger value="tokens">Tokens</TabsTrigger>
              <TabsTrigger value="achievements">Achievements</TabsTrigger>
            </TabsList>

            <TabsContent value="activity" className="mt-6">
              <Card className="glass-card border-white/5">
                <CardHeader>
                  <CardTitle className="text-white">Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {activities.map((activity) => (
                      <div key={activity.id} className="flex gap-4">
                        <div
                          className={`flex h-10 w-10 items-center justify-center rounded-full bg-white/5 ${activity.iconColor}`}
                        >
                          <activity.icon className="h-5 w-5" />
                        </div>
                        <div className="flex-1">
                          <p className="text-white/90">{activity.description}</p>
                          <p className="text-sm text-white/60">{activity.timestamp}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tokens" className="mt-6">
              <Card className="glass-card border-white/5">
                <CardHeader>
                  <CardTitle className="text-white">Token Holdings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {tokens.map((token, index) => (
                      <div key={token.symbol} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div
                            className={`flex h-10 w-10 items-center justify-center rounded-full ${
                              index % 2 === 0 ? "bg-doge/10 text-doge" : "bg-dogechain/10 text-dogechain"
                            } text-xs font-bold`}
                          >
                            {token.symbol.slice(0, 2)}
                          </div>
                          <div>
                            <p className="font-medium text-white">{token.name}</p>
                            <p className="text-xs text-white/60">
                              {token.balance} {token.symbol}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-white">{token.value}</p>
                          <p className={`text-xs ${token.change24h >= 0 ? "text-green-500" : "text-red-500"}`}>
                            {token.change24h >= 0 ? "+" : ""}
                            {token.change24h.toFixed(2)}%
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="achievements" className="mt-6">
              <Card className="glass-card border-white/5">
                <CardHeader>
                  <CardTitle className="text-white">Governance Achievements</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-medium text-white">Reputation Score</h3>
                      <span className="text-xl font-bold text-doge doge-text-glow">785</span>
                    </div>
                    <Progress
                      value={78.5}
                      className="h-2 bg-white/10"
                      indicatorClassName="bg-gradient-to-r from-doge to-dogechain"
                    />
                    <div className="flex justify-between mt-1 text-xs text-white/60">
                      <span>Beginner</span>
                      <span>Contributor</span>
                      <span>Expert</span>
                    </div>
                  </div>

                  <div className="space-y-6">
                    {achievements.map((achievement) => (
                      <div key={achievement.name} className="space-y-2">
                        <div className="flex items-center gap-3">
                          <div
                            className={`flex h-10 w-10 items-center justify-center rounded-full ${
                              achievement.completed ? "bg-doge/20 text-doge" : "bg-white/10 text-white/60"
                            }`}
                          >
                            <achievement.icon className="h-5 w-5" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <p className="font-medium text-white">{achievement.name}</p>
                              <p className={`text-sm ${achievement.completed ? "text-doge" : "text-white/60"}`}>
                                {achievement.progress}%
                              </p>
                            </div>
                            <p className="text-sm text-white/60">{achievement.description}</p>
                          </div>
                        </div>
                        <Progress
                          value={achievement.progress}
                          className="h-2 bg-white/10"
                          indicatorClassName={achievement.completed ? "bg-doge" : "bg-white/30"}
                        />
                      </div>
                    ))}

                    <Link
                      href="/governance/achievements"
                      className="flex items-center justify-center mt-4 text-doge hover:underline"
                    >
                      <Trophy className="h-4 w-4 mr-2" />
                      View All Achievements and Leaderboards
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <div>
        <Card className="glass-card border-white/5 sticky top-20">
          <CardHeader>
            <CardTitle className="text-white">Stats</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 rounded-lg bg-white/5 text-center">
                <p className="text-sm text-white/60">Trading Volume</p>
                <p className="text-2xl font-bold text-doge doge-text-glow">$1,250</p>
              </div>
              <div className="p-4 rounded-lg bg-white/5 text-center">
                <p className="text-sm text-white/60">Tokens Held</p>
                <p className="text-2xl font-bold text-dogechain dogechain-text-glow">4</p>
              </div>
              <div className="p-4 rounded-lg bg-white/5 text-center">
                <p className="text-sm text-white/60">Governance Votes</p>
                <p className="text-2xl font-bold text-doge doge-text-glow">3</p>
              </div>
              <div className="p-4 rounded-lg bg-white/5 text-center">
                <p className="text-sm text-white/60">Achievements</p>
                <p className="text-2xl font-bold text-dogechain dogechain-text-glow">1/10</p>
              </div>
            </div>

            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <h3 className="text-sm font-medium text-white mb-2">Rewards</h3>
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs text-white/70">Total Earned</span>
                <span className="text-sm font-medium text-doge">375 $PAW</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-white/70">Available</span>
                <span className="text-sm font-medium text-white">225 $PAW</span>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium text-white">Social</h3>
              <div className="flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                <div className="flex items-center gap-2">
                  <Heart className="h-4 w-4 text-doge" />
                  <span className="text-sm text-white/80">Likes Received</span>
                </div>
                <span className="text-sm font-medium text-white">42</span>
              </div>
              <div className="flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4 text-dogechain" />
                  <span className="text-sm text-white/80">Comments</span>
                </div>
                <span className="text-sm font-medium text-white">18</span>
              </div>
              <div className="flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                <div className="flex items-center gap-2">
                  <Share2 className="h-4 w-4 text-doge" />
                  <span className="text-sm text-white/80">Shares</span>
                </div>
                <span className="text-sm font-medium text-white">7</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
