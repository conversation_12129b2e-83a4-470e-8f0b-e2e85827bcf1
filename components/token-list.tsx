"use client"

import { useState, useMemo, memo } from "react"
import { Arrow<PERSON><PERSON>, ArrowDown, ArrowUpDown, ChevronUp, ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"

type Token = {
  id: string
  name: string
  symbol: string
  price: number
  change24h: number
  volume24h: number
  marketCap?: number
}

type SortField = 'name' | 'price' | 'change24h' | 'volume24h' | 'marketCap'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

const mockTokens: Token[] = [
  { id: "1", name: "PawPumps", symbol: "PAW", price: 0.0042, change24h: 12.5, volume24h: 1250000, marketCap: 4200000 },
  { id: "2", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", symbol: "DC", price: 0.0105, change24h: 3.2, volume24h: 3450000, marketCap: 10500000 },
  { id: "3", name: "<PERSON><PERSON><PERSON><PERSON>", symbol: "DGK", price: 0.00023, change24h: -5.7, volume24h: 890000, marketCap: 230000 },
  { id: "4", name: "<PERSON><PERSON><PERSON><PERSON>", symbol: "MDO<PERSON>", price: 0.00078, change24h: 28.4, volume24h: 2100000, marketCap: 780000 },
  { id: "5", name: "RocketPaw", symbol: "RPAW", price: 0.00056, change24h: -2.1, volume24h: 560000, marketCap: 560000 },
  { id: "6", name: "ShibaDoge", symbol: "SHIBDOGE", price: 0.00012, change24h: 15.3, volume24h: 420000, marketCap: 120000 },
  { id: "7", name: "DogeMoon", symbol: "DMOON", price: 0.00034, change24h: 7.8, volume24h: 340000, marketCap: 340000 },
  { id: "8", name: "PawToken", symbol: "PAWT", price: 0.00009, change24h: -8.2, volume24h: 290000, marketCap: 90000 },
]

export const TokenList = memo(function TokenList() {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ field: 'volume24h', direction: 'desc' })

  // Sort tokens based on current sort configuration
  const sortedTokens = useMemo(() => {
    const sorted = [...mockTokens].sort((a, b) => {
      const aValue = a[sortConfig.field]
      const bValue = b[sortConfig.field]

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortConfig.direction === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc'
          ? aValue - bValue
          : bValue - aValue
      }

      return 0
    })

    return sorted
  }, [sortConfig])

  // Handle column header click for sorting
  const handleSort = (field: SortField) => {
    setSortConfig(prevConfig => ({
      field,
      direction: prevConfig.field === field && prevConfig.direction === 'desc' ? 'asc' : 'desc'
    }))
  }

  // Get sort icon for column header
  const getSortIcon = (field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="h-3 w-3 opacity-50 transition-all duration-200 hover:opacity-80" />
    }

    return sortConfig.direction === 'asc'
      ? <ChevronUp className="h-3 w-3 text-doge transition-all duration-300 animate-flip" />
      : <ChevronDown className="h-3 w-3 text-doge transition-all duration-300 animate-flip" />
  }

  // Format large numbers
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(2)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b border-white/10">
            <th
              className="pb-2 text-left text-sm font-medium text-white/60 cursor-pointer hover:text-white transition-colors group"
              onClick={() => handleSort('name')}
            >
              <div className="flex items-center gap-1">
                Token
                <span className="opacity-0 group-hover:opacity-100 transition-opacity">
                  {getSortIcon('name')}
                </span>
              </div>
            </th>
            <th
              className="pb-2 text-right text-sm font-medium text-white/60 cursor-pointer hover:text-white transition-colors group"
              onClick={() => handleSort('price')}
            >
              <div className="flex items-center justify-end gap-1">
                Price
                {getSortIcon('price')}
              </div>
            </th>
            <th
              className="pb-2 text-right text-sm font-medium text-white/60 cursor-pointer hover:text-white transition-colors group"
              onClick={() => handleSort('change24h')}
            >
              <div className="flex items-center justify-end gap-1">
                24h
                {getSortIcon('change24h')}
              </div>
            </th>
            <th
              className="pb-2 text-right text-sm font-medium text-white/60 cursor-pointer hover:text-white transition-colors group"
              onClick={() => handleSort('volume24h')}
            >
              <div className="flex items-center justify-end gap-1">
                Volume
                {getSortIcon('volume24h')}
              </div>
            </th>
            <th
              className="pb-2 text-right text-sm font-medium text-white/60 cursor-pointer hover:text-white transition-colors group"
              onClick={() => handleSort('marketCap')}
            >
              <div className="flex items-center justify-end gap-1">
                Market Cap
                <span className="opacity-0 group-hover:opacity-100 transition-opacity">
                  {getSortIcon('marketCap')}
                </span>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          {sortedTokens.map((token, index) => (
            <tr
              key={token.id}
              className="border-b border-white/5 hover:bg-white/5 transition-colors duration-200"
            >
              <td className="py-3">
                <div className="flex items-center gap-2">
                  <div
                    className={`flex h-8 w-8 items-center justify-center rounded-full ${index % 2 === 0 ? "bg-doge/10 text-doge" : "bg-dogechain/10 text-dogechain"} text-xs font-bold`}
                  >
                    {token.symbol.slice(0, 2)}
                  </div>
                  <div>
                    <div className="font-medium text-white">{token.name}</div>
                    <div className="text-xs text-white/60">{token.symbol}</div>
                  </div>
                </div>
              </td>
              <td className="py-3 text-right font-medium text-white">${token.price.toFixed(6)}</td>
              <td className="py-3 text-right">
                <div
                  className={cn(
                    "flex items-center justify-end gap-1",
                    token.change24h >= 0 ? "text-green-500" : "text-red-500",
                  )}
                >
                  {token.change24h >= 0 ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />}
                  {Math.abs(token.change24h).toFixed(2)}%
                </div>
              </td>
              <td className="py-3 text-right text-white/60">${formatNumber(token.volume24h)}</td>
              <td className="py-3 text-right text-white/60">${formatNumber(token.marketCap || 0)}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
})
