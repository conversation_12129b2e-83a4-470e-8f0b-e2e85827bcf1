"use client"
import Image from "next/image"

export function FloatingTokens() {
  // Create an array of token configurations
  const tokens = [
    {
      id: 1,
      src: "/images/dogecoin-logo.png",
      alt: "Dogecoin Logo",
      size: 90,
      left: "10%",
      top: "70%",
      animationDuration: "25s",
      delay: "0s",
    },
    {
      id: 2,
      src: "/images/dogechain-logo.png",
      alt: "Dogechain Logo",
      size: 112,
      left: "75%",
      top: "85%",
      animationDuration: "30s",
      delay: "5s",
    },
    {
      id: 3,
      src: "/images/pawpumps-icon.png",
      alt: "PawPumps Logo",
      size: 75,
      left: "25%",
      top: "90%",
      animationDuration: "20s",
      delay: "2s",
    },
    {
      id: 4,
      src: "/images/dogecoin-logo.png",
      alt: "Dogecoin Logo",
      size: 60,
      left: "60%",
      top: "75%",
      animationDuration: "22s",
      delay: "7s",
    },
    {
      id: 5,
      src: "/images/dogechain-logo.png",
      alt: "Dogechain Logo",
      size: 135,
      left: "85%",
      top: "65%",
      animationDuration: "35s",
      delay: "3s",
    },
    {
      id: 6,
      src: "/images/pawpumps-icon.png",
      alt: "PawPumps Logo",
      size: 97,
      left: "40%",
      top: "80%",
      animationDuration: "28s",
      delay: "1s",
    },
  ]

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none" style={{ zIndex: 1 }}>
      <style jsx global>{`
        @keyframes floatUp {
          0% {
            transform: translateY(0) rotate(0deg);
          }
          100% {
            transform: translateY(-100vh) rotate(360deg);
          }
        }
      `}</style>

      {tokens.map((token) => (
        <div
          key={token.id}
          className="absolute"
          style={{
            left: token.left,
            top: token.top,
            width: token.size,
            height: token.size,
            animation: `floatUp ${token.animationDuration} linear infinite`,
            animationDelay: token.delay,
            opacity: 0.2,
            zIndex: 1, // Lower z-index to appear behind the moon
          }}
        >
          <Image
            src={token.src || "/placeholder.svg"}
            alt={token.alt}
            width={token.size}
            height={token.size}
            className="w-full h-full object-contain"
          />
        </div>
      ))}
    </div>
  )
}
