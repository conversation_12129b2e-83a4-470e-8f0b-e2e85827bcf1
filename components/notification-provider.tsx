"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { v4 as uuidv4 } from "uuid"

// Update the NotificationType to include more types
export type NotificationType = "success" | "error" | "info" | "warning" | "governance" | "price" | "token" | "system"

// Update the Notification interface to include more properties
export interface Notification {
  id: string
  title: string
  message: string
  type: NotificationType
  read: boolean
  timestamp: Date
  link?: string
  category?: string
  priority?: "low" | "medium" | "high"
  expiresAt?: Date
  actions?: Array<{
    label: string
    onClick: () => void
  }>
}

type NotificationContextType = {
  notifications: Notification[]
  unreadCount: number
  addNotification: (notification: Omit<Notification, "id" | "read" | "timestamp">) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  removeNotification: (id: string) => void
  clearAllNotifications: () => void
  getNotificationsByCategory: (category: string) => Notification[]
  getNotificationsByType: (type: NotificationType) => Notification[]
  getUnreadNotificationsByCategory: (category: string) => Notification[]
}

export const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  unreadCount: 0,
  addNotification: () => {},
  markAsRead: () => {},
  markAllAsRead: () => {},
  removeNotification: () => {},
  clearAllNotifications: () => {},
  getNotificationsByCategory: () => [],
  getNotificationsByType: () => [],
  getUnreadNotificationsByCategory: () => [],
})

export const useNotifications = () => useContext(NotificationContext)

// Add more mock notifications with the new properties
const mockNotifications: Omit<Notification, "id" | "read" | "timestamp">[] = [
  {
    title: "Welcome to PawPumps!",
    message: "Start exploring the premier memecoin launchpad and DEX on the Dogechain Network.",
    type: "info",
    category: "system",
    priority: "medium",
  },
  {
    title: "New Token Launch",
    message: "RocketDoge ($RDOGE) has just launched! Check it out now.",
    type: "token",
    link: "/token/RDOGE",
    category: "token",
    priority: "high",
  },
  {
    title: "Governance Proposal",
    message: "New proposal 'Add Perpetual Trading' is now open for voting.",
    type: "governance",
    link: "/governance",
    category: "governance",
    priority: "high",
  },
  {
    title: "Price Alert",
    message: "DOGE/USDT has increased by 5% in the last hour.",
    type: "price",
    link: "/trade?pair=DOGE-USDT",
    category: "price",
    priority: "medium",
  },
]

// Add the new methods to the context provider
export function NotificationProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)

  // Initialize with mock notifications
  useEffect(() => {
    const initialNotifications = mockNotifications.map((notification) => ({
      ...notification,
      id: uuidv4(),
      read: false,
      timestamp: new Date(Date.now() - Math.floor(Math.random() * 86400000)), // Random time in the last 24 hours
    }))

    setNotifications(initialNotifications)
    setUnreadCount(initialNotifications.length)

    // Check for saved notifications in localStorage
    const savedNotifications = localStorage.getItem("pawpumps-notifications")
    if (savedNotifications) {
      try {
        const parsedNotifications = JSON.parse(savedNotifications)
        // Convert string timestamps back to Date objects
        const processedNotifications = parsedNotifications.map((n: any) => ({
          ...n,
          timestamp: new Date(n.timestamp),
        }))
        setNotifications(processedNotifications)
        setUnreadCount(processedNotifications.filter((n: Notification) => !n.read).length)
      } catch (error) {
        console.error("Failed to parse saved notifications:", error)
      }
    }
  }, [])

  // Save notifications to localStorage when they change
  useEffect(() => {
    if (notifications.length > 0) {
      localStorage.setItem("pawpumps-notifications", JSON.stringify(notifications))
    }
  }, [notifications])

  const addNotification = (notification: Omit<Notification, "id" | "read" | "timestamp">) => {
    const newNotification: Notification = {
      ...notification,
      id: uuidv4(),
      read: false,
      timestamp: new Date(),
    }

    setNotifications((prev) => [newNotification, ...prev])
    setUnreadCount((prev) => prev + 1)
  }

  const markAsRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((notification) => {
        if (notification.id === id && !notification.read) {
          setUnreadCount((count) => Math.max(0, count - 1))
          return { ...notification, read: true }
        }
        return notification
      }),
    )
  }

  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({
        ...notification,
        read: true,
      })),
    )
    setUnreadCount(0)
  }

  const removeNotification = (id: string) => {
    setNotifications((prev) => {
      const notification = prev.find((n) => n.id === id)
      if (notification && !notification.read) {
        setUnreadCount((count) => Math.max(0, count - 1))
      }
      return prev.filter((notification) => notification.id !== id)
    })
  }

  const clearAllNotifications = () => {
    setNotifications([])
    setUnreadCount(0)
  }

  const getNotificationsByCategory = (category: string) => {
    return notifications.filter((notification) => notification.category === category)
  }

  const getNotificationsByType = (type: NotificationType) => {
    return notifications.filter((notification) => notification.type === type)
  }

  const getUnreadNotificationsByCategory = (category: string) => {
    return notifications.filter((notification) => notification.category === category && !notification.read)
  }

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        removeNotification,
        clearAllNotifications,
        getNotificationsByCategory,
        getNotificationsByType,
        getUnreadNotificationsByCategory,
      }}
    >
      {children}
    </NotificationContext.Provider>
  )
}
