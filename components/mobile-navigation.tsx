"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
  BarChart2,
  ChevronDown,
  FileText,
  GitPullRequest,
  Home,
  Landmark,
  Lock,
  Map,
  Rocket,
  Shield,
  TrendingUp,
  Users,
} from "lucide-react"
import { cn } from "@/lib/utils"

export function MobileNavigation() {
  const pathname = usePathname()
  const [activeSection, setActiveSection] = useState<"trading" | "governance">(
    pathname?.includes("/governance")
      ? "governance"
      : "trading",
  )

  const tradingNavItems = [
    {
      title: "Home",
      href: "/",
      icon: <Home className="h-5 w-5" />,
    },
    {
      title: "Trade",
      href: "/trade",
      icon: <TrendingUp className="h-5 w-5" />,
    },
    {
      title: "Launch",
      href: "/launch",
      icon: <Rocket className="h-5 w-5" />,
    },
    {
      title: "Analytics",
      href: "/analytics",
      icon: <BarChart2 className="h-5 w-5" />,
    },
    {
      title: "Social",
      href: "/social",
      icon: <Users className="h-5 w-5" />,
    },
    {
      title: "Docs",
      href: "/docs",
      icon: <FileText className="h-5 w-5" />,
    },
  ]

  const governanceNavItems = [
    {
      title: "Proposals",
      href: "/governance/proposals",
      icon: <GitPullRequest className="h-5 w-5" />,
    },
    {
      title: "Staking",
      href: "/governance/staking",
      icon: <Lock className="h-5 w-5" />,
    },
    {
      title: "Treasury",
      href: "/governance/treasury",
      icon: <Landmark className="h-5 w-5" />,
    },
    {
      title: "Admin",
      href: "/governance/admin/dashboard",
      icon: <Shield className="h-5 w-5" />,
    },
    {
      title: "Roadmap",
      href: "/roadmap",
      icon: <Map className="h-5 w-5" />,
    },
  ]





  return (
    <div className="space-y-6 px-4">
      <Tabs value={activeSection} onValueChange={(value) => setActiveSection(value as any)} className="w-full">
        <TabsList className="grid w-full grid-cols-2 glass">
          <TabsTrigger value="trading">Trading</TabsTrigger>
          <TabsTrigger value="governance">Governance</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="space-y-1">
        {activeSection === "trading" &&
          tradingNavItems.map((item) => (
            <Link key={item.href} href={item.href}>
              <Button
                variant="ghost"
                className={cn("w-full justify-start", pathname === item.href && "bg-white/10 text-white")}
              >
                {item.icon}
                <span className="ml-2">{item.title}</span>
              </Button>
            </Link>
          ))}

        {activeSection === "governance" &&
          governanceNavItems.map((item) => (
            <Link key={item.href} href={item.href}>
              <Button
                variant="ghost"
                className={cn("w-full justify-start", pathname === item.href && "bg-doge/10 text-doge")}
              >
                {item.icon}
                <span className="ml-2">{item.title}</span>
              </Button>
            </Link>
          ))}


      </div>

      {activeSection !== "trading" && (
        <Collapsible className="w-full">
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="w-full justify-between border-white/10 bg-white/5">
              <span>Quick Access</span>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-1 space-y-1">
            {tradingNavItems.slice(0, 3).map((item) => (
              <Link key={item.href} href={item.href}>
                <Button variant="ghost" className="w-full justify-start">
                  {item.icon}
                  <span className="ml-2">{item.title}</span>
                </Button>
              </Link>
            ))}
          </CollapsibleContent>
        </Collapsible>
      )}
    </div>
  )
}

function RocketIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z" />
      <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z" />
      <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0" />
      <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5" />
    </svg>
  )
}
