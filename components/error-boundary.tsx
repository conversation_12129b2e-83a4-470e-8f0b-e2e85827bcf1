"use client"

import { Component, type ErrorInfo, type ReactNode } from "react"
import { ErrorState } from "@/components/ui/error-state"
import { getGlobalErrorHandler, classifyError } from "@/lib/error-handling"

interface Props {
  children: ReactNode
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void; retryCount: number }>
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  maxRetries?: number
  showDetails?: boolean
  level?: 'page' | 'component' | 'feature'
}

interface State {
  hasError: boolean
  error: Error | null
  errorId?: string
  retryCount: number
}

export class ErrorBoundary extends Component<Props, State> {
  private errorId: string = ''

  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, error: null, retryCount: 0 }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    this.errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Classify and handle the error
    const appError = classifyError(error, {
      component: 'ErrorBoundary',
      action: 'componentDidCatch',
      metadata: {
        errorId: this.errorId,
        componentStack: errorInfo.componentStack,
        level: this.props.level || 'component',
        retryCount: this.state.retryCount,
      }
    })

    getGlobalErrorHandler().handleError(appError)

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo)

    this.setState({ errorId: this.errorId })
  }

  resetError = () => {
    const newRetryCount = this.state.retryCount + 1
    const maxRetries = this.props.maxRetries || 3

    if (newRetryCount > maxRetries) {
      // Too many retries, show permanent error state
      return
    }

    this.setState({
      hasError: false,
      error: null,
      errorId: undefined,
      retryCount: newRetryCount
    })
  }

  render(): ReactNode {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return (
          <FallbackComponent
            error={this.state.error || undefined}
            resetError={this.resetError}
            retryCount={this.state.retryCount}
          />
        )
      }

      const maxRetries = this.props.maxRetries || 3
      const canRetry = this.state.retryCount < maxRetries

      return (
        <ErrorState
          title={canRetry ? "Something went wrong" : "Multiple errors occurred"}
          description={
            canRetry
              ? this.state.error?.message || "An unexpected error occurred."
              : "Multiple attempts failed. Please refresh the page or contact support."
          }
          action={{
            label: canRetry ? "Try Again" : "Refresh Page",
            onClick: canRetry ? this.resetError : () => window.location.reload(),
          }}

        />
      )
    }

    return this.props.children
  }
}
