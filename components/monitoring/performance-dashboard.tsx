"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getCoreWebVitals, getPerformanceMetrics, PerformanceMetric } from '@/lib/monitoring/performance'
import { Activity, Clock, Zap, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react'

interface CoreWebVitalsDisplayProps {
  vitals: {
    LCP?: number
    FID?: number
    CLS?: number
    FCP?: number
  }
}

const CoreWebVitalsDisplay: React.FC<CoreWebVitalsDisplayProps> = ({ vitals }) => {
  const getRatingColor = (metric: string, value: number): string => {
    const thresholds: Record<string, [number, number]> = {
      LCP: [2500, 4000],
      FID: [100, 300],
      CLS: [0.1, 0.25],
      FCP: [1800, 3000],
    }

    const [good, poor] = thresholds[metric] || [1000, 3000]

    if (value <= good) return 'bg-green-600'
    if (value <= poor) return 'bg-yellow-600'
    return 'bg-red-600'
  }

  const formatValue = (metric: string, value: number): string => {
    if (metric === 'CLS') {
      return value.toFixed(3)
    }
    return `${Math.round(value)}ms`
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {Object.entries(vitals).map(([metric, value]) => (
        value !== undefined && (
          <Card key={metric} className="text-center">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">{metric}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatValue(metric, value)}</div>
              <div className={`w-full h-2 rounded-full mt-2 ${getRatingColor(metric, value)}`} />
            </CardContent>
          </Card>
        )
      ))}
    </div>
  )
}

interface PerformanceMetricsListProps {
  metrics: PerformanceMetric[]
}

const PerformanceMetricsList: React.FC<PerformanceMetricsListProps> = ({ metrics }) => {
  const recentMetrics = metrics.slice(-10).reverse() // Show last 10 metrics

  const getRatingBadge = (rating: string) => {
    const variants: Record<string, any> = {
      good: { variant: 'default', className: 'bg-green-600 hover:bg-green-700 text-white' },
      'needs-improvement': { variant: 'secondary', className: 'bg-yellow-600 hover:bg-yellow-700 text-white' },
      poor: { variant: 'destructive', className: 'bg-red-600 hover:bg-red-700 text-white' },
    }

    return variants[rating] || variants.good
  }

  return (
    <div className="space-y-2">
      {recentMetrics.map((metric, index) => (
        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {metric.rating === 'good' ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : metric.rating === 'poor' ? (
                <AlertTriangle className="h-4 w-4 text-red-500" />
              ) : (
                <Clock className="h-4 w-4 text-yellow-500" />
              )}
              <span className="font-medium">{metric.name}</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              {metric.value.toFixed(1)}{metric.name === 'CLS' ? '' : 'ms'}
            </span>
            <Badge {...getRatingBadge(metric.rating)}>
              {metric.rating}
            </Badge>
          </div>
        </div>
      ))}
    </div>
  )
}

export const PerformanceDashboard: React.FC = () => {
  const [vitals, setVitals] = useState<any>({})
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([])
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const updateMetrics = () => {
      setVitals(getCoreWebVitals())
      setMetrics(getPerformanceMetrics())
    }

    // Initial load
    updateMetrics()

    // Update every 5 seconds
    const interval = setInterval(updateMetrics, 5000)

    return () => clearInterval(interval)
  }, [])

  // Only show in development or when explicitly enabled
  useEffect(() => {
    const isDev = process.env.NODE_ENV === 'development'
    const isEnabled = localStorage.getItem('performance-dashboard') === 'true'
    setIsVisible(isDev || isEnabled)
  }, [])

  if (!isVisible) {
    return null
  }

  const hasVitals = Object.keys(vitals).length > 0
  const hasMetrics = metrics.length > 0

  return (
    <aside
      className="fixed bottom-4 right-4 w-96 max-h-96 overflow-y-auto bg-background border border-border rounded-lg shadow-lg z-50"
      aria-label="Performance monitoring dashboard"
      role="complementary"
    >
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <h3 className="font-semibold">Performance Monitor</h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
          >
            ×
          </Button>
        </div>

        {hasVitals && (
          <div className="mb-4">
            <h4 className="text-sm font-medium mb-2 flex items-center">
              <Zap className="h-4 w-4 mr-1" />
              Core Web Vitals
            </h4>
            <CoreWebVitalsDisplay vitals={vitals} />
          </div>
        )}

        {hasMetrics && (
          <div>
            <h4 className="text-sm font-medium mb-2 flex items-center">
              <TrendingUp className="h-4 w-4 mr-1" />
              Recent Metrics
            </h4>
            <PerformanceMetricsList metrics={metrics} />
          </div>
        )}

        {!hasVitals && !hasMetrics && (
          <div className="text-center text-muted-foreground py-8">
            <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No performance data yet</p>
            <p className="text-xs">Metrics will appear as you use the app</p>
          </div>
        )}
      </div>
    </aside>
  )
}

// Toggle function for enabling/disabling the dashboard
export const togglePerformanceDashboard = () => {
  const isEnabled = localStorage.getItem('performance-dashboard') === 'true'
  localStorage.setItem('performance-dashboard', (!isEnabled).toString())
  window.location.reload()
}
