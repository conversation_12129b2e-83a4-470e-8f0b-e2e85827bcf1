"use client"

import { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Activity, Eye, EyeOff, Loader2 } from 'lucide-react'

// Lazy load the full performance dashboard
const FullPerformanceDashboard = dynamic(
  () => import('./performance-dashboard').then(mod => ({ default: mod.PerformanceDashboard })),
  {
    ssr: false,
    loading: () => (
      <div className="p-4 bg-white/5 rounded-lg animate-pulse">
        <Loader2 className="h-4 w-4 animate-spin" />
      </div>
    )
  }
)

interface PerformanceMetrics {
  fcp: number
  lcp: number
  cls: number
  bundleSize: number
  memoryUsage: number
}

export function PerformanceDashboardOptimized() {
  const [isVisible, setIsVisible] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [isDev, setIsDev] = useState(false)

  useEffect(() => {
    setIsDev(process.env.NODE_ENV === 'development')
    
    // Only show in development or when explicitly enabled
    if (process.env.NODE_ENV === 'development' || 
        localStorage.getItem('pawpumps-show-performance') === 'true') {
      setIsVisible(true)
    }
  }, [])

  useEffect(() => {
    if (!isVisible) return

    // Lightweight performance monitoring
    const collectBasicMetrics = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      if (navigation) {
        setMetrics({
          fcp: navigation.domContentLoadedEventEnd - navigation.fetchStart,
          lcp: navigation.loadEventEnd - navigation.fetchStart,
          cls: 0, // Would need more complex calculation
          bundleSize: 0, // Estimated
          memoryUsage: (performance as any).memory?.usedJSHeapSize || 0
        })
      }
    }

    // Collect metrics after a short delay
    const timer = setTimeout(collectBasicMetrics, 1000)
    return () => clearTimeout(timer)
  }, [isVisible])

  const getMetricStatus = (value: number, thresholds: { good: number; poor: number }) => {
    if (value <= thresholds.good) return 'good'
    if (value <= thresholds.poor) return 'needs-improvement'
    return 'poor'
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  if (!isVisible) return null

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {!isExpanded ? (
        <Button
          onClick={() => setIsExpanded(true)}
          size="sm"
          variant="outline"
          className="glass-button border-white/20 hover:border-white/40 shadow-lg"
        >
          <Activity className="h-4 w-4 mr-2" />
          Performance
          {metrics && (
            <Badge 
              variant="secondary" 
              className={`ml-2 ${
                metrics.lcp > 4000 ? 'bg-red-500/20 text-red-300' :
                metrics.lcp > 2500 ? 'bg-yellow-500/20 text-yellow-300' :
                'bg-green-500/20 text-green-300'
              }`}
            >
              {formatTime(metrics.lcp)}
            </Badge>
          )}
        </Button>
      ) : (
        <Card className="glass-card border-white/10 w-80 max-h-96 overflow-auto">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm text-white flex items-center">
                <Activity className="h-4 w-4 mr-2" />
                Performance Monitor
              </CardTitle>
              <Button
                onClick={() => setIsExpanded(false)}
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0 text-white/50 hover:text-white"
              >
                <EyeOff className="h-3 w-3" />
              </Button>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-3">
            {metrics ? (
              <>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-white/70">FCP:</span>
                      <Badge 
                        variant="secondary"
                        className={`text-xs ${
                          getMetricStatus(metrics.fcp, { good: 1800, poor: 3000 }) === 'good' ? 'bg-green-500/20 text-green-300' :
                          getMetricStatus(metrics.fcp, { good: 1800, poor: 3000 }) === 'needs-improvement' ? 'bg-yellow-500/20 text-yellow-300' :
                          'bg-red-500/20 text-red-300'
                        }`}
                      >
                        {formatTime(metrics.fcp)}
                      </Badge>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-white/70">LCP:</span>
                      <Badge 
                        variant="secondary"
                        className={`text-xs ${
                          getMetricStatus(metrics.lcp, { good: 2500, poor: 4000 }) === 'good' ? 'bg-green-500/20 text-green-300' :
                          getMetricStatus(metrics.lcp, { good: 2500, poor: 4000 }) === 'needs-improvement' ? 'bg-yellow-500/20 text-yellow-300' :
                          'bg-red-500/20 text-red-300'
                        }`}
                      >
                        {formatTime(metrics.lcp)}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    {metrics.memoryUsage > 0 && (
                      <div className="flex justify-between">
                        <span className="text-white/70">Memory:</span>
                        <span className="text-white text-xs">{formatBytes(metrics.memoryUsage)}</span>
                      </div>
                    )}
                  </div>
                </div>

                <Button
                  onClick={() => {
                    // Load full dashboard
                    setIsExpanded(false)
                    // Could open a modal with full dashboard here
                  }}
                  size="sm"
                  variant="outline"
                  className="w-full glass-button border-white/20 hover:border-white/40 text-xs"
                >
                  <Eye className="h-3 w-3 mr-2" />
                  View Full Dashboard
                </Button>
              </>
            ) : (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin text-white/50" />
                <span className="ml-2 text-xs text-white/50">Collecting metrics...</span>
              </div>
            )}
            
            {isDev && (
              <div className="text-xs text-white/30 text-center pt-2 border-t border-white/10">
                Development Mode
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Hook to enable performance monitoring
export function usePerformanceMonitoring() {
  const enableMonitoring = () => {
    localStorage.setItem('pawpumps-show-performance', 'true')
    window.location.reload()
  }

  const disableMonitoring = () => {
    localStorage.removeItem('pawpumps-show-performance')
    window.location.reload()
  }

  return { enableMonitoring, disableMonitoring }
}
