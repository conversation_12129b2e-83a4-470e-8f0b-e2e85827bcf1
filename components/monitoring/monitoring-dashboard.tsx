"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  Users,
  DollarSign,
  Zap,
  Shield,
  Server,
  X
} from 'lucide-react'
import { getActiveAlerts, Alert, AlertSeverity } from '@/lib/monitoring/alerting'
import { getCoreWebVitals } from '@/lib/monitoring/performance'
import { getUptimeStats, getBusinessMetrics } from '@/lib/monitoring/uptime'

interface MonitoringDashboardProps {
  isVisible: boolean
  onClose: () => void
}

const AlertsList: React.FC<{ alerts: Alert[] }> = ({ alerts }) => {
  const getSeverityIcon = (severity: AlertSeverity) => {
    switch (severity) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />
      case 'medium':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'low':
        return <CheckCircle className="h-4 w-4 text-blue-500" />
    }
  }

  const getSeverityColor = (severity: AlertSeverity) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500 hover:bg-red-600'
      case 'high':
        return 'bg-orange-500 hover:bg-orange-600'
      case 'medium':
        return 'bg-yellow-500 hover:bg-yellow-600'
      case 'low':
        return 'bg-blue-500 hover:bg-blue-600'
    }
  }

  if (alerts.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
        <p>No active alerts</p>
        <p className="text-xs">All systems operational</p>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {alerts.slice(0, 5).map((alert) => (
        <div key={alert.id} className="flex items-start space-x-3 p-3 border rounded-lg">
          <div className="flex-shrink-0 mt-0.5">
            {getSeverityIcon(alert.severity)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium truncate">{alert.title}</h4>
              <Badge className={getSeverityColor(alert.severity)}>
                {alert.severity}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">{alert.message}</p>
            <p className="text-xs text-muted-foreground">
              {new Date(alert.timestamp).toLocaleTimeString()}
            </p>
          </div>
        </div>
      ))}
      {alerts.length > 5 && (
        <p className="text-xs text-muted-foreground text-center">
          +{alerts.length - 5} more alerts
        </p>
      )}
    </div>
  )
}

const MetricCard: React.FC<{
  title: string
  value: string | number
  icon: React.ReactNode
  trend?: 'up' | 'down' | 'stable'
  status?: 'good' | 'warning' | 'error'
}> = ({ title, value, icon, trend, status = 'good' }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'good':
        return 'text-green-500'
      case 'warning':
        return 'text-yellow-500'
      case 'error':
        return 'text-red-500'
    }
  }

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-500" />
      case 'down':
        return <TrendingDown className="h-3 w-3 text-red-500" />
      default:
        return null
    }
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={getStatusColor()}>{icon}</div>
            <div>
              <p className="text-xs text-muted-foreground">{title}</p>
              <p className="text-lg font-semibold">{value}</p>
            </div>
          </div>
          {getTrendIcon()}
        </div>
      </CardContent>
    </Card>
  )
}

export const MonitoringDashboard: React.FC<MonitoringDashboardProps> = ({ isVisible, onClose }) => {
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [vitals, setVitals] = useState<any>({})
  const [uptimeStats, setUptimeStats] = useState<any>({})
  const [businessMetrics, setBusinessMetrics] = useState<any>({})

  useEffect(() => {
    if (!isVisible) return

    const updateData = () => {
      // Get alerts
      setAlerts(getActiveAlerts())
      
      // Get Core Web Vitals
      setVitals(getCoreWebVitals())
      
      // Get uptime stats
      setUptimeStats(getUptimeStats())
      
      // Simulate business metrics (in production, fetch from API)
      setBusinessMetrics({
        activeUsers: Math.floor(Math.random() * 1000) + 500,
        dailyTransactions: Math.floor(Math.random() * 100) + 50,
        walletConnections: Math.floor(Math.random() * 200) + 100,
        revenue: Math.floor(Math.random() * 10000) + 5000,
      })
    }

    // Initial load
    updateData()

    // Update every 30 seconds
    const interval = setInterval(updateData, 30000)

    return () => clearInterval(interval)
  }, [isVisible])

  if (!isVisible) {
    return null
  }

  const overallUptime = Object.values(uptimeStats).length > 0 
    ? Object.values(uptimeStats).reduce((sum: number, stats: any) => sum + stats.uptime, 0) / Object.values(uptimeStats).length
    : 100

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-background border border-border rounded-lg shadow-lg w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <Activity className="h-6 w-6" />
              <h2 className="text-2xl font-bold">Monitoring Dashboard</h2>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="uptime">Uptime</TabsTrigger>
              <TabsTrigger value="business">Business</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* System Status */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <MetricCard
                  title="Overall Uptime"
                  value={`${overallUptime.toFixed(2)}%`}
                  icon={<Server className="h-4 w-4" />}
                  status={overallUptime >= 99 ? 'good' : overallUptime >= 95 ? 'warning' : 'error'}
                />
                <MetricCard
                  title="Active Alerts"
                  value={alerts.length}
                  icon={<AlertTriangle className="h-4 w-4" />}
                  status={alerts.length === 0 ? 'good' : alerts.length < 5 ? 'warning' : 'error'}
                />
                <MetricCard
                  title="LCP"
                  value={vitals.LCP ? `${Math.round(vitals.LCP)}ms` : 'N/A'}
                  icon={<Zap className="h-4 w-4" />}
                  status={vitals.LCP ? (vitals.LCP <= 2500 ? 'good' : vitals.LCP <= 4000 ? 'warning' : 'error') : 'good'}
                />
                <MetricCard
                  title="Active Users"
                  value={businessMetrics.activeUsers || 'N/A'}
                  icon={<Users className="h-4 w-4" />}
                  trend="up"
                />
              </div>

              {/* Alerts */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <AlertTriangle className="h-5 w-5" />
                    <span>Active Alerts</span>
                  </CardTitle>
                  <CardDescription>
                    Current system alerts and notifications
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <AlertsList alerts={alerts} />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <MetricCard
                  title="LCP"
                  value={vitals.LCP ? `${Math.round(vitals.LCP)}ms` : 'N/A'}
                  icon={<Zap className="h-4 w-4" />}
                  status={vitals.LCP ? (vitals.LCP <= 2500 ? 'good' : vitals.LCP <= 4000 ? 'warning' : 'error') : 'good'}
                />
                <MetricCard
                  title="FID"
                  value={vitals.FID ? `${Math.round(vitals.FID)}ms` : 'N/A'}
                  icon={<Clock className="h-4 w-4" />}
                  status={vitals.FID ? (vitals.FID <= 100 ? 'good' : vitals.FID <= 300 ? 'warning' : 'error') : 'good'}
                />
                <MetricCard
                  title="CLS"
                  value={vitals.CLS ? vitals.CLS.toFixed(3) : 'N/A'}
                  icon={<Activity className="h-4 w-4" />}
                  status={vitals.CLS ? (vitals.CLS <= 0.1 ? 'good' : vitals.CLS <= 0.25 ? 'warning' : 'error') : 'good'}
                />
                <MetricCard
                  title="FCP"
                  value={vitals.FCP ? `${Math.round(vitals.FCP)}ms` : 'N/A'}
                  icon={<TrendingUp className="h-4 w-4" />}
                  status={vitals.FCP ? (vitals.FCP <= 1800 ? 'good' : vitals.FCP <= 3000 ? 'warning' : 'error') : 'good'}
                />
              </div>
            </TabsContent>

            <TabsContent value="uptime" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(uptimeStats).map(([checkId, stats]: [string, any]) => (
                  <MetricCard
                    key={checkId}
                    title={checkId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    value={`${stats.uptime.toFixed(2)}%`}
                    icon={<Server className="h-4 w-4" />}
                    status={stats.uptime >= 99 ? 'good' : stats.uptime >= 95 ? 'warning' : 'error'}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="business" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <MetricCard
                  title="Active Users"
                  value={businessMetrics.activeUsers || 'N/A'}
                  icon={<Users className="h-4 w-4" />}
                  trend="up"
                />
                <MetricCard
                  title="Daily Transactions"
                  value={businessMetrics.dailyTransactions || 'N/A'}
                  icon={<DollarSign className="h-4 w-4" />}
                  trend="up"
                />
                <MetricCard
                  title="Wallet Connections"
                  value={businessMetrics.walletConnections || 'N/A'}
                  icon={<Shield className="h-4 w-4" />}
                  trend="stable"
                />
                <MetricCard
                  title="Revenue"
                  value={businessMetrics.revenue ? `$${businessMetrics.revenue.toLocaleString()}` : 'N/A'}
                  icon={<TrendingUp className="h-4 w-4" />}
                  trend="up"
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

// Toggle function for the monitoring dashboard
export const toggleMonitoringDashboard = () => {
  const event = new CustomEvent('toggle-monitoring-dashboard')
  window.dispatchEvent(event)
}
