"use client"

import { useState, useEffect, useRef } from "react"
import { TrendingUp, TrendingDown } from "lucide-react"

interface TickerToken {
  symbol: string
  price: number
  change24h: number
  volume: number
}

interface StandardizedTickerProps {
  className?: string
  tokens?: TickerToken[]
  updateInterval?: number
  scrollSpeed?: number
}

const defaultTokens: TickerToken[] = [
  { symbol: 'DOGE', price: 0.08234, change24h: 5.2, volume: 1250000000 },
  { symbol: 'SHIB', price: 0.000008234, change24h: -2.1, volume: 890000000 },
  { symbol: 'PEPE', price: 0.00000123, change24h: 12.8, volume: 560000000 },
  { symbol: 'FLOKI', price: 0.000034, change24h: -1.5, volume: 340000000 },
  { symbol: 'PAW', price: 0.00123, change24h: 8.7, volume: 780000000 },
  { symbol: 'BONK', price: 0.0000045, change24h: 15.3, volume: 920000000 },
  { symbol: 'WIF', price: 0.00567, change24h: -4.2, volume: 450000000 },
  { symbol: 'MEME', price: 0.00089, change24h: 22.1, volume: 670000000 },
]

export function StandardizedTicker({ 
  className = "", 
  tokens = defaultTokens,
  updateInterval = 5000,
  scrollSpeed = 60
}: StandardizedTickerProps) {
  const [tickerTokens, setTickerTokens] = useState<TickerToken[]>(tokens)
  const tickerRef = useRef<HTMLDivElement>(null)

  // Format price with appropriate decimal places
  const formatPrice = (price: number): string => {
    if (price >= 1) return price.toFixed(2)
    if (price >= 0.01) return price.toFixed(4)
    if (price >= 0.0001) return price.toFixed(6)
    return price.toFixed(8)
  }

  // Format volume in readable format
  const formatVolume = (volume: number): string => {
    if (volume >= 1e9) return `${(volume / 1e9).toFixed(1)}B`
    if (volume >= 1e6) return `${(volume / 1e6).toFixed(1)}M`
    if (volume >= 1e3) return `${(volume / 1e3).toFixed(1)}K`
    return volume.toString()
  }

  // Update prices with realistic fluctuations
  useEffect(() => {
    const interval = setInterval(() => {
      setTickerTokens(prevTokens => 
        prevTokens.map(token => ({
          ...token,
          price: token.price * (1 + (Math.random() - 0.5) * 0.01), // ±0.5% fluctuation
          change24h: token.change24h + (Math.random() - 0.5) * 0.2, // Small change fluctuation
        }))
      )
    }, updateInterval)

    return () => clearInterval(interval)
  }, [updateInterval])

  return (
    <div className={`bg-black/50 backdrop-blur-sm border-b border-white/10 overflow-hidden ${className}`}>
      <div className="container py-2">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-white/80 text-sm font-medium">
            <TrendingUp className="h-4 w-4 text-doge" />
            <span>Trending:</span>
          </div>
          
          <div className="flex-1 overflow-hidden">
            <div
              ref={tickerRef}
              className="flex gap-8"
              style={{
                animation: `scroll ${scrollSpeed}s linear infinite`
              }}
            >
              {/* Duplicate tokens for seamless loop */}
              {[...tickerTokens, ...tickerTokens].map((token, index) => (
                <div 
                  key={`${token.symbol}-${index}`}
                  className="flex items-center gap-3 whitespace-nowrap"
                >
                  <div className="flex items-center gap-2">
                    <span className="text-white font-medium text-sm">
                      {token.symbol}
                    </span>
                    <span className="text-white/70 text-sm">
                      ${formatPrice(token.price)}
                    </span>
                  </div>
                  
                  <div className={`flex items-center gap-1 text-sm transition-colors duration-1000 ${
                    token.change24h >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {token.change24h >= 0 ? (
                      <TrendingUp className="h-3 w-3" />
                    ) : (
                      <TrendingDown className="h-3 w-3" />
                    )}
                    <span className="font-medium">
                      {token.change24h >= 0 ? '+' : ''}{token.change24h.toFixed(1)}%
                    </span>
                  </div>
                  
                  <div className="text-white/50 text-xs">
                    Vol: {formatVolume(token.volume)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
