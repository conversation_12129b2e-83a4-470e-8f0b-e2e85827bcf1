"use client"

import type React from "react"

import { useState } from "react"
import { Flag, X } from "lucide-react"

interface ReportPostDialogProps {
  postId: string
  postContent: string
  postAuthor: string
  onClose: () => void
  onSubmit: (reason: string, details: string) => void
}

const REPORT_REASONS = [
  "Spam",
  "Harassment",
  "Misinformation",
  "Inappropriate Content",
  "Scam",
  "Hate Speech",
  "Violence",
  "Other",
]

export function ReportPostDialog({ postId, postContent, postAuthor, onClose, onSubmit }: ReportPostDialogProps) {
  const [selectedReason, setSelectedReason] = useState("")
  const [details, setDetails] = useState("")
  const [submitted, setSubmitted] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(selectedReason, details)
    setSubmitted(true)

    // Auto-close after 2 seconds
    setTimeout(() => {
      onClose()
    }, 2000)
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
      <div className="glass-card max-h-[90vh] w-full max-w-md overflow-y-auto rounded-lg border border-white/10 bg-black/80 p-6 backdrop-blur-md">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Flag className="h-5 w-5 text-red-400" />
            <h2 className="text-xl font-bold text-white">Report Post</h2>
          </div>
          <button onClick={onClose} className="rounded-full p-1 text-white/70 hover:bg-white/10 hover:text-white">
            <X className="h-5 w-5" />
          </button>
        </div>

        {!submitted ? (
          <form onSubmit={handleSubmit}>
            <div className="mb-4 rounded-md bg-black/30 p-3">
              <h3 className="mb-1 text-xs font-medium text-white/70">Post Content</h3>
              <p className="text-sm text-white">{postContent}</p>
              <p className="mt-1 text-xs text-white/60">Posted by: {postAuthor}</p>
            </div>

            <div className="mb-4">
              <label className="mb-2 block text-sm font-medium text-white">Reason for reporting</label>
              <div className="grid grid-cols-2 gap-2">
                {REPORT_REASONS.map((reason) => (
                  <button
                    key={reason}
                    type="button"
                    onClick={() => setSelectedReason(reason)}
                    className={`rounded-md border px-3 py-2 text-sm transition-colors ${
                      selectedReason === reason
                        ? "border-doge bg-doge/20 text-doge"
                        : "border-white/10 bg-black/30 text-white/70 hover:border-white/30 hover:text-white"
                    }`}
                  >
                    {reason}
                  </button>
                ))}
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="details" className="mb-2 block text-sm font-medium text-white">
                Additional details (optional)
              </label>
              <textarea
                id="details"
                value={details}
                onChange={(e) => setDetails(e.target.value)}
                className="h-24 w-full rounded-md border border-white/10 bg-black/30 p-3 text-sm text-white placeholder-white/50"
                placeholder="Please provide any additional context about why this post violates community guidelines..."
              />
            </div>

            <div className="flex justify-end gap-3">
              <button
                type="button"
                onClick={onClose}
                className="rounded-md bg-white/10 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-white/20"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={!selectedReason}
                className="rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-red-700 disabled:cursor-not-allowed disabled:opacity-50"
              >
                Submit Report
              </button>
            </div>
          </form>
        ) : (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-500/20">
              <Flag className="h-8 w-8 text-green-400" />
            </div>
            <h3 className="mb-2 text-xl font-medium text-white">Report Submitted</h3>
            <p className="text-center text-white/70">
              Thank you for helping keep our community safe. Our moderation team will review this report.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
