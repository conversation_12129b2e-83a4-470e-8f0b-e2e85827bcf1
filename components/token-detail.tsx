"use client"

import { useState } from "react"
import { ArrowUp, ArrowDown, Globe, Twitter, MessageSquare, Heart, AlertCircle } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { TradingChart } from "@/components/trading-chart"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { useToast } from "@/components/ui/use-toast"

// Mock token data
const tokenData = {
  PAW: {
    name: "PawPumps",
    symbol: "PAW",
    price: 0.0042,
    change24h: 12.5,
    volume24h: 1250000,
    marketCap: 4200000,
    totalSupply: "1,000,000,000",
    circulatingSupply: "420,000,000",
    description:
      "PawPumps ($PAW) is the governance and utility token of the PawPumps platform. It enables holders to participate in governance decisions, earn rewards, and access premium features.",
    website: "https://pawpumps.dog",
    twitter: "@PawPumps",
    creator: "0x1234...5678",
    launchDate: "2025-01-15",
    bondingCurve: "Linear",
    liquidityLocked: true,
    verified: true,
  },
  DC: {
    name: "DogeChain",
    symbol: "DC",
    price: 0.0105,
    change24h: 3.2,
    volume24h: 3450000,
    marketCap: 10500000,
    totalSupply: "1,000,000,000",
    circulatingSupply: "650,000,000",
    description:
      "DogeChain ($DC) is the governance token of the Dogechain Network, a Layer 2 EVM sidechain for Dogecoin. It enables holders to participate in governance decisions and earn rewards.",
    website: "https://dogechain.dog",
    twitter: "@DogeChain",
    creator: "0x8765...4321",
    launchDate: "2024-10-01",
    bondingCurve: "Logarithmic",
    liquidityLocked: true,
    verified: true,
  },
  RDOGE: {
    name: "RocketDoge",
    symbol: "RDOGE",
    price: 0.00078,
    change24h: 28.4,
    volume24h: 2100000,
    marketCap: 780000,
    totalSupply: "10,000,000,000",
    circulatingSupply: "1,000,000,000",
    description:
      "RocketDoge ($RDOGE) is a community-driven memecoin on the Dogechain Network. It aims to reach the moon with its innovative tokenomics and strong community.",
    website: "https://rocketdoge.xyz",
    twitter: "@RocketDoge",
    creator: "0x2468...1357",
    launchDate: "2025-04-20",
    bondingCurve: "Exponential",
    liquidityLocked: true,
    verified: false,
  },
}

// Default token data for unknown symbols
const defaultToken = {
  name: "Unknown Token",
  symbol: "???",
  price: 0,
  change24h: 0,
  volume24h: 0,
  marketCap: 0,
  totalSupply: "0",
  circulatingSupply: "0",
  description: "This token does not exist or has not been indexed yet.",
  website: "",
  twitter: "",
  creator: "",
  launchDate: "",
  bondingCurve: "",
  liquidityLocked: false,
  verified: false,
}

export function TokenDetail({ symbol }: { symbol: string }) {
  const { toast } = useToast()
  const [timeframe, setTimeframe] = useState("1d")
  const [fromAmount, setFromAmount] = useState("")
  const [toAmount, setToAmount] = useState("")
  const [isSwapping, setIsSwapping] = useState(false)

  // Get token data or use default if not found
  const token = (tokenData as any)[symbol.toUpperCase()] || { ...defaultToken, symbol: symbol.toUpperCase() }

  // Handle swap
  const handleSwap = async () => {
    if (!fromAmount) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid amount to swap",
        variant: "destructive",
      })
      return
    }

    setIsSwapping(true)

    try {
      // Simulate swap
      await new Promise((resolve) => setTimeout(resolve, 2000))

      toast({
        title: "Swap successful!",
        description: `Swapped ${fromAmount} wDOGE for ${toAmount} ${token.symbol}`,
      })

      // Reset form
      setFromAmount("")
      setToAmount("")
    } catch (error) {
      toast({
        title: "Swap failed",
        description: "There was an error processing your swap. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSwapping(false)
    }
  }

  // Calculate to amount based on from amount
  const handleFromAmountChange = (value: string) => {
    setFromAmount(value)
    if (value && !isNaN(Number.parseFloat(value))) {
      // Mock conversion rate
      const calculatedAmount = (Number.parseFloat(value) / token.price).toFixed(6)
      setToAmount(calculatedAmount)
    } else {
      setToAmount("")
    }
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-doge/20 text-doge font-bold">
            {token.symbol.slice(0, 2)}
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight sm:text-4xl gradient-text">{token.name}</h1>
            <div className="flex items-center gap-2">
              <span className="text-lg text-white/70">${token.symbol}</span>
              {token.verified && (
                <div className="flex h-5 w-5 items-center justify-center rounded-full bg-doge/20 text-doge">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="12"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M20 6 9 17l-5-5" />
                  </svg>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-end">
          <div className="text-3xl font-bold text-white">${token.price.toFixed(6)}</div>
          <div
            className={`flex items-center gap-1 text-lg font-medium ${
              token.change24h >= 0 ? "text-green-500" : "text-red-500"
            }`}
          >
            {token.change24h >= 0 ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
            {Math.abs(token.change24h).toFixed(2)}%
          </div>
        </div>
      </div>

      <div className="grid gap-8 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card className="glass-card border-white/5 liquid-glow">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white">Price Chart</CardTitle>
                <Select value={timeframe} onValueChange={setTimeframe}>
                  <SelectTrigger className="w-[100px] glass-input border-white/10">
                    <SelectValue placeholder="Timeframe" />
                  </SelectTrigger>
                  <SelectContent className="glass">
                    <SelectItem value="1h">1H</SelectItem>
                    <SelectItem value="4h">4H</SelectItem>
                    <SelectItem value="1d">1D</SelectItem>
                    <SelectItem value="1w">1W</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <TradingChart />
            </CardContent>
          </Card>

          <div className="mt-8">
            <Tabs defaultValue="about" className="w-full">
              <TabsList className="grid w-full grid-cols-3 glass">
                <TabsTrigger value="about">About</TabsTrigger>
                <TabsTrigger value="comments">Comments</TabsTrigger>
                <TabsTrigger value="holders">Holders</TabsTrigger>
              </TabsList>

              <TabsContent value="about" className="mt-6 space-y-6">
                <Card className="glass-card border-white/5">
                  <CardHeader>
                    <CardTitle className="text-white">Token Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-white/80">{token.description}</p>

                    <div className="grid gap-4 sm:grid-cols-2">
                      <div>
                        <h3 className="mb-2 text-sm font-medium text-white/60">Market Data</h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-white/80">Market Cap</span>
                            <span className="font-medium text-white">${token.marketCap.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-white/80">24h Volume</span>
                            <span className="font-medium text-white">${token.volume24h.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-white/80">Total Supply</span>
                            <span className="font-medium text-white">{token.totalSupply}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-white/80">Circulating Supply</span>
                            <span className="font-medium text-white">{token.circulatingSupply}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="mb-2 text-sm font-medium text-white/60">Token Details</h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-white/80">Creator</span>
                            <span className="font-medium text-white">{token.creator}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-white/80">Launch Date</span>
                            <span className="font-medium text-white">{token.launchDate}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-white/80">Bonding Curve</span>
                            <span className="font-medium text-white">{token.bondingCurve}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-white/80">Liquidity</span>
                            <span className="font-medium text-white">
                              {token.liquidityLocked ? "Locked" : "Unlocked"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-4 pt-2">
                      {token.website && (
                        <Button variant="outline" size="sm" className="glass-button" asChild>
                          <a href={token.website} target="_blank" rel="noopener noreferrer">
                            <Globe className="mr-2 h-4 w-4" />
                            Website
                          </a>
                        </Button>
                      )}
                      {token.twitter && (
                        <Button variant="outline" size="sm" className="glass-button" asChild>
                          <a href={`https://twitter.com/${token.twitter}`} target="_blank" rel="noopener noreferrer">
                            <Twitter className="mr-2 h-4 w-4" />
                            Twitter
                          </a>
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {!token.verified && (
                  <Alert className="glass border-yellow-500/20 bg-yellow-500/5">
                    <AlertCircle className="h-4 w-4 text-yellow-500" />
                    <AlertTitle className="text-yellow-500">Unverified Token</AlertTitle>
                    <AlertDescription className="text-white/70">
                      This token has not been verified by the PawPumps team. Trade with caution.
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>

              <TabsContent value="comments" className="mt-6 space-y-6">
                <Card className="glass-card border-white/5">
                  <CardHeader>
                    <CardTitle className="text-white">Comments</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex gap-4">
                      <Avatar>
                        <AvatarImage src="/placeholder.svg?key=ofh48" alt="Your avatar" />
                        <AvatarFallback>YO</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <textarea
                          className="w-full glass-input border-white/10 focus:border-white/20 rounded-lg p-3 min-h-[100px] resize-none bg-transparent text-white"
                          placeholder="Share your thoughts about this token..."
                        ></textarea>
                        <div className="mt-2 flex justify-end">
                          <Button className="doge-button doge-shine">Post Comment</Button>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-6">
                      <div className="flex gap-4">
                        <Avatar>
                          <AvatarImage src="/placeholder.svg?key=jg7nh" alt="DogeWhale" />
                          <AvatarFallback>DW</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-white">DogeWhale</p>
                              <p className="text-sm text-white/60">2 hours ago</p>
                            </div>
                          </div>
                          <p className="my-3 text-white/90">
                            This token has amazing potential! The team is very responsive and the community is growing
                            fast.
                          </p>
                          <div className="flex items-center gap-4">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="flex items-center gap-1 text-white/60 hover:text-doge"
                            >
                              <Heart className="h-4 w-4" />
                              <span>24</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="flex items-center gap-1 text-white/60 hover:text-dogechain"
                            >
                              <MessageSquare className="h-4 w-4" />
                              <span>Reply</span>
                            </Button>
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-4">
                        <Avatar>
                          <AvatarImage src="/placeholder.svg?key=5s4nu" alt="MoonShot" />
                          <AvatarFallback>MS</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-white">MoonShot</p>
                              <p className="text-sm text-white/60">5 hours ago</p>
                            </div>
                          </div>
                          <p className="my-3 text-white/90">
                            I've been holding since launch and the price action has been incredible. Looking forward to
                            seeing where this goes!
                          </p>
                          <div className="flex items-center gap-4">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="flex items-center gap-1 text-white/60 hover:text-doge"
                            >
                              <Heart className="h-4 w-4" />
                              <span>18</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="flex items-center gap-1 text-white/60 hover:text-dogechain"
                            >
                              <MessageSquare className="h-4 w-4" />
                              <span>Reply</span>
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="holders" className="mt-6">
                <Card className="glass-card border-white/5">
                  <CardHeader>
                    <CardTitle className="text-white">Top Holders</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-doge/10 text-doge text-xs font-bold">
                            1
                          </div>
                          <div>
                            <p className="font-medium text-white">0x8765...4321</p>
                            <p className="text-xs text-white/60">15% of supply</p>
                          </div>
                        </div>
                        <p className="font-medium text-white">
                          {(Number.parseInt(token.totalSupply.replace(/,/g, "")) * 0.15).toLocaleString()}{" "}
                          {token.symbol}
                        </p>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-doge/10 text-doge text-xs font-bold">
                            2
                          </div>
                          <div>
                            <p className="font-medium text-white">0x1234...5678</p>
                            <p className="text-xs text-white/60">10% of supply</p>
                          </div>
                        </div>
                        <p className="font-medium text-white">
                          {(Number.parseInt(token.totalSupply.replace(/,/g, "")) * 0.1).toLocaleString()} {token.symbol}
                        </p>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-doge/10 text-doge text-xs font-bold">
                            3
                          </div>
                          <div>
                            <p className="font-medium text-white">0x2468...1357</p>
                            <p className="text-xs text-white/60">8% of supply</p>
                          </div>
                        </div>
                        <p className="font-medium text-white">
                          {(Number.parseInt(token.totalSupply.replace(/,/g, "")) * 0.08).toLocaleString()}{" "}
                          {token.symbol}
                        </p>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-doge/10 text-doge text-xs font-bold">
                            4
                          </div>
                          <div>
                            <p className="font-medium text-white">0x9876...5432</p>
                            <p className="text-xs text-white/60">5% of supply</p>
                          </div>
                        </div>
                        <p className="font-medium text-white">
                          {(Number.parseInt(token.totalSupply.replace(/,/g, "")) * 0.05).toLocaleString()}{" "}
                          {token.symbol}
                        </p>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-doge/10 text-doge text-xs font-bold">
                            5
                          </div>
                          <div>
                            <p className="font-medium text-white">0x5432...9876</p>
                            <p className="text-xs text-white/60">3% of supply</p>
                          </div>
                        </div>
                        <p className="font-medium text-white">
                          {(Number.parseInt(token.totalSupply.replace(/,/g, "")) * 0.03).toLocaleString()}{" "}
                          {token.symbol}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        <div>
          <Card className="glass-card border-white/5 liquid-glow sticky top-20">
            <CardHeader>
              <CardTitle className="text-white">Trade {token.symbol}</CardTitle>
              <CardDescription className="text-white/70">Swap wDOGE for {token.symbol}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label htmlFor="fromAmount" className="text-white/80">
                    From
                  </label>
                  <span className="text-xs text-white/60">Balance: 1000</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative flex-1">
                    <Input
                      id="fromAmount"
                      type="number"
                      placeholder="0.0"
                      value={fromAmount}
                      onChange={(e) => handleFromAmountChange(e.target.value)}
                      className="glass-input border-white/10 pr-20"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <Button
                        variant="ghost"
                        className="h-6 text-xs font-medium text-white"
                        onClick={() => handleFromAmountChange("1000")}
                      >
                        MAX
                      </Button>
                    </div>
                  </div>
                  <div className="w-[80px] glass-input border-white/10 flex items-center justify-center rounded-md px-3 py-2">
                    <span className="text-white">wDOGE</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-center">
                <div className="rounded-full bg-white/5 p-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-white/60"
                  >
                    <path d="M7 10v12" />
                    <path d="M17 2v12" />
                    <path d="M17 14H7" />
                    <path d="m7 2 4 4-4 4" />
                    <path d="m17 22-4-4 4-4" />
                  </svg>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label htmlFor="toAmount" className="text-white/80">
                    To
                  </label>
                  <span className="text-xs text-white/60">Balance: 0</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Input
                    id="toAmount"
                    type="number"
                    placeholder="0.0"
                    value={toAmount}
                    readOnly
                    className="flex-1 glass-input border-white/10"
                  />
                  <div className="w-[80px] glass-input border-white/10 flex items-center justify-center rounded-md px-3 py-2">
                    <span className="text-white">{token.symbol}</span>
                  </div>
                </div>
              </div>

              {fromAmount && toAmount && (
                <div className="rounded-md bg-white/5 p-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-white/60">Price</span>
                    <span className="text-white/90">
                      1 wDOGE = {(1 / token.price).toFixed(6)} {token.symbol}
                    </span>
                  </div>
                  <div className="mt-1 flex items-center justify-between">
                    <span className="text-white/60">Fee (0.5%)</span>
                    <span className="text-white/90">{(Number.parseFloat(fromAmount) * 0.005).toFixed(6)} wDOGE</span>
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleSwap}
                disabled={isSwapping || !fromAmount || !toAmount}
                className="w-full doge-button doge-shine"
              >
                {isSwapping ? "Swapping..." : "Swap"}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}
