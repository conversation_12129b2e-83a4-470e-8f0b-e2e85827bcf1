"use client"

import type React from "react"
import { useEffect, useRef } from "react"

interface ShimmerTextProps {
  children: React.ReactNode
  className?: string
  as?: React.ElementType
}

export function ShimmerText({ children, className = "", as: Component = "span" }: ShimmerTextProps) {
  const textRef = useRef<HTMLElement>(null)

  useEffect(() => {
    if (textRef.current) {
      // Set the data-text attribute to match the content
      textRef.current.setAttribute("data-text", textRef.current.textContent || "")
    }
  }, [children])

  return (
    <Component ref={textRef} className={`shimmer-text ${className}`}>
      {children}
    </Component>
  )
}
