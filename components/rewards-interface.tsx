"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, <PERSON>, Clock, <PERSON>, ArrowRight, <PERSON>rkles, Wallet } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useWallet } from "@/components/wallet-provider"

type Reward = {
  id: string
  title: string
  description: string
  amount: number
  type: "token" | "nft" | "badge"
  status: "available" | "claimed" | "locked"
  progress?: number
  requirement?: string
  expiresIn?: string
}

const mockRewards: Reward[] = [
  {
    id: "reward1",
    title: "First Trade Bonus",
    description: "Complete your first trade on PawPumps",
    amount: 50,
    type: "token",
    status: "available",
  },
  {
    id: "reward2",
    title: "Liquidity Provider",
    description: "Add liquidity to any trading pair",
    amount: 100,
    type: "token",
    status: "available",
  },
  {
    id: "reward3",
    title: "Social Butterfly",
    description: "Make 5 posts in the social feed",
    amount: 75,
    type: "token",
    status: "locked",
    progress: 40,
    requirement: "2/5 posts made",
  },
  {
    id: "reward4",
    title: "Governance Participant",
    description: "Vote on at least 3 governance proposals",
    amount: 150,
    type: "token",
    status: "locked",
    progress: 33,
    requirement: "1/3 votes cast",
  },
  {
    id: "reward5",
    title: "Diamond Hands",
    description: "Hold $PAW tokens for 30 days",
    amount: 200,
    type: "token",
    status: "locked",
    progress: 70,
    requirement: "21/30 days completed",
  },
  {
    id: "reward6",
    title: "Early Adopter NFT",
    description: "Limited edition NFT for platform early adopters",
    amount: 1,
    type: "nft",
    status: "available",
    expiresIn: "3 days",
  },
]

type Achievement = {
  id: string
  title: string
  description: string
  level: number
  maxLevel: number
  progress: number
  reward: number
  icon: React.ElementType
}

const mockAchievements: Achievement[] = [
  {
    id: "achievement1",
    title: "Trading Volume",
    description: "Reach trading volume milestones",
    level: 2,
    maxLevel: 5,
    progress: 65,
    reward: 50,
    icon: Flame,
  },
  {
    id: "achievement2",
    title: "Token Creator",
    description: "Launch your own memecoin",
    level: 1,
    maxLevel: 3,
    progress: 33,
    reward: 100,
    icon: Sparkles,
  },
  {
    id: "achievement3",
    title: "Liquidity Provider",
    description: "Add liquidity to trading pairs",
    level: 3,
    maxLevel: 5,
    progress: 80,
    reward: 75,
    icon: Wallet,
  },
]

export function RewardsInterface() {
  const { isConnected } = useWallet()
  const [claimedRewards, setClaimedRewards] = useState<string[]>([])

  const handleClaimReward = (rewardId: string) => {
    setClaimedRewards((prev) => [...prev, rewardId])
  }

  return (
    <div className="grid gap-8 lg:grid-cols-3">
      <div className="lg:col-span-2">
        <Tabs defaultValue="rewards" className="w-full">
          <TabsList className="grid w-full grid-cols-2 glass mb-6">
            <TabsTrigger value="rewards" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
              Rewards
            </TabsTrigger>
            <TabsTrigger
              value="achievements"
              className="data-[state=active]:text-dogechain data-[state=active]:bg-dogechain/10"
            >
              Achievements
            </TabsTrigger>
          </TabsList>

          <TabsContent value="rewards" className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              {mockRewards.map((reward) => (
                <Card
                  key={reward.id}
                  className={`glass-card border-white/5 transition-all duration-300 ${
                    reward.status === "available"
                      ? "doge-glow hover:border-doge/20"
                      : reward.status === "claimed" || claimedRewards.includes(reward.id)
                        ? "opacity-70"
                        : ""
                  }`}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {reward.type === "token" ? (
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-doge/10 text-doge">
                            <Gift className="h-4 w-4" />
                          </div>
                        ) : reward.type === "nft" ? (
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-dogechain/10 text-dogechain">
                            <Trophy className="h-4 w-4" />
                          </div>
                        ) : (
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white/10 text-white">
                            <Trophy className="h-4 w-4" />
                          </div>
                        )}
                        <CardTitle className="text-lg text-white">{reward.title}</CardTitle>
                      </div>
                      {reward.expiresIn && (
                        <div className="flex items-center gap-1 text-xs text-white/60">
                          <Clock className="h-3 w-3" />
                          <span>Expires in {reward.expiresIn}</span>
                        </div>
                      )}
                    </div>
                    <CardDescription className="text-white/70">{reward.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {reward.progress !== undefined && (
                      <div className="mb-4 space-y-2">
                        <Progress
                          value={reward.progress}
                          className="h-2 bg-white/10"
                          indicatorClassName={reward.type === "nft" ? "bg-dogechain" : "bg-doge"}
                        />
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-white/60">{reward.requirement}</span>
                          <span className="text-white/60">{reward.progress}%</span>
                        </div>
                      </div>
                    )}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-white">{reward.amount}</span>
                        <span className="text-white/70">
                          {reward.type === "token" ? "$PAW" : reward.type === "nft" ? "NFT" : "Badge"}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    {reward.status === "available" && !claimedRewards.includes(reward.id) ? (
                      <Button
                        className="w-full doge-button doge-shine"
                        onClick={() => handleClaimReward(reward.id)}
                        disabled={!isConnected}
                      >
                        Claim Reward
                      </Button>
                    ) : reward.status === "claimed" || claimedRewards.includes(reward.id) ? (
                      <Button className="w-full glass-button" disabled>
                        Claimed
                      </Button>
                    ) : (
                      <Button className="w-full glass-button" disabled>
                        Locked
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="achievements" className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              {mockAchievements.map((achievement) => {
                const Icon = achievement.icon
                return (
                  <Card
                    key={achievement.id}
                    className="glass-card border-white/5 dogechain-glow hover:border-dogechain/20"
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-dogechain/10 text-dogechain">
                          <Icon className="h-4 w-4" />
                        </div>
                        <div>
                          <CardTitle className="text-lg text-white">{achievement.title}</CardTitle>
                          <div className="flex items-center gap-1 text-xs text-white/60">
                            <span>Level {achievement.level}</span>
                            <span>/</span>
                            <span>{achievement.maxLevel}</span>
                          </div>
                        </div>
                      </div>
                      <CardDescription className="mt-2 text-white/70">{achievement.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="mb-4 space-y-2">
                        <Progress
                          value={achievement.progress}
                          className="h-2 bg-white/10"
                          indicatorClassName="bg-dogechain"
                        />
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-white/60">Progress to next level</span>
                          <span className="text-white/60">{achievement.progress}%</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-white/70">Reward:</span>
                          <span className="text-lg font-bold text-dogechain">{achievement.reward}</span>
                          <span className="text-white/70">$PAW</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <div>
        <Card className="glass-card border-white/5 sticky top-20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Trophy className="h-5 w-5 text-doge" />
              Your Rewards
            </CardTitle>
            <CardDescription className="text-white/70">Track your earnings and progress</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {isConnected ? (
              <>
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-sm text-white/70">Total Earned</span>
                    <span className="text-2xl font-bold text-doge doge-text-glow">375 $PAW</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Available</span>
                      <span className="text-white">225 $PAW</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Claimed</span>
                      <span className="text-white">150 $PAW</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-white">Achievements</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-dogechain/10 text-dogechain">
                          <Trophy className="h-3 w-3" />
                        </div>
                        <span className="text-sm text-white">Completed</span>
                      </div>
                      <span className="text-sm font-medium text-white">4/12</span>
                    </div>
                    <Progress value={33} className="h-2 bg-white/10" indicatorClassName="bg-dogechain" />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-white">Recent Activity</h3>
                    <Button variant="ghost" size="sm" className="text-white/60 hover:text-white">
                      View All
                    </Button>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                      <div className="flex items-center gap-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-doge/10 text-doge">
                          <Gift className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-white">First Trade Bonus</p>
                          <p className="text-xs text-white/60">2 hours ago</p>
                        </div>
                      </div>
                      <span className="text-sm font-medium text-doge">+50 $PAW</span>
                    </div>
                    <div className="flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                      <div className="flex items-center gap-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-dogechain/10 text-dogechain">
                          <Trophy className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-white">Achievement Unlocked</p>
                          <p className="text-xs text-white/60">1 day ago</p>
                        </div>
                      </div>
                      <span className="text-sm font-medium text-dogechain">Level Up</span>
                    </div>
                  </div>
                </div>

                <Button className="w-full doge-button doge-shine">
                  Claim All Rewards <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center py-6 text-center">
                <Trophy className="h-12 w-12 text-white/20 mb-4" />
                <p className="text-white/70 mb-4">Connect your wallet to view and claim your rewards</p>
                <Button className="doge-button doge-shine">Connect Wallet</Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
