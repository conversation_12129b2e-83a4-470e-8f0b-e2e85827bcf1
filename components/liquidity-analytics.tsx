"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
  <PERSON><PERSON>hart,
  <PERSON>,
} from "recharts"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useSorting, createSortableHeader, createSortableHeaderRight } from "@/hooks/use-sorting"

// Dynamic mock data for liquidity pools based on timeframe
const generateLiquidityPools = (timeframe: string) => {
  const basePools = [
    { pair: "PAW/wDOGE", liquidity: 2500000, volume24h: 450000, fees24h: 2250, apy: 32.5 },
    { pair: "DC/wDOGE", liquidity: 1800000, volume24h: 320000, fees24h: 1600, apy: 28.7 },
    { pair: "SHIB/wDOGE", liquidity: 950000, volume24h: 180000, fees24h: 900, apy: 24.3 },
    { pair: "RDOGE/wDOGE", liquidity: 750000, volume24h: 120000, fees24h: 600, apy: 22.1 },
    { pair: "PAW/DC", liquidity: 650000, volume24h: 95000, fees24h: 475, apy: 19.8 },
    { pair: "MOON/wDOGE", liquidity: 450000, volume24h: 75000, fees24h: 375, apy: 18.5 },
    { pair: "DGK/wDOGE", liquidity: 350000, volume24h: 60000, fees24h: 300, apy: 17.2 },
    { pair: "PAWT/wDOGE", liquidity: 250000, volume24h: 40000, fees24h: 200, apy: 15.8 },
  ]

  // Adjust volumes and liquidity based on timeframe
  const multiplier = timeframe === "24h" ? 0.8 : timeframe === "30d" ? 1.2 : timeframe === "90d" ? 1.5 : 1

  return basePools.map(pool => ({
    ...pool,
    liquidity: Math.round(pool.liquidity * multiplier),
    volume24h: Math.round(pool.volume24h * multiplier * (0.8 + Math.random() * 0.4)),
    fees24h: Math.round(pool.fees24h * multiplier),
    apy: Number((pool.apy * (0.9 + Math.random() * 0.2)).toFixed(1))
  }))
}

// Dynamic mock data for liquidity distribution based on timeframe
const generateLiquidityDistribution = (timeframe: string) => {
  const baseDistribution = [
    { name: "PAW/wDOGE", value: 32 },
    { name: "DC/wDOGE", value: 23 },
    { name: "SHIB/wDOGE", value: 12 },
    { name: "RDOGE/wDOGE", value: 10 },
    { name: "Other", value: 23 },
  ]

  // Adjust distribution based on timeframe
  switch (timeframe) {
    case "24h":
      return [
        { name: "PAW/wDOGE", value: 35 },
        { name: "DC/wDOGE", value: 25 },
        { name: "SHIB/wDOGE", value: 15 },
        { name: "RDOGE/wDOGE", value: 8 },
        { name: "Other", value: 17 },
      ]
    case "30d":
      return [
        { name: "PAW/wDOGE", value: 30 },
        { name: "DC/wDOGE", value: 22 },
        { name: "SHIB/wDOGE", value: 18 },
        { name: "RDOGE/wDOGE", value: 12 },
        { name: "Other", value: 18 },
      ]
    case "90d":
      return [
        { name: "PAW/wDOGE", value: 28 },
        { name: "DC/wDOGE", value: 20 },
        { name: "SHIB/wDOGE", value: 20 },
        { name: "RDOGE/wDOGE", value: 15 },
        { name: "Other", value: 17 },
      ]
    default:
      return baseDistribution
  }
}

const COLORS = ["#FFC107", "#8A2BE2", "#FF6B6B", "#4CAF50", "#2196F3"]

export function LiquidityAnalytics({ timeframe }: { timeframe: string }) {
  const liquidityPools = generateLiquidityPools(timeframe)
  const liquidityDistribution = generateLiquidityDistribution(timeframe)

  // Use sorting hook for the APY pools table
  const {
    sortedData: sortedPools,
    handleSort: handlePoolSort,
    getSortIcon: getPoolSortIcon
  } = useSorting({
    initialField: 'apy',
    initialDirection: 'desc',
    data: liquidityPools
  })

  // Format liquidity data for chart
  const liquidityData = liquidityPools.slice(0, 5).map((pool) => ({
    name: pool.pair,
    liquidity: pool.liquidity,
    volume: pool.volume24h,
  }))

  return (
    <div className="space-y-6">
      <Card className="glass-card border-white/5 liquid-glow">
        <CardHeader>
          <CardTitle className="text-white">Top Liquidity Pools</CardTitle>
          <CardDescription className="text-white/70">Liquidity and volume for top trading pairs</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={liquidityData} margin={{ top: 5, right: 30, left: 20, bottom: 25 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis
                  dataKey="name"
                  stroke="rgba(255,255,255,0.5)"
                  tick={{ fill: "rgba(255,255,255,0.5)" }}
                  tickLine={{ stroke: "rgba(255,255,255,0.2)" }}
                />
                <YAxis
                  stroke="rgba(255,255,255,0.5)"
                  tick={{ fill: "rgba(255,255,255,0.5)" }}
                  tickLine={{ stroke: "rgba(255,255,255,0.2)" }}
                  tickFormatter={(value) =>
                    `$${value >= 1000000 ? `${(value / 1000000).toFixed(1)}M` : `${(value / 1000).toFixed(1)}K`}`
                  }
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px'
                  }}
                  labelStyle={{ color: 'white', fontWeight: 'bold', marginBottom: '8px' }}
                  formatter={(value: any, name: string) => {
                    const formattedValue = `$${value >= 1000000 ? `${(value / 1000000).toFixed(2)}M` : `${(value / 1000).toFixed(1)}K`}`
                    return [formattedValue, name]
                  }}
                />
                <Legend />
                <Bar dataKey="liquidity" name="Liquidity" fill="#FFC107">
                  {liquidityData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={`hsl(${45 + index * 10}, 100%, 50%)`} />
                  ))}
                </Bar>
                <Bar dataKey="volume" name="Volume (24h)" fill="#8A2BE2">
                  {liquidityData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={`hsl(${270 + index * 10}, 60%, 50%)`} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card className="glass-card border-white/5 liquid-glow">
          <CardHeader>
            <CardTitle className="text-white">Liquidity Distribution</CardTitle>
            <CardDescription className="text-white/70">Distribution of liquidity across trading pairs</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={liquidityDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                  >
                    {liquidityDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value: number) => [`${value}%`, "Percentage"]}
                    contentStyle={{
                      backgroundColor: "rgba(20, 20, 25, 0.8)",
                      borderColor: "rgba(255, 255, 255, 0.1)",
                      borderRadius: "0.5rem",
                      color: "white",
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5 liquid-glow">
          <CardHeader>
            <CardTitle className="text-white">Top APY Pools</CardTitle>
            <CardDescription className="text-white/70">Highest yielding liquidity pools</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="border-white/10">
                    {createSortableHeader<typeof liquidityPools[0]>("Pair", "pair", getPoolSortIcon, handlePoolSort, "text-left", true)}
                    {createSortableHeaderRight<typeof liquidityPools[0]>("Liquidity", "liquidity", getPoolSortIcon, handlePoolSort)}
                    {createSortableHeaderRight<typeof liquidityPools[0]>("Fees (24h)", "fees24h", getPoolSortIcon, handlePoolSort)}
                    {createSortableHeaderRight<typeof liquidityPools[0]>("APY", "apy", getPoolSortIcon, handlePoolSort)}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedPools.slice(0, 8).map((pool) => (
                    <TableRow key={pool.pair} className="border-white/5 hover:bg-white/5 transition-colors duration-200">
                      <TableCell className="font-medium text-white">{pool.pair}</TableCell>
                      <TableCell className="text-right text-white/80">
                        ${(pool.liquidity / 1000000).toFixed(2)}M
                      </TableCell>
                      <TableCell className="text-right text-white/80">${pool.fees24h}</TableCell>
                      <TableCell className="text-right font-medium text-doge">{pool.apy.toFixed(1)}%</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
