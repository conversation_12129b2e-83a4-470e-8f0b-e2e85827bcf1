"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"

interface MilestoneTask {
  id: string
  title: string
  status: "completed" | "in-progress" | "planned" | "blocked"
}

interface Milestone {
  id: string
  title: string
  description: string
  targetDate: string
  status: "completed" | "in-progress" | "planned" | "blocked"
  tasks: MilestoneTask[]
}

const milestones: Milestone[] = [
  {
    id: "M1",
    title: "Core Infrastructure",
    description: "Establish the foundational infrastructure and critical functionality",
    targetDate: "2023-06-30",
    status: "in-progress",
    tasks: [
      { id: "CF-007", title: "Integrate Multiple Wallet Providers", status: "in-progress" },
      { id: "CF-008", title: "Implement Dogechain Integration", status: "in-progress" },
      { id: "CF-009", title: "Add Transaction Signing Process", status: "planned" },
      { id: "TC-004", title: "Standardize State Management", status: "in-progress" },
      { id: "TC-005", title: "Create Global Store", status: "planned" },
    ],
  },
  {
    id: "M2",
    title: "Token Launch Experience",
    description: "Complete the token launch functionality and user experience",
    targetDate: "2023-07-31",
    status: "planned",
    tasks: [
      { id: "CF-001", title: "Implement Token Launch Confirmation Flow", status: "planned" },
      { id: "CF-002", title: "Add Transaction Simulation", status: "planned" },
      { id: "CF-003", title: "Create Token Deployment Status Tracker", status: "in-progress" },
      { id: "UX-007", title: "Improve Error Handling", status: "planned" },
      { id: "UX-008", title: "Add Success Confirmations", status: "planned" },
    ],
  },
  {
    id: "M3",
    title: "Trading Enhancements",
    description: "Expand trading capabilities and portfolio management",
    targetDate: "2023-08-31",
    status: "planned",
    tasks: [
      { id: "CF-004", title: "Implement Limit Orders", status: "planned" },
      { id: "CF-005", title: "Create Trade History View", status: "planned" },
      { id: "CF-006", title: "Develop Portfolio Tracking", status: "planned" },
      { id: "PF-001", title: "Add Skeleton Loaders", status: "planned" },
      { id: "PF-002", title: "Improve Loading Indicators", status: "planned" },
    ],
  },
  {
    id: "M4",
    title: "Mobile & Accessibility",
    description: "Optimize for mobile devices and improve accessibility",
    targetDate: "2023-09-30",
    status: "planned",
    tasks: [
      { id: "MR-001", title: "Optimize Tables for Mobile", status: "planned" },
      { id: "MR-002", title: "Improve Chart Touch Interactions", status: "in-progress" },
      { id: "MR-003", title: "Optimize Form Fields for Mobile", status: "planned" },
      { id: "AC-001", title: "Standardize Focus States", status: "planned" },
      { id: "AC-004", title: "Complete ARIA Attributes", status: "planned" },
      { id: "AC-005", title: "Add Alternative Text", status: "in-progress" },
    ],
  },
  {
    id: "M5",
    title: "Performance & Technical Debt",
    description: "Improve performance and address technical debt",
    targetDate: "2023-10-31",
    status: "planned",
    tasks: [
      { id: "PF-004", title: "Optimize Images", status: "in-progress" },
      { id: "PF-005", title: "Implement Code Splitting", status: "planned" },
      { id: "PF-006", title: "Add Lazy Loading", status: "planned" },
      { id: "TC-001", title: "Implement Error Boundaries", status: "planned" },
      { id: "TC-002", title: "Create Error Logging System", status: "planned" },
      { id: "TC-007", title: "Add Unit Tests", status: "planned" },
    ],
  },
  {
    id: "M6",
    title: "Security & Compliance",
    description: "Enhance security features and ensure compliance",
    targetDate: "2023-11-30",
    status: "planned",
    tasks: [
      { id: "SC-001", title: "Implement Two-Factor Authentication", status: "planned" },
      { id: "SC-002", title: "Create Security Settings Page", status: "planned" },
      { id: "SC-003", title: "Add User Activity Logs", status: "planned" },
      { id: "CT-004", title: "Create Terms of Service", status: "planned" },
      { id: "CT-005", title: "Develop Privacy Policy", status: "planned" },
      { id: "CT-006", title: "Add Compliance Information", status: "planned" },
    ],
  },
  {
    id: "M7",
    title: "Community & Social",
    description: "Implement community and social features",
    targetDate: "2023-12-31",
    status: "planned",
    tasks: [
      { id: "SF-001", title: "Implement Chat/Messaging", status: "planned" },
      { id: "SF-002", title: "Enhance Social Feed", status: "planned" },
      { id: "SF-003", title: "Create Community Forums", status: "planned" },
      { id: "UX-003", title: "Create Help/Support Section", status: "planned" },
      { id: "CT-003", title: "Expand FAQ Section", status: "planned" },
    ],
  },
]

// Helper function to get status badge
const getStatusBadge = (status: string) => {
  switch (status) {
    case "completed":
      return <Badge className="bg-green-500">Completed</Badge>
    case "in-progress":
      return <Badge className="bg-blue-500">In Progress</Badge>
    case "planned":
      return <Badge className="bg-gray-500">Planned</Badge>
    case "blocked":
      return <Badge variant="destructive">Blocked</Badge>
    default:
      return <Badge className="bg-gray-500">Planned</Badge>
  }
}

export function RoadmapView() {
  const [activeTab, setActiveTab] = useState("timeline")

  return (
    <Card>
      <CardHeader>
        <CardTitle>Development Roadmap</CardTitle>
        <CardDescription>Strategic plan for implementing features and improvements</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="timeline">Timeline View</TabsTrigger>
            <TabsTrigger value="milestones">Milestone View</TabsTrigger>
          </TabsList>

          <TabsContent value="timeline" className="pt-4">
            <div className="relative">
              <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-800" />
              <div className="space-y-8">
                {milestones.map((milestone) => (
                  <div key={milestone.id} className="relative pl-10">
                    <div className="absolute left-0 top-1 h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-800 flex items-center justify-center">
                      <span className="text-xs font-bold">{milestone.id}</span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold">{milestone.title}</h3>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">{milestone.targetDate}</span>
                          {getStatusBadge(milestone.status)}
                        </div>
                      </div>
                      <p className="text-muted-foreground">{milestone.description}</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                        {milestone.tasks.map((task) => (
                          <div key={task.id} className="flex items-center gap-2 text-sm">
                            <div
                              className={`h-2 w-2 rounded-full ${
                                task.status === "completed"
                                  ? "bg-green-500"
                                  : task.status === "in-progress"
                                    ? "bg-blue-500"
                                    : task.status === "blocked"
                                      ? "bg-red-500"
                                      : "bg-gray-500"
                              }`}
                            />
                            <span className="font-mono text-xs">{task.id}</span>
                            <span>{task.title}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="milestones" className="pt-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {milestones.map((milestone) => (
                <Card key={milestone.id}>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">{milestone.title}</CardTitle>
                      {getStatusBadge(milestone.status)}
                    </div>
                    <CardDescription>{milestone.targetDate}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm mb-4">{milestone.description}</p>
                    <div className="space-y-2">
                      {milestone.tasks.map((task) => (
                        <div key={task.id} className="flex items-center gap-2 text-sm">
                          <div
                            className={`h-2 w-2 rounded-full ${
                              task.status === "completed"
                                ? "bg-green-500"
                                : task.status === "in-progress"
                                  ? "bg-blue-500"
                                  : task.status === "blocked"
                                    ? "bg-red-500"
                                    : "bg-gray-500"
                            }`}
                          />
                          <span>{task.title}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
