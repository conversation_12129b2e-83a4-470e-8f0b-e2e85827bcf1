"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { useWallet } from "@/components/wallet-provider"
import { useNotification } from "@/hooks/use-notification"
import { AlertTriangle, Clock, Info, Search, UserCheck, UserMinus, Users } from "lucide-react"

// Types
interface Delegate {
  address: string
  name: string
  avatar?: string
  votingPower: number
  delegatedPower: number
  proposalsVoted: number
  proposalsCreated: number
  delegators: number
  bio?: string
}

interface DelegationInterfaceProps {
  userVotingPower?: number
  currentDelegate?: string
  onDelegate?: (delegateAddress: string, amount: number) => Promise<void>
  onRevoke?: () => Promise<void>
}

export function DelegationInterface({
  userVotingPower = 5000,
  currentDelegate,
  onDelegate,
  onRevoke,
}: DelegationInterfaceProps) {
  const { isConnected, address } = useWallet()
  const { showNotification } = useNotification()

  // State
  const [activeTab, setActiveTab] = useState<"delegate" | "manage">("delegate")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedDelegate, setSelectedDelegate] = useState<Delegate | null>(null)
  const [delegationAmount, setDelegationAmount] = useState<number>(userVotingPower)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)

  // Mock delegates data
  const topDelegates: Delegate[] = [
    {
      address: "0x1234...5678",
      name: "DogeWhale",
      avatar: "/avatar-doge.png",
      votingPower: 250000,
      delegatedPower: 150000,
      proposalsVoted: 32,
      proposalsCreated: 5,
      delegators: 24,
      bio: "Active community member focused on sustainable growth and fair governance.",
    },
    {
      address: "0x8765...4321",
      name: "CryptoWizard",
      avatar: "/avatar-wizard.png",
      votingPower: 180000,
      delegatedPower: 120000,
      proposalsVoted: 28,
      proposalsCreated: 3,
      delegators: 18,
      bio: "DeFi expert with 5+ years experience. Focused on treasury management and risk assessment.",
    },
    {
      address: "0x5678...1234",
      name: "BlockchainDev",
      avatar: "/avatar-developer.png",
      votingPower: 120000,
      delegatedPower: 80000,
      proposalsVoted: 25,
      proposalsCreated: 7,
      delegators: 12,
      bio: "Smart contract developer and security researcher. Voting for technical excellence.",
    },
    {
      address: "0x4321...8765",
      name: "MemeEnthusiast",
      avatar: "/avatar-enthusiast.png",
      votingPower: 90000,
      delegatedPower: 50000,
      proposalsVoted: 20,
      proposalsCreated: 2,
      delegators: 8,
      bio: "Memecoin expert and community builder. Supporting innovative token economics.",
    },
  ]

  // Filter delegates based on search query
  const filteredDelegates = topDelegates.filter(
    (delegate) =>
      delegate.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      delegate.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (delegate.bio && delegate.bio.toLowerCase().includes(searchQuery.toLowerCase())),
  )

  // Handle delegation
  const handleDelegate = async () => {
    if (!selectedDelegate || delegationAmount <= 0) return

    setShowConfirmation(true)
  }

  // Handle confirmation
  const handleConfirmDelegation = async () => {
    if (!selectedDelegate) return

    setIsSubmitting(true)

    try {
      // Call the onDelegate function if provided
      if (onDelegate) {
        await onDelegate(selectedDelegate.address, delegationAmount)
      }

      showNotification({
        title: "Delegation Successful",
        message: `You have successfully delegated ${delegationAmount} voting power to ${selectedDelegate.name}`,
        type: "success",
        addToCenter: true,
      })

      // Reset state
      setShowConfirmation(false)
      setSelectedDelegate(null)
      setActiveTab("manage")
    } catch (error) {
      showNotification({
        title: "Delegation Failed",
        message: error instanceof Error ? error.message : "Failed to delegate voting power",
        type: "error",
        addToCenter: true,
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle revocation
  const handleRevokeDelegation = async () => {
    setIsSubmitting(true)

    try {
      // Call the onRevoke function if provided
      if (onRevoke) {
        await onRevoke()
      }

      showNotification({
        title: "Delegation Revoked",
        message: "You have successfully revoked your delegation",
        type: "success",
        addToCenter: true,
      })
    } catch (error) {
      showNotification({
        title: "Revocation Failed",
        message: error instanceof Error ? error.message : "Failed to revoke delegation",
        type: "error",
        addToCenter: true,
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Format number with commas
  const formatNumber = (num: number) => {
    return num.toLocaleString(undefined, { maximumFractionDigits: 2 })
  }

  return (
    <Card className="glass-card border-white/5">
      <CardHeader>
        <CardTitle className="text-white">Voting Delegation</CardTitle>
        <CardDescription className="text-white/70">
          Delegate your voting power to trusted community members
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {isConnected ? (
          <>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-white/70">Your Voting Power</span>
                <span className="text-dogechain font-medium dogechain-text-glow">{formatNumber(userVotingPower)}</span>
              </div>

              {currentDelegate && (
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Currently Delegated To</span>
                  <Badge className="bg-blue-500/20 text-blue-500">
                    {topDelegates.find((d) => d.address === currentDelegate)?.name || currentDelegate}
                  </Badge>
                </div>
              )}
            </div>

            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "delegate" | "manage")}>
              <TabsList className="grid w-full grid-cols-2 glass mb-4">
                <TabsTrigger value="delegate" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                  <UserCheck className="mr-2 h-4 w-4" />
                  Delegate Power
                </TabsTrigger>
                <TabsTrigger value="manage" className="data-[state=active]:text-white data-[state=active]:bg-white/10">
                  <Users className="mr-2 h-4 w-4" />
                  Manage Delegation
                </TabsTrigger>
              </TabsList>

              <TabsContent value="delegate" className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
                  <Input
                    placeholder="Search delegates by name or address..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="glass-input text-white pl-9"
                  />
                </div>

                <div className="space-y-4 mt-4">
                  <h3 className="text-lg font-medium text-white">Top Delegates</h3>

                  {filteredDelegates.length === 0 ? (
                    <p className="text-white/70 text-center py-4">No delegates found matching your search.</p>
                  ) : (
                    <div className="space-y-3">
                      {filteredDelegates.map((delegate) => (
                        <div
                          key={delegate.address}
                          className={`p-4 rounded-md ${
                            selectedDelegate?.address === delegate.address
                              ? "bg-doge/10 border border-doge/20"
                              : "bg-white/5 border border-white/10 hover:bg-white/10"
                          } cursor-pointer transition-colors`}
                          onClick={() => setSelectedDelegate(delegate)}
                        >
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage src={delegate.avatar || "/placeholder.svg"} alt={delegate.name} />
                              <AvatarFallback>{delegate.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <h4 className="text-white font-medium">{delegate.name}</h4>
                                <Badge className="bg-dogechain/20 text-dogechain">
                                  {formatNumber(delegate.votingPower)} Power
                                </Badge>
                              </div>
                              <p className="text-sm text-white/70 mt-1">{delegate.address}</p>
                            </div>
                          </div>

                          {selectedDelegate?.address === delegate.address && (
                            <div className="mt-3 space-y-3">
                              <p className="text-sm text-white/80">{delegate.bio}</p>

                              <div className="grid grid-cols-3 gap-2 text-center">
                                <div className="p-2 rounded-md bg-white/5">
                                  <p className="text-xs text-white/70">Proposals Voted</p>
                                  <p className="text-sm font-medium text-white">{delegate.proposalsVoted}</p>
                                </div>
                                <div className="p-2 rounded-md bg-white/5">
                                  <p className="text-xs text-white/70">Proposals Created</p>
                                  <p className="text-sm font-medium text-white">{delegate.proposalsCreated}</p>
                                </div>
                                <div className="p-2 rounded-md bg-white/5">
                                  <p className="text-xs text-white/70">Delegators</p>
                                  <p className="text-sm font-medium text-white">{delegate.delegators}</p>
                                </div>
                              </div>

                              <div className="space-y-2">
                                <Label htmlFor="delegation-amount" className="text-white">
                                  Amount to Delegate
                                </Label>
                                <Input
                                  id="delegation-amount"
                                  type="number"
                                  value={delegationAmount}
                                  onChange={(e) =>
                                    setDelegationAmount(Math.min(Number(e.target.value), userVotingPower))
                                  }
                                  className="glass-input text-white"
                                  min={1}
                                  max={userVotingPower}
                                />
                                <div className="flex justify-between text-xs text-white/60">
                                  <span>Min: 1</span>
                                  <span>Max: {formatNumber(userVotingPower)}</span>
                                </div>
                              </div>

                              <Button
                                className="w-full doge-button doge-shine"
                                onClick={handleDelegate}
                                disabled={delegationAmount <= 0 || delegationAmount > userVotingPower}
                              >
                                <UserCheck className="mr-2 h-4 w-4" />
                                Delegate to {delegate.name}
                              </Button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="manage" className="space-y-4">
                {currentDelegate ? (
                  <div className="space-y-4">
                    <div className="p-4 rounded-md bg-white/5 border border-white/10">
                      <h3 className="text-lg font-medium text-white mb-2">Current Delegation</h3>

                      {topDelegates.find((d) => d.address === currentDelegate) ? (
                        <div className="space-y-4">
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage
                                src={
                                  topDelegates.find((d) => d.address === currentDelegate)?.avatar || "/placeholder.svg"
                                }
                                alt={topDelegates.find((d) => d.address === currentDelegate)?.name || ""}
                              />
                              <AvatarFallback>
                                {(topDelegates.find((d) => d.address === currentDelegate)?.name || "").charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <h4 className="text-white font-medium">
                                {topDelegates.find((d) => d.address === currentDelegate)?.name}
                              </h4>
                              <p className="text-sm text-white/70">{currentDelegate}</p>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-2 text-center">
                            <div className="p-2 rounded-md bg-white/5">
                              <p className="text-xs text-white/70">Delegated Power</p>
                              <p className="text-sm font-medium text-dogechain">{formatNumber(userVotingPower)}</p>
                            </div>
                            <div className="p-2 rounded-md bg-white/5">
                              <p className="text-xs text-white/70">Delegation Date</p>
                              <p className="text-sm font-medium text-white">May 1, 2025</p>
                            </div>
                          </div>

                          <div className="p-3 rounded-md bg-blue-500/10 border border-blue-500/20 flex items-start gap-3">
                            <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                            <div>
                              <h4 className="text-sm font-medium text-white mb-1">Recent Voting Activity</h4>
                              <p className="text-sm text-white/70">
                                Your delegate has voted on 3 proposals in the last 7 days.
                              </p>
                            </div>
                          </div>

                          <Button
                            variant="outline"
                            className="w-full border-red-500/20 text-red-500 hover:bg-red-500/10"
                            onClick={handleRevokeDelegation}
                          >
                            <UserMinus className="mr-2 h-4 w-4" />
                            Revoke Delegation
                          </Button>
                        </div>
                      ) : (
                        <div className="text-white/70">Delegated to {currentDelegate}</div>
                      )}
                    </div>

                    <div className="p-4 rounded-md bg-white/5 border border-white/10">
                      <h3 className="text-lg font-medium text-white mb-2">Delegation History</h3>
                      <div className="space-y-3">
                        <div className="flex items-start gap-3">
                          <Clock className="h-4 w-4 text-white/60 mt-1" />
                          <div>
                            <p className="text-sm text-white">Delegated to DogeWhale</p>
                            <p className="text-xs text-white/60">May 1, 2025</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <Clock className="h-4 w-4 text-white/60 mt-1" />
                          <div>
                            <p className="text-sm text-white">Delegated to CryptoWizard</p>
                            <p className="text-xs text-white/60">Apr 15, 2025</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <Clock className="h-4 w-4 text-white/60 mt-1" />
                          <div>
                            <p className="text-sm text-white">Revoked delegation</p>
                            <p className="text-xs text-white/60">Apr 10, 2025</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <Users className="h-12 w-12 text-white/20 mb-4" />
                    <h3 className="text-lg font-medium text-white mb-2">No Active Delegation</h3>
                    <p className="text-white/70 max-w-md mb-4">
                      You haven't delegated your voting power to anyone yet. Delegate your voting power to have someone
                      vote on your behalf.
                    </p>
                    <Button className="doge-button doge-shine" onClick={() => setActiveTab("delegate")}>
                      <UserCheck className="mr-2 h-4 w-4" />
                      Delegate Now
                    </Button>
                  </div>
                )}
              </TabsContent>
            </Tabs>

            <Separator className="bg-white/10" />

            <div className="p-4 rounded-lg bg-white/5 border border-white/10 flex items-start gap-3">
              <Info className="h-5 w-5 text-white/70 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-white mb-1">About Delegation</h4>
                <p className="text-sm text-white/70">
                  Delegation allows you to assign your voting power to another address without transferring your tokens.
                  Your delegate will be able to vote on your behalf, but you can revoke this delegation at any time.
                </p>
              </div>
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <Users className="h-12 w-12 text-white/20 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">Connect Wallet to Delegate</h3>
            <p className="text-white/70 max-w-md mb-4">
              Connect your wallet to delegate your voting power or manage your existing delegation.
            </p>
            <Button className="doge-button doge-shine">Connect Wallet</Button>
          </div>
        )}
      </CardContent>

      {/* Confirmation Modal */}
      {showConfirmation && selectedDelegate && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card className="glass-card border-white/5 w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-white">Confirm Delegation</CardTitle>
              <CardDescription className="text-white/70">
                Please review the delegation details before confirming
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              {isSubmitting ? (
                <div className="flex flex-col items-center justify-center py-6">
                  <div className="h-12 w-12 rounded-full border-4 border-t-doge border-white/10 animate-spin mb-4" />
                  <p className="text-white font-medium">Processing delegation...</p>
                  <p className="text-white/70 text-sm">Please wait while your delegation is being processed</p>
                </div>
              ) : (
                <>
                  <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <div className="flex items-center gap-3 mb-4">
                      <Avatar>
                        <AvatarImage src={selectedDelegate.avatar || "/placeholder.svg"} alt={selectedDelegate.name} />
                        <AvatarFallback>{selectedDelegate.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <h4 className="text-white font-medium">{selectedDelegate.name}</h4>
                        <p className="text-sm text-white/70">{selectedDelegate.address}</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/70">Delegation Amount</span>
                        <span className="text-white font-medium">{formatNumber(delegationAmount)} voting power</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-white/70">Remaining Voting Power</span>
                        <span className="text-white font-medium">
                          {formatNumber(userVotingPower - delegationAmount)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <Alert className="glass-card border-yellow-500/20 bg-yellow-500/5">
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    <AlertTitle className="text-white">Important Information</AlertTitle>
                    <AlertDescription className="text-white/70">
                      By delegating your voting power, you are allowing the delegate to vote on your behalf. You can
                      revoke this delegation at any time.
                    </AlertDescription>
                  </Alert>
                </>
              )}
            </CardContent>

            <CardFooter className="flex gap-4">
              {!isSubmitting && (
                <>
                  <Button variant="outline" className="flex-1 glass-button" onClick={() => setShowConfirmation(false)}>
                    Cancel
                  </Button>
                  <Button className="flex-1 doge-button doge-shine" onClick={handleConfirmDelegation}>
                    Confirm Delegation
                  </Button>
                </>
              )}
            </CardFooter>
          </Card>
        </div>
      )}
    </Card>
  )
}
