"use client"

import { useState, useEffect, useRef, createContext, useContext, ReactNode } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

// Accessibility context
interface AccessibilityContextType {
  highContrast: boolean
  reducedMotion: boolean
  fontSize: "normal" | "large" | "extra-large"
  screenReader: boolean
  toggleHighContrast: () => void
  toggleReducedMotion: () => void
  setFontSize: (size: "normal" | "large" | "extra-large") => void
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined)

export function useAccessibility() {
  const context = useContext(AccessibilityContext)
  if (!context) {
    throw new Error("useAccessibility must be used within an AccessibilityProvider")
  }
  return context
}

// Accessibility provider
export function AccessibilityProvider({ children }: { children: ReactNode }) {
  const [highContrast, setHighContrast] = useState(false)
  const [reducedMotion, setReducedMotion] = useState(false)
  const [fontSize, setFontSize] = useState<"normal" | "large" | "extra-large">("normal")
  const [screenReader, setScreenReader] = useState(false)

  // Load preferences from localStorage
  useEffect(() => {
    const savedPrefs = localStorage.getItem("pawpumps-accessibility")
    if (savedPrefs) {
      const prefs = JSON.parse(savedPrefs)
      setHighContrast(prefs.highContrast || false)
      setReducedMotion(prefs.reducedMotion || false)
      setFontSize(prefs.fontSize || "normal")
    }

    // Detect screen reader
    const detectScreenReader = () => {
      setScreenReader(window.navigator.userAgent.includes("NVDA") || 
                     window.navigator.userAgent.includes("JAWS") ||
                     window.speechSynthesis !== undefined)
    }
    detectScreenReader()

    // Detect system preferences
    const motionQuery = window.matchMedia("(prefers-reduced-motion: reduce)")
    setReducedMotion(motionQuery.matches)
    motionQuery.addEventListener("change", (e) => setReducedMotion(e.matches))

    const contrastQuery = window.matchMedia("(prefers-contrast: high)")
    setHighContrast(contrastQuery.matches)
    contrastQuery.addEventListener("change", (e) => setHighContrast(e.matches))

    return () => {
      motionQuery.removeEventListener("change", (e) => setReducedMotion(e.matches))
      contrastQuery.removeEventListener("change", (e) => setHighContrast(e.matches))
    }
  }, [])

  // Save preferences to localStorage
  useEffect(() => {
    const prefs = { highContrast, reducedMotion, fontSize }
    localStorage.setItem("pawpumps-accessibility", JSON.stringify(prefs))
  }, [highContrast, reducedMotion, fontSize])

  // Apply CSS classes based on preferences
  useEffect(() => {
    const root = document.documentElement
    root.classList.toggle("high-contrast", highContrast)
    root.classList.toggle("reduced-motion", reducedMotion)
    root.classList.toggle("large-text", fontSize === "large")
    root.classList.toggle("extra-large-text", fontSize === "extra-large")
  }, [highContrast, reducedMotion, fontSize])

  const toggleHighContrast = () => setHighContrast(!highContrast)
  const toggleReducedMotion = () => setReducedMotion(!reducedMotion)

  return (
    <AccessibilityContext.Provider value={{
      highContrast,
      reducedMotion,
      fontSize,
      screenReader,
      toggleHighContrast,
      toggleReducedMotion,
      setFontSize,
    }}>
      {children}
    </AccessibilityContext.Provider>
  )
}

// Skip to content link
export function SkipToContent() {
  return (
    <a
      href="#main-content"
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-doge focus:text-black focus:rounded-md focus:font-medium"
    >
      Skip to main content
    </a>
  )
}

// Accessible button component
interface AccessibleButtonProps {
  children: ReactNode
  onClick?: () => void
  disabled?: boolean
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
  ariaLabel?: string
  ariaDescribedBy?: string
  type?: "button" | "submit" | "reset"
}

export function AccessibleButton({
  children,
  onClick,
  disabled = false,
  variant = "default",
  size = "default",
  className,
  ariaLabel,
  ariaDescribedBy,
  type = "button",
}: AccessibleButtonProps) {
  const { reducedMotion } = useAccessibility()

  return (
    <Button
      type={type}
      variant={variant}
      size={size}
      onClick={onClick}
      disabled={disabled}
      className={cn(
        className,
        reducedMotion && "transition-none",
        "focus:ring-2 focus:ring-doge focus:ring-offset-2 focus:ring-offset-black"
      )}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
    >
      {children}
    </Button>
  )
}

// Accessible form field
interface AccessibleFieldProps {
  label: string
  id: string
  children: ReactNode
  error?: string
  description?: string
  required?: boolean
}

export function AccessibleField({
  label,
  id,
  children,
  error,
  description,
  required = false,
}: AccessibleFieldProps) {
  const errorId = error ? `${id}-error` : undefined
  const descriptionId = description ? `${id}-description` : undefined

  return (
    <div className="space-y-2">
      <label
        htmlFor={id}
        className="block text-sm font-medium text-white"
      >
        {label}
        {required && (
          <span className="text-red-400 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
      
      {description && (
        <p id={descriptionId} className="text-sm text-white/70">
          {description}
        </p>
      )}
      
      <div
        aria-describedby={cn(descriptionId, errorId)}
        aria-invalid={!!error}
      >
        {children}
      </div>
      
      {error && (
        <p id={errorId} className="text-sm text-red-400" role="alert">
          {error}
        </p>
      )}
    </div>
  )
}

// Accessible modal
interface AccessibleModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: ReactNode
  className?: string
}

export function AccessibleModal({
  isOpen,
  onClose,
  title,
  children,
  className,
}: AccessibleModalProps) {
  const modalRef = useRef<HTMLDivElement>(null)
  const previousFocus = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (isOpen) {
      // Store previous focus
      previousFocus.current = document.activeElement as HTMLElement
      
      // Focus modal
      modalRef.current?.focus()
      
      // Prevent body scroll
      document.body.style.overflow = "hidden"
      
      // Trap focus
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === "Escape") {
          onClose()
        }
        
        if (e.key === "Tab") {
          const focusableElements = modalRef.current?.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          )
          
          if (focusableElements && focusableElements.length > 0) {
            const firstElement = focusableElements[0] as HTMLElement
            const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement
            
            if (e.shiftKey && document.activeElement === firstElement) {
              e.preventDefault()
              lastElement.focus()
            } else if (!e.shiftKey && document.activeElement === lastElement) {
              e.preventDefault()
              firstElement.focus()
            }
          }
        }
      }
      
      document.addEventListener("keydown", handleKeyDown)
      
      return () => {
        document.removeEventListener("keydown", handleKeyDown)
        document.body.style.overflow = ""
        
        // Restore previous focus
        if (previousFocus.current) {
          previousFocus.current.focus()
        }
      }
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <Card
        ref={modalRef}
        className={cn("glass-card border-white/5 max-w-2xl w-full", className)}
        tabIndex={-1}
      >
        <CardContent className="p-6">
          <h2 id="modal-title" className="text-xl font-semibold text-white mb-4">
            {title}
          </h2>
          {children}
        </CardContent>
      </Card>
    </div>
  )
}

// Live region for announcements
export function LiveRegion({ children, priority = "polite" }: {
  children: ReactNode
  priority?: "polite" | "assertive"
}) {
  return (
    <div
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
    >
      {children}
    </div>
  )
}

// Accessibility settings panel
export function AccessibilitySettings() {
  const {
    highContrast,
    reducedMotion,
    fontSize,
    toggleHighContrast,
    toggleReducedMotion,
    setFontSize,
  } = useAccessibility()

  return (
    <Card className="glass-card border-white/5">
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4">
          Accessibility Settings
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label htmlFor="high-contrast" className="text-white">
              High Contrast Mode
            </label>
            <Button
              id="high-contrast"
              variant={highContrast ? "default" : "outline"}
              size="sm"
              onClick={toggleHighContrast}
              className="border-white/10"
            >
              {highContrast ? "On" : "Off"}
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <label htmlFor="reduced-motion" className="text-white">
              Reduced Motion
            </label>
            <Button
              id="reduced-motion"
              variant={reducedMotion ? "default" : "outline"}
              size="sm"
              onClick={toggleReducedMotion}
              className="border-white/10"
            >
              {reducedMotion ? "On" : "Off"}
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <label htmlFor="font-size" className="text-white">
              Font Size
            </label>
            <select
              id="font-size"
              value={fontSize}
              onChange={(e) => setFontSize(e.target.value as any)}
              className="bg-black/20 border border-white/10 rounded px-3 py-1 text-white"
            >
              <option value="normal">Normal</option>
              <option value="large">Large</option>
              <option value="extra-large">Extra Large</option>
            </select>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
