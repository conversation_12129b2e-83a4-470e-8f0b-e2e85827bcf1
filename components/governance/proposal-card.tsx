"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ron<PERSON><PERSON>, <PERSON> } from "lucide-react"
import { governanceTokens } from "@/styles/governance-tokens"
import { StatusBadge } from "./status-badge"
import { CategoryBadge } from "./category-badge"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"

// Add imports for the execution functionality
import { useState } from "react"
import { ProposalExecution } from "./proposal-execution"
import type { ProposalAction } from "@/utils/proposal-executor"

// Types
type ProposalStatus = "active" | "passed" | "failed" | "pending" | "implemented"
type CategoryType =
  | "core-functionality"
  | "user-experience"
  | "visual-design"
  | "mobile"
  | "performance"
  | "accessibility"
  | "content"
  | "technical"
  | "security"
  | "social"

// Add to the ProposalCardProps interface
interface ProposalCardProps {
  id: string
  title: string
  description: string
  status: ProposalStatus
  category: CategoryType
  votesFor: number
  votesAgainst: number
  quorum: number
  creator: string
  createdAt: string
  endTime: string
  onClick?: () => void
  className?: string
  canExecute?: boolean
  actions?: ProposalAction[]
  onExecuted?: () => void
}

// Helper function to get status icon
const getStatusIcon = (status: ProposalStatus) => {
  switch (status) {
    case "active":
      return <Clock className="h-4 w-4" />
    case "passed":
      return <Check className="h-4 w-4" />
    case "failed":
      return <AlertTriangle className="h-4 w-4" />
    case "pending":
      return <Clock className="h-4 w-4" />
    case "implemented":
      return <Check className="h-4 w-4" />
    default:
      return <Clock className="h-4 w-4" />
  }
}

// Helper function to format votes
const formatVotes = (votes: number) => {
  if (votes >= 1000000) {
    return `${(votes / 1000000).toFixed(1)}M`
  } else if (votes >= 1000) {
    return `${(votes / 1000).toFixed(1)}K`
  }
  return votes.toString()
}

// Helper function to calculate progress
const calculateProgress = (votesFor: number, votesAgainst: number) => {
  const total = votesFor + votesAgainst
  if (total === 0) return 0
  return (votesFor / total) * 100
}

// Add state for execution modal
export function ProposalCard({
  id,
  title,
  description,
  status,
  category,
  votesFor,
  votesAgainst,
  quorum,
  creator,
  createdAt,
  endTime,
  onClick,
  className,
  canExecute = false,
  actions = [],
  onExecuted,
}: ProposalCardProps) {
  const router = useRouter()
  const [showExecutionModal, setShowExecutionModal] = useState(false)

  const handleClick = () => {
    if (onClick) {
      onClick()
    } else {
      // Navigate to the proposal detail page
      router.push(`/governance/proposals/${id}`)
    }
  }

  return (
    <div
      className={cn(
        "glass-card border border-white/5 rounded-lg",
        governanceTokens.spacing.card.padding,
        governanceTokens.animation.transition,
        "cursor-pointer hover:border-dogechain/20",
        className,
      )}
      onClick={handleClick}
      tabIndex={0}
      role="button"
      aria-label={`Proposal: ${title}`}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault()
          handleClick()
        }
      }}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <span className={cn("font-mono", governanceTokens.typography.metadata)}>{id}</span>
          <StatusBadge status={status} type="proposal" icon={getStatusIcon(status)} />
        </div>
        <CategoryBadge category={category} />
      </div>

      <div className="flex items-center justify-between">
        <h3 className={cn("text-white", governanceTokens.typography.title.small, "mb-2")}>{title}</h3>
        <ChevronRight className="h-4 w-4 text-white/40 flex-shrink-0 ml-2" />
      </div>

      <p className={cn("text-white/70", governanceTokens.typography.body.medium, "mb-4 line-clamp-2")}>{description}</p>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className={governanceTokens.typography.metadata}>For: {formatVotes(votesFor)}</span>
            <div className="w-2 h-2 rounded-full bg-green-500 ml-1" aria-hidden="true"></div>
          </div>
          <div className="flex items-center">
            <span className={governanceTokens.typography.metadata}>Against: {formatVotes(votesAgainst)}</span>
            <div className="w-2 h-2 rounded-full bg-red-500 ml-1" aria-hidden="true"></div>
          </div>
        </div>

        <Progress
          value={calculateProgress(votesFor, votesAgainst)}
          className="h-2 bg-white/10"
          indicatorClassName={status === "active" ? "bg-dogechain" : status === "passed" ? "bg-green-500" : "bg-doge"}
          aria-label={`Voting progress: ${calculateProgress(votesFor, votesAgainst).toFixed(1)}% in favor`}
        />

        <div className="flex items-center justify-between">
          <span className={governanceTokens.typography.metadata}>
            Created by {creator.slice(0, 6)}...{creator.slice(-4)}
          </span>
          <span className={governanceTokens.typography.metadata}>
            {status === "active" || status === "pending" ? `Ends: ${endTime}` : `Ended: ${endTime}`}
          </span>
        </div>
      </div>
      {status === "passed" && canExecute && (
        <div className="mt-4">
          <Button
            onClick={(e) => {
              e.stopPropagation()
              setShowExecutionModal(true)
            }}
            className="w-full bg-doge text-black hover:bg-doge/90"
          >
            <Code className="mr-2 h-4 w-4" />
            Execute Proposal
          </Button>
        </div>
      )}
      {showExecutionModal && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <ProposalExecution
            proposalId={id}
            title={title}
            description={description}
            actions={actions}
            onClose={() => setShowExecutionModal(false)}
            onSuccess={() => {
              setShowExecutionModal(false)
              onExecuted?.()
            }}
          />
        </div>
      )}
    </div>
  )
}
