"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { useAuditLog } from "@/utils/audit-logger"
import { useWallet } from "@/components/wallet-provider"
import { useAdminAuthContext } from "@/contexts/admin-auth-context"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { AlertTriangle, Edit, Lock, Shield, Trash2 } from "lucide-react"

interface AdminProposalActionsProps {
  proposal: any
  onUpdate?: (updatedProposal: any) => void
}

export function AdminProposalActions({ proposal, onUpdate }: AdminProposalActionsProps) {
  const { toast } = useToast()
  const { logAction } = useAuditLog()
  const { address } = useWallet()
  const { hasPermission } = useAdminAuthContext()

  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showPauseDialog, setShowPauseDialog] = useState(false)
  const [adminNote, setAdminNote] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const canModerate = hasPermission("proposals:write")
  const canEmergency = hasPermission("emergency:control")

  const handlePauseProposal = async () => {
    setIsSubmitting(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    const updatedProposal = {
      ...proposal,
      status: proposal.status === "paused" ? "active" : "paused",
      adminNote: adminNote,
      lastModifiedBy: address,
      lastModifiedAt: new Date().toISOString(),
    }

    // Log the action
    await logAction(proposal.status === "paused" ? "proposal:resume" : "proposal:pause", address || "unknown", {
      proposalId: proposal.id,
      title: proposal.title,
      reason: adminNote,
    })

    toast({
      title: `Proposal ${proposal.status === "paused" ? "Resumed" : "Paused"}`,
      description: `The proposal has been ${proposal.status === "paused" ? "resumed" : "paused"} successfully.`,
    })

    if (onUpdate) {
      onUpdate(updatedProposal)
    }

    setIsSubmitting(false)
    setShowPauseDialog(false)
    setAdminNote("")
  }

  const handleDeleteProposal = async () => {
    setIsSubmitting(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Log the action
    await logAction("proposal:delete", address || "unknown", {
      proposalId: proposal.id,
      title: proposal.title,
      reason: adminNote,
    })

    toast({
      title: "Proposal Deleted",
      description: "The proposal has been deleted successfully.",
    })

    // In a real app, this would redirect to the proposals list
    // For now, we'll just close the dialog
    setIsSubmitting(false)
    setShowDeleteDialog(false)
    setAdminNote("")
  }

  if (!canModerate && !canEmergency) {
    return null
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border border-white/10 bg-white/5 p-4">
        <div className="flex items-center gap-2 mb-3">
          <Shield className="h-5 w-5 text-doge" />
          <h3 className="text-lg font-medium text-white">Admin Actions</h3>
        </div>

        <div className="flex flex-wrap gap-2">
          {canModerate && (
            <>
              <Button variant="outline" size="sm" className="border-white/10 bg-white/5 text-white hover:bg-white/10">
                <Edit className="mr-2 h-4 w-4" />
                Edit Proposal
              </Button>

              <Button
                variant="outline"
                size="sm"
                className="border-red-500/20 bg-red-500/5 text-red-500 hover:bg-red-500/10"
                onClick={() => setShowDeleteDialog(true)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Proposal
              </Button>
            </>
          )}

          {canEmergency && (
            <Button
              variant="outline"
              size="sm"
              className={
                proposal.status === "paused"
                  ? "border-green-500/20 bg-green-500/5 text-green-500 hover:bg-green-500/10"
                  : "border-yellow-500/20 bg-yellow-500/5 text-yellow-500 hover:bg-yellow-500/10"
              }
              onClick={() => setShowPauseDialog(true)}
            >
              <Lock className="mr-2 h-4 w-4" />
              {proposal.status === "paused" ? "Resume Proposal" : "Pause Proposal"}
            </Button>
          )}
        </div>
      </div>

      {/* Pause/Resume Dialog */}
      <Dialog open={showPauseDialog} onOpenChange={setShowPauseDialog}>
        <DialogContent className="glass-card border-white/5">
          <DialogHeader>
            <DialogTitle className="text-white">
              {proposal.status === "paused" ? "Resume Proposal" : "Pause Proposal"}
            </DialogTitle>
            <DialogDescription className="text-white/70">
              {proposal.status === "paused"
                ? "This will resume the proposal and allow voting to continue."
                : "This will pause the proposal and prevent any further voting."}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-4 rounded-md bg-white/5 border border-white/10">
              <h3 className="font-medium text-white">{proposal.title}</h3>
              <p className="text-sm text-white/70 mt-1">ID: {proposal.id}</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="adminNote" className="text-white">
                Admin Note (Required)
              </Label>
              <Textarea
                id="adminNote"
                value={adminNote}
                onChange={(e) => setAdminNote(e.target.value)}
                className="min-h-[100px] border-white/20 bg-black/20 text-white"
                placeholder="Explain why you are pausing/resuming this proposal..."
              />
            </div>

            <Alert className="glass-card border-yellow-500/20 bg-yellow-500/5">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              <AlertTitle className="text-white">Warning</AlertTitle>
              <AlertDescription className="text-white/70">
                {proposal.status === "paused"
                  ? "Resuming this proposal will allow voting to continue. This action will be logged."
                  : "Pausing this proposal will prevent any further voting. This action will be logged."}
              </AlertDescription>
            </Alert>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowPauseDialog(false)}
              className="border-white/10 bg-white/5 text-white"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handlePauseProposal}
              className={
                proposal.status === "paused"
                  ? "bg-green-600 hover:bg-green-700 text-white"
                  : "bg-yellow-600 hover:bg-yellow-700 text-white"
              }
              disabled={isSubmitting || !adminNote}
            >
              {isSubmitting ? (
                <>
                  <div className="h-4 w-4 rounded-full border-2 border-t-white border-white/10 animate-spin mr-2" />
                  {proposal.status === "paused" ? "Resuming..." : "Pausing..."}
                </>
              ) : (
                <>
                  <Lock className="mr-2 h-4 w-4" />
                  {proposal.status === "paused" ? "Resume Proposal" : "Pause Proposal"}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="glass-card border-white/5">
          <DialogHeader>
            <DialogTitle className="text-white">Delete Proposal</DialogTitle>
            <DialogDescription className="text-white/70">
              This will permanently delete the proposal and all associated votes and comments.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-4 rounded-md bg-white/5 border border-white/10">
              <h3 className="font-medium text-white">{proposal.title}</h3>
              <p className="text-sm text-white/70 mt-1">ID: {proposal.id}</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="deleteNote" className="text-white">
                Admin Note (Required)
              </Label>
              <Textarea
                id="deleteNote"
                value={adminNote}
                onChange={(e) => setAdminNote(e.target.value)}
                className="min-h-[100px] border-white/20 bg-black/20 text-white"
                placeholder="Explain why you are deleting this proposal..."
              />
            </div>

            <Alert className="glass-card border-red-500/20 bg-red-500/5">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <AlertTitle className="text-white">Warning</AlertTitle>
              <AlertDescription className="text-white/70">
                Deleting a proposal is permanent and cannot be undone. All votes and comments will be lost.
              </AlertDescription>
            </Alert>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              className="border-white/10 bg-white/5 text-white"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteProposal}
              className="bg-red-600 hover:bg-red-700 text-white"
              disabled={isSubmitting || !adminNote}
            >
              {isSubmitting ? (
                <>
                  <div className="h-4 w-4 rounded-full border-2 border-t-white border-white/10 animate-spin mr-2" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Proposal
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
