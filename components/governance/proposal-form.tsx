"use client"

import type React from "react"

import { useState } from "react"
import { X, HelpCircle, AlertTriangle } from "lucide-react"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { governanceTokens } from "@/styles/governance-tokens"
import { cn } from "@/lib/utils"

// Types
type CategoryType =
  | "core-functionality"
  | "user-experience"
  | "visual-design"
  | "mobile"
  | "performance"
  | "accessibility"
  | "content"
  | "technical"
  | "security"
  | "social"

interface ProposalFormProps {
  onClose: () => void
  onSubmit: (proposal: {
    title: string
    description: string
    category: CategoryType
  }) => void
  isSubmitting?: boolean
  minStakeRequired?: number
  userStake?: number
}

export function ProposalForm({
  onClose,
  onSubmit,
  isSubmitting = false,
  minStakeRequired = 1000,
  userStake = 5000,
}: ProposalFormProps) {
  // Form state
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [category, setCategory] = useState<CategoryType | "">("")

  // Validation state
  const [errors, setErrors] = useState<{
    title?: string
    description?: string
    category?: string
  }>({})

  // Character limits
  const TITLE_MAX_LENGTH = 100
  const DESCRIPTION_MAX_LENGTH = 2000

  // Validate form
  const validateForm = () => {
    const newErrors: {
      title?: string
      description?: string
      category?: string
    } = {}

    if (!title.trim()) {
      newErrors.title = "Title is required"
    } else if (title.length > TITLE_MAX_LENGTH) {
      newErrors.title = `Title must be ${TITLE_MAX_LENGTH} characters or less`
    }

    if (!description.trim()) {
      newErrors.description = "Description is required"
    } else if (description.length > DESCRIPTION_MAX_LENGTH) {
      newErrors.description = `Description must be ${DESCRIPTION_MAX_LENGTH} characters or less`
    }

    if (!category) {
      newErrors.category = "Category is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (validateForm()) {
      onSubmit({
        title,
        description,
        category: category as CategoryType,
      })
    }
  }

  // Check if user has enough stake
  const hasEnoughStake = userStake >= minStakeRequired

  return (
    <div
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto"
      role="dialog"
      aria-modal="true"
      aria-labelledby="proposal-form-title"
    >
      <Card className="glass-card border-white/5 w-full max-w-2xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle id="proposal-form-title" className={governanceTokens.typography.title.medium}>
              Create New Proposal
            </CardTitle>
            <Button
              variant="ghost"
              size="icon"
              className="text-white/70 hover:text-white"
              onClick={onClose}
              aria-label="Close dialog"
            >
              <span className="sr-only">Close</span>
              <X className="h-6 w-6" />
            </Button>
          </div>
          <CardDescription className="text-white/70">Submit a proposal for the community to vote on</CardDescription>
        </CardHeader>

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {!hasEnoughStake && (
              <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-red-500 mb-1">Insufficient Stake</h4>
                  <p className="text-sm text-white/90">
                    You need at least {minStakeRequired.toLocaleString()} $PAW staked to create a proposal. You
                    currently have {userStake.toLocaleString()} $PAW staked.
                  </p>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="title" className="text-white">
                  Proposal Title
                </Label>
                <span
                  className={cn(
                    governanceTokens.typography.metadata,
                    title.length > TITLE_MAX_LENGTH ? "text-red-500" : "",
                  )}
                >
                  {title.length}/{TITLE_MAX_LENGTH}
                </span>
              </div>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter a clear, concise title for your proposal"
                className={cn("glass-input text-white", errors.title ? "border-red-500" : "")}
                aria-invalid={errors.title ? "true" : "false"}
                aria-describedby={errors.title ? "title-error" : undefined}
                maxLength={TITLE_MAX_LENGTH + 10}
              />
              {errors.title && (
                <p id="title-error" className="text-sm text-red-500 mt-1">
                  {errors.title}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="description" className="text-white">
                  Description
                </Label>
                <span
                  className={cn(
                    governanceTokens.typography.metadata,
                    description.length > DESCRIPTION_MAX_LENGTH ? "text-red-500" : "",
                  )}
                >
                  {description.length}/{DESCRIPTION_MAX_LENGTH}
                </span>
              </div>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Provide a detailed description of your proposal, including its purpose and expected impact"
                className={cn("glass-input text-white min-h-[200px]", errors.description ? "border-red-500" : "")}
                aria-invalid={errors.description ? "true" : "false"}
                aria-describedby={errors.description ? "description-error" : undefined}
                maxLength={DESCRIPTION_MAX_LENGTH + 100}
              />
              {errors.description && (
                <p id="description-error" className="text-sm text-red-500 mt-1">
                  {errors.description}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="category" className="text-white">
                Category
              </Label>
              <Select value={category} onValueChange={(value) => setCategory(value as CategoryType)}>
                <SelectTrigger
                  id="category"
                  className={cn("glass-input text-white", errors.category ? "border-red-500" : "")}
                  aria-invalid={errors.category ? "true" : "false"}
                  aria-describedby={errors.category ? "category-error" : undefined}
                >
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="core-functionality">Core Functionality</SelectItem>
                  <SelectItem value="user-experience">User Experience</SelectItem>
                  <SelectItem value="visual-design">Visual Design</SelectItem>
                  <SelectItem value="mobile">Mobile</SelectItem>
                  <SelectItem value="performance">Performance</SelectItem>
                  <SelectItem value="accessibility">Accessibility</SelectItem>
                  <SelectItem value="content">Content</SelectItem>
                  <SelectItem value="technical">Technical</SelectItem>
                  <SelectItem value="security">Security</SelectItem>
                  <SelectItem value="social">Social</SelectItem>
                </SelectContent>
              </Select>
              {errors.category && (
                <p id="category-error" className="text-sm text-red-500 mt-1">
                  {errors.category}
                </p>
              )}
            </div>

            <div className="p-4 rounded-lg bg-white/5 border border-white/10 flex items-start gap-3">
              <HelpCircle className="h-5 w-5 text-white/70 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-white mb-1">Proposal Guidelines</h4>
                <ul className="text-sm text-white/70 space-y-1 list-disc pl-4">
                  <li>Be specific and clear about what you're proposing</li>
                  <li>Provide context and reasoning for your proposal</li>
                  <li>Consider the technical feasibility of your suggestion</li>
                  <li>Outline the expected benefits to the community</li>
                  <li>Proposals require a quorum of votes to be considered valid</li>
                </ul>
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex gap-4">
            <Button type="button" variant="outline" className="flex-1 glass-button" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="flex-1 doge-button doge-shine" disabled={isSubmitting || !hasEnoughStake}>
              {isSubmitting ? "Submitting..." : "Submit Proposal"}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}
