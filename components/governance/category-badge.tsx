import { governanceTokens } from "@/styles/governance-tokens"
import { cn } from "@/lib/utils"

type CategoryType =
  | "core-functionality"
  | "user-experience"
  | "visual-design"
  | "mobile"
  | "performance"
  | "accessibility"
  | "content"
  | "technical"
  | "security"
  | "social"

interface CategoryBadgeProps {
  category: CategoryType
  className?: string
}

// Map categories to human-readable descriptions for screen readers
const getCategoryDescription = (category: CategoryType): string => {
  switch (category) {
    case "core-functionality":
      return "Core functionality category"
    case "user-experience":
      return "User experience category"
    case "visual-design":
      return "Visual design category"
    case "mobile":
      return "Mobile category"
    case "performance":
      return "Performance category"
    case "accessibility":
      return "Accessibility category"
    case "content":
      return "Content category"
    case "technical":
      return "Technical category"
    case "security":
      return "Security category"
    case "social":
      return "Social category"
    default:
      return `Category: ${(category as string).replace(/-/g, " ")}`
  }
}

export function CategoryBadge({ category, className }: CategoryBadgeProps) {
  // Get category colors
  const categoryColors = {
    "core-functionality": "bg-blue-500/20 text-blue-500 border-blue-500/30",
    "user-experience": "bg-purple-500/20 text-purple-500 border-purple-500/30",
    "visual-design": "bg-pink-500/20 text-pink-500 border-pink-500/30",
    mobile: "bg-orange-500/20 text-orange-500 border-orange-500/30",
    performance: "bg-yellow-500/20 text-yellow-500 border-yellow-500/30",
    accessibility: "bg-green-500/20 text-green-500 border-green-500/30",
    content: "bg-teal-500/20 text-teal-500 border-teal-500/30",
    technical: "bg-cyan-500/20 text-cyan-500 border-cyan-500/30",
    security: "bg-red-500/20 text-red-500 border-red-500/30",
    social: "bg-indigo-500/20 text-indigo-500 border-indigo-500/30",
  }

  // Get the category description for screen readers
  const categoryDescription = getCategoryDescription(category)

  return (
    <div
      className={cn(
        "px-2 py-0.5 inline-flex items-center text-xs font-medium rounded-full",
        "border",
        categoryColors[category as keyof typeof categoryColors] || "bg-white/10 text-white/70 border-white/20",
        className,
      )}
      aria-label={categoryDescription}
    >
      <span className="capitalize">{category.replace(/-/g, " ")}</span>
    </div>
  )
}
