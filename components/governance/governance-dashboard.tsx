"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Plus, Filter, Search, ArrowUpDown, Info } from "lucide-react"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ProposalCard } from "./proposal-card"
import { VotingModal } from "./voting-modal"
import { ProposalForm } from "./proposal-form"
import { StakingInterface } from "./staking-interface"
import { useWallet } from "@/components/wallet-provider"
import { useNotification } from "@/hooks/use-notification"
import { mockProposals } from "@/data/mock-proposals"
import type { Proposal, ProposalStatus, CategoryType } from "@/data/mock-proposals"

export function GovernanceDashboard() {
  const router = useRouter()
  const { isConnected } = useWallet()
  const { showNotification } = useNotification()

  // State
  const [activeTab, setActiveTab] = useState<ProposalStatus>("active")
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(null)
  const [showProposalForm, setShowProposalForm] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState<string>("all")
  const [sortOrder, setSortOrder] = useState<"newest" | "oldest" | "most-votes" | "least-votes">("newest")

  // Handle voting
  const handleVote = (vote: "for" | "against") => {
    if (!selectedProposal) return

    showNotification({
      title: "Vote Submitted",
      message: `You voted ${vote} the proposal "${selectedProposal.title}"`,
      type: "success",
      addToCenter: true,
    })

    setSelectedProposal(null)
  }

  // Handle proposal creation
  const handleCreateProposal = (proposal: {
    title: string
    description: string
    category: CategoryType
  }) => {
    // In a real app, this would send the proposal to the blockchain
    showNotification({
      title: "Proposal Created",
      message: `Your proposal "${proposal.title}" has been submitted for review`,
      type: "success",
      addToCenter: true,
    })

    setShowProposalForm(false)
  }

  // Add a function to handle proposal execution
  const handleProposalExecuted = (proposalId: string) => {
    showNotification({
      title: "Proposal Executed",
      message: `Proposal ${proposalId} has been successfully executed`,
      type: "success",
      addToCenter: true,
    })

    // In a real app, this would refresh the proposals list
    // For now, we'll just update the UI to show it's been executed
    // This is a simplified example
  }

  // Navigate to proposal detail page
  const handleProposalClick = (proposal: Proposal) => {
    router.push(`/governance/proposals/${proposal.id}`)
  }

  // Filter and sort proposals
  const filteredProposals = mockProposals
    .filter((proposal) => {
      // Filter by status
      if (proposal.status !== activeTab) return false

      // Filter by search query
      if (
        searchQuery &&
        !proposal.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !proposal.description.toLowerCase().includes(searchQuery.toLowerCase())
      ) {
        return false
      }

      // Filter by category
      if (categoryFilter !== "all" && proposal.category !== categoryFilter) {
        return false
      }

      return true
    })
    .sort((a, b) => {
      // Sort by selected order
      switch (sortOrder) {
        case "newest":
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case "oldest":
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case "most-votes":
          return b.votesFor + b.votesAgainst - (a.votesFor + a.votesAgainst)
        case "least-votes":
          return a.votesFor + a.votesAgainst - (b.votesFor + b.votesAgainst)
        default:
          return 0
      }
    })

  return (
    <div className="grid gap-8 lg:grid-cols-3">
      <div className="lg:col-span-2 space-y-6">
        <Card className="glass-card border-white/5 doge-glow">
          <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="text-white">Governance Proposals</CardTitle>
              <CardDescription className="text-white/70">
                Vote on proposals with your $PAW tokens to shape the future of the platform
              </CardDescription>
            </div>
            <Button
              className="doge-button doge-shine sm:self-start"
              onClick={() => setShowProposalForm(true)}
              disabled={!isConnected}
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Proposal
            </Button>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
                <Input
                  placeholder="Search proposals..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="glass-input text-white pl-9"
                  aria-label="Search proposals"
                />
              </div>

              <div className="flex gap-2">
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="glass-input text-white w-[140px]" aria-label="Filter by category">
                    <Filter className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="core-functionality">Core Functionality</SelectItem>
                    <SelectItem value="user-experience">User Experience</SelectItem>
                    <SelectItem value="visual-design">Visual Design</SelectItem>
                    <SelectItem value="mobile">Mobile</SelectItem>
                    <SelectItem value="performance">Performance</SelectItem>
                    <SelectItem value="accessibility">Accessibility</SelectItem>
                    <SelectItem value="content">Content</SelectItem>
                    <SelectItem value="technical">Technical</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                    <SelectItem value="social">Social</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortOrder} onValueChange={(value) => setSortOrder(value as any)}>
                  <SelectTrigger className="glass-input text-white w-[140px]" aria-label="Sort proposals">
                    <ArrowUpDown className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest First</SelectItem>
                    <SelectItem value="oldest">Oldest First</SelectItem>
                    <SelectItem value="most-votes">Most Votes</SelectItem>
                    <SelectItem value="least-votes">Least Votes</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Tabs
              defaultValue={activeTab}
              onValueChange={(value) => setActiveTab(value as ProposalStatus)}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-5 glass mb-6">
                <TabsTrigger value="active" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                  Active
                </TabsTrigger>
                <TabsTrigger
                  value="passed"
                  className="data-[state=active]:text-green-500 data-[state=active]:bg-green-500/10"
                >
                  Passed
                </TabsTrigger>
                <TabsTrigger
                  value="failed"
                  className="data-[state=active]:text-red-500 data-[state=active]:bg-red-500/10"
                >
                  Failed
                </TabsTrigger>
                <TabsTrigger value="pending" className="data-[state=active]:text-white data-[state=active]:bg-white/10">
                  Pending
                </TabsTrigger>
                <TabsTrigger
                  value="implemented"
                  className="data-[state=active]:text-purple-500 data-[state=active]:bg-purple-500/10"
                >
                  Implemented
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="space-y-4">
                {filteredProposals.length > 0 ? (
                  filteredProposals.map((proposal) => (
                    <ProposalCard
                      key={proposal.id}
                      id={proposal.id}
                      title={proposal.title}
                      description={proposal.description}
                      status={proposal.status}
                      category={proposal.category}
                      votesFor={proposal.votesFor}
                      votesAgainst={proposal.votesAgainst}
                      quorum={proposal.quorum}
                      creator={proposal.creator}
                      createdAt={proposal.createdAt}
                      endTime={proposal.endTime}
                      onClick={() => handleProposalClick(proposal)}
                      canExecute={proposal.status === "passed" && isConnected}
                      actions={proposal.actions || []}
                      onExecuted={() => handleProposalExecuted(proposal.id)}
                    />
                  ))
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <Info className="h-12 w-12 text-white/20 mb-4" />
                    <h3 className="text-lg font-medium text-white mb-2">No proposals found</h3>
                    <p className="text-white/70 max-w-md">
                      {searchQuery || categoryFilter !== "all"
                        ? "Try adjusting your search or filters to find what you're looking for."
                        : `There are no ${activeTab} proposals at the moment.`}
                    </p>
                    {activeTab === "active" && isConnected && (
                      <Button className="mt-4 doge-button doge-shine" onClick={() => setShowProposalForm(true)}>
                        <Plus className="mr-2 h-4 w-4" />
                        Create Proposal
                      </Button>
                    )}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>

          <CardFooter className="flex justify-center">
            <div className="p-4 rounded-lg bg-white/5 border border-white/10 flex items-start gap-3 w-full">
              <Info className="h-5 w-5 text-white/70 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-white mb-1">About Governance</h4>
                <p className="text-sm text-white/70">
                  PawPumps governance allows token holders to propose and vote on changes to the platform. Proposals
                  require a quorum of votes to be considered valid, and must receive a majority of "For" votes to pass.
                </p>
                <a href="/docs/governance" className="text-sm text-doge hover:underline mt-2 inline-flex items-center">
                  Learn more about governance
                  <ArrowUpDown className="ml-1 h-3 w-3" />
                </a>
              </div>
            </div>
          </CardFooter>
        </Card>

        {/* Governance Stats Card */}
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white text-lg">Governance Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-4 rounded-lg bg-white/5 border border-white/10 text-center">
                <p className="text-white/70 text-sm mb-1">Total Proposals</p>
                <p className="text-2xl font-bold text-white">{mockProposals.length}</p>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10 text-center">
                <p className="text-white/70 text-sm mb-1">Active Proposals</p>
                <p className="text-2xl font-bold text-doge">
                  {mockProposals.filter((p) => p.status === "active").length}
                </p>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10 text-center">
                <p className="text-white/70 text-sm mb-1">Passed Rate</p>
                <p className="text-2xl font-bold text-green-500">
                  {Math.round(
                    (mockProposals.filter((p) => p.status === "passed").length /
                      (mockProposals.filter((p) => p.status === "passed").length +
                        mockProposals.filter((p) => p.status === "failed").length)) *
                      100,
                  )}
                  %
                </p>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10 text-center">
                <p className="text-white/70 text-sm mb-1">Total Votes</p>
                <p className="text-2xl font-bold text-dogechain">
                  {(mockProposals.reduce((acc, p) => acc + p.votesFor + p.votesAgainst, 0) / 1000000).toFixed(1)}M
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div>
        <StakingInterface />
      </div>

      {/* Voting Modal */}
      {selectedProposal && (
        <VotingModal
          id={selectedProposal.id}
          title={selectedProposal.title}
          description={selectedProposal.description}
          status={selectedProposal.status}
          category={selectedProposal.category}
          votesFor={selectedProposal.votesFor}
          votesAgainst={selectedProposal.votesAgainst}
          quorum={selectedProposal.quorum}
          creator={selectedProposal.creator}
          createdAt={selectedProposal.createdAt}
          endTime={selectedProposal.endTime}
          onClose={() => setSelectedProposal(null)}
          onVote={handleVote}
          userVotingPower={5000}
        />
      )}

      {/* Proposal Form Modal */}
      {showProposalForm && <ProposalForm onClose={() => setShowProposalForm(false)} onSubmit={handleCreateProposal} />}
    </div>
  )
}
