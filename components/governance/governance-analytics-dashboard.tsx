"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar, Download, Users } from "lucide-react"
import { Line, LineChart, Bar, BarChart, XAxis, YAxis, CartesianGrid, Legend, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

// Mock data for the charts
const participationData = [
  { month: "Jan", participation: 32, quorum: 50 },
  { month: "Feb", participation: 45, quorum: 50 },
  { month: "Mar", participation: 43, quorum: 50 },
  { month: "Apr", participation: 50, quorum: 50 },
  { month: "May", participation: 65, quorum: 50 },
  { month: "Jun", participation: 59, quorum: 50 },
]

const proposalOutcomeData = [
  { name: "Passed", value: 65 },
  { name: "Failed", value: 25 },
  { name: "Cancelled", value: 10 },
]

const voterDemographicsData = [
  { category: "Whales (>100k)", percentage: 45 },
  { category: "Large (10k-100k)", percentage: 30 },
  { category: "Medium (1k-10k)", percentage: 15 },
  { category: "Small (<1k)", percentage: 10 },
]

const recentProposalsData = [
  {
    id: "PROP-001",
    title: "Add Perpetual Trading",
    status: "passed",
    participation: 85,
    forVotes: 75,
    againstVotes: 25,
  },
  {
    id: "PROP-002",
    title: "Reduce Trading Fees",
    status: "passed",
    participation: 90,
    forVotes: 90,
    againstVotes: 10,
  },
  {
    id: "PROP-003",
    title: "Add New Token Pair",
    status: "failed",
    participation: 60,
    forVotes: 40,
    againstVotes: 60,
  },
  {
    id: "PROP-004",
    title: "Implement Token Burning Mechanism",
    status: "active",
    participation: 45,
    forVotes: 70,
    againstVotes: 30,
  },
]

export function GovernanceAnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState("6m")
  const [categoryFilter, setCategoryFilter] = useState("all")

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Governance Analytics</h2>
          <p className="text-white/70">Insights into governance participation and outcomes</p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="glass-input text-white w-[120px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1m">Last Month</SelectItem>
              <SelectItem value="3m">Last 3 Months</SelectItem>
              <SelectItem value="6m">Last 6 Months</SelectItem>
              <SelectItem value="1y">Last Year</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" className="glass-button">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Total Proposals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">42</div>
                <p className="text-sm text-green-500">+12% from previous period</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <GitPullRequestIcon className="h-6 w-6 text-doge" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Avg. Participation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">58%</div>
                <p className="text-sm text-green-500">+5% from previous period</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <Users className="h-6 w-6 text-dogechain" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Proposal Success Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">72%</div>
                <p className="text-sm text-red-500">-3% from previous period</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <CheckCircleIcon className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Active Voters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">1,245</div>
                <p className="text-sm text-green-500">+18% from previous period</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <VoteIcon className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Participation Rate</CardTitle>
            <CardDescription className="text-white/70">Voter participation vs. quorum requirement</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                participation: {
                  label: "Participation",
                  color: "hsl(var(--chart-1))",
                },
                quorum: {
                  label: "Quorum",
                  color: "hsl(var(--chart-2))",
                },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={participationData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="rgba(255,255,255,0.5)" />
                  <YAxis stroke="rgba(255,255,255,0.5)" />
                  <ChartTooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="participation"
                    stroke="var(--color-participation)"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="quorum"
                    stroke="var(--color-quorum)"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                  />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Proposal Outcomes</CardTitle>
            <CardDescription className="text-white/70">Distribution of proposal results</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                value: {
                  label: "Proposals",
                  color: "hsl(var(--chart-1))",
                },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={proposalOutcomeData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="name" stroke="rgba(255,255,255,0.5)" />
                  <YAxis stroke="rgba(255,255,255,0.5)" />
                  <ChartTooltip />
                  <Bar dataKey="value" fill="var(--color-value)" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card className="glass-card border-white/5 md:col-span-2">
          <CardHeader>
            <CardTitle className="text-white">Recent Proposals</CardTitle>
            <CardDescription className="text-white/70">Performance of recent governance proposals</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentProposalsData.map((proposal) => (
                <div key={proposal.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-white/60">{proposal.id}</span>
                      <span className="text-sm font-medium text-white">{proposal.title}</span>
                      <span
                        className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                          proposal.status === "passed"
                            ? "bg-green-500/10 text-green-500"
                            : proposal.status === "failed"
                              ? "bg-red-500/10 text-red-500"
                              : "bg-doge/10 text-doge"
                        }`}
                      >
                        {proposal.status.charAt(0).toUpperCase() + proposal.status.slice(1)}
                      </span>
                    </div>
                    <span className="text-sm text-white/70">{proposal.participation}% participation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-full bg-white/10 rounded-full h-2 overflow-hidden">
                      <div className="h-full bg-green-500" style={{ width: `${proposal.forVotes}%` }}></div>
                    </div>
                    <span className="text-xs text-white/70 whitespace-nowrap">
                      {proposal.forVotes}% For / {proposal.againstVotes}% Against
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Voter Demographics</CardTitle>
            <CardDescription className="text-white/70">Distribution of voting power</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {voterDemographicsData.map((item) => (
                <div key={item.category} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-white">{item.category}</span>
                    <span className="text-sm font-medium text-white">{item.percentage}%</span>
                  </div>
                  <Progress value={item.percentage} className="h-2 bg-white/10" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Icons
function GitPullRequestIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="18" cy="18" r="3" />
      <circle cx="6" cy="6" r="3" />
      <path d="M13 6h3a2 2 0 0 1 2 2v7" />
      <line x1="6" x2="6" y1="9" y2="21" />
    </svg>
  )
}

function CheckCircleIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
      <polyline points="22 4 12 14.01 9 11.01" />
    </svg>
  )
}

function VoteIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="m9 12 2 2 4-4" />
      <path d="M5 7c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v12H5V7Z" />
      <path d="M22 19H2" />
    </svg>
  )
}
