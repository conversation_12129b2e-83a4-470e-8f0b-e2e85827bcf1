import type React from "react"
import { governanceTokens } from "@/styles/governance-tokens"
import { cn } from "@/lib/utils"

type StatusType =
  | "active"
  | "passed"
  | "failed"
  | "pending"
  | "implemented" // proposal statuses
  | "completed"
  | "in-progress"
  | "planned"
  | "blocked" // task statuses
  | "critical"
  | "high"
  | "medium"
  | "low" // priority levels

interface StatusBadgeProps {
  status: StatusType
  type: "proposal" | "task" | "priority"
  icon?: React.ReactNode
  className?: string
}

// Map status to human-readable descriptions for screen readers
const getStatusDescription = (status: StatusType, type: "proposal" | "task" | "priority"): string => {
  if (type === "proposal") {
    switch (status) {
      case "active":
        return "Active proposal, currently accepting votes"
      case "passed":
        return "Passed proposal, received majority approval"
      case "failed":
        return "Failed proposal, did not receive majority approval"
      case "pending":
        return "Pending proposal, voting has not started"
      case "implemented":
        return "Implemented proposal, changes have been applied"
      default:
        return `Status: ${status.replace(/-/g, " ")}`
    }
  } else if (type === "task") {
    switch (status) {
      case "completed":
        return "Completed task"
      case "in-progress":
        return "Task in progress"
      case "planned":
        return "Planned task, not yet started"
      case "blocked":
        return "Blocked task, facing impediments"
      default:
        return `Status: ${status.replace(/-/g, " ")}`
    }
  } else {
    switch (status) {
      case "critical":
        return "Critical priority"
      case "high":
        return "High priority"
      case "medium":
        return "Medium priority"
      case "low":
        return "Low priority"
      default:
        return `Priority: ${status.replace(/-/g, " ")}`
    }
  }
}

export function StatusBadge({ status, type, icon, className }: StatusBadgeProps) {
  // Determine which token set to use based on type
  let tokenSet: Record<string, { text: string; bg: string; border: string }> = governanceTokens.colors.proposal

  if (type === "task") {
    tokenSet = governanceTokens.colors.task
  } else if (type === "priority") {
    tokenSet = governanceTokens.colors.priority
  }

  // Get the specific token for this status
  const statusToken = tokenSet[status as keyof typeof tokenSet] || {
    text: "text-white/70",
    bg: "bg-white/5",
    border: "border-white/10",
  }

  // Get the status description for screen readers
  const statusDescription = getStatusDescription(status, type)

  return (
    <div
      className={cn(
        "px-2 py-0.5 inline-flex items-center gap-1.5",
        governanceTokens.typography.status,
        governanceTokens.borderRadius.badge,
        "border",
        statusToken.text,
        statusToken.bg,
        statusToken.border,
        className,
      )}
      aria-label={statusDescription}
    >
      {icon && (
        <span className="flex-shrink-0" aria-hidden="true">
          {icon}
        </span>
      )}
      <span className="capitalize">{status.replace(/-/g, " ")}</span>
    </div>
  )
}
