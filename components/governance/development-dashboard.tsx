"use client"

import { useState } from "react"
import { Plus, Filter, Search, Arrow<PERSON>pDown, Info, ThumbsUp, Thum<PERSON>Down, X } from "lucide-react"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { TaskCard } from "./task-card"
import { ProposalCard } from "./proposal-card"
import { StakingInterface } from "./staking-interface"
import { useWallet } from "@/components/wallet-provider"
import { useNotification } from "@/hooks/use-notification"

// Types
type TaskStatus = "completed" | "in-progress" | "planned" | "blocked"
type PriorityLevel = "critical" | "high" | "medium" | "low"
type CategoryType =
  | "core-functionality"
  | "user-experience"
  | "visual-design"
  | "mobile"
  | "performance"
  | "accessibility"
  | "content"
  | "technical"
  | "security"
  | "social"
type ProposalStatus = "active" | "passed" | "failed" | "pending" | "implemented"

// Task interface
interface Task {
  id: string
  title: string
  description: string
  status: TaskStatus
  priority: PriorityLevel
  category: CategoryType
  estimatedHours: number
  assignedTo?: string
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  dependencies?: string[]
  notes?: string
  votes?: {
    for: number
    against: number
  }
  communityPriority?: number // 1-100 score based on community votes
}

// Community proposal interface
interface CommunityProposal {
  id: string
  title: string
  description: string
  proposedBy: string
  createdAt: Date
  status: ProposalStatus
  category: CategoryType
  votesFor: number
  votesAgainst: number
  quorum: number
  implementationTask?: string // Task ID if implemented
}

// Mock tasks with voting data
const mockTasks: Task[] = [
  {
    id: "CF-001",
    title: "Implement Token Launch Confirmation Flow",
    description: "Add a confirmation step before token launch to review parameters and confirm deployment",
    status: "planned",
    priority: "critical",
    category: "core-functionality",
    estimatedHours: 16,
    createdAt: new Date(),
    updatedAt: new Date(),
    votes: {
      for: 1250000,
      against: 50000,
    },
    communityPriority: 92,
  },
  {
    id: "CF-007",
    title: "Integrate Multiple Wallet Providers",
    description: "Add support for multiple wallet providers (MetaMask, WalletConnect, etc.)",
    status: "in-progress",
    priority: "critical",
    category: "core-functionality",
    estimatedHours: 40,
    createdAt: new Date(),
    updatedAt: new Date(),
    votes: {
      for: 1800000,
      against: 100000,
    },
    communityPriority: 95,
  },
  {
    id: "UX-004",
    title: "Design User Onboarding Flow",
    description: "Create guided tour for new users to learn platform features",
    status: "planned",
    priority: "high",
    category: "user-experience",
    estimatedHours: 40,
    createdAt: new Date(),
    updatedAt: new Date(),
    votes: {
      for: 950000,
      against: 350000,
    },
    communityPriority: 73,
  },
  {
    id: "MR-002",
    title: "Improve Chart Touch Interactions",
    description: "Enhance charts for better touch interaction on mobile devices",
    status: "in-progress",
    priority: "high",
    category: "mobile",
    estimatedHours: 32,
    createdAt: new Date(),
    updatedAt: new Date(),
    votes: {
      for: 1100000,
      against: 200000,
    },
    communityPriority: 85,
  },
  {
    id: "PF-001",
    title: "Add Skeleton Loaders",
    description: "Implement skeleton loading states for asynchronous content",
    status: "planned",
    priority: "high",
    category: "performance",
    estimatedHours: 16,
    createdAt: new Date(),
    updatedAt: new Date(),
    votes: {
      for: 800000,
      against: 300000,
    },
    communityPriority: 68,
  },
]

// Mock community proposals
const mockProposals: CommunityProposal[] = [
  {
    id: "PROP-005",
    title: "Add Multi-Chain Support",
    description: "Expand beyond Dogechain to support Ethereum, Binance Smart Chain, and other EVM chains",
    proposedBy: "0x1234...5678",
    createdAt: new Date("2025-05-03"),
    status: "active",
    category: "core-functionality",
    votesFor: 950000,
    votesAgainst: 350000,
    quorum: 2000000,
  },
  {
    id: "PROP-006",
    title: "Implement Dark Mode",
    description: "Add a dark mode option for the entire platform to reduce eye strain and save battery",
    proposedBy: "0x8765...4321",
    createdAt: new Date("2025-05-02"),
    status: "active",
    category: "user-experience",
    votesFor: 1200000,
    votesAgainst: 100000,
    quorum: 2000000,
  },
  {
    id: "PROP-007",
    title: "Create Mobile App",
    description: "Develop native mobile applications for iOS and Android",
    proposedBy: "0x5678...1234",
    createdAt: new Date("2025-05-01"),
    status: "active",
    category: "mobile",
    votesFor: 800000,
    votesAgainst: 600000,
    quorum: 2000000,
  },
  {
    id: "PROP-008",
    title: "Add NFT Support",
    description: "Implement NFT creation, trading, and display functionality",
    proposedBy: "0x4321...8765",
    createdAt: new Date("2025-04-30"),
    status: "passed",
    category: "core-functionality",
    votesFor: 1500000,
    votesAgainst: 300000,
    quorum: 1500000,
    implementationTask: "CF-010", // This would be the task ID created after approval
  },
]

// Helper function to format votes
const formatVotes = (votes: number) => {
  if (votes >= 1000000) {
    return `${(votes / 1000000).toFixed(1)}M`
  } else if (votes >= 1000) {
    return `${(votes / 1000).toFixed(1)}K`
  }
  return votes.toString()
}

// Helper function to calculate progress
const calculateProgress = (votesFor: number, votesAgainst: number) => {
  const total = votesFor + votesAgainst
  if (total === 0) return 0
  return (votesFor / total) * 100
}

export function DevelopmentDashboard() {
  const { isConnected } = useWallet()
  const { showNotification } = useNotification()

  // State
  const [activeTab, setActiveTab] = useState("community-priorities")
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [selectedProposal, setSelectedProposal] = useState<CommunityProposal | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState<string>("all")
  const [priorityFilter, setPriorityFilter] = useState<string>("all")
  const [sortOrder, setSortOrder] = useState<"priority" | "newest" | "oldest" | "most-votes">("priority")

  // Handle task voting
  const handleTaskVote = (vote: "for" | "against") => {
    if (!selectedTask) return

    showNotification({
      title: "Vote Submitted",
      message: `You voted ${vote} prioritizing "${selectedTask.title}"`,
      type: "success",
      addToCenter: true,
    })

    setSelectedTask(null)
  }

  // Handle proposal voting
  const handleProposalVote = (vote: "for" | "against") => {
    if (!selectedProposal) return

    showNotification({
      title: "Vote Submitted",
      message: `You voted ${vote} the proposal "${selectedProposal.title}"`,
      type: "success",
      addToCenter: true,
    })

    setSelectedProposal(null)
  }

  // Filter and sort tasks
  const filteredTasks = mockTasks
    .filter((task) => {
      // Filter by search query
      if (
        searchQuery &&
        !task.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !task.description.toLowerCase().includes(searchQuery.toLowerCase())
      ) {
        return false
      }

      // Filter by category
      if (categoryFilter !== "all" && task.category !== categoryFilter) {
        return false
      }

      // Filter by priority
      if (priorityFilter !== "all" && task.priority !== priorityFilter) {
        return false
      }

      return true
    })
    .sort((a, b) => {
      // Sort by selected order
      switch (sortOrder) {
        case "priority":
          return (b.communityPriority || 0) - (a.communityPriority || 0)
        case "newest":
          return b.createdAt.getTime() - a.createdAt.getTime()
        case "oldest":
          return a.createdAt.getTime() - b.createdAt.getTime()
        case "most-votes":
          return (b.votes?.for || 0) + (b.votes?.against || 0) - ((a.votes?.for || 0) + (a.votes?.against || 0))
        default:
          return 0
      }
    })

  // Filter proposals
  const filteredProposals = mockProposals.filter((proposal) => {
    // Filter by search query
    if (
      searchQuery &&
      !proposal.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !proposal.description.toLowerCase().includes(searchQuery.toLowerCase())
    ) {
      return false
    }

    // Filter by category
    if (categoryFilter !== "all" && proposal.category !== categoryFilter) {
      return false
    }

    return true
  })

  return (
    <div className="space-y-6">
      <Card className="glass-card border-white/5 doge-glow">
        <CardHeader>
          <CardTitle className="text-white">Community-Driven Development</CardTitle>
          <CardDescription className="text-white/70">
            Shape the future of PawPumps by voting on development priorities and proposing new features
          </CardDescription>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 glass mb-6">
              <TabsTrigger
                value="community-priorities"
                className="data-[state=active]:text-doge data-[state=active]:bg-doge/10"
              >
                Community Priorities
              </TabsTrigger>
              <TabsTrigger
                value="feature-proposals"
                className="data-[state=active]:text-dogechain data-[state=active]:bg-dogechain/10"
              >
                Feature Proposals
              </TabsTrigger>
              <TabsTrigger
                value="development-progress"
                className="data-[state=active]:text-green-500 data-[state=active]:bg-green-500/10"
              >
                Development Progress
              </TabsTrigger>
            </TabsList>

            {/* Community Priorities Tab */}
            <TabsContent value="community-priorities" className="space-y-4">
              <div className="flex flex-col sm:flex-row items-center justify-between mb-4 gap-4">
                <div className="relative flex-1 w-full">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
                  <Input
                    placeholder="Search tasks..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="glass-input text-white pl-9 w-full"
                  />
                </div>

                <div className="flex gap-2 w-full sm:w-auto">
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="glass-input text-white w-full sm:w-[140px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="core-functionality">Core Functionality</SelectItem>
                      <SelectItem value="user-experience">User Experience</SelectItem>
                      <SelectItem value="visual-design">Visual Design</SelectItem>
                      <SelectItem value="mobile">Mobile</SelectItem>
                      <SelectItem value="performance">Performance</SelectItem>
                      <SelectItem value="accessibility">Accessibility</SelectItem>
                      <SelectItem value="content">Content</SelectItem>
                      <SelectItem value="technical">Technical</SelectItem>
                      <SelectItem value="security">Security</SelectItem>
                      <SelectItem value="social">Social</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                    <SelectTrigger className="glass-input text-white w-full sm:w-[140px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priorities</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={sortOrder} onValueChange={(value) => setSortOrder(value as any)}>
                    <SelectTrigger className="glass-input text-white w-full sm:w-[140px]">
                      <ArrowUpDown className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="priority">Community Priority</SelectItem>
                      <SelectItem value="newest">Newest First</SelectItem>
                      <SelectItem value="oldest">Oldest First</SelectItem>
                      <SelectItem value="most-votes">Most Votes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {filteredTasks.length > 0 ? (
                filteredTasks.map((task) => (
                  <TaskCard
                    key={task.id}
                    id={task.id}
                    title={task.title}
                    description={task.description}
                    status={task.status}
                    priority={task.priority}
                    category={task.category}
                    estimatedHours={task.estimatedHours}
                    votes={task.votes}
                    communityPriority={task.communityPriority}
                    onClick={() => setSelectedTask(task)}
                  />
                ))
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <Info className="h-12 w-12 text-white/20 mb-4" />
                  <h3 className="text-lg font-medium text-white mb-2">No tasks found</h3>
                  <p className="text-white/70 max-w-md">
                    Try adjusting your search or filters to find what you're looking for.
                  </p>
                </div>
              )}
            </TabsContent>

            {/* Feature Proposals Tab */}
            <TabsContent value="feature-proposals" className="space-y-4">
              <div className="flex flex-col sm:flex-row items-center justify-between mb-4 gap-4">
                <div className="relative flex-1 w-full">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
                  <Input
                    placeholder="Search proposals..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="glass-input text-white pl-9 w-full"
                  />
                </div>

                <div className="flex gap-2 w-full sm:w-auto">
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="glass-input text-white w-full sm:w-[140px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="core-functionality">Core Functionality</SelectItem>
                      <SelectItem value="user-experience">User Experience</SelectItem>
                      <SelectItem value="visual-design">Visual Design</SelectItem>
                      <SelectItem value="mobile">Mobile</SelectItem>
                      <SelectItem value="performance">Performance</SelectItem>
                      <SelectItem value="accessibility">Accessibility</SelectItem>
                      <SelectItem value="content">Content</SelectItem>
                      <SelectItem value="technical">Technical</SelectItem>
                      <SelectItem value="security">Security</SelectItem>
                      <SelectItem value="social">Social</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button className="doge-button doge-shine">
                    <Plus className="mr-2 h-4 w-4" />
                    <span>Create Proposal</span>
                  </Button>
                </div>
              </div>

              {filteredProposals.length > 0 ? (
                filteredProposals.map((proposal) => (
                  <ProposalCard
                    key={proposal.id}
                    id={proposal.id}
                    title={proposal.title}
                    description={proposal.description}
                    status={proposal.status}
                    category={proposal.category}
                    votesFor={proposal.votesFor}
                    votesAgainst={proposal.votesAgainst}
                    quorum={proposal.quorum}
                    creator={proposal.proposedBy}
                    createdAt={proposal.createdAt.toISOString().split("T")[0]}
                    endTime={
                      new Date(proposal.createdAt.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]
                    }
                    onClick={() => setSelectedProposal(proposal)}
                  />
                ))
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <Info className="h-12 w-12 text-white/20 mb-4" />
                  <h3 className="text-lg font-medium text-white mb-2">No proposals found</h3>
                  <p className="text-white/70 max-w-md">
                    Try adjusting your search or filters to find what you're looking for.
                  </p>
                </div>
              )}
            </TabsContent>

            {/* Development Progress Tab */}
            <TabsContent value="development-progress" className="space-y-4">
              <Card className="glass-card border-white/5">
                <CardHeader className="pb-2">
                  <CardTitle className="text-white text-lg">Development Milestones</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/90">Core Infrastructure</span>
                        <span className="text-white/70">20% complete</span>
                      </div>
                      <Progress value={20} className="h-2 bg-white/10" indicatorClassName="bg-green-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/90">Token Launch Experience</span>
                        <span className="text-white/70">10% complete</span>
                      </div>
                      <Progress value={10} className="h-2 bg-white/10" indicatorClassName="bg-green-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/90">Trading Enhancements</span>
                        <span className="text-white/70">0% complete</span>
                      </div>
                      <Progress value={0} className="h-2 bg-white/10" indicatorClassName="bg-green-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/90">Mobile & Accessibility</span>
                        <span className="text-white/70">15% complete</span>
                      </div>
                      <Progress value={15} className="h-2 bg-white/10" indicatorClassName="bg-green-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/90">Performance & Technical Debt</span>
                        <span className="text-white/70">5% complete</span>
                      </div>
                      <Progress value={5} className="h-2 bg-white/10" indicatorClassName="bg-green-500" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/5">
                <CardHeader className="pb-2">
                  <CardTitle className="text-white text-lg">Recently Completed Tasks</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3 p-3 rounded-md bg-white/5">
                      <div className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="lucide lucide-check-circle-2"
                        >
                          <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                          <path d="m9 12 2 2 4-4" />
                        </svg>
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-xs text-white/60">VD-001</span>
                          <span className="text-white font-medium">Standardize Card Styling</span>
                        </div>
                        <p className="text-sm text-white/70 mt-1">
                          Created consistent card styling patterns across the platform
                        </p>
                        <p className="text-xs text-white/50 mt-1">Completed 2 days ago</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3 p-3 rounded-md bg-white/5">
                      <div className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="lucide lucide-check-circle-2"
                        >
                          <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                          <path d="m9 12 2 2 4-4" />
                        </svg>
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-xs text-white/60">UX-006</span>
                          <span className="text-white font-medium">Add Contextual Tooltips</span>
                        </div>
                        <p className="text-sm text-white/70 mt-1">Implemented tooltips for complex UI elements</p>
                        <p className="text-xs text-white/50 mt-1">Completed 5 days ago</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3 p-3 rounded-md bg-white/5">
                      <div className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="lucide lucide-check-circle-2"
                        >
                          <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                          <path d="m9 12 2 2 4-4" />
                        </svg>
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-xs text-white/60">AC-005</span>
                          <span className="text-white font-medium">Add Alternative Text</span>
                        </div>
                        <p className="text-sm text-white/70 mt-1">Ensured all images have proper alternative text</p>
                        <p className="text-xs text-white/50 mt-1">Completed 1 week ago</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full glass-button">
                    View All Tasks
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="glass-card border-white/5 doge-glow">
          <CardHeader>
            <CardTitle className="text-white">Development Participation Rewards</CardTitle>
            <CardDescription className="text-white/70">
              Earn rewards for participating in the development process
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <h3 className="text-white font-medium mb-2">Voting Rewards</h3>
              <p className="text-sm text-white/70 mb-3">
                Earn $PAW tokens for voting on development priorities and feature proposals
              </p>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Reward per vote:</span>
                <span className="text-doge font-medium">5 $PAW</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Your votes this week:</span>
                <span className="text-white">12</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Rewards earned:</span>
                <span className="text-doge font-medium">60 $PAW</span>
              </div>
            </div>

            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <h3 className="text-white font-medium mb-2">Proposal Rewards</h3>
              <p className="text-sm text-white/70 mb-3">
                Earn $PAW tokens when your feature proposals are approved and implemented
              </p>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Reward per approved proposal:</span>
                <span className="text-doge font-medium">1,000 $PAW</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Your approved proposals:</span>
                <span className="text-white">1</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Rewards earned:</span>
                <span className="text-doge font-medium">1,000 $PAW</span>
              </div>
            </div>

            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <h3 className="text-white font-medium mb-2">Bug Bounty Rewards</h3>
              <p className="text-sm text-white/70 mb-3">
                Earn $PAW tokens for reporting bugs and suggesting improvements
              </p>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Reward per verified bug:</span>
                <span className="text-doge font-medium">50-5,000 $PAW</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Your verified reports:</span>
                <span className="text-white">2</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Rewards earned:</span>
                <span className="text-doge font-medium">350 $PAW</span>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full doge-button doge-shine">Claim Rewards</Button>
          </CardFooter>
        </Card>

        <StakingInterface />
      </div>

      {/* Task Voting Modal */}
      {selectedTask && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto">
          <Card className="glass-card border-white/5 w-full max-w-2xl max-h-[80vh] overflow-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium text-white/60">{selectedTask.id}</span>
                    <div className="px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1 border border-white/10 bg-white/5">
                      <span className="capitalize">{selectedTask.status.replace("-", " ")}</span>
                    </div>
                  </div>
                  <CardTitle className="text-white">{selectedTask.title}</CardTitle>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white/70 hover:text-white"
                  onClick={() => setSelectedTask(null)}
                >
                  <span className="sr-only">Close</span>
                  <X className="h-6 w-6" />
                </Button>
              </div>
              <CardDescription className="text-white/70">
                Community Priority: {selectedTask.communityPriority}%
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <p className="text-white/90">{selectedTask.description}</p>
                {selectedProposal && (
                  <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <h4 className="text-sm font-medium text-white mb-2">Voting Status</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-white/60">For: {formatVotes(selectedProposal.votesFor)}</span>
                        <span className="text-white/60">Against: {formatVotes(selectedProposal.votesAgainst)}</span>
                      </div>
                      <Progress
                        value={calculateProgress(selectedProposal.votesFor, selectedProposal.votesAgainst)}
                        className="h-2 bg-white/10"
                        indicatorClassName="bg-dogechain"
                      />
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-white/60">
                          Quorum: {formatVotes(selectedProposal.votesFor + selectedProposal.votesAgainst)}/
                          {formatVotes(selectedProposal.quorum)}
                        </span>
                        <span className="text-white/60">
                          Category:{" "}
                          {selectedProposal.category
                            .split("-")
                            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                            .join(" ")}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
                {selectedProposal?.implementationTask && (
                  <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/20">
                    <h4 className="text-sm font-medium text-green-500 mb-2">Implementation Status</h4>
                    <p className="text-sm text-white/90">
                      This proposal has been approved and added to the development roadmap as task{" "}
                      <span className="font-mono">{selectedProposal.implementationTask}</span>.
                    </p>
                    <a href="/development-tracker" className="text-sm text-green-500 hover:underline mt-2 inline-block">
                      View implementation status →
                    </a>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex gap-4">
              {selectedProposal?.status === "active" && (
                <>
                  <Button className="flex-1 doge-button doge-shine" onClick={() => handleProposalVote("for")}>
                    <ThumbsUp className="mr-2 h-4 w-4" />
                    Vote For
                  </Button>
                  <Button className="flex-1 glass-button" onClick={() => handleProposalVote("against")}>
                    <ThumbsDown className="mr-2 h-4 w-4" />
                    Vote Against
                  </Button>
                </>
              )}
              {selectedProposal?.status === "passed" && !selectedProposal?.implementationTask && (
                <Button className="w-full glass-button" disabled>
                  Awaiting Implementation
                </Button>
              )}
              {(selectedProposal?.status === "failed" ||
                selectedProposal?.status === "implemented" ||
                (selectedProposal?.status === "passed" && selectedProposal?.implementationTask)) && (
                <Button className="w-full glass-button" disabled>
                  Voting Ended
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      )}
    </div>
  )
}
