"use client"

import { useState, useEffect } from "react"
import { X, ChevronRight, ChevronLeft, Info } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface OnboardingStep {
  title: string
  description: string
  target: string // CSS selector for the element to highlight
  position: "top" | "right" | "bottom" | "left" | "center"
}

interface OnboardingTourProps {
  steps: OnboardingStep[]
  onComplete: () => void
  onSkip: () => void
  isOpen: boolean
}

export function OnboardingTour({ steps, onComplete, onSkip, isOpen }: OnboardingTourProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [targetRect, setTargetRect] = useState<DOMRect | null>(null)

  // Update target element position when step changes
  useEffect(() => {
    if (!isOpen) return

    const updateTargetRect = () => {
      const targetElement = document.querySelector(steps[currentStep].target)
      if (targetElement) {
        setTargetRect(targetElement.getBoundingClientRect())
      } else {
        setTargetRect(null)
      }
    }

    updateTargetRect()

    // Update on resize and scroll
    window.addEventListener("resize", updateTargetRect)
    window.addEventListener("scroll", updateTargetRect)

    return () => {
      window.removeEventListener("resize", updateTargetRect)
      window.removeEventListener("scroll", updateTargetRect)
    }
  }, [currentStep, steps, isOpen])

  if (!isOpen) return null

  const currentStepData = steps[currentStep]

  // Calculate tooltip position
  const getTooltipPosition = () => {
    if (!targetRect) {
      return {
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
      }
    }

    const margin = 12 // Margin from target element

    switch (currentStepData.position) {
      case "top":
        return {
          bottom: `${window.innerHeight - targetRect.top + margin}px`,
          left: `${targetRect.left + targetRect.width / 2}px`,
          transform: "translateX(-50%)",
        }
      case "right":
        return {
          top: `${targetRect.top + targetRect.height / 2}px`,
          left: `${targetRect.right + margin}px`,
          transform: "translateY(-50%)",
        }
      case "bottom":
        return {
          top: `${targetRect.bottom + margin}px`,
          left: `${targetRect.left + targetRect.width / 2}px`,
          transform: "translateX(-50%)",
        }
      case "left":
        return {
          top: `${targetRect.top + targetRect.height / 2}px`,
          right: `${window.innerWidth - targetRect.left + margin}px`,
          transform: "translateY(-50%)",
        }
      case "center":
      default:
        return {
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
        }
    }
  }

  // Handle next step
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      onComplete()
    }
  }

  // Handle previous step
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/70 z-50" onClick={onSkip} />

      {/* Highlight target element */}
      {targetRect && (
        <div
          className="fixed z-50 border-2 border-doge rounded-lg pointer-events-none"
          style={{
            top: targetRect.top - 4 + window.scrollY,
            left: targetRect.left - 4,
            width: targetRect.width + 8,
            height: targetRect.height + 8,
            boxShadow: "0 0 0 9999px rgba(0, 0, 0, 0.7)",
          }}
        />
      )}

      {/* Tooltip */}
      <Card className="fixed z-50 glass-card border-white/5 max-w-md w-full" style={getTooltipPosition()}>
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-doge" />
              <h3 className="text-lg font-medium text-white">{currentStepData.title}</h3>
            </div>
            <Button variant="ghost" size="icon" className="text-white/70 hover:text-white -mt-1 -mr-1" onClick={onSkip}>
              <X className="h-4 w-4" />
              <span className="sr-only">Skip tour</span>
            </Button>
          </div>

          <p className="text-white/80 mb-4">{currentStepData.description}</p>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={cn("h-1.5 rounded-full", index === currentStep ? "w-4 bg-doge" : "w-1.5 bg-white/20")}
                />
              ))}
            </div>

            <div className="flex items-center gap-2">
              {currentStep > 0 && (
                <Button variant="outline" size="sm" className="glass-button" onClick={handlePrevious}>
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Back
                </Button>
              )}

              <Button size="sm" className="doge-button doge-shine" onClick={handleNext}>
                {currentStep < steps.length - 1 ? (
                  <>
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </>
                ) : (
                  "Finish"
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}
