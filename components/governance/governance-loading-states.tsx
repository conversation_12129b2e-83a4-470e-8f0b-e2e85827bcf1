"use client"

import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { LoadingState } from "@/components/ui/loading-state"

// Governance Dashboard Loading
export function GovernanceDashboardLoading() {
  return (
    <div className="container py-8 md:py-12">
      {/* Header */}
      <div className="mb-8">
        <Skeleton className="h-10 w-80 mb-2 bg-white/10" />
        <Skeleton className="h-6 w-96 bg-white/10" />
      </div>

      {/* Navigation Tabs */}
      <div className="mb-8">
        <div className="flex space-x-2 mb-4">
          <Skeleton className="h-10 w-24 bg-white/10" />
          <Skeleton className="h-10 w-24 bg-white/10" />
          <Skeleton className="h-10 w-24 bg-white/10" />
          <Skeleton className="h-10 w-24 bg-white/10" />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-4 mb-8">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="glass-card border-white/5">
            <CardContent className="p-6">
              <Skeleton className="h-6 w-32 mb-2 bg-white/10" />
              <Skeleton className="h-8 w-16 mb-1 bg-white/10" />
              <Skeleton className="h-4 w-24 bg-white/10" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Area */}
      <div className="grid gap-6 lg:grid-cols-2">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <Skeleton className="h-6 w-48 bg-white/10" />
            <Skeleton className="h-4 w-64 bg-white/10" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-full bg-white/10" />
                  <Skeleton className="h-4 w-3/4 bg-white/10" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <Skeleton className="h-6 w-48 bg-white/10" />
            <Skeleton className="h-4 w-64 bg-white/10" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-full bg-white/10" />
                  <Skeleton className="h-4 w-2/3 bg-white/10" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Proposal List Loading
export function ProposalListLoading() {
  return (
    <div className="space-y-6">
      {Array.from({ length: 5 }).map((_, i) => (
        <Card key={i} className="glass-card border-white/5">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton className="h-6 w-64 bg-white/10" />
                <Skeleton className="h-4 w-32 bg-white/10" />
              </div>
              <Skeleton className="h-8 w-20 bg-white/10" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Skeleton className="h-4 w-full bg-white/10" />
              <Skeleton className="h-4 w-3/4 bg-white/10" />
              <div className="flex items-center justify-between mt-4">
                <Skeleton className="h-6 w-24 bg-white/10" />
                <Skeleton className="h-6 w-24 bg-white/10" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

// Staking Interface Loading
export function StakingInterfaceLoading() {
  return (
    <div className="space-y-6">
      {/* Staking Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="glass-card border-white/5">
            <CardContent className="p-6">
              <Skeleton className="h-6 w-32 mb-2 bg-white/10" />
              <Skeleton className="h-8 w-20 mb-1 bg-white/10" />
              <Skeleton className="h-4 w-16 bg-white/10" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Staking Form */}
      <Card className="glass-card border-white/5">
        <CardHeader>
          <Skeleton className="h-6 w-48 bg-white/10" />
          <Skeleton className="h-4 w-64 bg-white/10" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-24 bg-white/10" />
            <Skeleton className="h-10 w-full bg-white/10" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-32 bg-white/10" />
            <Skeleton className="h-10 w-full bg-white/10" />
          </div>
          <Skeleton className="h-10 w-full bg-white/10" />
        </CardContent>
      </Card>
    </div>
  )
}

// Admin Dashboard Loading
export function AdminDashboardLoading() {
  return (
    <div className="container py-8 md:py-12">
      {/* Header */}
      <div className="mb-8">
        <Skeleton className="h-10 w-80 mb-2 bg-white/10" />
        <Skeleton className="h-6 w-96 bg-white/10" />
      </div>

      {/* Admin Navigation */}
      <div className="mb-8">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i} className="glass-card border-white/5">
              <CardContent className="p-6">
                <Skeleton className="h-8 w-8 mb-3 bg-white/10" />
                <Skeleton className="h-5 w-32 mb-2 bg-white/10" />
                <Skeleton className="h-4 w-24 bg-white/10" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

// Generic Loading with Message
export function GovernanceLoadingWithMessage({
  title = "Loading Governance Data...",
  description = "Please wait while we fetch the latest information."
}: {
  title?: string
  description?: string
}) {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <LoadingState
        type="data"
        title={title}
        description={description}
        size="lg"
      />
    </div>
  )
}

// Table Loading
export function TableLoading({ rows = 5, columns = 4 }: { rows?: number; columns?: number }) {
  return (
    <div className="space-y-3">
      {/* Table Header */}
      <div className="flex space-x-4">
        {Array.from({ length: columns }).map((_, i) => (
          <Skeleton key={i} className="h-6 w-24 bg-white/10" />
        ))}
      </div>
      
      {/* Table Rows */}
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, j) => (
            <Skeleton key={j} className="h-8 w-24 bg-white/10" />
          ))}
        </div>
      ))}
    </div>
  )
}

// Chart Loading
export function ChartLoading() {
  return (
    <div className="h-[300px] w-full flex items-center justify-center bg-black/20 rounded-lg border border-white/5">
      <LoadingState
        type="chart"
        title="Loading Chart..."
        description="Preparing analytics data"
        size="md"
      />
    </div>
  )
}
