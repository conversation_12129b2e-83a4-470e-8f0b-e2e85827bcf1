"use client"

import { useState, useEffect } from "react"
import { CheckCircle2, AlertCircle, Info, X, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

// Toast notification system
interface ToastProps {
  id: string
  type: "success" | "error" | "info" | "warning"
  title: string
  description?: string
  duration?: number
  onClose: (id: string) => void
}

export function Toast({ id, type, title, description, duration = 5000, onClose }: ToastProps) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose(id)
    }, duration)
    return () => clearTimeout(timer)
  }, [id, duration, onClose])

  const icons = {
    success: CheckCircle2,
    error: AlertCircle,
    info: Info,
    warning: AlertCircle,
  }

  const colors = {
    success: "bg-green-500/20 border-green-500/30 text-green-400",
    error: "bg-red-500/20 border-red-500/30 text-red-400",
    info: "bg-blue-500/20 border-blue-500/30 text-blue-400",
    warning: "bg-yellow-500/20 border-yellow-500/30 text-yellow-400",
  }

  const Icon = icons[type]

  return (
    <Card className={cn("glass-card border", colors[type])}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <Icon className="h-5 w-5 mt-0.5 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-white">{title}</h4>
            {description && (
              <p className="text-sm text-white/70 mt-1">{description}</p>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-white/50 hover:text-white"
            onClick={() => onClose(id)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Toast container
export function ToastContainer({ toasts, onClose }: { 
  toasts: ToastProps[]
  onClose: (id: string) => void 
}) {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {toasts.map((toast) => (
        <Toast key={toast.id} {...toast} onClose={onClose} />
      ))}
    </div>
  )
}

// Enhanced button with feedback states
interface FeedbackButtonProps {
  children: React.ReactNode
  onClick?: () => void | Promise<void>
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
  disabled?: boolean
  successMessage?: string
  errorMessage?: string
  loadingMessage?: string
}

export function FeedbackButton({
  children,
  onClick,
  variant = "default",
  size = "default",
  className,
  disabled,
  successMessage = "Action completed successfully",
  errorMessage = "An error occurred",
  loadingMessage = "Processing...",
}: FeedbackButtonProps) {
  const [state, setState] = useState<"idle" | "loading" | "success" | "error">("idle")

  const handleClick = async () => {
    if (!onClick || disabled || state === "loading") return

    setState("loading")
    try {
      await onClick()
      setState("success")
      setTimeout(() => setState("idle"), 2000)
    } catch (error) {
      setState("error")
      setTimeout(() => setState("idle"), 3000)
    }
  }

  const getButtonContent = () => {
    switch (state) {
      case "loading":
        return (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            {loadingMessage}
          </div>
        )
      case "success":
        return (
          <div className="flex items-center gap-2">
            <CheckCircle2 className="h-4 w-4" />
            {successMessage}
          </div>
        )
      case "error":
        return (
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            {errorMessage}
          </div>
        )
      default:
        return children
    }
  }

  const getButtonVariant = () => {
    if (state === "success") return "default"
    if (state === "error") return "destructive"
    return variant
  }

  return (
    <Button
      variant={getButtonVariant()}
      size={size}
      className={cn(
        className,
        state === "success" && "bg-green-600 hover:bg-green-700",
        state === "loading" && "cursor-not-allowed"
      )}
      onClick={handleClick}
      disabled={disabled || state === "loading"}
    >
      {getButtonContent()}
    </Button>
  )
}

// Progress indicator for multi-step processes
interface ProgressIndicatorProps {
  steps: string[]
  currentStep: number
  completedSteps?: number[]
  className?: string
}

export function ProgressIndicator({ 
  steps, 
  currentStep, 
  completedSteps = [], 
  className 
}: ProgressIndicatorProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={index} className="flex items-center">
            <div
              className={cn(
                "flex h-8 w-8 items-center justify-center rounded-full border-2 text-sm font-medium",
                completedSteps.includes(index)
                  ? "bg-green-600 border-green-600 text-white"
                  : index === currentStep
                  ? "bg-doge border-doge text-black"
                  : "border-white/20 text-white/50"
              )}
            >
              {completedSteps.includes(index) ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                index + 1
              )}
            </div>
            {index < steps.length - 1 && (
              <div
                className={cn(
                  "h-0.5 w-16 mx-2",
                  completedSteps.includes(index) ? "bg-green-600" : "bg-white/20"
                )}
              />
            )}
          </div>
        ))}
      </div>
      <div className="text-center">
        <p className="text-sm text-white/70">
          Step {currentStep + 1} of {steps.length}: {steps[currentStep]}
        </p>
      </div>
    </div>
  )
}

// Confirmation dialog with enhanced feedback
interface ConfirmationDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void | Promise<void>
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  type?: "default" | "destructive"
}

export function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText = "Confirm",
  cancelText = "Cancel",
  type = "default"
}: ConfirmationDialogProps) {
  const [isConfirming, setIsConfirming] = useState(false)

  if (!isOpen) return null

  const handleConfirm = async () => {
    setIsConfirming(true)
    try {
      await onConfirm()
      onClose()
    } catch (error) {
      // Error handling can be done by parent component
    } finally {
      setIsConfirming(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="glass-card border-white/5 max-w-md w-full">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-white mb-2">{title}</h3>
          <p className="text-white/70 mb-6">{description}</p>
          
          <div className="flex gap-3 justify-end">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isConfirming}
              className="border-white/10 bg-white/5 text-white hover:bg-white/10"
            >
              {cancelText}
            </Button>
            <FeedbackButton
              variant={type === "destructive" ? "destructive" : "default"}
              onClick={handleConfirm}
              disabled={isConfirming}
              loadingMessage="Processing..."
              successMessage="Confirmed"
              errorMessage="Failed"
            >
              {confirmText}
            </FeedbackButton>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
