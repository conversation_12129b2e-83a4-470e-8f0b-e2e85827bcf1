"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useNotification } from "@/hooks/use-notification"
import { Code, Coins, FileText, Globe, Lock, Search, Shield, Users } from "lucide-react"

// Types
export type WorkingGroupType =
  | "development"
  | "marketing"
  | "treasury"
  | "community"
  | "security"
  | "governance"
  | "documentation"

interface WorkingGroup {
  id: string
  name: string
  type: WorkingGroupType
  description: string
  members: number
  tasks: number
  completedTasks: number
  budget: number
  icon: React.ReactNode
  color: string
}

interface Member {
  address: string
  name: string
  avatar?: string
  role: string
  joinedDate: string
  contributions: number
}

interface Task {
  id: string
  title: string
  description: string
  status: "open" | "in-progress" | "completed" | "blocked"
  assignee?: string
  dueDate?: string
  priority: "low" | "medium" | "high"
}

interface WorkingGroupsProps {
  userGroups?: string[]
  onJoinGroup?: (groupId: string) => Promise<void>
  onLeaveGroup?: (groupId: string) => Promise<void>
}

export function WorkingGroups({ userGroups = [], onJoinGroup, onLeaveGroup }: WorkingGroupsProps) {
  const { showNotification } = useNotification()

  // State
  const [activeTab, setActiveTab] = useState<"all" | "my-groups">("all")
  const [selectedGroup, setSelectedGroup] = useState<WorkingGroup | null>(null)
  const [groupDetailTab, setGroupDetailTab] = useState<"overview" | "members" | "tasks">("overview")
  const [searchQuery, setSearchQuery] = useState("")
  const [isJoining, setIsJoining] = useState(false)

  // Mock working groups data
  const workingGroups: WorkingGroup[] = [
    {
      id: "dev-group",
      name: "Development",
      type: "development",
      description: "Responsible for smart contract development, frontend implementation, and technical infrastructure.",
      members: 12,
      tasks: 24,
      completedTasks: 18,
      budget: 50000,
      icon: <Code className="h-5 w-5" />,
      color: "bg-blue-500/20 text-blue-500",
    },
    {
      id: "marketing-group",
      name: "Marketing",
      type: "marketing",
      description: "Handles platform promotion, content creation, and community engagement strategies.",
      members: 8,
      tasks: 16,
      completedTasks: 10,
      budget: 30000,
      icon: <Globe className="h-5 w-5" />,
      color: "bg-purple-500/20 text-purple-500",
    },
    {
      id: "treasury-group",
      name: "Treasury",
      type: "treasury",
      description: "Manages DAO funds, investment strategies, and financial reporting.",
      members: 5,
      tasks: 12,
      completedTasks: 9,
      budget: 20000,
      icon: <Coins className="h-5 w-5" />,
      color: "bg-yellow-500/20 text-yellow-500",
    },
    {
      id: "community-group",
      name: "Community",
      type: "community",
      description: "Focuses on community building, moderation, and user support.",
      members: 10,
      tasks: 20,
      completedTasks: 15,
      budget: 25000,
      icon: <Users className="h-5 w-5" />,
      color: "bg-green-500/20 text-green-500",
    },
    {
      id: "security-group",
      name: "Security",
      type: "security",
      description: "Responsible for platform security, audits, and incident response.",
      members: 6,
      tasks: 14,
      completedTasks: 11,
      budget: 35000,
      icon: <Shield className="h-5 w-5" />,
      color: "bg-red-500/20 text-red-500",
    },
    {
      id: "governance-group",
      name: "Governance",
      type: "governance",
      description: "Oversees governance processes, proposal reviews, and voting mechanisms.",
      members: 7,
      tasks: 15,
      completedTasks: 12,
      budget: 15000,
      icon: <Lock className="h-5 w-5" />,
      color: "bg-doge/20 text-doge",
    },
    {
      id: "docs-group",
      name: "Documentation",
      type: "documentation",
      description: "Creates and maintains platform documentation, guides, and educational content.",
      members: 4,
      tasks: 10,
      completedTasks: 7,
      budget: 10000,
      icon: <FileText className="h-5 w-5" />,
      color: "bg-white/20 text-white",
    },
  ]

  // Mock members data
  const groupMembers: Record<string, Member[]> = {
    "dev-group": [
      {
        address: "0x1234...5678",
        name: "BlockchainDev",
        avatar: "/avatar-developer.png",
        role: "Lead Developer",
        joinedDate: "Jan 15, 2025",
        contributions: 32,
      },
      {
        address: "0x8765...4321",
        name: "CryptoWizard",
        avatar: "/avatar-wizard.png",
        role: "Smart Contract Engineer",
        joinedDate: "Feb 3, 2025",
        contributions: 28,
      },
      {
        address: "0x5678...1234",
        name: "DogeWhale",
        avatar: "/avatar-doge.png",
        role: "Frontend Developer",
        joinedDate: "Mar 10, 2025",
        contributions: 24,
      },
    ],
    "marketing-group": [
      {
        address: "0x4321...8765",
        name: "MemeEnthusiast",
        avatar: "/avatar-enthusiast.png",
        role: "Marketing Lead",
        joinedDate: "Feb 5, 2025",
        contributions: 18,
      },
    ],
  }

  // Mock tasks data
  const groupTasks: Record<string, Task[]> = {
    "dev-group": [
      {
        id: "task-1",
        title: "Implement Delegation Contract",
        description: "Create a smart contract for delegation functionality with security features.",
        status: "completed",
        assignee: "BlockchainDev",
        dueDate: "Apr 15, 2025",
        priority: "high",
      },
      {
        id: "task-2",
        title: "Develop Frontend for Delegation",
        description: "Build UI components for delegation management and visualization.",
        status: "in-progress",
        assignee: "DogeWhale",
        dueDate: "Apr 20, 2025",
        priority: "medium",
      },
      {
        id: "task-3",
        title: "Write Unit Tests for Delegation",
        description: "Create comprehensive test suite for delegation functionality.",
        status: "open",
        dueDate: "Apr 25, 2025",
        priority: "medium",
      },
    ],
    "marketing-group": [
      {
        id: "task-4",
        title: "Create Delegation Announcement",
        description: "Prepare announcement materials for the new delegation feature.",
        status: "in-progress",
        assignee: "MemeEnthusiast",
        dueDate: "Apr 22, 2025",
        priority: "high",
      },
    ],
  }

  // Filter groups based on active tab and search query
  const filteredGroups = workingGroups.filter((group) => {
    const matchesSearch =
      group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      group.description.toLowerCase().includes(searchQuery.toLowerCase())

    if (activeTab === "my-groups") {
      return userGroups.includes(group.id) && matchesSearch
    }

    return matchesSearch
  })

  // Handle joining a group
  const handleJoinGroup = async (groupId: string) => {
    if (!onJoinGroup) return

    setIsJoining(true)

    try {
      await onJoinGroup(groupId)
      showNotification({
        title: "Joined Group",
        message: `You have successfully joined the ${workingGroups.find((g) => g.id === groupId)?.name} working group`,
        type: "success",
        addToCenter: true,
      })
    } catch (error) {
      showNotification({
        title: "Failed to Join",
        message: error instanceof Error ? error.message : "Failed to join working group",
        type: "error",
        addToCenter: true,
      })
    } finally {
      setIsJoining(false)
    }
  }

  // Handle leaving a group
  const handleLeaveGroup = async (groupId: string) => {
    if (!onLeaveGroup) return

    try {
      await onLeaveGroup(groupId)
      showNotification({
        title: "Left Group",
        message: `You have successfully left the ${workingGroups.find((g) => g.id === groupId)?.name} working group`,
        type: "success",
        addToCenter: true,
      })
    } catch (error) {
      showNotification({
        title: "Failed to Leave",
        message: error instanceof Error ? error.message : "Failed to leave working group",
        type: "error",
        addToCenter: true,
      })
    }
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <Card className="glass-card border-white/5">
      <CardHeader>
        <CardTitle className="text-white">Working Groups</CardTitle>
        <CardDescription className="text-white/70">
          Specialized committees focused on specific areas of the platform
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as "all" | "my-groups")}
            className="w-full sm:w-auto"
          >
            <TabsList className="glass w-full">
              <TabsTrigger value="all">All Groups</TabsTrigger>
              <TabsTrigger value="my-groups">My Groups</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="relative w-full sm:w-64">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
            <Input
              placeholder="Search groups..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="glass-input text-white pl-9 w-full"
            />
          </div>
        </div>

        {selectedGroup ? (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <Button variant="outline" className="border-white/10 bg-white/5" onClick={() => setSelectedGroup(null)}>
                Back to Groups
              </Button>
              {userGroups.includes(selectedGroup.id) ? (
                <Button
                  variant="outline"
                  className="border-red-500/20 text-red-500 hover:bg-red-500/10"
                  onClick={() => handleLeaveGroup(selectedGroup.id)}
                >
                  Leave Group
                </Button>
              ) : (
                <Button
                  className="doge-button doge-shine"
                  onClick={() => handleJoinGroup(selectedGroup.id)}
                  disabled={isJoining}
                >
                  {isJoining ? "Joining..." : "Join Group"}
                </Button>
              )}
            </div>

            <div className="flex items-center gap-3">
              <div className={`p-3 rounded-full ${selectedGroup.color}`}>{selectedGroup.icon}</div>
              <div>
                <h3 className="text-xl font-medium text-white">{selectedGroup.name}</h3>
                <p className="text-white/70">{selectedGroup.description}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-4 rounded-md bg-white/5 border border-white/10 text-center">
                <p className="text-white/70 text-sm mb-1">Members</p>
                <p className="text-xl font-medium text-white">{selectedGroup.members}</p>
              </div>
              <div className="p-4 rounded-md bg-white/5 border border-white/10 text-center">
                <p className="text-white/70 text-sm mb-1">Tasks</p>
                <p className="text-xl font-medium text-white">
                  {selectedGroup.completedTasks}/{selectedGroup.tasks}
                </p>
              </div>
              <div className="p-4 rounded-md bg-white/5 border border-white/10 text-center">
                <p className="text-white/70 text-sm mb-1">Completion</p>
                <p className="text-xl font-medium text-white">
                  {Math.round((selectedGroup.completedTasks / selectedGroup.tasks) * 100)}%
                </p>
              </div>
              <div className="p-4 rounded-md bg-white/5 border border-white/10 text-center">
                <p className="text-white/70 text-sm mb-1">Budget</p>
                <p className="text-xl font-medium text-white">{formatCurrency(selectedGroup.budget)}</p>
              </div>
            </div>

            <Tabs
              value={groupDetailTab}
              onValueChange={(value) => setGroupDetailTab(value as "overview" | "members" | "tasks")}
            >
              <TabsList className="glass w-full">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="members">Members</TabsTrigger>
                <TabsTrigger value="tasks">Tasks</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4 mt-4">
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-white">Progress</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Task Completion</span>
                      <span className="text-white">
                        {selectedGroup.completedTasks}/{selectedGroup.tasks}
                      </span>
                    </div>
                    <Progress
                      value={(selectedGroup.completedTasks / selectedGroup.tasks) * 100}
                      className="h-2 bg-white/10"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Budget Utilization</span>
                      <span className="text-white">65%</span>
                    </div>
                    <Progress value={65} className="h-2 bg-white/10" />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-white">Recent Activity</h4>
                  <div className="space-y-3">
                    <div className="p-3 rounded-md bg-white/5 border border-white/10">
                      <p className="text-white font-medium">New task added: Security Audit</p>
                      <p className="text-sm text-white/70">Added by BlockchainDev - 2 days ago</p>
                    </div>
                    <div className="p-3 rounded-md bg-white/5 border border-white/10">
                      <p className="text-white font-medium">Task completed: Implement Delegation Contract</p>
                      <p className="text-sm text-white/70">Completed by BlockchainDev - 3 days ago</p>
                    </div>
                    <div className="p-3 rounded-md bg-white/5 border border-white/10">
                      <p className="text-white font-medium">New member joined: DogeWhale</p>
                      <p className="text-sm text-white/70">5 days ago</p>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="members" className="space-y-4 mt-4">
                {groupMembers[selectedGroup.id] ? (
                  <div className="space-y-4">
                    {groupMembers[selectedGroup.id].map((member) => (
                      <div
                        key={member.address}
                        className="p-4 rounded-md bg-white/5 border border-white/10 flex items-center gap-3"
                      >
                        <Avatar>
                          <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                          <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="text-white font-medium">{member.name}</h4>
                            <Badge className={selectedGroup.color}>{member.role}</Badge>
                          </div>
                          <div className="flex items-center justify-between mt-1">
                            <p className="text-sm text-white/70">{member.address}</p>
                            <p className="text-sm text-white/70">
                              Joined {member.joinedDate} · {member.contributions} contributions
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-white/70 text-center py-4">No members found for this group.</p>
                )}
              </TabsContent>

              <TabsContent value="tasks" className="space-y-4 mt-4">
                {groupTasks[selectedGroup.id] ? (
                  <div className="space-y-4">
                    {groupTasks[selectedGroup.id].map((task) => (
                      <div key={task.id} className="p-4 rounded-md bg-white/5 border border-white/10 space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-white font-medium">{task.title}</h4>
                          <Badge
                            className={
                              task.status === "completed"
                                ? "bg-green-500/20 text-green-500"
                                : task.status === "in-progress"
                                  ? "bg-blue-500/20 text-blue-500"
                                  : task.status === "blocked"
                                    ? "bg-red-500/20 text-red-500"
                                    : "bg-yellow-500/20 text-yellow-500"
                            }
                          >
                            {task.status.replace("-", " ")}
                          </Badge>
                        </div>
                        <p className="text-white/70">{task.description}</p>
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center gap-2">
                            <span className="text-white/70">Priority:</span>
                            <Badge
                              className={
                                task.priority === "high"
                                  ? "bg-red-500/20 text-red-500"
                                  : task.priority === "medium"
                                    ? "bg-yellow-500/20 text-yellow-500"
                                    : "bg-blue-500/20 text-blue-500"
                              }
                            >
                              {task.priority}
                            </Badge>
                          </div>
                          {task.assignee && (
                            <div className="text-white/70">
                              Assigned to: <span className="text-white">{task.assignee}</span>
                            </div>
                          )}
                          {task.dueDate && (
                            <div className="text-white/70">
                              Due: <span className="text-white">{task.dueDate}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-white/70 text-center py-4">No tasks found for this group.</p>
                )}
              </TabsContent>
            </Tabs>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredGroups.length === 0 ? (
              <div className="md:col-span-2 lg:col-span-3 text-center py-8">
                <p className="text-white/70">No working groups found matching your search.</p>
              </div>
            ) : (
              filteredGroups.map((group) => (
                <div
                  key={group.id}
                  className="p-4 rounded-md bg-white/5 border border-white/10 hover:bg-white/10 transition-colors cursor-pointer"
                  onClick={() => setSelectedGroup(group)}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`p-2 rounded-full ${group.color}`}>{group.icon}</div>
                    <h3 className="text-lg font-medium text-white">{group.name}</h3>
                  </div>
                  <p className="text-white/70 mb-4 line-clamp-2">{group.description}</p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-white/70">
                      {group.members} {group.members === 1 ? "member" : "members"}
                    </span>
                    <span className="text-white/70">
                      {group.completedTasks}/{group.tasks} tasks
                    </span>
                  </div>
                  <Progress value={(group.completedTasks / group.tasks) * 100} className="h-1.5 bg-white/10 mt-2" />
                </div>
              ))
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
