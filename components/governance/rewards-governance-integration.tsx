"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Trophy, Star, Award, Gift, Zap, TrendingUp, Users, Clock } from "lucide-react"
import { useWallet } from "@/components/wallet-provider"
import { useNotification } from "@/hooks/use-notification"

// Types for rewards
type RewardType = "governance" | "development" | "social" | "trading"
type RewardStatus = "claimed" | "available" | "locked"

interface Reward {
  id: string
  title: string
  description: string
  points: number
  type: RewardType
  status: RewardStatus
  progress?: number
  total?: number
  unlockDate?: string
}

// Mock data for rewards
const mockRewards: Reward[] = [
  {
    id: "reward-1",
    title: "Governance Participation",
    description: "Vote on at least 5 governance proposals",
    points: 500,
    type: "governance",
    status: "available",
    progress: 3,
    total: 5,
  },
  {
    id: "reward-2",
    title: "Development Contributor",
    description: "Stake tokens in the Development DAO for 30 days",
    points: 1000,
    type: "development",
    status: "locked",
    progress: 15,
    total: 30,
    unlockDate: "2025-06-15",
  },
  {
    id: "reward-3",
    title: "Social Engagement",
    description: "Create 3 posts in the social feed",
    points: 300,
    type: "social",
    status: "claimed",
    progress: 3,
    total: 3,
  },
  {
    id: "reward-4",
    title: "Trading Volume",
    description: "Trade at least 10,000 $PAW",
    points: 750,
    type: "trading",
    status: "available",
    progress: 7500,
    total: 10000,
  },
  {
    id: "reward-5",
    title: "Proposal Creator",
    description: "Create a governance proposal that reaches quorum",
    points: 1500,
    type: "governance",
    status: "locked",
    unlockDate: "2025-06-01",
  },
  {
    id: "reward-6",
    title: "Development Feedback",
    description: "Provide feedback on 3 development updates",
    points: 450,
    type: "development",
    status: "available",
    progress: 2,
    total: 3,
  },
]

// Achievements data
const achievements = [
  {
    title: "Early Adopter",
    description: "Joined during the platform's first month",
    icon: <Star className="h-8 w-8 text-yellow-400" />,
    earned: true,
  },
  {
    title: "Governance Guru",
    description: "Participated in 10+ governance votes",
    icon: <Trophy className="h-8 w-8 text-amber-500" />,
    earned: false,
    progress: 60,
  },
  {
    title: "Diamond Hands",
    description: "Staked tokens for over 90 days",
    icon: <Award className="h-8 w-8 text-blue-400" />,
    earned: false,
    progress: 45,
  },
  {
    title: "Community Pillar",
    description: "Created 5+ successful proposals",
    icon: <Users className="h-8 w-8 text-green-400" />,
    earned: false,
    progress: 20,
  },
]

// Leaderboard data
const leaderboard = [
  { rank: 1, address: "0x1a2b...3c4d", points: 12500, avatar: "/avatar-doge.png" },
  { rank: 2, address: "0x5e6f...7g8h", points: 10750, avatar: "/avatar-wizard.png" },
  { rank: 3, address: "0x9i0j...1k2l", points: 9800, avatar: "/generic-character-exchange.png" },
  { rank: 4, address: "0x3m4n...5o6p", points: 8650, avatar: "/avatar-developer.png" },
  { rank: 5, address: "0x7q8r...9s0t", points: 7200, avatar: "/avatar-enthusiast.png" },
]

export function RewardsGovernanceIntegration() {
  const { isConnected } = useWallet()
  const { showNotification } = useNotification()
  const [activeTab, setActiveTab] = useState("rewards")

  const handleClaimReward = (reward: Reward) => {
    showNotification({
      title: "Reward Claimed",
      message: `You've claimed ${reward.points} points for "${reward.title}"`,
      type: "success",
    })
  }

  const getRewardIcon = (type: RewardType) => {
    switch (type) {
      case "governance":
        return <Users className="h-5 w-5 text-doge" />
      case "development":
        return <Zap className="h-5 w-5 text-dogechain" />
      case "social":
        return <TrendingUp className="h-5 w-5 text-purple-500" />
      case "trading":
        return <Gift className="h-5 w-5 text-green-500" />
      default:
        return <Award className="h-5 w-5 text-blue-500" />
    }
  }

  const getRewardStatusColor = (status: RewardStatus) => {
    switch (status) {
      case "claimed":
        return "text-green-500"
      case "available":
        return "text-doge"
      case "locked":
        return "text-white/50"
      default:
        return "text-white"
    }
  }

  return (
    <Card className="glass-card border-white/5 doge-glow">
      <CardHeader>
        <CardTitle className="text-white">Governance Rewards & Achievements</CardTitle>
        <CardDescription className="text-white/70">
          Earn rewards for participating in governance and development activities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 glass mb-6">
            <TabsTrigger value="rewards" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
              <Trophy className="mr-2 h-4 w-4" />
              Rewards
            </TabsTrigger>
            <TabsTrigger
              value="achievements"
              className="data-[state=active]:text-dogechain data-[state=active]:bg-dogechain/10"
            >
              <Award className="mr-2 h-4 w-4" />
              Achievements
            </TabsTrigger>
            <TabsTrigger
              value="leaderboard"
              className="data-[state=active]:text-purple-500 data-[state=active]:bg-purple-500/10"
            >
              <TrendingUp className="mr-2 h-4 w-4" />
              Leaderboard
            </TabsTrigger>
          </TabsList>

          <TabsContent value="rewards" className="space-y-4">
            {isConnected ? (
              <>
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-white">Your Rewards</h3>
                    <p className="text-sm text-white/70">Complete tasks to earn points and rewards</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-white/70">Total Points</p>
                    <p className="text-2xl font-bold text-doge doge-text-glow">3,250</p>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  {mockRewards.map((reward) => (
                    <div
                      key={reward.id}
                      className="p-4 rounded-lg border border-white/10 bg-white/5 hover:bg-white/10 transition-colors"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center">
                          <div className="mr-3 p-2 rounded-full bg-white/10">{getRewardIcon(reward.type)}</div>
                          <div>
                            <h4 className="font-medium text-white">{reward.title}</h4>
                            <p className="text-sm text-white/70">{reward.description}</p>
                          </div>
                        </div>
                        <span className={`font-medium ${getRewardStatusColor(reward.status)}`}>
                          {reward.points} pts
                        </span>
                      </div>

                      {reward.progress !== undefined && reward.total !== undefined && (
                        <div className="mt-3">
                          <div className="flex justify-between text-xs mb-1">
                            <span className="text-white/70">Progress</span>
                            <span className="text-white/70">
                              {reward.progress} / {reward.total}
                            </span>
                          </div>
                          <Progress
                            value={(reward.progress / reward.total) * 100}
                            className="h-2 bg-white/10"
                            indicatorClassName={
                              reward.type === "governance"
                                ? "bg-doge"
                                : reward.type === "development"
                                  ? "bg-dogechain"
                                  : reward.type === "social"
                                    ? "bg-purple-500"
                                    : "bg-green-500"
                            }
                          />
                        </div>
                      )}

                      {reward.unlockDate && (
                        <div className="mt-3 flex items-center text-xs text-white/50">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>Unlocks on {reward.unlockDate}</span>
                        </div>
                      )}

                      <div className="mt-3">
                        {reward.status === "available" ? (
                          <Button
                            onClick={() => handleClaimReward(reward)}
                            className="w-full doge-button doge-shine text-sm h-8"
                          >
                            Claim Reward
                          </Button>
                        ) : reward.status === "claimed" ? (
                          <Button disabled className="w-full glass-button text-sm h-8 opacity-50">
                            Claimed
                          </Button>
                        ) : (
                          <Button disabled className="w-full glass-button text-sm h-8 opacity-50">
                            Locked
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <Trophy className="h-16 w-16 mx-auto text-white/20 mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">Connect Wallet to View Rewards</h3>
                <p className="text-white/70 mb-6 max-w-md mx-auto">
                  Connect your wallet to view and claim rewards for participating in governance and development
                  activities.
                </p>
                <Button className="doge-button doge-shine">Connect Wallet</Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="achievements" className="space-y-4">
            {isConnected ? (
              <div className="grid gap-4 md:grid-cols-2">
                {achievements.map((achievement, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded-lg border transition-colors ${
                      achievement.earned
                        ? "border-doge/30 bg-doge/5 hover:bg-doge/10"
                        : "border-white/10 bg-white/5 hover:bg-white/10"
                    }`}
                  >
                    <div className="flex items-start">
                      <div className="mr-4">{achievement.icon}</div>
                      <div>
                        <h4 className="font-medium text-white">{achievement.title}</h4>
                        <p className="text-sm text-white/70">{achievement.description}</p>

                        {achievement.earned ? (
                          <span className="inline-block mt-2 text-xs font-medium text-doge bg-doge/10 px-2 py-1 rounded">
                            Earned
                          </span>
                        ) : achievement.progress !== undefined ? (
                          <div className="mt-2">
                            <div className="flex justify-between text-xs mb-1">
                              <span className="text-white/70">Progress</span>
                              <span className="text-white/70">{achievement.progress}%</span>
                            </div>
                            <Progress value={achievement.progress} className="h-2 bg-white/10" />
                          </div>
                        ) : (
                          <span className="inline-block mt-2 text-xs font-medium text-white/50 bg-white/10 px-2 py-1 rounded">
                            Locked
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Award className="h-16 w-16 mx-auto text-white/20 mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">Connect Wallet to View Achievements</h3>
                <p className="text-white/70 mb-6 max-w-md mx-auto">
                  Connect your wallet to view your achievements and progress.
                </p>
                <Button className="doge-button doge-shine">Connect Wallet</Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="leaderboard">
            <div className="rounded-lg border border-white/10 overflow-hidden">
              <table className="w-full">
                <thead className="bg-white/5">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                      Rank
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-white/70 uppercase tracking-wider">
                      Points
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-white/10">
                  {leaderboard.map((user) => (
                    <tr key={user.rank} className="bg-white/5 hover:bg-white/10 transition-colors">
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="flex items-center">
                          <span
                            className={`flex items-center justify-center h-6 w-6 rounded-full text-xs font-medium ${
                              user.rank === 1
                                ? "bg-yellow-500/20 text-yellow-500"
                                : user.rank === 2
                                  ? "bg-gray-400/20 text-gray-400"
                                  : user.rank === 3
                                    ? "bg-amber-600/20 text-amber-600"
                                    : "bg-white/10 text-white/70"
                            }`}
                          >
                            {user.rank}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="flex items-center">
                          <img
                            src={user.avatar || "/placeholder.svg"}
                            alt="User avatar"
                            className="h-8 w-8 rounded-full mr-3 bg-white/10"
                          />
                          <span className="text-sm text-white">{user.address}</span>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-right">
                        <span className="text-sm font-medium text-doge">{user.points.toLocaleString()}</span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
