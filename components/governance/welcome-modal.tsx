"use client"

import { useState } from "react"
import { X, <PERSON>, Vote, Users, Coins } from "lucide-react"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

interface WelcomeModalProps {
  isOpen: boolean
  onClose: () => void
  onStartTour: () => void
}

export function WelcomeModal({ isOpen, onClose, onStartTour }: WelcomeModalProps) {
  const [activeTab, setActiveTab] = useState("overview")
  
  if (!isOpen) return null
  
  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto">
      <Card className="glass-card border-white/5 w-full max-w-3xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white text-2xl">Welcome to PawPumps Governance</CardTitle>
            <Button
              variant="ghost"
              size="icon"
              className="text-white/70 hover:text-white"
              onClick={onClose}
            >
              <X className="h-6 w-6" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
          <CardDescription className="text-white/70">
            Learn how to participate in shaping the future of PawPumps
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="glass mb-6 grid w-full grid-cols-3">
              <TabsTrigger value="overview" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                Overview
              </TabsTrigger>
              <TabsTrigger value="how-it-works" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                How It Works
              </TabsTrigger>
              <TabsTrigger value="rewards" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                Rewards
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview">
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row gap-6">
                  <div className="flex-1 p-4 rounded-lg bg-white/5 border border-white/10 flex flex-col items-center text-center">
                    <Vote className="h-12 w-12 text-doge mb-3" />
                    <h3 className="text-white font-medium mb-2">Governance Proposals</h3>
                    <p className="text-white/70 text-sm">
                      Vote on proposals to decide the future direction of the platform
                    </p>
                  </div>
                  
                  <div className="flex-1 p-4 rounded-lg bg-white/5 border border-white/10 flex flex-col items-center text-center">
                    <Users className="h-12 w-12 text-dogechain mb-3" />
                    <h3 className="text-white font-medium mb-2">Development DAO</h3>
                    <p className="text-white/70 text-sm">
                      Prioritize development tasks and suggest new features
                    </p>
                  </div>
                </div>
                
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Why Participate?</h3>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <p className="text-white/80">Shape the future of PawPumps according to your needs</p>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <p className="text-white/80">Earn rewards for active participation in governance</p>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <p className="text-white/80">Increase the value of your $PAW tokens through strategic decisions</p>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <p className="text-white/80">Become part of a community-driven platform</p>
                    </li>
                  </ul>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="how-it-works">
              <div className="space-y-6">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Governance Process</h3>
                  <ol className="space-y-4">
                    <li className="flex items-start gap-3">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-doge/20 text-doge flex items-center justify-center font-medium">
                        1
                      </div>
                      <div>
                        <h4 className="text-white font-medium mb-1">Proposal Creation</h4>
                        <p className="text-white/70 text-sm">
                          Any user with enough staked $PAW tokens can create a governance proposal
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-doge/20 text-doge flex items-center justify-center font-medium">
                        2
                      </div>
                      <div>
                        <h4 className="text-white font-medium mb-1">Voting Period</h4>
                        <p className="text-white/70 text-sm">
                          Proposals are open for voting for 7 days, during which token holders can vote for or against
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-doge/20 text-doge flex items-center justify-center font-medium">
                        3
                      </div>
                      <div>
                        <h4 className="text-white font-medium mb-1">Quorum Requirement</h4>
                        <p className="text-white/70 text-sm">
                          A minimum number of votes (quorum) is required for a proposal to be valid
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-doge/20 text-doge flex items-center justify-center font-medium">
                        4
                      </div>
                      <div>
                        <h4 className="text-white font-medium mb-1">Implementation</h4>
                        <p className="text-white/70 text-sm">
                          Passed proposals are implemented by the development team or through smart contracts
                        </p>
                      </div>
                    </li>
                  </ol>
                </div>
                
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Voting Power</h3>
                  <p className="text-white/80 mb-3">
                    Your voting power is determined by the amount of $PAW tokens you have staked and the lock period:
                  </p>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="p-3 rounded bg-white/5">
                      <p className="text-white/70 text-sm mb-1">30 Days Lock</p>
                      <p className="text-doge font-medium">1x Voting Power</p>
                    </div>
                    <div className="p-3 rounded bg-white/5">
                      <p className="text-white/70 text-sm mb-1">90 Days Lock</p>
                      <p className="text-doge font-medium">1.5x Voting Power</p>
                    </div>
                    <div className="p-3 rounded bg-white/5">
                      <p className="text-white/70 text-sm mb-1">180 Days Lock</p>
                      <p className="text-doge font-medium">2x Voting Power</p>
                    </div>
                    <div className="p-3 rounded bg-white/5">
                      <p className="text-white/70 text-sm mb-1">365 Days Lock</p>
                      <p className="text-doge font-medium">3x Voting Power</p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="rewards">
              <div className="space-y-6">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Staking Rewards</h3>
                  <p className="text-white/80 mb-3">
                    Earn rewards for staking your $PAW tokens in the governance system:
                  </p>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="p-3 rounded bg-white/5">
                      <p className="text-white/70 text-sm mb-1">Base APR</p>
                      <p className="text-doge font-medium">12.5%</p>
                    </div>
                    <div className="p-3 rounded bg-white/5">
                      <p className="text-white/70 text-sm mb-1">90 Days Lock Bonus</p>
                      <p className="text-doge font-medium">+2.5% APR</p>
                    </div>
                    <div className="p-3 rounded bg-white/5">
                      <p className="text-white/70 text-sm mb-1">180 Days Lock Bonus</p>
                      <p className="text-doge font-medium">+5% APR</p>
                    </div>
                    <div className="p-3 rounded bg-white/5">
                      <p className="text-white/70 text-sm mb-1">365 Days Lock Bonus</p>
                      <p className="text-doge font-medium">+10% APR</p>
                    </div>
                  </div>
                </div>
                
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Participation Rewards</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-3">
                      <Vote className="h-5 w-5 text-doge mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="text-white font-medium mb-1">Voting Rewards</h4>
                        <p className="text-white/70 text-sm">
                          Earn 5 $PAW tokens for each vote you cast on governance proposals
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start gap-3">
                      <Coins className="h-5 w-5 text-doge mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="text-white font-medium mb-1">Proposal Rewards</h4>
                        <p className="text-white/70 text-sm">
                          Earn 1,000 $PAW tokens when your proposal is approved and implemented
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start gap-3">
                      <Users className="h-5 w-5 text-purple-500 mt-0.5" />
                      <div>
                        <h4 className="text-white font-medium mb-1">Community Voting</h4>
                        <p className="text-white/70 text-sm">
                          Participate in community votes and earn rewards for active participation
                        </p>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
          <CardFooter className="flex gap-4">
            <Button className="flex-1 doge-button doge-shine" onClick={onStartTour}>
              Start Interactive Tour
            </Button>
            <Button variant="outline" className="flex-1 border-white/20 text-white hover:bg-white/10" onClick={onClose}>
              Explore on My Own
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }
