"use client"

import { useState, useEffect, create<PERSON>ontext, use<PERSON>ontext, ReactNode } from "react"
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import Link from "next/link"
import { useRouter } from "next/navigation"

// Error types
export interface AppError {
  id: string
  type: "network" | "validation" | "permission" | "blockchain" | "unknown"
  title: string
  message: string
  details?: string
  timestamp: Date
  recoverable: boolean
  retryAction?: () => void
}

// Error context
interface ErrorContextType {
  errors: AppError[]
  addError: (error: Omit<AppError, "id" | "timestamp">) => void
  removeError: (id: string) => void
  clearErrors: () => void
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined)

export function useErrorHandler() {
  const context = useContext(ErrorContext)
  if (!context) {
    throw new Error("useErrorHandler must be used within an ErrorProvider")
  }
  return context
}

// Error provider
export function ErrorProvider({ children }: { children: ReactNode }) {
  const [errors, setErrors] = useState<AppError[]>([])

  const addError = (error: Omit<AppError, "id" | "timestamp">) => {
    const newError: AppError = {
      ...error,
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
    }
    setErrors(prev => [...prev, newError])
  }

  const removeError = (id: string) => {
    setErrors(prev => prev.filter(error => error.id !== id))
  }

  const clearErrors = () => {
    setErrors([])
  }

  return (
    <ErrorContext.Provider value={{ errors, addError, removeError, clearErrors }}>
      {children}
    </ErrorContext.Provider>
  )
}

// Error boundary component
interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error) => void
}

export function ErrorBoundary({ children, fallback, onError }: ErrorBoundaryProps) {
  const [hasError, setHasError] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      setHasError(true)
      setError(new Error(event.message))
      onError?.(new Error(event.message))
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      setHasError(true)
      setError(new Error(event.reason))
      onError?.(new Error(event.reason))
    }

    window.addEventListener("error", handleError)
    window.addEventListener("unhandledrejection", handleUnhandledRejection)

    return () => {
      window.removeEventListener("error", handleError)
      window.removeEventListener("unhandledrejection", handleUnhandledRejection)
    }
  }, [onError])

  if (hasError) {
    return fallback || <ErrorFallback error={error} onRetry={() => setHasError(false)} />
  }

  return <>{children}</>
}

// Error fallback component
interface ErrorFallbackProps {
  error: Error | null
  onRetry?: () => void
}

export function ErrorFallback({ error, onRetry }: ErrorFallbackProps) {
  const router = useRouter()

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="glass-card border-red-500/20 max-w-md w-full">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-500/20 flex items-center justify-center">
            <AlertTriangle className="h-6 w-6 text-red-400" />
          </div>
          <CardTitle className="text-red-400">Something went wrong</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-white/70 text-center">
            {error?.message || "An unexpected error occurred. Please try again."}
          </p>
          
          <div className="flex flex-col gap-2">
            {onRetry && (
              <Button
                onClick={onRetry}
                className="w-full doge-button doge-shine"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            )}
            
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="w-full border-white/10 bg-white/5 text-white hover:bg-white/10"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
            
            <Link href="/">
              <Button
                variant="ghost"
                className="w-full text-white/70 hover:text-white"
              >
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Network error component
export function NetworkError({ onRetry }: { onRetry?: () => void }) {
  return (
    <Alert className="border-yellow-500/20 bg-yellow-500/10">
      <AlertTriangle className="h-4 w-4 text-yellow-400" />
      <AlertTitle className="text-yellow-400">Connection Issue</AlertTitle>
      <AlertDescription className="text-white/70">
        Unable to connect to the network. Please check your internet connection and try again.
        {onRetry && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onRetry}
            className="ml-2 h-auto p-1 text-yellow-400 hover:text-yellow-300"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

// Blockchain error component
export function BlockchainError({ error, onRetry }: { error: string; onRetry?: () => void }) {
  return (
    <Alert className="border-red-500/20 bg-red-500/10">
      <AlertTriangle className="h-4 w-4 text-red-400" />
      <AlertTitle className="text-red-400">Transaction Failed</AlertTitle>
      <AlertDescription className="text-white/70">
        {error}
        {onRetry && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onRetry}
            className="ml-2 h-auto p-1 text-red-400 hover:text-red-300"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Try Again
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

// Permission error component
export function PermissionError({ message }: { message?: string }) {
  return (
    <Alert className="border-orange-500/20 bg-orange-500/10">
      <AlertTriangle className="h-4 w-4 text-orange-400" />
      <AlertTitle className="text-orange-400">Access Denied</AlertTitle>
      <AlertDescription className="text-white/70">
        {message || "You don't have permission to perform this action."}
      </AlertDescription>
    </Alert>
  )
}

// Error notification list
export function ErrorNotifications() {
  const { errors, removeError } = useErrorHandler()

  if (errors.length === 0) return null

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {errors.map((error) => (
        <Alert key={error.id} className="border-red-500/20 bg-red-500/10">
          <AlertTriangle className="h-4 w-4 text-red-400" />
          <AlertTitle className="text-red-400">{error.title}</AlertTitle>
          <AlertDescription className="text-white/70">
            {error.message}
            <div className="flex gap-2 mt-2">
              {error.retryAction && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={error.retryAction}
                  className="h-auto p-1 text-red-400 hover:text-red-300"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Retry
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeError(error.id)}
                className="h-auto p-1 text-white/50 hover:text-white"
              >
                Dismiss
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      ))}
    </div>
  )
}

// Hook for common error scenarios
export function useCommonErrors() {
  const { addError } = useErrorHandler()

  const handleNetworkError = (retryAction?: () => void) => {
    addError({
      type: "network",
      title: "Network Error",
      message: "Unable to connect to the server. Please check your connection.",
      recoverable: true,
      retryAction,
    })
  }

  const handleBlockchainError = (message: string, retryAction?: () => void) => {
    addError({
      type: "blockchain",
      title: "Transaction Failed",
      message,
      recoverable: true,
      retryAction,
    })
  }

  const handlePermissionError = (message?: string) => {
    addError({
      type: "permission",
      title: "Access Denied",
      message: message || "You don't have permission to perform this action.",
      recoverable: false,
    })
  }

  const handleValidationError = (message: string) => {
    addError({
      type: "validation",
      title: "Validation Error",
      message,
      recoverable: true,
    })
  }

  return {
    handleNetworkError,
    handleBlockchainError,
    handlePermissionError,
    handleValidationError,
  }
}
