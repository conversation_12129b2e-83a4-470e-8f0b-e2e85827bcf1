"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { type ProposalAction, type ProposalExecutionResult, executeProposal } from "@/utils/proposal-executor"
import { useAuditLogger } from "@/hooks/use-audit-logger"
import { useWallet } from "@/components/wallet-provider"
import { AlertTriangle, CheckCircle2, Clock, Code, ExternalLink } from "lucide-react"

interface ProposalExecutionProps {
  proposalId: string
  title: string
  description: string
  actions: ProposalAction[]
  onClose: () => void
  onSuccess: () => void
}

export function ProposalExecution({
  proposalId,
  title,
  description,
  actions,
  onClose,
  onSuccess,
}: ProposalExecutionProps) {
  const { address } = useWallet()
  const { logAction } = useAuditLogger()
  const [executing, setExecuting] = useState(false)
  const [result, setResult] = useState<ProposalExecutionResult | null>(null)
  const [currentStep, setCurrentStep] = useState(0)

  const handleExecute = async () => {
    if (!address) return

    setExecuting(true)
    setCurrentStep(1)

    try {
      // Log the execution attempt
      await logAction("proposal:execute", {
        proposalId,
        title,
        actionCount: actions.length,
      })

      // Simulate preparation step
      await new Promise((resolve) => setTimeout(resolve, 1500))
      setCurrentStep(2)

      // Execute the proposal
      const executionResult = await executeProposal(proposalId, actions, address)
      setResult(executionResult)
      setCurrentStep(executionResult.success ? 3 : 4)

      // Log the execution result
      await logAction("proposal:execute", {
        proposalId,
        title,
        result: executionResult,
      })

      // If successful, call the success callback
      if (executionResult.success) {
        setTimeout(() => {
          onSuccess()
        }, 2000)
      }
    } catch (error) {
      console.error("Error during proposal execution:", error)
      setResult({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error during execution",
        timestamp: new Date().toISOString(),
      })
      setCurrentStep(4)

      // Log the execution error
      await logAction("proposal:execute", {
        proposalId,
        title,
        error: error instanceof Error ? error.message : "Unknown error",
      })
    } finally {
      setExecuting(false)
    }
  }

  return (
    <Card className="glass-card border-white/5 w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-white">Execute Proposal</CardTitle>
        <CardDescription className="text-white/70">
          Execute the passed proposal to implement its changes
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-white">{title}</h3>
          <p className="text-white/70">{description}</p>
        </div>

        <Separator className="bg-white/10" />

        <div className="space-y-4">
          <h4 className="text-sm font-medium text-white">Actions to Execute</h4>

          <div className="space-y-3">
            {actions.map((action, index) => (
              <div key={index} className="p-3 rounded-md bg-white/5 border border-white/10">
                <div className="flex items-center gap-2 mb-2">
                  <Badge className="bg-blue-500/20 text-blue-500">{action.type}</Badge>
                </div>
                <div className="text-sm text-white/70 font-mono">{JSON.stringify(action.params, null, 2)}</div>
              </div>
            ))}
          </div>
        </div>

        {currentStep > 0 && (
          <>
            <Separator className="bg-white/10" />

            <div className="space-y-4">
              <h4 className="text-sm font-medium text-white">Execution Progress</h4>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center">
                    <CheckCircle2 className="w-4 h-4 text-green-500" />
                  </div>
                  <span className="text-white">Preparing execution</span>
                </div>

                <div className="flex items-center gap-3">
                  {currentStep >= 2 ? (
                    <div className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center">
                      <CheckCircle2 className="w-4 h-4 text-green-500" />
                    </div>
                  ) : (
                    <div className="w-6 h-6 rounded-full bg-white/10 flex items-center justify-center">
                      {currentStep === 1 ? (
                        <div className="w-4 h-4 rounded-full border-2 border-t-doge border-white/10 animate-spin" />
                      ) : (
                        <Clock className="w-4 h-4 text-white/40" />
                      )}
                    </div>
                  )}
                  <span className={currentStep >= 2 ? "text-white" : "text-white/40"}>Executing actions</span>
                </div>

                <div className="flex items-center gap-3">
                  {currentStep >= 3 ? (
                    <div className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center">
                      <CheckCircle2 className="w-4 h-4 text-green-500" />
                    </div>
                  ) : currentStep === 4 ? (
                    <div className="w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center">
                      <AlertTriangle className="w-4 h-4 text-red-500" />
                    </div>
                  ) : (
                    <div className="w-6 h-6 rounded-full bg-white/10 flex items-center justify-center">
                      <Clock className="w-4 h-4 text-white/40" />
                    </div>
                  )}
                  <span className={currentStep >= 3 ? "text-white" : "text-white/40"}>
                    {currentStep === 3
                      ? "Execution complete"
                      : currentStep === 4
                        ? "Execution failed"
                        : "Finalizing execution"}
                  </span>
                </div>
              </div>
            </div>
          </>
        )}

        {result && (
          <>
            <Separator className="bg-white/10" />

            {result.success ? (
              <Alert className="glass-card border-green-500/20 bg-green-500/5">
                <CheckCircle2 className="h-5 w-5 text-green-500" />
                <AlertTitle className="text-white">Execution Successful</AlertTitle>
                <AlertDescription className="text-white/70">
                  The proposal has been successfully executed. The changes are now in effect.
                </AlertDescription>
                {result.transactionHash && (
                  <div className="mt-2">
                    <a
                      href={`https://explorer.dogechain.dog/tx/${result.transactionHash}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-doge flex items-center hover:underline"
                    >
                      View transaction <ExternalLink className="ml-1 h-3 w-3" />
                    </a>
                  </div>
                )}
              </Alert>
            ) : (
              <Alert className="glass-card border-red-500/20 bg-red-500/5">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <AlertTitle className="text-white">Execution Failed</AlertTitle>
                <AlertDescription className="text-white/70">
                  {result.error || "There was an error executing the proposal. Please try again."}
                </AlertDescription>
              </Alert>
            )}
          </>
        )}
      </CardContent>

      <CardFooter className="flex justify-end gap-3">
        {!executing && !result?.success && (
          <>
            <Button variant="outline" onClick={onClose} className="border-white/10 bg-white/5">
              Cancel
            </Button>
            {!result ? (
              <Button onClick={handleExecute} className="bg-doge text-black hover:bg-doge/90" disabled={executing}>
                <Code className="mr-2 h-4 w-4" />
                Execute Proposal
              </Button>
            ) : (
              <Button onClick={handleExecute} className="bg-doge text-black hover:bg-doge/90" disabled={executing}>
                Try Again
              </Button>
            )}
          </>
        )}

        {(executing || result?.success) && (
          <Button variant="outline" onClick={onClose} className="border-white/10 bg-white/5">
            {result?.success ? "Close" : "Cancel"}
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
