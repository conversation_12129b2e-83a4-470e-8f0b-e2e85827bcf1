"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { AlertTriangle, Download, Info, TrendingDown, TrendingUp } from "lucide-react"
import { useState } from "react"

// Types
interface HealthMetric {
  name: string
  value: number
  target: number
  status: "healthy" | "warning" | "critical"
  trend: "up" | "down" | "stable"
  description: string
}

interface HealthMetricsProps {
  timeframe?: "7d" | "30d" | "90d" | "1y"
}

export function GovernanceHealthMetrics({ timeframe = "30d" }: HealthMetricsProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState<"7d" | "30d" | "90d" | "1y">(timeframe)

  // Mock health metrics data
  const healthMetrics: HealthMetric[] = [
    {
      name: "Voter Participation",
      value: 34,
      target: 50,
      status: "warning",
      trend: "up",
      description: "Percentage of eligible voters participating in governance",
    },
    {
      name: "Proposal Success Rate",
      value: 72,
      target: 70,
      status: "healthy",
      trend: "stable",
      description: "Percentage of proposals that pass voting",
    },
    {
      name: "Governance Decentralization",
      value: 68,
      target: 80,
      status: "warning",
      trend: "up",
      description: "Measure of voting power distribution (Nakamoto coefficient)",
    },
    {
      name: "Proposal Quality",
      value: 85,
      target: 75,
      status: "healthy",
      trend: "up",
      description: "Average quality score of proposals based on community feedback",
    },
    {
      name: "Execution Rate",
      value: 62,
      target: 90,
      status: "critical",
      trend: "down",
      description: "Percentage of passed proposals that are successfully implemented",
    },
    {
      name: "Governance Velocity",
      value: 78,
      target: 70,
      status: "healthy",
      trend: "up",
      description: "Speed of proposal processing and implementation",
    },
  ]

  // Mock historical data for charts
  const participationData = [
    { month: "Jan", participation: 28 },
    { month: "Feb", participation: 30 },
    { month: "Mar", participation: 29 },
    { month: "Apr", participation: 32 },
    { month: "May", participation: 34 },
  ]

  const proposalData = [
    { month: "Jan", submitted: 12, passed: 8, implemented: 6 },
    { month: "Feb", submitted: 15, passed: 10, implemented: 7 },
    { month: "Mar", submitted: 10, passed: 7, implemented: 5 },
    { month: "Apr", submitted: 18, passed: 13, implemented: 8 },
    { month: "May", submitted: 14, passed: 10, implemented: 6 },
  ]

  const votingPowerDistribution = [
    { name: "Top 1%", value: 35 },
    { name: "Next 9%", value: 30 },
    { name: "Next 20%", value: 20 },
    { name: "Next 30%", value: 10 },
    { name: "Bottom 40%", value: 5 },
  ]

  // Colors for pie chart
  const COLORS = ["#FF8042", "#FFBB28", "#00C49F", "#0088FE", "#8884d8"]

  // Get status color
  const getStatusColor = (status: "healthy" | "warning" | "critical") => {
    switch (status) {
      case "healthy":
        return "bg-green-500/20 text-green-500"
      case "warning":
        return "bg-yellow-500/20 text-yellow-500"
      case "critical":
        return "bg-red-500/20 text-red-500"
      default:
        return "bg-white/20 text-white"
    }
  }

  // Get trend icon
  const getTrendIcon = (trend: "up" | "down" | "stable") => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-500" />
      case "stable":
        return <span className="h-4 w-4 text-yellow-500">→</span>
      default:
        return null
    }
  }

  // Get progress color
  const getProgressColor = (value: number, target: number) => {
    const percentage = (value / target) * 100
    if (percentage >= 90) return "bg-green-500"
    if (percentage >= 70) return "bg-yellow-500"
    return "bg-red-500"
  }

  // Calculate overall health score
  const calculateHealthScore = () => {
    const totalScore = healthMetrics.reduce((acc, metric) => {
      const score = (metric.value / metric.target) * 100
      return acc + (score > 100 ? 100 : score)
    }, 0)
    return Math.round(totalScore / healthMetrics.length)
  }

  const healthScore = calculateHealthScore()

  return (
    <Card className="glass-card border-white/5">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle className="text-white">Governance Health Metrics</CardTitle>
            <CardDescription className="text-white/70">
              Key indicators of governance system health and performance
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Tabs value={selectedTimeframe} onValueChange={(value) => setSelectedTimeframe(value as any)}>
              <TabsList className="glass">
                <TabsTrigger value="7d">7 Days</TabsTrigger>
                <TabsTrigger value="30d">30 Days</TabsTrigger>
                <TabsTrigger value="90d">90 Days</TabsTrigger>
                <TabsTrigger value="1y">1 Year</TabsTrigger>
              </TabsList>
            </Tabs>
            <Button variant="outline" size="sm" className="border-white/10 bg-white/5">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid gap-6 md:grid-cols-3">
          <Card className="glass-card border-white/5 col-span-1">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg text-white">Overall Health</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center text-center">
                <div
                  className={`text-5xl font-bold mb-2 ${
                    healthScore >= 80 ? "text-green-500" : healthScore >= 60 ? "text-yellow-500" : "text-red-500"
                  }`}
                >
                  {healthScore}%
                </div>
                <Badge
                  className={
                    healthScore >= 80
                      ? "bg-green-500/20 text-green-500"
                      : healthScore >= 60
                        ? "bg-yellow-500/20 text-yellow-500"
                        : "bg-red-500/20 text-red-500"
                  }
                >
                  {healthScore >= 80 ? "Healthy" : healthScore >= 60 ? "Needs Attention" : "Critical"}
                </Badge>
                <p className="text-white/70 mt-2">
                  {healthScore >= 80
                    ? "Governance system is functioning well"
                    : healthScore >= 60
                      ? "Some metrics need improvement"
                      : "Immediate action required"}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/5 col-span-2">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg text-white">Participation Trend</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px]">
                <ChartContainer
                  config={{
                    participation: {
                      label: "Voter Participation",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-full"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={participationData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis dataKey="month" stroke="rgba(255,255,255,0.5)" tick={{ fill: "rgba(255,255,255,0.5)" }} />
                      <YAxis stroke="rgba(255,255,255,0.5)" tick={{ fill: "rgba(255,255,255,0.5)" }} />
                      <ChartTooltip />
                      <Line
                        type="monotone"
                        dataKey="participation"
                        stroke="var(--color-participation)"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          {healthMetrics.map((metric) => (
            <div
              key={metric.name}
              className={`p-4 rounded-md border ${
                metric.status === "healthy"
                  ? "border-green-500/20 bg-green-500/5"
                  : metric.status === "warning"
                    ? "border-yellow-500/20 bg-yellow-500/5"
                    : "border-red-500/20 bg-red-500/5"
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white font-medium">{metric.name}</h3>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(metric.status)}>{metric.status}</Badge>
                  {getTrendIcon(metric.trend)}
                </div>
              </div>
              <p className="text-white/70 text-sm mb-3">{metric.description}</p>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Current: {metric.value}%</span>
                  <span className="text-white/70">Target: {metric.target}%</span>
                </div>
                <Progress
                  value={(metric.value / metric.target) * 100}
                  className="h-2 bg-white/10"
                  indicatorClassName={getProgressColor(metric.value, metric.target)}
                />
              </div>
            </div>
          ))}
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card className="glass-card border-white/5">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg text-white">Proposal Lifecycle</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[250px]">
                <ChartContainer
                  config={{
                    submitted: {
                      label: "Submitted",
                      color: "hsl(var(--chart-1))",
                    },
                    passed: {
                      label: "Passed",
                      color: "hsl(var(--chart-2))",
                    },
                    implemented: {
                      label: "Implemented",
                      color: "hsl(var(--chart-3))",
                    },
                  }}
                  className="h-full"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={proposalData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis dataKey="month" stroke="rgba(255,255,255,0.5)" tick={{ fill: "rgba(255,255,255,0.5)" }} />
                      <YAxis stroke="rgba(255,255,255,0.5)" tick={{ fill: "rgba(255,255,255,0.5)" }} />
                      <ChartTooltip />
                      <Bar dataKey="submitted" fill="var(--color-submitted)" radius={[4, 4, 0, 0]} />
                      <Bar dataKey="passed" fill="var(--color-passed)" radius={[4, 4, 0, 0]} />
                      <Bar dataKey="implemented" fill="var(--color-implemented)" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/5">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg text-white">Voting Power Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[250px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={votingPowerDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                    >
                      {votingPowerDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value: number, name: string) => [`${value}%`, name]}
                      contentStyle={{
                        backgroundColor: "rgba(0, 0, 0, 0.8)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "4px",
                        color: "white",
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {healthScore < 70 && (
          <Alert className="glass-card border-yellow-500/20 bg-yellow-500/5">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            <AlertTitle className="text-white">Governance Health Needs Improvement</AlertTitle>
            <AlertDescription className="text-white/70">
              Several key metrics are below target thresholds. Consider implementing delegation and working groups to
              improve participation and execution rates.
            </AlertDescription>
          </Alert>
        )}

        <div className="p-4 rounded-lg bg-white/5 border border-white/10 flex items-start gap-3">
          <Info className="h-5 w-5 text-white/70 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-white mb-1">About Governance Health Metrics</h4>
            <p className="text-sm text-white/70">
              These metrics provide insights into the health and effectiveness of the governance system. Regular
              monitoring helps identify areas for improvement and ensures the long-term sustainability of the platform's
              governance.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
