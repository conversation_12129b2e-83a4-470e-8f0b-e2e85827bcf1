import { AlertCircle, CheckCircle, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"

interface BlockchainTransactionStatusProps {
  status: "pending" | "success" | "error"
  message: string
  txHash?: string
}

export function BlockchainTransactionStatus({ status, message, txHash }: BlockchainTransactionStatusProps) {
  return (
    <div
      className={cn(
        "p-4 rounded-lg border flex items-center gap-3",
        status === "pending" && "bg-blue-500/10 border-blue-500/30 text-blue-500",
        status === "success" && "bg-green-500/10 border-green-500/30 text-green-500",
        status === "error" && "bg-red-500/10 border-red-500/30 text-red-500",
      )}
      role="status"
      aria-live="polite"
    >
      {status === "pending" && <Loader2 className="h-5 w-5 animate-spin" />}
      {status === "success" && <CheckCircle className="h-5 w-5" />}
      {status === "error" && <AlertCircle className="h-5 w-5" />}

      <div className="flex-1">
        <p className="font-medium">
          {status === "pending" && "Transaction in Progress"}
          {status === "success" && "Transaction Successful"}
          {status === "error" && "Transaction Failed"}
        </p>
        <p className="text-sm opacity-80">{message}</p>
        {txHash && <p className="text-xs font-mono mt-1 opacity-70 truncate">TX: {txHash}</p>}
      </div>
    </div>
  )
}
