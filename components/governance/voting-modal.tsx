"use client"

import { useEffect, useRef } from "react"
import { X, ThumbsUp, ThumbsDown } from "lucide-react"
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { StatusBadge } from "./status-badge"
import { CategoryBadge } from "./category-badge"
import { governanceTokens } from "@/styles/governance-tokens"
import { cn } from "@/lib/utils"
import { FeedbackButton } from "@/components/governance/enhanced-feedback"

// Types
type ProposalStatus = "active" | "passed" | "failed" | "pending" | "implemented"
type CategoryType =
  | "core-functionality"
  | "user-experience"
  | "visual-design"
  | "mobile"
  | "performance"
  | "accessibility"
  | "content"
  | "technical"
  | "security"
  | "social"

interface VotingModalProps {
  id: string
  title: string
  description: string
  status: ProposalStatus
  category: CategoryType
  votesFor: number
  votesAgainst: number
  quorum: number
  creator: string
  createdAt: string
  endTime: string
  onClose: () => void
  onVote: (vote: "for" | "against") => void
  isSubmitting?: boolean
  userVotingPower?: number
}

// Helper function to format votes
const formatVotes = (votes: number) => {
  if (votes >= 1000000) {
    return `${(votes / 1000000).toFixed(1)}M`
  } else if (votes >= 1000) {
    return `${(votes / 1000).toFixed(1)}K`
  }
  return votes.toString()
}

// Helper function to calculate progress
const calculateProgress = (votesFor: number, votesAgainst: number) => {
  const total = votesFor + votesAgainst
  if (total === 0) return 0
  return (votesFor / total) * 100
}

export function VotingModal({
  id,
  title,
  description,
  status,
  category,
  votesFor,
  votesAgainst,
  quorum,
  creator,
  createdAt,
  endTime,
  onClose,
  onVote,
  isSubmitting = false,
  userVotingPower,
}: VotingModalProps) {
  // Ref for the modal to trap focus
  const modalRef = useRef<HTMLDivElement>(null)

  // Handle escape key to close modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose()
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [onClose])

  // Prevent body scroll when modal is open
  useEffect(() => {
    document.body.style.overflow = "hidden"
    return () => {
      document.body.style.overflow = ""
    }
  }, [])

  // Focus trap
  useEffect(() => {
    const modal = modalRef.current
    if (!modal) return

    const focusableElements = modal.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
    )

    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== "Tab") return

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus()
          e.preventDefault()
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus()
          e.preventDefault()
        }
      }
    }

    modal.addEventListener("keydown", handleTabKey)
    firstElement.focus()

    return () => modal.removeEventListener("keydown", handleTabKey)
  }, [])

  return (
    <div
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <div className="w-full max-w-2xl max-h-[80vh] overflow-auto" ref={modalRef}>
        <Card className="glass-card border-white/5">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <span className={cn("font-mono", governanceTokens.typography.metadata)}>{id}</span>
                  <StatusBadge status={status} type="proposal" />
                </div>
                <CardTitle id="modal-title" className={governanceTokens.typography.title.medium}>
                  {title}
                </CardTitle>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="text-white/70 hover:text-white"
                onClick={onClose}
                aria-label="Close dialog"
              >
                <span className="sr-only">Close</span>
                <X className="h-6 w-6" />
              </Button>
            </div>
            <CardDescription className="text-white/70">
              Created by {creator.slice(0, 6)}...{creator.slice(-4)} on {createdAt}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="space-y-4">
              <p className="text-white/90">{description}</p>

              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h4 className="text-sm font-medium text-white mb-2">Voting Status</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-white/60">For: {formatVotes(votesFor)}</span>
                    <span className="text-white/60">Against: {formatVotes(votesAgainst)}</span>
                  </div>
                  <Progress
                    value={calculateProgress(votesFor, votesAgainst)}
                    className="h-2 bg-white/10"
                    indicatorClassName={
                      status === "active" ? "bg-dogechain" : status === "passed" ? "bg-green-500" : "bg-doge"
                    }
                  />
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-white/60">
                      Quorum: {formatVotes(votesFor + votesAgainst)}/{formatVotes(quorum)}
                    </span>
                    <div className="flex items-center gap-2">
                      <CategoryBadge category={category} />
                      <span className="text-white/60">
                        {status === "active" || status === "pending" ? `Ends: ${endTime}` : `Ended: ${endTime}`}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {userVotingPower && status === "active" && (
                <div className="p-4 rounded-lg bg-dogechain/10 border border-dogechain/20">
                  <h4 className="text-sm font-medium text-dogechain mb-2">Your Voting Power</h4>
                  <p className="text-sm text-white/90">
                    You have <span className="text-dogechain font-medium">{formatVotes(userVotingPower)}</span> voting
                    power. Your vote will have a significant impact on this proposal.
                  </p>
                </div>
              )}

              {status === "passed" && (
                <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/20">
                  <h4 className="text-sm font-medium text-green-500 mb-2">Proposal Passed</h4>
                  <p className="text-sm text-white/90">
                    This proposal has been approved by the community and will be implemented.
                  </p>
                </div>
              )}

              {status === "failed" && (
                <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20">
                  <h4 className="text-sm font-medium text-red-500 mb-2">Proposal Failed</h4>
                  <p className="text-sm text-white/90">
                    This proposal did not receive enough support from the community.
                  </p>
                </div>
              )}
            </div>
          </CardContent>

          <CardFooter className="flex gap-4">
            {status === "active" && (
              <>
                <FeedbackButton
                  className="flex-1 doge-button doge-shine"
                  onClick={() => onVote("for")}
                  disabled={isSubmitting}
                  successMessage="Vote cast successfully!"
                  errorMessage="Failed to cast vote"
                  loadingMessage="Casting vote..."
                >
                  <ThumbsUp className="mr-2 h-4 w-4" />
                  Vote For
                </FeedbackButton>
                <FeedbackButton
                  className="flex-1 glass-button"
                  onClick={() => onVote("against")}
                  disabled={isSubmitting}
                  successMessage="Vote cast successfully!"
                  errorMessage="Failed to cast vote"
                  loadingMessage="Casting vote..."
                >
                  <ThumbsDown className="mr-2 h-4 w-4" />
                  Vote Against
                </FeedbackButton>
              </>
            )}
            {status === "pending" && (
              <Button className="w-full glass-button" disabled>
                Voting Not Started
              </Button>
            )}
            {(status === "passed" || status === "failed" || status === "implemented") && (
              <Button className="w-full glass-button" disabled>
                Voting Ended
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
