"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ron<PERSON><PERSON> } from "lucide-react"
import { governanceTokens } from "@/styles/governance-tokens"
import { StatusBadge } from "./status-badge"
import { CategoryBadge } from "./category-badge"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"

// Types
type TaskStatus = "completed" | "in-progress" | "planned" | "blocked"
type PriorityLevel = "critical" | "high" | "medium" | "low"
type CategoryType =
  | "core-functionality"
  | "user-experience"
  | "visual-design"
  | "mobile"
  | "performance"
  | "accessibility"
  | "content"
  | "technical"
  | "security"
  | "social"

interface TaskCardProps {
  id: string
  title: string
  description: string
  status: TaskStatus
  priority: PriorityLevel
  category: CategoryType
  estimatedHours: number
  votes?: {
    for: number
    against: number
  }
  communityPriority?: number
  assignedTo?: string
  onClick?: () => void
  className?: string
}

// Helper function to get status icon
const getStatusIcon = (status: TaskStatus) => {
  switch (status) {
    case "completed":
      return <CheckCircle2 className="h-4 w-4" />
    case "in-progress":
      return <Clock className="h-4 w-4" />
    case "planned":
      return <Clock className="h-4 w-4" />
    case "blocked":
      return <AlertTriangle className="h-4 w-4" />
    default:
      return <Clock className="h-4 w-4" />
  }
}

// Helper function to format votes
const formatVotes = (votes: number) => {
  if (votes >= 1000000) {
    return `${(votes / 1000000).toFixed(1)}M`
  } else if (votes >= 1000) {
    return `${(votes / 1000).toFixed(1)}K`
  }
  return votes.toString()
}

// Helper function to calculate progress
const calculateProgress = (votesFor: number, votesAgainst: number) => {
  const total = votesFor + votesAgainst
  if (total === 0) return 0
  return (votesFor / total) * 100
}

export function TaskCard({
  id,
  title,
  description,
  status,
  priority,
  category,
  estimatedHours,
  votes,
  communityPriority,
  assignedTo,
  onClick,
  className,
}: TaskCardProps) {
  return (
    <div
      className={cn(
        "glass-card border border-white/5 rounded-lg",
        governanceTokens.spacing.card.padding,
        governanceTokens.animation.transition,
        "cursor-pointer hover:border-doge/20",
        className,
      )}
      onClick={onClick}
      tabIndex={0}
      role="button"
      aria-label={`Task: ${title}`}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault()
          onClick?.()
        }
      }}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <span className={cn("font-mono", governanceTokens.typography.metadata)}>{id}</span>
          <StatusBadge status={status} type="task" icon={getStatusIcon(status)} />
        </div>
        <div className="flex items-center gap-2">
          <StatusBadge status={priority} type="priority" />
          {communityPriority !== undefined && (
            <div className="bg-doge text-black px-2 py-0.5 rounded-full text-xs font-medium">
              Priority: {communityPriority}%
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <h3 className={cn("text-white", governanceTokens.typography.title.small, "mb-2")}>{title}</h3>
        <ChevronRight className="h-4 w-4 text-white/40 flex-shrink-0 ml-2" />
      </div>

      <p className={cn("text-white/70", governanceTokens.typography.body.medium, "mb-4 line-clamp-2")}>{description}</p>

      {votes && (
        <div className="space-y-2 mb-3">
          <div className="flex items-center justify-between">
            <span className={governanceTokens.typography.metadata}>For: {formatVotes(votes.for)}</span>
            <span className={governanceTokens.typography.metadata}>Against: {formatVotes(votes.against)}</span>
          </div>

          <Progress
            value={calculateProgress(votes.for, votes.against)}
            className="h-2 bg-white/10"
            indicatorClassName="bg-doge"
          />
        </div>
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <CategoryBadge category={category} />
          <span className={governanceTokens.typography.metadata}>{estimatedHours} hours</span>
        </div>
        {assignedTo && (
          <span className={governanceTokens.typography.metadata}>
            Assigned to: {assignedTo.slice(0, 6)}...{assignedTo.slice(-4)}
          </span>
        )}
      </div>
    </div>
  )
}
