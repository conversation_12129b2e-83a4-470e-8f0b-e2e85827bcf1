"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronRight, Home } from "lucide-react"
import { cn } from "@/lib/utils"

interface BreadcrumbItem {
  label: string
  href: string
  isActive?: boolean
}

interface BreadcrumbNavProps {
  className?: string
  showHome?: boolean
}

// Route mapping for better breadcrumb labels
const routeLabels: Record<string, string> = {
  governance: "Governance",
  admin: "Admin",
  dashboard: "Dashboard",
  proposals: "Proposals",
  staking: "Staking",
  treasury: "Treasury",
  analytics: "Analytics",
  "audit-analytics": "Audit Analytics",
  "audit-logs": "Audit Logs",
  "development-dao": "Development DAO",
  emergency: "Emergency Controls",
  feedback: "Feedback",
  moderation: "Moderation",
  notifications: "Notifications",
  performance: "Performance",
  rewards: "Rewards",
  security: "Security",
  settings: "Settings",
  users: "User Management",
  delegation: "Delegation",
  health: "System Health",
  achievements: "Achievements",
  "working-groups": "Working Groups",
}

export function BreadcrumbNav({ className, showHome = true }: BreadcrumbNavProps) {
  const pathname = usePathname()
  
  // Split pathname and filter out empty segments
  const segments = pathname.split("/").filter(Boolean)
  
  // Build breadcrumb items
  const breadcrumbs: BreadcrumbItem[] = []
  
  // Add home if requested
  if (showHome) {
    breadcrumbs.push({
      label: "Home",
      href: "/",
      isActive: pathname === "/"
    })
  }
  
  // Build breadcrumbs from path segments
  let currentPath = ""
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const isLast = index === segments.length - 1
    
    breadcrumbs.push({
      label: routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1),
      href: currentPath,
      isActive: isLast
    })
  })
  
  // Don't show breadcrumbs if there's only one item (home)
  if (breadcrumbs.length <= 1) {
    return null
  }
  
  return (
    <nav className={cn("flex items-center space-x-1 text-sm text-white/70", className)}>
      {breadcrumbs.map((item, index) => (
        <div key={item.href} className="flex items-center">
          {index > 0 && (
            <ChevronRight className="h-4 w-4 mx-2 text-white/40" />
          )}
          
          {item.isActive ? (
            <span className="text-white font-medium">
              {item.label}
            </span>
          ) : (
            <Link
              href={item.href}
              className="hover:text-white transition-colors duration-200"
            >
              {index === 0 && showHome ? (
                <div className="flex items-center">
                  <Home className="h-4 w-4 mr-1" />
                  {item.label}
                </div>
              ) : (
                item.label
              )}
            </Link>
          )}
        </div>
      ))}
    </nav>
  )
}

// Specialized breadcrumb for governance admin pages
export function GovernanceAdminBreadcrumb({ className }: { className?: string }) {
  return (
    <div className={cn("mb-6", className)}>
      <BreadcrumbNav showHome={true} />
    </div>
  )
}

// Breadcrumb with custom styling for admin pages
export function AdminPageBreadcrumb({ 
  className,
  title,
  description 
}: { 
  className?: string
  title?: string
  description?: string
}) {
  const pathname = usePathname()
  
  return (
    <div className={cn("mb-8", className)}>
      <BreadcrumbNav className="mb-4" />
      
      {title && (
        <div>
          <h1 className="text-3xl font-bold tracking-tight sm:text-4xl text-white mb-2">
            {title}
          </h1>
          {description && (
            <p className="text-lg text-white/70">{description}</p>
          )}
        </div>
      )}
    </div>
  )
}

// Compact breadcrumb for smaller spaces
export function CompactBreadcrumb({ className }: { className?: string }) {
  const pathname = usePathname()
  const segments = pathname.split("/").filter(Boolean)
  
  if (segments.length <= 1) return null
  
  const currentPage = segments[segments.length - 1]
  const parentPage = segments[segments.length - 2]
  
  return (
    <nav className={cn("flex items-center text-sm text-white/70", className)}>
      {parentPage && (
        <>
          <Link
            href={`/${segments.slice(0, -1).join("/")}`}
            className="hover:text-white transition-colors"
          >
            {routeLabels[parentPage] || parentPage.charAt(0).toUpperCase() + parentPage.slice(1)}
          </Link>
          <ChevronRight className="h-4 w-4 mx-2 text-white/40" />
        </>
      )}
      <span className="text-white font-medium">
        {routeLabels[currentPage] || currentPage.charAt(0).toUpperCase() + currentPage.slice(1)}
      </span>
    </nav>
  )
}
