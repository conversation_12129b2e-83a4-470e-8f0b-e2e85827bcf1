"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Info, <PERSON>, Unlock, <PERSON><PERSON><PERSON>riangle, CheckCircle2 } from "lucide-react"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { StakingInterfaceLoading } from "@/components/governance/governance-loading-states"
import { FeedbackButton, ProgressIndicator } from "@/components/governance/enhanced-feedback"

interface StakingInterfaceProps {
  walletBalance?: number
  stakedAmount?: number
  stakingAPR?: number
  rewardsEarned?: number
  votingPower?: number
  totalVotingPower?: number
  onStake?: (amount: number, lockPeriod: number) => Promise<void>
  onUnstake?: (amount: number) => Promise<void>
  onClaimRewards?: () => Promise<void>
  isConnected?: boolean
  minStakeAmount?: number
}

export function StakingInterface({
  walletBalance = 10000,
  stakedAmount = 5000,
  stakingAPR = 12.5,
  rewardsEarned = 125,
  votingPower = 5000,
  totalVotingPower = 2000000,
  onStake,
  onUnstake,
  onClaimRewards,
  isConnected = true,
  minStakeAmount = 100,
}: StakingInterfaceProps) {
  // State for staking
  const [stakeTab, setStakeTab] = useState<"stake" | "unstake">("stake")
  const [isLoading, setIsLoading] = useState(true)
  const [stakeAmount, setStakeAmount] = useState<number>(minStakeAmount)
  const [lockPeriod, setLockPeriod] = useState<number>(30) // days
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
  const [showConfirmation, setShowConfirmation] = useState<boolean>(false)
  const [transactionStatus, setTransactionStatus] = useState<"idle" | "pending" | "success" | "error">("idle")
  const [errorMessage, setErrorMessage] = useState<string>("")

  // Simulate loading data
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1200)
    return () => clearTimeout(timer)
  }, [])

  if (isLoading) {
    return <StakingInterfaceLoading />
  }

  // Calculate effective APR based on lock period
  const getEffectiveAPR = () => {
    // Base APR + bonus for lock period
    // 30 days: no bonus, 90 days: +2.5%, 180 days: +5%, 365 days: +10%
    let bonus = 0
    if (lockPeriod >= 365) {
      bonus = 10
    } else if (lockPeriod >= 180) {
      bonus = 5
    } else if (lockPeriod >= 90) {
      bonus = 2.5
    }

    return stakingAPR + bonus
  }

  // Calculate voting power multiplier based on lock period
  const getVotingMultiplier = () => {
    // 30 days: 1x, 90 days: 1.5x, 180 days: 2x, 365 days: 3x
    if (lockPeriod >= 365) {
      return 3
    } else if (lockPeriod >= 180) {
      return 2
    } else if (lockPeriod >= 90) {
      return 1.5
    }
    return 1
  }

  // Calculate projected rewards
  const getProjectedRewards = () => {
    const effectiveAPR = getEffectiveAPR()
    // Calculate daily rewards and multiply by lock period
    const dailyRate = effectiveAPR / 365 / 100
    return stakeAmount * dailyRate * lockPeriod
  }

  // Calculate projected voting power
  const getProjectedVotingPower = () => {
    return stakeAmount * getVotingMultiplier()
  }

  // Handle stake amount input
  const handleStakeAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number.parseFloat(e.target.value)
    if (isNaN(value) || value < 0) {
      setStakeAmount(0)
    } else {
      setStakeAmount(Math.min(value, stakeTab === "stake" ? walletBalance : stakedAmount))
    }
  }

  // Handle stake amount slider
  const handleStakeSliderChange = (value: number[]) => {
    setStakeAmount(Math.floor(value[0]))
  }

  // Handle lock period slider
  const handleLockPeriodChange = (value: number[]) => {
    setLockPeriod(value[0])
  }

  // Handle max button
  const handleMaxClick = () => {
    setStakeAmount(stakeTab === "stake" ? walletBalance : stakedAmount)
  }

  // Handle stake/unstake submission
  const handleSubmit = async () => {
    if (stakeAmount <= 0) return

    setShowConfirmation(true)
  }

  // Handle confirmation
  const handleConfirm = async () => {
    try {
      setIsSubmitting(true)
      setTransactionStatus("pending")

      if (stakeTab === "stake") {
        await onStake?.(stakeAmount, lockPeriod)
      } else {
        await onUnstake?.(stakeAmount)
      }

      setTransactionStatus("success")

      // Reset form after success
      setTimeout(() => {
        setShowConfirmation(false)
        setTransactionStatus("idle")
        setStakeAmount(minStakeAmount)
      }, 3000)
    } catch (error) {
      setTransactionStatus("error")
      setErrorMessage(error instanceof Error ? error.message : "Transaction failed")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle claim rewards
  const handleClaimRewards = async () => {
    try {
      setIsSubmitting(true)
      await onClaimRewards?.()
      // Handle success
    } catch (error) {
      // Handle error
    } finally {
      setIsSubmitting(false)
    }
  }

  // Format number with commas
  const formatNumber = (num: number) => {
    return num.toLocaleString(undefined, { maximumFractionDigits: 2 })
  }

  // Calculate voting weight percentage
  const votingWeightPercentage = (votingPower / totalVotingPower) * 100

  return (
    <Card className="glass-card border-white/5 dogechain-glow">
      <CardHeader>
        <CardTitle className="text-white">Governance Staking</CardTitle>
        <CardDescription className="text-white/70">
          Stake $PAW tokens to increase your voting power and earn rewards
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {isConnected ? (
          <>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-white/70">$PAW Balance</span>
                <span className="text-white font-medium">{formatNumber(walletBalance)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70">Staked $PAW</span>
                <span className="text-doge font-medium doge-text-glow">{formatNumber(stakedAmount)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70">Voting Power</span>
                <span className="text-dogechain font-medium dogechain-text-glow">{formatNumber(votingPower)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70">Voting Weight</span>
                <span className="text-white/90">{votingWeightPercentage.toFixed(3)}% of total</span>
              </div>
            </div>

            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <h4 className="text-sm font-medium text-white mb-2">Staking Rewards</h4>
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs text-white/70">Base APR</span>
                <span className="text-xs text-doge">{stakingAPR}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-white/70">Earned</span>
                <span className="text-xs text-doge">+{formatNumber(rewardsEarned)} $PAW</span>
              </div>

              {rewardsEarned > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-3 w-full text-doge border-doge/20 hover:bg-doge/10"
                  onClick={handleClaimRewards}
                  disabled={isSubmitting}
                >
                  Claim Rewards
                </Button>
              )}
            </div>

            <Tabs value={stakeTab} onValueChange={(value) => setStakeTab(value as "stake" | "unstake")}>
              <TabsList className="grid w-full grid-cols-2 glass mb-4">
                <TabsTrigger value="stake" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                  Stake
                </TabsTrigger>
                <TabsTrigger value="unstake" className="data-[state=active]:text-white data-[state=active]:bg-white/10">
                  Unstake
                </TabsTrigger>
              </TabsList>

              <TabsContent value="stake" className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="stake-amount" className="text-white">
                      Amount to Stake
                    </Label>
                    <Button variant="outline" size="sm" className="h-6 px-2 text-xs" onClick={handleMaxClick}>
                      MAX
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Input
                      id="stake-amount"
                      type="number"
                      value={stakeAmount}
                      onChange={handleStakeAmountChange}
                      className="glass-input text-white"
                      min={0}
                      max={walletBalance}
                      step={1}
                    />
                    <span className="text-white/70 whitespace-nowrap">$PAW</span>
                  </div>
                  <Slider
                    value={[stakeAmount]}
                    max={walletBalance}
                    step={1}
                    onValueChange={handleStakeSliderChange}
                    className="py-2"
                  />
                  <div className="flex items-center justify-between text-xs text-white/60">
                    <span>0</span>
                    <span>{formatNumber(walletBalance)}</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lock-period" className="text-white">
                    Lock Period (Days)
                  </Label>
                  <div className="flex items-center gap-2">
                    <span className="text-white/70">{lockPeriod} days</span>
                    <div className="flex-1">
                      <Slider
                        id="lock-period"
                        value={[lockPeriod]}
                        min={30}
                        max={365}
                        step={1}
                        onValueChange={handleLockPeriodChange}
                        className="py-2"
                      />
                    </div>
                    <span className="text-white/70 whitespace-nowrap">{getVotingMultiplier()}x power</span>
                  </div>
                  <div className="flex items-center justify-between text-xs text-white/60">
                    <span>30 days</span>
                    <span>90 days</span>
                    <span>180 days</span>
                    <span>365 days</span>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-dogechain/10 border border-dogechain/20">
                  <h4 className="text-sm font-medium text-dogechain mb-2">Staking Preview</h4>
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Effective APR</span>
                      <span className="text-doge">{getEffectiveAPR()}%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Voting Multiplier</span>
                      <span className="text-dogechain">{getVotingMultiplier()}x</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Projected Rewards</span>
                      <span className="text-doge">+{formatNumber(getProjectedRewards())} $PAW</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Voting Power</span>
                      <span className="text-dogechain">+{formatNumber(getProjectedVotingPower())}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Unlock Date</span>
                      <span className="text-white/90">
                        {new Date(Date.now() + lockPeriod * 24 * 60 * 60 * 1000).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="unstake" className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="unstake-amount" className="text-white">
                      Amount to Unstake
                    </Label>
                    <Button variant="outline" size="sm" className="h-6 px-2 text-xs" onClick={handleMaxClick}>
                      MAX
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Input
                      id="unstake-amount"
                      type="number"
                      value={stakeAmount}
                      onChange={handleStakeAmountChange}
                      className="glass-input text-white"
                      min={0}
                      max={stakedAmount}
                      step={1}
                    />
                    <span className="text-white/70 whitespace-nowrap">$PAW</span>
                  </div>
                  <Slider
                    value={[stakeAmount]}
                    max={stakedAmount}
                    step={1}
                    onValueChange={handleStakeSliderChange}
                    className="py-2"
                  />
                  <div className="flex items-center justify-between text-xs text-white/60">
                    <span>0</span>
                    <span>{formatNumber(stakedAmount)}</span>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="text-sm font-medium text-red-500 mb-1">Unstaking Warning</h4>
                      <p className="text-sm text-white/90">
                        Unstaking will reduce your voting power and may affect your ability to participate in
                        governance. You will also forfeit any rewards associated with the unstaked tokens.
                      </p>
                    </div>
                  </div>

                  <div className="mt-3 space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Current Voting Power</span>
                      <span className="text-dogechain">{formatNumber(votingPower)}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">New Voting Power</span>
                      <span className="text-dogechain">{formatNumber(Math.max(0, votingPower - stakeAmount))}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Voting Power Loss</span>
                      <span className="text-red-500">-{formatNumber(Math.min(votingPower, stakeAmount))}</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <Lock className="h-12 w-12 text-white/20 mb-4" />
            <p className="text-white/70 mb-4">
              Connect your wallet to view your staking status and participate in governance
            </p>
            <Button className="doge-button doge-shine">Connect Wallet</Button>
          </div>
        )}
      </CardContent>

      <CardFooter>
        {isConnected && (
          <FeedbackButton
            className={cn("w-full", stakeTab === "stake" ? "doge-button doge-shine" : "glass-button")}
            onClick={handleSubmit}
            disabled={stakeAmount <= 0 || isSubmitting}
            successMessage={stakeTab === "stake" ? "Tokens staked successfully!" : "Tokens unstaked successfully!"}
            errorMessage={stakeTab === "stake" ? "Failed to stake tokens" : "Failed to unstake tokens"}
            loadingMessage={stakeTab === "stake" ? "Staking tokens..." : "Unstaking tokens..."}
          >
            {stakeTab === "stake" ? (
              <>
                <Lock className="mr-2 h-4 w-4" />
                Stake Tokens
              </>
            ) : (
              <>
                <Unlock className="mr-2 h-4 w-4" />
                Unstake Tokens
              </>
            )}
          </FeedbackButton>
        )}
      </CardFooter>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card className="glass-card border-white/5 w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-white">
                {stakeTab === "stake" ? "Confirm Staking" : "Confirm Unstaking"}
              </CardTitle>
              <CardDescription className="text-white/70">Please review the details before confirming</CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              {transactionStatus === "pending" ? (
                <div className="flex flex-col items-center justify-center py-6">
                  <div className="h-12 w-12 rounded-full border-4 border-t-doge border-white/10 animate-spin mb-4" />
                  <p className="text-white font-medium">Transaction in progress...</p>
                  <p className="text-white/70 text-sm">Please wait while your transaction is being processed</p>
                </div>
              ) : transactionStatus === "success" ? (
                <div className="flex flex-col items-center justify-center py-6">
                  <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center mb-4">
                    <CheckCircle2 className="h-8 w-8 text-green-500" />
                  </div>
                  <p className="text-white font-medium">Transaction successful!</p>
                  <p className="text-white/70 text-sm">
                    {stakeTab === "stake"
                      ? `You have successfully staked ${formatNumber(stakeAmount)} $PAW tokens`
                      : `You have successfully unstaked ${formatNumber(stakeAmount)} $PAW tokens`}
                  </p>
                </div>
              ) : transactionStatus === "error" ? (
                <div className="flex flex-col items-center justify-center py-6">
                  <div className="h-12 w-12 rounded-full bg-red-500/10 flex items-center justify-center mb-4">
                    <AlertTriangle className="h-8 w-8 text-red-500" />
                  </div>
                  <p className="text-white font-medium">Transaction failed</p>
                  <p className="text-white/70 text-sm">{errorMessage || "Please try again later"}</p>
                </div>
              ) : (
                <>
                  <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/70">
                          {stakeTab === "stake" ? "Amount to Stake" : "Amount to Unstake"}
                        </span>
                        <span className="text-white font-medium">{formatNumber(stakeAmount)} $PAW</span>
                      </div>

                      {stakeTab === "stake" && (
                        <>
                          <div className="flex items-center justify-between">
                            <span className="text-white/70">Lock Period</span>
                            <span className="text-white font-medium">{lockPeriod} days</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-white/70">Unlock Date</span>
                            <span className="text-white font-medium">
                              {new Date(Date.now() + lockPeriod * 24 * 60 * 60 * 1000).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-white/70">Voting Multiplier</span>
                            <span className="text-dogechain font-medium">{getVotingMultiplier()}x</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-white/70">Effective APR</span>
                            <span className="text-doge font-medium">{getEffectiveAPR()}%</span>
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  <div className="p-4 rounded-lg bg-white/5 border border-white/10 flex items-start gap-3">
                    <Info className="h-5 w-5 text-white/70 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm text-white/90">
                        {stakeTab === "stake"
                          ? "By staking your tokens, you agree to lock them for the specified period. During this time, you won't be able to transfer or sell these tokens."
                          : "Unstaking your tokens will reduce your voting power and may affect your ability to participate in governance."}
                      </p>
                    </div>
                  </div>
                </>
              )}
            </CardContent>

            <CardFooter className="flex gap-4">
              {transactionStatus === "idle" ? (
                <>
                  <Button variant="outline" className="flex-1 glass-button" onClick={() => setShowConfirmation(false)}>
                    Cancel
                  </Button>
                  <Button className="flex-1 doge-button doge-shine" onClick={handleConfirm} disabled={isSubmitting}>
                    Confirm
                  </Button>
                </>
              ) : transactionStatus === "success" || transactionStatus === "error" ? (
                <Button
                  className="w-full glass-button"
                  onClick={() => {
                    setShowConfirmation(false)
                    setTransactionStatus("idle")
                  }}
                >
                  Close
                </Button>
              ) : null}
            </CardFooter>
          </Card>
        </div>
      )}
    </Card>
  )
}
