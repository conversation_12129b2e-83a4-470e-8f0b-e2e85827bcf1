"use client"

import { useState, useEffect, create<PERSON>ontext, useContext, ReactNode } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Vote, 
  DollarSign, 
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3
} from "lucide-react"
import { cn } from "@/lib/utils"

// Analytics data types
interface AnalyticsEvent {
  id: string
  type: "page_view" | "user_action" | "error" | "performance" | "governance"
  category: string
  action: string
  label?: string
  value?: number
  userId?: string
  sessionId: string
  timestamp: Date
  metadata?: Record<string, any>
}

interface PerformanceMetrics {
  pageLoadTime: number
  renderTime: number
  memoryUsage: number
  errorCount: number
  userSessions: number
  bounceRate: number
}

interface GovernanceMetrics {
  totalProposals: number
  activeProposals: number
  totalVotes: number
  uniqueVoters: number
  averageParticipation: number
  stakingRatio: number
  treasuryBalance: number
  governanceHealth: number
}

// Analytics context
interface AnalyticsContextType {
  trackEvent: (event: Omit<AnalyticsEvent, "id" | "sessionId" | "timestamp">) => void
  getMetrics: () => Promise<PerformanceMetrics>
  getGovernanceMetrics: () => Promise<GovernanceMetrics>
  events: AnalyticsEvent[]
}

const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined)

export function useAnalytics() {
  const context = useContext(AnalyticsContext)
  if (!context) {
    throw new Error("useAnalytics must be used within an AnalyticsProvider")
  }
  return context
}

// Analytics provider
export function AnalyticsProvider({ children }: { children: ReactNode }) {
  const [events, setEvents] = useState<AnalyticsEvent[]>([])
  const [sessionId] = useState(() => Math.random().toString(36).substr(2, 9))

  const trackEvent = (event: Omit<AnalyticsEvent, "id" | "sessionId" | "timestamp">) => {
    const newEvent: AnalyticsEvent = {
      ...event,
      id: Math.random().toString(36).substr(2, 9),
      sessionId,
      timestamp: new Date(),
    }
    
    setEvents(prev => [...prev, newEvent])
    
    // Send to analytics service (mock)
    console.log("Analytics Event:", newEvent)
  }

  const getMetrics = async (): Promise<PerformanceMetrics> => {
    // Mock performance metrics
    return {
      pageLoadTime: performance.now(),
      renderTime: Math.random() * 100,
      memoryUsage: (performance as any).memory?.usedJSHeapSize / 1024 / 1024 || 0,
      errorCount: events.filter(e => e.type === "error").length,
      userSessions: new Set(events.map(e => e.sessionId)).size,
      bounceRate: Math.random() * 30,
    }
  }

  const getGovernanceMetrics = async (): Promise<GovernanceMetrics> => {
    // Mock governance metrics
    return {
      totalProposals: 45,
      activeProposals: 8,
      totalVotes: 1250,
      uniqueVoters: 320,
      averageParticipation: 68.5,
      stakingRatio: 42.3,
      treasuryBalance: 5000000,
      governanceHealth: 85.2,
    }
  }

  // Track page views automatically
  useEffect(() => {
    trackEvent({
      type: "page_view",
      category: "navigation",
      action: "page_load",
      label: window.location.pathname,
    })
  }, [])

  return (
    <AnalyticsContext.Provider value={{
      trackEvent,
      getMetrics,
      getGovernanceMetrics,
      events,
    }}>
      {children}
    </AnalyticsContext.Provider>
  )
}

// Performance dashboard
export function PerformanceDashboard() {
  const { getMetrics } = useAnalytics()
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadMetrics = async () => {
      try {
        const data = await getMetrics()
        setMetrics(data)
      } catch (error) {
        console.error("Failed to load metrics:", error)
      } finally {
        setLoading(false)
      }
    }

    loadMetrics()
    const interval = setInterval(loadMetrics, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [getMetrics])

  if (loading || !metrics) {
    return (
      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-4 bg-white/10 rounded animate-pulse" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return "text-green-400"
    if (value <= thresholds.warning) return "text-yellow-400"
    return "text-red-400"
  }

  return (
    <Card className="glass-card border-white/5">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Performance Metrics
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">Page Load Time</span>
              <span className={cn("text-sm font-medium", getStatusColor(metrics.pageLoadTime, { good: 1000, warning: 3000 }))}>
                {metrics.pageLoadTime.toFixed(0)}ms
              </span>
            </div>
            <Progress 
              value={Math.min((metrics.pageLoadTime / 5000) * 100, 100)} 
              className="h-2"
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">Memory Usage</span>
              <span className={cn("text-sm font-medium", getStatusColor(metrics.memoryUsage, { good: 50, warning: 100 }))}>
                {metrics.memoryUsage.toFixed(1)}MB
              </span>
            </div>
            <Progress 
              value={Math.min((metrics.memoryUsage / 200) * 100, 100)} 
              className="h-2"
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">Error Count</span>
              <span className={cn("text-sm font-medium", getStatusColor(metrics.errorCount, { good: 0, warning: 5 }))}>
                {metrics.errorCount}
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">Bounce Rate</span>
              <span className={cn("text-sm font-medium", getStatusColor(metrics.bounceRate, { good: 20, warning: 40 }))}>
                {metrics.bounceRate.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Governance analytics dashboard
export function GovernanceAnalyticsDashboard() {
  const { getGovernanceMetrics } = useAnalytics()
  const [metrics, setMetrics] = useState<GovernanceMetrics | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadMetrics = async () => {
      try {
        const data = await getGovernanceMetrics()
        setMetrics(data)
      } catch (error) {
        console.error("Failed to load governance metrics:", error)
      } finally {
        setLoading(false)
      }
    }

    loadMetrics()
  }, [getGovernanceMetrics])

  if (loading || !metrics) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="glass-card border-white/5">
            <CardContent className="p-6">
              <div className="h-4 bg-white/10 rounded animate-pulse mb-2" />
              <div className="h-8 bg-white/10 rounded animate-pulse mb-1" />
              <div className="h-3 bg-white/10 rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const healthColor = metrics.governanceHealth >= 80 ? "text-green-400" : 
                     metrics.governanceHealth >= 60 ? "text-yellow-400" : "text-red-400"

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Total Proposals</p>
                <p className="text-2xl font-bold text-white">{metrics.totalProposals}</p>
                <p className="text-green-400 text-xs flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12% this month
                </p>
              </div>
              <Vote className="h-8 w-8 text-doge" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Active Voters</p>
                <p className="text-2xl font-bold text-white">{metrics.uniqueVoters}</p>
                <p className="text-green-400 text-xs flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +8% this week
                </p>
              </div>
              <Users className="h-8 w-8 text-doge" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Treasury Balance</p>
                <p className="text-2xl font-bold text-white">
                  ${(metrics.treasuryBalance / 1000000).toFixed(1)}M
                </p>
                <p className="text-green-400 text-xs flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +5% this month
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-doge" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Governance Health</p>
                <p className={cn("text-2xl font-bold", healthColor)}>
                  {metrics.governanceHealth.toFixed(1)}%
                </p>
                <p className="text-white/50 text-xs">Excellent</p>
              </div>
              <BarChart3 className="h-8 w-8 text-doge" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Participation Metrics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-white/70">Average Participation</span>
                <span className="text-white font-medium">{metrics.averageParticipation}%</span>
              </div>
              <Progress value={metrics.averageParticipation} className="h-2" />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-white/70">Staking Ratio</span>
                <span className="text-white font-medium">{metrics.stakingRatio}%</span>
              </div>
              <Progress value={metrics.stakingRatio} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">System Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-white/70">Proposal System</span>
              <Badge className="bg-green-500/20 text-green-400 border-green-500/20">
                <CheckCircle className="h-3 w-3 mr-1" />
                Operational
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-white/70">Voting System</span>
              <Badge className="bg-green-500/20 text-green-400 border-green-500/20">
                <CheckCircle className="h-3 w-3 mr-1" />
                Operational
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-white/70">Treasury</span>
              <Badge className="bg-green-500/20 text-green-400 border-green-500/20">
                <CheckCircle className="h-3 w-3 mr-1" />
                Operational
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
