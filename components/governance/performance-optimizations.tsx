"use client"

import { useState, useEffect, use<PERSON>emo, use<PERSON><PERSON>back, memo } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"

// Cache management
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  private static instance: CacheManager

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager()
    }
    return CacheManager.instance
  }

  set(key: string, data: any, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    })
  }

  get(key: string): any | null {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  invalidate(key: string): void {
    this.cache.delete(key)
  }

  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern)
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key)
      }
    }
  }

  clear(): void {
    this.cache.clear()
  }

  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    }
  }
}

// Custom hook for caching
export function useCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl: number = 5 * 60 * 1000
): {
  data: T | null
  loading: boolean
  error: Error | null
  refetch: () => Promise<void>
} {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const cache = CacheManager.getInstance()

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Check cache first
      const cachedData = cache.get(key)
      if (cachedData) {
        setData(cachedData)
        setLoading(false)
        return
      }

      // Fetch new data
      const newData = await fetcher()
      cache.set(key, newData, ttl)
      setData(newData)
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Unknown error"))
    } finally {
      setLoading(false)
    }
  }, [key, fetcher, ttl, cache])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  const refetch = useCallback(async () => {
    cache.invalidate(key)
    await fetchData()
  }, [key, fetchData, cache])

  return { data, loading, error, refetch }
}

// Debounced search hook
export function useDebouncedSearch<T>(
  searchFn: (query: string) => Promise<T[]>,
  delay: number = 300
): {
  query: string
  setQuery: (query: string) => void
  results: T[]
  loading: boolean
  error: Error | null
} {
  const [query, setQuery] = useState("")
  const [results, setResults] = useState<T[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!query.trim()) {
      setResults([])
      return
    }

    const timeoutId = setTimeout(async () => {
      try {
        setLoading(true)
        setError(null)
        const searchResults = await searchFn(query)
        setResults(searchResults)
      } catch (err) {
        setError(err instanceof Error ? err : new Error("Search failed"))
        setResults([])
      } finally {
        setLoading(false)
      }
    }, delay)

    return () => clearTimeout(timeoutId)
  }, [query, searchFn, delay])

  return { query, setQuery, results, loading, error }
}

// Virtual scrolling hook for large lists
export function useVirtualScrolling<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
): {
  visibleItems: T[]
  startIndex: number
  endIndex: number
  totalHeight: number
  offsetY: number
} {
  const [scrollTop, setScrollTop] = useState(0)

  const visibleCount = Math.ceil(containerHeight / itemHeight)
  const startIndex = Math.floor(scrollTop / itemHeight)
  const endIndex = Math.min(startIndex + visibleCount + 1, items.length)

  const visibleItems = items.slice(startIndex, endIndex)
  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  return {
    visibleItems,
    startIndex,
    endIndex,
    totalHeight,
    offsetY,
  }
}

// Optimized proposal list component
interface OptimizedProposalListProps {
  proposals: any[]
  onProposalClick: (proposal: any) => void
  itemHeight?: number
  containerHeight?: number
}

export const OptimizedProposalList = memo(function OptimizedProposalList({
  proposals,
  onProposalClick,
  itemHeight = 120,
  containerHeight = 600,
}: OptimizedProposalListProps) {
  const { visibleItems, totalHeight, offsetY } = useVirtualScrolling(
    proposals,
    itemHeight,
    containerHeight
  )

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop
    // Update scroll position for virtual scrolling
  }, [])

  return (
    <div
      className="overflow-auto"
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: "relative" }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((proposal, index) => (
            <div
              key={proposal.id}
              style={{ height: itemHeight }}
              className="border-b border-white/10 p-4 cursor-pointer hover:bg-white/5"
              onClick={() => onProposalClick(proposal)}
            >
              <h3 className="font-medium text-white">{proposal.title}</h3>
              <p className="text-white/70 text-sm mt-1">{proposal.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
})

// Lazy loading image component
interface LazyImageProps {
  src: string
  alt: string
  className?: string
  placeholder?: string
}

export const LazyImage = memo(function LazyImage({
  src,
  alt,
  className,
  placeholder = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjY2NjIi8+PC9zdmc+",
}: LazyImageProps) {
  const [imageSrc, setImageSrc] = useState(placeholder)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const img = new Image()
    img.onload = () => {
      setImageSrc(src)
      setIsLoaded(true)
    }
    img.src = src
  }, [src])

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={`${className} ${isLoaded ? "opacity-100" : "opacity-50"} transition-opacity duration-300`}
    />
  )
})

// Performance monitoring hook
export function usePerformanceMonitoring() {
  const [metrics, setMetrics] = useState({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
  })

  useEffect(() => {
    // Measure page load time
    const loadTime = performance.now()
    setMetrics(prev => ({ ...prev, loadTime }))

    // Measure memory usage (if available)
    if ('memory' in performance) {
      const memory = (performance as any).memory
      setMetrics(prev => ({
        ...prev,
        memoryUsage: memory.usedJSHeapSize / 1024 / 1024, // MB
      }))
    }

    // Measure render time
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        if (entry.entryType === 'measure') {
          setMetrics(prev => ({ ...prev, renderTime: entry.duration }))
        }
      })
    })

    observer.observe({ entryTypes: ['measure'] })

    return () => observer.disconnect()
  }, [])

  return metrics
}

// Prefetch hook for route optimization
export function usePrefetch() {
  const router = useRouter()

  const prefetchRoute = useCallback((href: string) => {
    router.prefetch(href)
  }, [router])

  return { prefetchRoute }
}

// Optimized data fetcher with retry logic
export async function optimizedFetch<T>(
  url: string,
  options: RequestInit = {},
  retries: number = 3
): Promise<T> {
  const cache = CacheManager.getInstance()
  const cacheKey = `fetch:${url}:${JSON.stringify(options)}`

  // Check cache first
  const cachedData = cache.get(cacheKey)
  if (cachedData) {
    return cachedData
  }

  let lastError: Error

  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      // Cache successful response
      cache.set(cacheKey, data, 5 * 60 * 1000) // 5 minutes
      
      return data
    } catch (error) {
      lastError = error instanceof Error ? error : new Error("Unknown error")
      
      if (i < retries - 1) {
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000))
      }
    }
  }

  throw lastError!
}
