"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useWallet } from "@/components/wallet-provider"
import { useNotification } from "@/hooks/use-notification"
import { Reply, Send, ThumbsUp } from "lucide-react"
import { mockProposals } from "@/data/mock-proposals"
import type { Comment } from "@/data/mock-proposals"
import { simulateBlockchainTransaction } from "@/utils/blockchain-simulation"
// Import the ConnectWalletButton
import { ConnectWalletButton } from "@/components/connect-wallet-button"

interface ProposalDiscussionProps {
  proposalId: string
}

export function ProposalDiscussion({ proposalId }: ProposalDiscussionProps) {
  const { isConnected, address, connect } = useWallet()
  const { showNotification } = useNotification()
  const [comment, setComment] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState("")
  const [comments, setComments] = useState<Comment[]>(() => {
    const proposal = mockProposals.find((p) => p.id === proposalId)
    return proposal?.comments || []
  })

  const handleSubmitComment = async () => {
    if (!comment.trim()) return
    if (!isConnected) {
      showNotification({
        title: "Wallet Not Connected",
        message: "Please connect your wallet to participate in discussions",
        type: "error",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Simulate blockchain transaction for storing comment
      const result = await simulateBlockchainTransaction({
        type: "comment",
        proposalId,
        content: comment,
      })

      if (result.success) {
        // Add the new comment to the local state
        const newComment: Comment = {
          id: `comment-${Date.now()}`,
          author: address ? `${address.slice(0, 6)}...${address.slice(-4)}` : "Anonymous",
          authorAddress: address || "0x0000...0000",
          content: comment,
          timestamp: new Date().toISOString(),
          replies: [],
        }

        setComments((prev) => [newComment, ...prev])
        setComment("")

        showNotification({
          title: "Comment Posted",
          message: "Your comment has been added to the discussion",
          type: "success",
        })
      } else {
        throw new Error(result.error || "Failed to post comment")
      }
    } catch (error) {
      showNotification({
        title: "Comment Failed",
        message: error instanceof Error ? error.message : "An error occurred",
        type: "error",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSubmitReply = async (commentId: string) => {
    if (!replyContent.trim()) return
    if (!isConnected) {
      showNotification({
        title: "Wallet Not Connected",
        message: "Please connect your wallet to participate in discussions",
        type: "error",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Simulate blockchain transaction for storing reply
      const result = await simulateBlockchainTransaction({
        type: "reply",
        proposalId,
        commentId,
        content: replyContent,
      })

      if (result.success) {
        // Add the new reply to the local state
        const newReply: Comment = {
          id: `reply-${Date.now()}`,
          author: address ? `${address.slice(0, 6)}...${address.slice(-4)}` : "Anonymous",
          authorAddress: address || "0x0000...0000",
          content: replyContent,
          timestamp: new Date().toISOString(),
        }

        setComments((prev) =>
          prev.map((c) => {
            if (c.id === commentId) {
              return {
                ...c,
                replies: [...(c.replies || []), newReply],
              }
            }
            return c
          }),
        )

        setReplyingTo(null)
        setReplyContent("")

        showNotification({
          title: "Reply Posted",
          message: "Your reply has been added to the discussion",
          type: "success",
        })
      } else {
        throw new Error(result.error || "Failed to post reply")
      }
    } catch (error) {
      showNotification({
        title: "Reply Failed",
        message: error instanceof Error ? error.message : "An error occurred",
        type: "error",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString(undefined, {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      })
    } catch (e) {
      return dateString
    }
  }

  return (
    <div className="space-y-6">
      <div className="p-4 rounded-lg bg-white/5 border border-white/10">
        <h3 className="text-lg font-medium text-white mb-4">Join the Discussion</h3>

        {isConnected ? (
          <div className="space-y-4">
            <Textarea
              placeholder="Share your thoughts on this proposal..."
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              className="glass-input text-white min-h-[100px]"
              disabled={isSubmitting}
            />
            <div className="flex justify-end">
              <Button
                onClick={handleSubmitComment}
                disabled={!comment.trim() || isSubmitting}
                className="bg-doge text-black hover:bg-doge/90"
              >
                {isSubmitting ? (
                  <span className="animate-pulse">Posting...</span>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    Post Comment
                  </>
                )}
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center p-4 bg-white/5 rounded-lg">
            <p className="text-white/70 mb-4">Connect your wallet to join the discussion</p>
            <ConnectWalletButton className="doge-button doge-shine" />
          </div>
        )}
      </div>

      <div className="space-y-6">
        <h3 className="text-lg font-medium text-white">Comments ({comments.length})</h3>

        {comments.length > 0 ? (
          <div className="space-y-6">
            {comments.map((comment) => (
              <div key={comment.id} className="space-y-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <div className="flex items-start gap-3">
                    <Avatar className="h-10 w-10 border border-white/10">
                      <AvatarImage
                        src={`/diverse-group-avatars.png?height=40&width=40&query=avatar ${comment.author}`}
                        alt={comment.author}
                      />
                      <AvatarFallback>{comment.author.slice(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>

                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="font-medium text-white">{comment.author}</span>
                          <span className="text-xs text-white/50 ml-2">{formatDate(comment.timestamp)}</span>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="sm" className="h-8 px-2 text-white/50 hover:text-white">
                            <ThumbsUp className="h-4 w-4" />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 px-2 text-white/50 hover:text-white"
                            onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                          >
                            <Reply className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      <p className="text-white/90 whitespace-pre-wrap">{comment.content}</p>
                    </div>
                  </div>

                  {replyingTo === comment.id && (
                    <div className="mt-4 pl-12 space-y-3">
                      <Textarea
                        placeholder={`Reply to ${comment.author}...`}
                        value={replyContent}
                        onChange={(e) => setReplyContent(e.target.value)}
                        className="glass-input text-white min-h-[80px]"
                        disabled={isSubmitting}
                      />
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setReplyingTo(null)
                            setReplyContent("")
                          }}
                          disabled={isSubmitting}
                        >
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleSubmitReply(comment.id)}
                          disabled={!replyContent.trim() || isSubmitting}
                          className="bg-doge text-black hover:bg-doge/90"
                        >
                          {isSubmitting ? "Posting..." : "Reply"}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {comment.replies && comment.replies.length > 0 && (
                  <div className="pl-12 space-y-3">
                    {comment.replies.map((reply) => (
                      <div key={reply.id} className="p-3 rounded-lg bg-white/5 border border-white/10">
                        <div className="flex items-start gap-3">
                          <Avatar className="h-8 w-8 border border-white/10">
                            <AvatarImage
                              src={`/diverse-group-avatars.png?height=32&width=32&query=avatar ${reply.author}`}
                              alt={reply.author}
                            />
                            <AvatarFallback>{reply.author.slice(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>

                          <div className="flex-1 space-y-1">
                            <div className="flex items-center justify-between">
                              <div>
                                <span className="font-medium text-white text-sm">{reply.author}</span>
                                <span className="text-xs text-white/50 ml-2">{formatDate(reply.timestamp)}</span>
                              </div>

                              <Button variant="ghost" size="sm" className="h-6 px-2 text-white/50 hover:text-white">
                                <ThumbsUp className="h-3 w-3" />
                              </Button>
                            </div>

                            <p className="text-white/90 text-sm whitespace-pre-wrap">{reply.content}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-8 bg-white/5 rounded-lg">
            <p className="text-white/50">No comments yet. Be the first to share your thoughts!</p>
          </div>
        )}
      </div>
    </div>
  )
}
