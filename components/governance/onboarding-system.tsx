"use client"

import { useState, useEffect, create<PERSON>ontex<PERSON>, use<PERSON>ontext, ReactNode } from "react"
import { X, ChevronLeft, ChevronRight, Check, Play, Lightbulb } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

// Onboarding step interface
interface OnboardingStep {
  id: string
  title: string
  description: string
  content: ReactNode
  targetElement?: string
  position?: "top" | "bottom" | "left" | "right"
  optional?: boolean
}

// Onboarding flow interface
interface OnboardingFlow {
  id: string
  title: string
  description: string
  steps: OnboardingStep[]
  category: "governance" | "staking" | "voting" | "admin"
}

// Onboarding context
interface OnboardingContextType {
  activeFlow: OnboardingFlow | null
  currentStep: number
  isOnboarding: boolean
  completedFlows: string[]
  startFlow: (flow: OnboardingFlow) => void
  nextStep: () => void
  prevStep: () => void
  skipStep: () => void
  completeFlow: () => void
  dismissOnboarding: () => void
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined)

export function useOnboarding() {
  const context = useContext(OnboardingContext)
  if (!context) {
    throw new Error("useOnboarding must be used within an OnboardingProvider")
  }
  return context
}

// Onboarding provider
export function OnboardingProvider({ children }: { children: ReactNode }) {
  const [activeFlow, setActiveFlow] = useState<OnboardingFlow | null>(null)
  const [currentStep, setCurrentStep] = useState(0)
  const [isOnboarding, setIsOnboarding] = useState(false)
  const [completedFlows, setCompletedFlows] = useState<string[]>([])

  // Load completed flows from localStorage
  useEffect(() => {
    const saved = localStorage.getItem("pawpumps-onboarding-completed")
    if (saved) {
      setCompletedFlows(JSON.parse(saved))
    }
  }, [])

  // Save completed flows to localStorage
  useEffect(() => {
    localStorage.setItem("pawpumps-onboarding-completed", JSON.stringify(completedFlows))
  }, [completedFlows])

  const startFlow = (flow: OnboardingFlow) => {
    setActiveFlow(flow)
    setCurrentStep(0)
    setIsOnboarding(true)
  }

  const nextStep = () => {
    if (activeFlow && currentStep < activeFlow.steps.length - 1) {
      setCurrentStep(prev => prev + 1)
    } else {
      completeFlow()
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const skipStep = () => {
    nextStep()
  }

  const completeFlow = () => {
    if (activeFlow && !completedFlows.includes(activeFlow.id)) {
      setCompletedFlows(prev => [...prev, activeFlow.id])
    }
    setActiveFlow(null)
    setCurrentStep(0)
    setIsOnboarding(false)
  }

  const dismissOnboarding = () => {
    setActiveFlow(null)
    setCurrentStep(0)
    setIsOnboarding(false)
  }

  return (
    <OnboardingContext.Provider value={{
      activeFlow,
      currentStep,
      isOnboarding,
      completedFlows,
      startFlow,
      nextStep,
      prevStep,
      skipStep,
      completeFlow,
      dismissOnboarding,
    }}>
      {children}
    </OnboardingContext.Provider>
  )
}

// Onboarding modal
export function OnboardingModal() {
  const { activeFlow, currentStep, isOnboarding, nextStep, prevStep, dismissOnboarding } = useOnboarding()

  if (!isOnboarding || !activeFlow) return null

  const step = activeFlow.steps[currentStep]
  const progress = ((currentStep + 1) / activeFlow.steps.length) * 100

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="glass-card border-white/5 max-w-2xl w-full max-h-[80vh] overflow-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white">{step.title}</CardTitle>
              <p className="text-white/70 text-sm mt-1">{step.description}</p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={dismissOnboarding}
              className="text-white/50 hover:text-white"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-white/70">
                Step {currentStep + 1} of {activeFlow.steps.length}
              </span>
              <span className="text-white/70">{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Step content */}
          <div className="text-white/80">
            {step.content}
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="border-white/10 bg-white/5 text-white hover:bg-white/10"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>

            <div className="flex gap-2">
              {step.optional && (
                <Button
                  variant="ghost"
                  onClick={nextStep}
                  className="text-white/70 hover:text-white"
                >
                  Skip
                </Button>
              )}
              
              <Button
                onClick={nextStep}
                className="doge-button doge-shine"
              >
                {currentStep === activeFlow.steps.length - 1 ? (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Complete
                  </>
                ) : (
                  <>
                    Next
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Onboarding trigger button
interface OnboardingTriggerProps {
  flow: OnboardingFlow
  className?: string
  variant?: "default" | "outline" | "ghost"
  size?: "default" | "sm" | "lg"
}

export function OnboardingTrigger({ flow, className, variant = "outline", size = "sm" }: OnboardingTriggerProps) {
  const { startFlow, completedFlows } = useOnboarding()
  const isCompleted = completedFlows.includes(flow.id)

  return (
    <Button
      variant={variant}
      size={size}
      onClick={() => startFlow(flow)}
      className={cn(
        "border-white/10 bg-white/5 text-white hover:bg-white/10",
        isCompleted && "border-green-500/20 bg-green-500/10 text-green-400",
        className
      )}
    >
      {isCompleted ? (
        <>
          <Check className="mr-2 h-4 w-4" />
          Review Guide
        </>
      ) : (
        <>
          <Play className="mr-2 h-4 w-4" />
          Start Guide
        </>
      )}
    </Button>
  )
}

// Quick tips component
interface QuickTipProps {
  title: string
  description: string
  tips: string[]
  className?: string
}

export function QuickTip({ title, description, tips, className }: QuickTipProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <Card className={cn("glass-card border-blue-500/20 bg-blue-500/5", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5 text-blue-400" />
          <CardTitle className="text-blue-400 text-lg">{title}</CardTitle>
        </div>
        <p className="text-white/70 text-sm">{description}</p>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="pt-0">
          <ul className="space-y-2">
            {tips.map((tip, index) => (
              <li key={index} className="flex items-start gap-2 text-sm text-white/80">
                <span className="text-blue-400 mt-1">•</span>
                {tip}
              </li>
            ))}
          </ul>
        </CardContent>
      )}
      
      <div className="px-6 pb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-blue-400 hover:text-blue-300 h-auto p-0"
        >
          {isExpanded ? "Show Less" : "Show Tips"}
        </Button>
      </div>
    </Card>
  )
}

// Onboarding badge for completed flows
export function OnboardingBadge({ flowId }: { flowId: string }) {
  const { completedFlows } = useOnboarding()
  const isCompleted = completedFlows.includes(flowId)

  if (!isCompleted) return null

  return (
    <Badge className="bg-green-500/20 text-green-400 border-green-500/20">
      <Check className="mr-1 h-3 w-3" />
      Completed
    </Badge>
  )
}

// Predefined onboarding flows
export const governanceOnboardingFlow: OnboardingFlow = {
  id: "governance-basics",
  title: "Governance Basics",
  description: "Learn how to participate in PawPumps governance",
  category: "governance",
  steps: [
    {
      id: "welcome",
      title: "Welcome to Governance",
      description: "Your voice matters in shaping PawPumps",
      content: (
        <div className="space-y-4">
          <p>Welcome to the PawPumps governance system! Here you can:</p>
          <ul className="list-disc list-inside space-y-2 text-white/80">
            <li>Vote on proposals that shape the platform</li>
            <li>Stake tokens to increase your voting power</li>
            <li>Create your own proposals</li>
            <li>Participate in community discussions</li>
          </ul>
        </div>
      ),
    },
    {
      id: "voting-power",
      title: "Understanding Voting Power",
      description: "Learn how voting power works",
      content: (
        <div className="space-y-4">
          <p>Your voting power is determined by:</p>
          <ul className="list-disc list-inside space-y-2 text-white/80">
            <li><strong>Staked Tokens:</strong> More staked $PAW = more voting power</li>
            <li><strong>Lock Period:</strong> Longer locks give bonus voting power</li>
            <li><strong>Delegation:</strong> You can delegate your power to others</li>
          </ul>
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <p className="text-blue-400 text-sm">
              💡 Tip: Start by staking some tokens to participate in governance!
            </p>
          </div>
        </div>
      ),
    },
    {
      id: "proposals",
      title: "Voting on Proposals",
      description: "How to review and vote on proposals",
      content: (
        <div className="space-y-4">
          <p>When voting on proposals:</p>
          <ul className="list-disc list-inside space-y-2 text-white/80">
            <li>Read the full proposal description carefully</li>
            <li>Check the discussion thread for community feedback</li>
            <li>Consider the impact on the platform</li>
            <li>Vote "For" or "Against" based on your judgment</li>
          </ul>
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
            <p className="text-yellow-400 text-sm">
              ⚠️ Remember: Your vote is final and cannot be changed!
            </p>
          </div>
        </div>
      ),
    },
  ],
}

export const stakingOnboardingFlow: OnboardingFlow = {
  id: "staking-guide",
  title: "Staking Guide",
  description: "Learn how to stake tokens and earn rewards",
  category: "staking",
  steps: [
    {
      id: "staking-intro",
      title: "What is Staking?",
      description: "Understanding token staking",
      content: (
        <div className="space-y-4">
          <p>Staking allows you to:</p>
          <ul className="list-disc list-inside space-y-2 text-white/80">
            <li>Earn rewards on your $PAW tokens</li>
            <li>Gain voting power in governance</li>
            <li>Support the network security</li>
            <li>Participate in exclusive features</li>
          </ul>
        </div>
      ),
    },
    {
      id: "lock-periods",
      title: "Lock Periods & Rewards",
      description: "Choose your staking strategy",
      content: (
        <div className="space-y-4">
          <p>Different lock periods offer different benefits:</p>
          <div className="space-y-3">
            <div className="bg-white/5 rounded-lg p-3">
              <div className="flex justify-between items-center">
                <span className="font-medium">30 Days</span>
                <span className="text-green-400">12.5% APR</span>
              </div>
              <p className="text-sm text-white/70">Base rewards, flexible</p>
            </div>
            <div className="bg-white/5 rounded-lg p-3">
              <div className="flex justify-between items-center">
                <span className="font-medium">90 Days</span>
                <span className="text-green-400">15% APR</span>
              </div>
              <p className="text-sm text-white/70">+2.5% bonus, good balance</p>
            </div>
            <div className="bg-white/5 rounded-lg p-3">
              <div className="flex justify-between items-center">
                <span className="font-medium">365 Days</span>
                <span className="text-green-400">22.5% APR</span>
              </div>
              <p className="text-sm text-white/70">+10% bonus, maximum rewards</p>
            </div>
          </div>
        </div>
      ),
    },
  ],
}
