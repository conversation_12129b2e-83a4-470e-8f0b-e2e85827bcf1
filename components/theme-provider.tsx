'use client'

import * as React from 'react'
import {
  ThemeProvider as NextThemesProvider,
  type ThemeProviderProps,
} from 'next-themes'

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  // Simplified Arc Browser compatibility without infinite loops
  React.useEffect(() => {
    // Simple one-time theme application
    const applyDarkTheme = () => {
      const html = document.documentElement
      const body = document.body

      // Force dark theme
      html.classList.add('dark')
      html.setAttribute('data-theme', 'dark')

      // Set CSS custom properties
      html.style.setProperty('--background', '240 10% 4%')
      html.style.setProperty('--foreground', '0 0% 88%')

      // Force background colors for Arc Browser
      const darkBg = 'hsl(240, 10%, 4%)'
      const darkFg = 'hsl(0, 0%, 88%)'

      html.style.setProperty('background-color', darkBg, 'important')
      body.style.setProperty('background-color', darkBg, 'important')
      body.style.setProperty('color', darkFg, 'important')
    }

    // Apply once on mount
    applyDarkTheme()

    // Apply after a short delay
    setTimeout(applyDarkTheme, 100)
  }, [])

  return <NextThemesProvider {...props}>{children}</NextThemesProvider>
}
