"use client"

import { useEffect } from 'react'

export function ArcBrowserDetector() {
  useEffect(() => {
    // Detect Arc Browser and apply fixes
    const detectAndFixArcBrowser = () => {
      // Multiple methods to detect Arc Browser
      const userAgent = navigator.userAgent
      const isArcBrowser = 
        userAgent.includes('Arc') ||
        userAgent.includes('Company') ||
        (window as any).arc !== undefined ||
        // Arc often identifies as Chrome but has unique properties
        (userAgent.includes('Chrome') && (window as any).chrome && !(window as any).chrome.webstore)

      if (isArcBrowser) {
        console.log('Arc Browser detected - applying theme fixes')
        
        // Mark the document as Arc Browser
        document.documentElement.setAttribute('data-arc-browser', 'true')
        document.documentElement.setAttribute('data-browser', 'arc')
        
        // Force dark theme immediately
        const applyArcFix = () => {
          const html = document.documentElement
          const body = document.body
          
          // Force dark class
          html.classList.add('dark')
          html.setAttribute('data-theme', 'dark')
          
          // Apply styles directly
          const darkBg = 'hsl(240, 10%, 4%)'
          const darkFg = 'hsl(0, 0%, 88%)'
          
          html.style.setProperty('background', darkBg, 'important')
          html.style.setProperty('background-color', darkBg, 'important')
          body.style.setProperty('background', darkBg, 'important')
          body.style.setProperty('background-color', darkBg, 'important')
          body.style.setProperty('color', darkFg, 'important')
          
          // Set CSS custom properties
          html.style.setProperty('--background', '240 10% 4%', 'important')
          html.style.setProperty('--foreground', '0 0% 88%', 'important')
          
          // Create and inject Arc-specific styles
          let arcStyle = document.getElementById('arc-browser-override')
          if (!arcStyle) {
            arcStyle = document.createElement('style')
            arcStyle.id = 'arc-browser-override'
            document.head.appendChild(arcStyle)
          }
          
          arcStyle.textContent = `
            /* Arc Browser Override Styles */
            html, html.dark, html[data-theme="dark"], html[data-arc-browser="true"] {
              background: ${darkBg} !important;
              background-color: ${darkBg} !important;
              --background: 240 10% 4% !important;
              --foreground: 0 0% 88% !important;
            }
            
            body, html.dark body, html[data-theme="dark"] body, html[data-arc-browser="true"] body {
              background: ${darkBg} !important;
              background-color: ${darkBg} !important;
              color: ${darkFg} !important;
            }
            
            .bg-background {
              background-color: ${darkBg} !important;
            }
            
            .text-foreground {
              color: ${darkFg} !important;
            }
            
            /* Force all potential background elements */
            #__next, #root, [data-testid="app-loaded"], .min-h-screen {
              background-color: ${darkBg} !important;
            }
            
            /* Override any Tailwind classes that might interfere */
            .bg-white, .bg-gray-50, .bg-slate-50 {
              background-color: ${darkBg} !important;
            }
          `
          
          // Force a reflow to ensure styles are applied
          html.offsetHeight
          body.offsetHeight
        }
        
        // Apply fixes immediately
        applyArcFix()
        
        // Apply fixes after DOM is ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', applyArcFix)
        }
        
        // Apply fixes after a delay (Arc Browser sometimes needs this)
        setTimeout(applyArcFix, 100)
        setTimeout(applyArcFix, 500)
        setTimeout(applyArcFix, 1000)
        
        // Watch for any changes that might reset the theme
        const observer = new MutationObserver(() => {
          // Re-apply fixes if theme gets reset
          const htmlElement = document.documentElement
          if (!htmlElement.classList.contains('dark') || htmlElement.style.backgroundColor !== 'hsl(240, 10%, 4%)') {
            applyArcFix()
          }
        })
        
        observer.observe(document.documentElement, {
          attributes: true,
          attributeFilter: ['class', 'data-theme', 'style']
        })
        
        observer.observe(document.body, {
          attributes: true,
          attributeFilter: ['class', 'style']
        })
        
        // Cleanup function
        return () => observer.disconnect()
      }
    }
    
    // Run detection immediately
    detectAndFixArcBrowser()
    
    // Also run after the page is fully loaded
    window.addEventListener('load', detectAndFixArcBrowser)
    
    return () => {
      window.removeEventListener('load', detectAndFixArcBrowser)
    }
  }, [])

  return null // This component doesn't render anything
}
