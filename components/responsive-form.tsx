"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useResponsive } from "@/hooks/use-responsive"
import { useDebounce } from "@/hooks/use-debounce"
import { validateForm, type ValidationErrors, type FieldValidation } from "@/utils/form-validation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

type FormField = {
  name: string
  label: string
  type: string
  placeholder?: string
  required?: boolean
  autoComplete?: string
}

type ResponsiveFormProps = {
  fields: FormField[]
  validations: FieldValidation
  onSubmit: (values: Record<string, any>) => Promise<void>
  submitLabel: string
  successMessage?: string
}

export function ResponsiveForm({
  fields,
  validations,
  onSubmit,
  submitLabel,
  successMessage = "Form submitted successfully!",
}: ResponsiveFormProps) {
  const { isMobile } = useResponsive()
  const [values, setValues] = useState<Record<string, any>>({})
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [generalError, setGeneralError] = useState<string | null>(null)

  // Initialize form values
  useEffect(() => {
    const initialValues: Record<string, any> = {}
    fields.forEach((field) => {
      initialValues[field.name] = ""
    })
    setValues(initialValues)
  }, [fields])

  // Debounce validation to improve performance
  const debouncedValues = useDebounce(values, 300)

  // Validate on value change
  useEffect(() => {
    const newErrors = validateForm(debouncedValues, validations)
    setErrors(newErrors)
  }, [debouncedValues, validations])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target
    setValues((prev) => ({
      ...prev,
      [name]: type === "number" ? (value === "" ? "" : Number(value)) : value,
    }))
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name } = e.target
    setTouched((prev) => ({
      ...prev,
      [name]: true,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Mark all fields as touched
    const allTouched: Record<string, boolean> = {}
    fields.forEach((field) => {
      allTouched[field.name] = true
    })
    setTouched(allTouched)

    // Validate all fields
    const formErrors = validateForm(values, validations)
    setErrors(formErrors)

    // If there are errors, don't submit
    if (Object.keys(formErrors).length > 0) {
      return
    }

    setIsSubmitting(true)
    setGeneralError(null)

    try {
      await onSubmit(values)
      setIsSuccess(true)

      // Reset form after 3 seconds
      setTimeout(() => {
        setIsSuccess(false)
      }, 3000)
    } catch (error) {
      setGeneralError(error instanceof Error ? error.message : "An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6" aria-label="Form">
      {generalError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{generalError}</AlertDescription>
        </Alert>
      )}

      {isSuccess && (
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      <div className={`grid gap-6 ${isMobile ? "grid-cols-1" : "grid-cols-2"}`}>
        {fields.map((field) => (
          <div key={field.name} className="space-y-2">
            <Label
              htmlFor={field.name}
              className={`text-sm font-medium ${touched[field.name] && errors[field.name] ? "text-red-500" : ""}`}
            >
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <Input
              id={field.name}
              name={field.name}
              type={field.type}
              placeholder={field.placeholder}
              value={values[field.name] || ""}
              onChange={handleChange}
              onBlur={handleBlur}
              autoComplete={field.autoComplete}
              aria-invalid={touched[field.name] && errors[field.name] ? "true" : "false"}
              aria-describedby={touched[field.name] && errors[field.name] ? `${field.name}-error` : undefined}
              className={`w-full ${touched[field.name] && errors[field.name] ? "border-red-500 focus:ring-red-500" : ""}`}
            />
            {touched[field.name] && errors[field.name] && (
              <div id={`${field.name}-error`} className="text-sm text-red-500" aria-live="polite">
                {errors[field.name][0]}
              </div>
            )}
          </div>
        ))}
      </div>

      <Button type="submit" disabled={isSubmitting} className="w-full md:w-auto" aria-busy={isSubmitting}>
        {isSubmitting ? "Submitting..." : submitLabel}
      </Button>
    </form>
  )
}
