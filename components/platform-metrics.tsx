"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON>, <PERSON><PERSON>hart, XAxis, YA<PERSON>s, CartesianGrid, Legend, ResponsiveContainer, Toolt<PERSON> } from "recharts"
import { TrendingUp, TrendingDown, Users, DollarSign, Activity } from "lucide-react"

// Enhanced mock data for platform metrics with more realistic patterns
const generateData = (timeframe: string) => {
  const data = []
  let days = 7
  let pointsPerDay = 1

  switch (timeframe) {
    case "24h":
      days = 1
      pointsPerDay = 24
      break
    case "7d":
      days = 7
      pointsPerDay = 1
      break
    case "30d":
      days = 30
      pointsPerDay = 1
      break
    case "90d":
      days = 90
      pointsPerDay = 1
      break
    default:
      days = 7
      pointsPerDay = 1
  }

  const totalPoints = days * pointsPerDay

  for (let i = 0; i < totalPoints; i++) {
    const date = new Date()

    if (timeframe === "24h") {
      date.setHours(date.getHours() - i)
    } else {
      date.setDate(date.getDate() - i)
    }

    // More realistic base values with market cycles
    const dayOfWeek = date.getDay()
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
    const weekendMultiplier = isWeekend ? 0.7 : 1

    // Base values with growth trends
    const baseVolume = (150000 + Math.sin(i * 0.1) * 30000 + Math.random() * 40000) * weekendMultiplier
    const baseUsers = (1200 + Math.sin(i * 0.15) * 200 + Math.random() * 300) * weekendMultiplier
    const baseFees = (1500 + Math.sin(i * 0.12) * 400 + Math.random() * 200) * weekendMultiplier

    // Add growth trend over time
    const growthFactor = 1 + (totalPoints - i) / totalPoints * 0.3

    data.push({
      date: timeframe === "24h"
        ? date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
        : date.toLocaleDateString([], { month: "short", day: "numeric" }),
      volume: Math.round(baseVolume * growthFactor),
      users: Math.round(baseUsers * growthFactor),
      fees: Math.round(baseFees * growthFactor),
      timestamp: date.getTime()
    })
  }

  return data.reverse()
}

// Calculate summary statistics
const calculateSummaryStats = (data: any[], timeframe: string) => {
  if (!data.length) return { volume: 0, users: 0, fees: 0, volumeChange: 0, usersChange: 0, feesChange: 0 }

  const latest = data[data.length - 1]
  const previous = data.length > 1 ? data[data.length - 2] : latest

  const volumeChange = previous.volume ? ((latest.volume - previous.volume) / previous.volume) * 100 : 0
  const usersChange = previous.users ? ((latest.users - previous.users) / previous.users) * 100 : 0
  const feesChange = previous.fees ? ((latest.fees - previous.fees) / previous.fees) * 100 : 0

  return {
    volume: latest.volume,
    users: latest.users,
    fees: latest.fees,
    volumeChange: Number(volumeChange.toFixed(1)),
    usersChange: Number(usersChange.toFixed(1)),
    feesChange: Number(feesChange.toFixed(1))
  }
}

// Metric card component for key statistics
interface MetricCardProps {
  title: string
  value: string | number
  change: number
  icon: React.ReactNode
  formatter?: (value: number) => string
}

function MetricCard({ title, value, change, icon, formatter }: MetricCardProps) {
  const isPositive = change >= 0
  const formattedValue = typeof value === 'number' && formatter ? formatter(value) : value

  return (
    <Card className="glass-card border-white/5">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm text-white/60">{title}</p>
            <p className="text-2xl font-bold text-white">{formattedValue}</p>
            <div className={`flex items-center gap-1 text-sm ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
              {isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
              {Math.abs(change).toFixed(1)}%
            </div>
          </div>
          <div className="rounded-full bg-white/5 p-3">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function PlatformMetrics({
  timeframe,
  showMetricCards = true
}: {
  timeframe: string
  showMetricCards?: boolean
}) {
  const data = generateData(timeframe)
  const stats = calculateSummaryStats(data, timeframe)

  const formatVolume = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(2)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value}`
  }

  const formatUsers = (value: number) => {
    if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
    return value.toString()
  }

  const formatFees = (value: number) => {
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value}`
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards - Only show if showMetricCards is true */}
      {showMetricCards && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <MetricCard
            title="Trading Volume"
            value={stats.volume}
            change={stats.volumeChange}
            icon={<Activity className="h-6 w-6 text-doge" />}
            formatter={formatVolume}
          />
          <MetricCard
            title="Active Users"
            value={stats.users}
            change={stats.usersChange}
            icon={<Users className="h-6 w-6 text-dogechain" />}
            formatter={formatUsers}
          />
          <MetricCard
            title="Fees Collected"
            value={stats.fees}
            change={stats.feesChange}
            icon={<DollarSign className="h-6 w-6 text-green-500" />}
            formatter={formatFees}
          />
        </div>
      )}

      {/* Chart */}
      <Card className="glass-card border-white/5 liquid-glow">
        <CardHeader>
          <CardTitle className="text-white">Platform Metrics Trends</CardTitle>
          <CardDescription className="text-white/70">
            Trading volume, active users, and fees collected over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 25 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis
                  dataKey="date"
                  stroke="rgba(255,255,255,0.5)"
                  tick={{ fill: "rgba(255,255,255,0.5)" }}
                  tickLine={{ stroke: "rgba(255,255,255,0.2)" }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis
                  stroke="rgba(255,255,255,0.5)"
                  tick={{ fill: "rgba(255,255,255,0.5)" }}
                  tickLine={{ stroke: "rgba(255,255,255,0.2)" }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px'
                  }}
                  labelStyle={{ color: 'white', fontWeight: 'bold', marginBottom: '8px' }}
                  formatter={(value: any, name: string) => {
                    const formattedValue = name === 'Trading Volume' ? formatVolume(value) :
                                         name === 'Active Users' ? formatUsers(value) :
                                         formatFees(value)
                    return [formattedValue, name]
                  }}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="volume"
                  stroke="#FFC107"
                  strokeWidth={2}
                  dot={{ r: 2, fill: "#FFC107" }}
                  activeDot={{ r: 6, fill: "#FFC107", stroke: "#FFC107", strokeWidth: 2 }}
                  name="Trading Volume"
                />
                <Line
                  type="monotone"
                  dataKey="users"
                  stroke="#8A2BE2"
                  strokeWidth={2}
                  dot={{ r: 2, fill: "#8A2BE2" }}
                  activeDot={{ r: 6, fill: "#8A2BE2", stroke: "#8A2BE2", strokeWidth: 2 }}
                  name="Active Users"
                />
                <Line
                  type="monotone"
                  dataKey="fees"
                  stroke="#4CAF50"
                  strokeWidth={2}
                  dot={{ r: 2, fill: "#4CAF50" }}
                  activeDot={{ r: 6, fill: "#4CAF50", stroke: "#4CAF50", strokeWidth: 2 }}
                  name="Fees Collected"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
