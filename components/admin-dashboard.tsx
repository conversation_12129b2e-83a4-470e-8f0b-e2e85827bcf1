"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
// Badge import removed as unused
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { 
  Users, 
  DollarSign, 
  Activity, 
  AlertTriangle, 
  Shield, 
  Settings,
  TrendingUp,
  Database,
  Server,
  Eye
} from "lucide-react"

interface SystemMetric {
  label: string
  value: string | number
  change?: number
  status: 'good' | 'warning' | 'critical'
}

const systemMetrics: SystemMetric[] = [
  { label: "Total Users", value: "12,543", change: 8.2, status: "good" },
  { label: "Active Tokens", value: "1,247", change: 12.5, status: "good" },
  { label: "Total Volume (24h)", value: "$2.4M", change: -3.1, status: "warning" },
  { label: "Platform Revenue", value: "$45.2K", change: 15.7, status: "good" },
  { label: "System Uptime", value: "99.97%", status: "good" },
  { label: "Pending Reviews", value: "23", status: "warning" },
]

const recentActivity = [
  { id: 1, type: "token_launch", user: "0x1234...5678", action: "Launched token MOON", timestamp: "2 minutes ago" },
  { id: 2, type: "large_trade", user: "0xabcd...efgh", action: "Traded $50K DOGE", timestamp: "5 minutes ago" },
  { id: 3, type: "user_report", user: "0x9876...5432", action: "Reported suspicious activity", timestamp: "8 minutes ago" },
  { id: 4, type: "proposal", user: "0xdef0...1234", action: "Created governance proposal", timestamp: "12 minutes ago" },
  { id: 5, type: "token_launch", user: "0x5555...7777", action: "Launched token ROCKET", timestamp: "15 minutes ago" },
]

export function AdminDashboard() {
  const [selectedTab, setSelectedTab] = useState("overview")

  // getStatusColor function removed as unused

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'token_launch': return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'large_trade': return <DollarSign className="h-4 w-4 text-blue-500" />
      case 'user_report': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'proposal': return <Users className="h-4 w-4 text-purple-500" />
      default: return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
          <p className="text-white/70">Platform management and monitoring</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button className="bg-doge hover:bg-doge/90" size="sm">
            <Shield className="h-4 w-4 mr-2" />
            Security
          </Button>
        </div>
      </div>

      {/* System Status Alert */}
      <Card className="glass-card border-yellow-500/20 bg-yellow-500/5">
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            <div>
              <p className="text-white font-semibold">System Maintenance Scheduled</p>
              <p className="text-white/70 text-sm">Planned maintenance window: Tomorrow 2:00 AM - 4:00 AM UTC</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {systemMetrics.map((metric, index) => (
          <Card key={index} className="glass-card border-white/5">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-white/70">{metric.label}</p>
                  <p className="text-2xl font-bold text-white">{metric.value}</p>
                  {metric.change !== undefined && (
                    <p className={`text-sm ${metric.change >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {metric.change >= 0 ? '+' : ''}{metric.change}% from last week
                    </p>
                  )}
                </div>
                <div className={`w-3 h-3 rounded-full ${
                  metric.status === 'good' ? 'bg-green-500' : 
                  metric.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                }`} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="tokens">Tokens</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <Card className="glass-card border-white/5">
              <CardHeader>
                <CardTitle className="text-white">Recent Activity</CardTitle>
                <CardDescription className="text-white/70">
                  Latest platform events and user actions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center space-x-3">
                      {getActivityIcon(activity.type)}
                      <div className="flex-1">
                        <p className="text-sm text-white">{activity.action}</p>
                        <p className="text-xs text-white/70">{activity.user} • {activity.timestamp}</p>
                      </div>
                      <Button size="sm" variant="outline">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* System Health */}
            <Card className="glass-card border-white/5">
              <CardHeader>
                <CardTitle className="text-white">System Health</CardTitle>
                <CardDescription className="text-white/70">
                  Real-time system performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-white/70">CPU Usage</span>
                    <span className="text-white">45%</span>
                  </div>
                  <Progress value={45} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-white/70">Memory Usage</span>
                    <span className="text-white">67%</span>
                  </div>
                  <Progress value={67} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-white/70">Database Load</span>
                    <span className="text-white">23%</span>
                  </div>
                  <Progress value={23} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-white/70">Network I/O</span>
                    <span className="text-white">89%</span>
                  </div>
                  <Progress value={89} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card className="glass-card border-white/5">
            <CardContent className="p-6">
              <div className="text-center">
                <Users className="h-12 w-12 text-doge mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">User Management</h3>
                <p className="text-white/70 mb-4">Manage user accounts, permissions, and activity</p>
                <Button className="bg-doge hover:bg-doge/90">
                  View All Users
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tokens" className="space-y-4">
          <Card className="glass-card border-white/5">
            <CardContent className="p-6">
              <div className="text-center">
                <Database className="h-12 w-12 text-doge mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">Token Management</h3>
                <p className="text-white/70 mb-4">Review, approve, and manage token listings</p>
                <Button className="bg-doge hover:bg-doge/90">
                  Manage Tokens
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <Card className="glass-card border-white/5">
            <CardContent className="p-6">
              <div className="text-center">
                <Server className="h-12 w-12 text-doge mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">System Configuration</h3>
                <p className="text-white/70 mb-4">Configure platform settings and parameters</p>
                <Button className="bg-doge hover:bg-doge/90">
                  System Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
