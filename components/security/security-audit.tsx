"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Eye, 
  Lock,
  Activity,
  Clock,
  User,
  Globe
} from "lucide-react"
import { AuditLogger, type AuditEvent } from "@/lib/security"

interface SecurityMetrics {
  totalEvents: number
  criticalEvents: number
  warningEvents: number
  infoEvents: number
  lastAuditTime: number
}

interface SecurityCheck {
  id: string
  name: string
  status: 'pass' | 'warning' | 'fail'
  description: string
  lastChecked: number
}

export function SecurityAudit() {
  const [auditEvents, setAuditEvents] = useState<AuditEvent[]>([])
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    totalEvents: 0,
    criticalEvents: 0,
    warningEvents: 0,
    infoEvents: 0,
    lastAuditTime: Date.now()
  })
  const [securityChecks, setSecurityChecks] = useState<SecurityCheck[]>([])

  useEffect(() => {
    // Load audit events
    const events = AuditLogger.getEvents()
    setAuditEvents(events)
    
    // Calculate metrics
    setMetrics({
      totalEvents: events.length,
      criticalEvents: events.filter(e => e.action.includes('CRITICAL')).length,
      warningEvents: events.filter(e => e.action.includes('WARNING')).length,
      infoEvents: events.filter(e => e.action.includes('INFO')).length,
      lastAuditTime: Date.now()
    })

    // Perform security checks
    performSecurityChecks()
  }, [])

  const performSecurityChecks = () => {
    const checks: SecurityCheck[] = [
      {
        id: 'https',
        name: 'HTTPS Enabled',
        status: window.location.protocol === 'https:' ? 'pass' : 'fail',
        description: 'Ensures secure communication',
        lastChecked: Date.now()
      },
      {
        id: 'csp',
        name: 'Content Security Policy',
        status: document.querySelector('meta[http-equiv="Content-Security-Policy"]') ? 'pass' : 'warning',
        description: 'Prevents XSS attacks',
        lastChecked: Date.now()
      },
      {
        id: 'session',
        name: 'Secure Session Storage',
        status: typeof Storage !== 'undefined' ? 'pass' : 'warning',
        description: 'Secure client-side storage',
        lastChecked: Date.now()
      },
      {
        id: 'wallet',
        name: 'Wallet Security',
        status: typeof window !== 'undefined' && window.ethereum ? 'pass' : 'warning',
        description: 'Secure wallet integration',
        lastChecked: Date.now()
      }
    ]
    
    setSecurityChecks(checks)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-400" />
      case 'fail':
        return <XCircle className="h-4 w-4 text-red-400" />
      default:
        return <Activity className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return 'bg-green-500/20 text-green-400'
      case 'warning':
        return 'bg-yellow-500/20 text-yellow-400'
      case 'fail':
        return 'bg-red-500/20 text-red-400'
      default:
        return 'bg-gray-500/20 text-gray-400'
    }
  }

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  const clearAuditLog = () => {
    AuditLogger.clear()
    setAuditEvents([])
    setMetrics(prev => ({ ...prev, totalEvents: 0, criticalEvents: 0, warningEvents: 0, infoEvents: 0 }))
  }

  return (
    <div className="space-y-6">
      {/* Security Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-400" />
              <div>
                <p className="text-sm text-white/60">Total Events</p>
                <p className="text-xl font-bold text-white">{metrics.totalEvents}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-400" />
              <div>
                <p className="text-sm text-white/60">Critical</p>
                <p className="text-xl font-bold text-white">{metrics.criticalEvents}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-400" />
              <div>
                <p className="text-sm text-white/60">Warnings</p>
                <p className="text-xl font-bold text-white">{metrics.warningEvents}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <div>
                <p className="text-sm text-white/60">Info</p>
                <p className="text-xl font-bold text-white">{metrics.infoEvents}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="checks" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="checks">Security Checks</TabsTrigger>
          <TabsTrigger value="events">Audit Events</TabsTrigger>
          <TabsTrigger value="monitoring">Real-time Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="checks" className="space-y-4">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                Security Checks
              </CardTitle>
              <CardDescription>
                Automated security validation results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {securityChecks.map((check) => (
                  <div key={check.id} className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(check.status)}
                      <div>
                        <p className="font-medium text-white">{check.name}</p>
                        <p className="text-sm text-white/60">{check.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge className={getStatusColor(check.status)}>
                        {check.status.toUpperCase()}
                      </Badge>
                      <p className="text-xs text-white/40 mt-1">
                        {formatTimestamp(check.lastChecked)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button onClick={performSecurityChecks} variant="outline" size="sm">
                  <Activity className="h-4 w-4 mr-2" />
                  Run Security Checks
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card className="glass-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Audit Events
                  </CardTitle>
                  <CardDescription>
                    Security-related events and activities
                  </CardDescription>
                </div>
                <Button onClick={clearAuditLog} variant="outline" size="sm">
                  Clear Log
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {auditEvents.length === 0 ? (
                  <p className="text-white/60 text-center py-8">No audit events recorded</p>
                ) : (
                  auditEvents.slice(-20).reverse().map((event, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-white/5">
                      <Clock className="h-4 w-4 text-white/40" />
                      <div className="flex-1">
                        <p className="font-medium text-white">{event.action}</p>
                        <p className="text-sm text-white/60">{formatTimestamp(event.timestamp)}</p>
                      </div>
                      {event.details && (
                        <Badge variant="outline" className="text-xs">
                          {Object.keys(event.details).length} details
                        </Badge>
                      )}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Real-time Security Monitoring
              </CardTitle>
              <CardDescription>
                Live security status and monitoring
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 rounded-lg bg-green-500/10">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-white">Security Monitoring Active</span>
                  </div>
                  <Badge className="bg-green-500/20 text-green-400">ONLINE</Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-3 rounded-lg bg-white/5">
                    <p className="text-sm text-white/60">Last Check</p>
                    <p className="font-medium text-white">{formatTimestamp(metrics.lastAuditTime)}</p>
                  </div>
                  <div className="p-3 rounded-lg bg-white/5">
                    <p className="text-sm text-white/60">Status</p>
                    <p className="font-medium text-green-400">Secure</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
