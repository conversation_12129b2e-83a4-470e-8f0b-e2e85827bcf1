"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { CacheManager, apiCache, priceCache, chartCache, userCache, CacheWarmer } from '@/lib/cache-manager'

interface CacheContextValue {
  apiCache: CacheManager
  priceCache: CacheManager
  chartCache: CacheManager
  userCache: CacheManager
  clearAllCaches: () => void
  getCacheStats: () => Record<string, any>
  warmCaches: () => Promise<void>
}

const CacheContext = createContext<CacheContextValue | null>(null)

interface CacheProviderProps {
  children: ReactNode
}

export function CacheProvider({ children }: CacheProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    // Initialize cache warming on mount
    const initializeCaches = async () => {
      try {
        const warmer = CacheWarmer.getInstance()
        
        // Warm critical caches
        await Promise.allSettled([
          warmer.warmPriceData(),
          warmer.warmChartData(),
        ])
        
        console.log('Cache warming completed')
      } catch (error) {
        console.warn('Cache warming failed:', error)
      } finally {
        setIsInitialized(true)
      }
    }

    initializeCaches()

    // Set up periodic cache cleanup
    const cleanupInterval = setInterval(() => {
      // Clean up expired entries
      const caches = [apiCache, priceCache, chartCache, userCache]
      caches.forEach(cache => {
        const keys = cache.keys()
        keys.forEach(key => {
          // This will automatically remove expired entries
          cache.get(key)
        })
      })
    }, 60 * 1000) // Every minute

    return () => {
      clearInterval(cleanupInterval)
    }
  }, [])

  const clearAllCaches = () => {
    apiCache.clear()
    priceCache.clear()
    chartCache.clear()
    userCache.clear()
    console.log('All caches cleared')
  }

  const getCacheStats = () => {
    return {
      api: apiCache.getStats(),
      price: priceCache.getStats(),
      chart: chartCache.getStats(),
      user: userCache.getStats(),
    }
  }

  const warmCaches = async () => {
    const warmer = CacheWarmer.getInstance()
    await Promise.allSettled([
      warmer.warmPriceData(),
      warmer.warmChartData(),
    ])
  }

  const value: CacheContextValue = {
    apiCache,
    priceCache,
    chartCache,
    userCache,
    clearAllCaches,
    getCacheStats,
    warmCaches,
  }

  return (
    <CacheContext.Provider value={value}>
      {children}
      {process.env.NODE_ENV === 'development' && <CacheDebugPanel />}
    </CacheContext.Provider>
  )
}

export function useCache() {
  const context = useContext(CacheContext)
  if (!context) {
    throw new Error('useCache must be used within a CacheProvider')
  }
  return context
}

// Debug panel for development
function CacheDebugPanel() {
  const [isOpen, setIsOpen] = useState(false)
  const [stats, setStats] = useState<Record<string, any>>({})
  const { getCacheStats, clearAllCaches, warmCaches } = useCache()

  useEffect(() => {
    if (!isOpen) return

    const updateStats = () => {
      setStats(getCacheStats())
    }

    updateStats()
    const interval = setInterval(updateStats, 1000)

    return () => clearInterval(interval)
  }, [isOpen, getCacheStats])

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 bg-black/80 text-white px-3 py-2 rounded-lg text-xs z-50"
        style={{ fontSize: '10px' }}
      >
        Cache Debug
      </button>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/90 text-white p-4 rounded-lg text-xs z-50 max-w-sm">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-semibold">Cache Debug Panel</h3>
        <button
          onClick={() => setIsOpen(false)}
          className="text-white/60 hover:text-white"
        >
          ×
        </button>
      </div>

      <div className="space-y-3">
        {Object.entries(stats).map(([cacheName, cacheStats]) => (
          <div key={cacheName} className="border border-white/20 rounded p-2">
            <div className="font-medium capitalize mb-1">{cacheName} Cache</div>
            <div className="text-white/70 space-y-1">
              <div>Size: {cacheStats.size}/{cacheStats.maxSize}</div>
              <div>Strategy: {cacheStats.strategy}</div>
              <div>Total Accesses: {cacheStats.totalAccesses}</div>
              <div>Avg Age: {Math.round(cacheStats.averageAge / 1000)}s</div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex gap-2 mt-3">
        <button
          onClick={clearAllCaches}
          className="bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-xs"
        >
          Clear All
        </button>
        <button
          onClick={warmCaches}
          className="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs"
        >
          Warm Caches
        </button>
      </div>
    </div>
  )
}

// HOC for cache-aware components
export function withCache<P extends object>(
  Component: React.ComponentType<P>
) {
  return function CachedComponent(props: P) {
    return (
      <CacheProvider>
        <Component {...props} />
      </CacheProvider>
    )
  }
}

// Cache invalidation utilities
export class CacheInvalidator {
  static invalidateUserData(address: string) {
    userCache.remove(`user:${address}`)
  }

  static invalidatePriceData(token?: string) {
    if (token) {
      priceCache.remove(`price:${token}`)
    } else {
      // Clear all price data
      const keys = priceCache.keys().filter(key => key.startsWith('price:'))
      keys.forEach(key => priceCache.remove(key))
    }
  }

  static invalidateChartData(token?: string, timeframe?: string) {
    if (token && timeframe) {
      chartCache.remove(`chart:${token}:${timeframe}`)
    } else if (token) {
      // Clear all chart data for token
      const keys = chartCache.keys().filter(key => key.startsWith(`chart:${token}:`))
      keys.forEach(key => chartCache.remove(key))
    } else {
      // Clear all chart data
      const keys = chartCache.keys().filter(key => key.startsWith('chart:'))
      keys.forEach(key => chartCache.remove(key))
    }
  }

  static invalidateApiData(pattern?: string) {
    if (pattern) {
      const keys = apiCache.keys().filter(key => key.includes(pattern))
      keys.forEach(key => apiCache.remove(key))
    } else {
      apiCache.clear()
    }
  }
}

// React hook for cache invalidation
export function useCacheInvalidation() {
  return {
    invalidateUserData: CacheInvalidator.invalidateUserData,
    invalidatePriceData: CacheInvalidator.invalidatePriceData,
    invalidateChartData: CacheInvalidator.invalidateChartData,
    invalidateApiData: CacheInvalidator.invalidateApiData,
  }
}
