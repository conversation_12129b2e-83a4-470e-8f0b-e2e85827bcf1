"use client"

import React from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

export interface TimeframeOption {
  value: string
  label: string
  description?: string
}

export const DEFAULT_TIMEFRAMES: TimeframeOption[] = [
  { value: "24h", label: "24H", description: "Last 24 hours" },
  { value: "7d", label: "7D", description: "Last 7 days" },
  { value: "30d", label: "30D", description: "Last 30 days" },
  { value: "90d", label: "90D", description: "Last 90 days" },
]

export const EXTENDED_TIMEFRAMES: TimeframeOption[] = [
  ...DEFAULT_TIMEFRAMES,
  { value: "1y", label: "1Y", description: "Last year" },
  { value: "all", label: "ALL", description: "All time" },
]

interface TimeframeSelectorProps {
  value: string
  onValueChange: (value: string) => void
  options?: TimeframeOption[]
  variant?: "select" | "buttons"
  className?: string
  size?: "sm" | "md" | "lg"
}

export function TimeframeSelector({
  value,
  onValueChange,
  options = DEFAULT_TIMEFRAMES,
  variant = "select",
  className,
  size = "md"
}: TimeframeSelectorProps) {
  // Map our size prop to Button component's accepted sizes
  const buttonSize = size === "md" ? "default" : size;

  if (variant === "buttons") {
    return (
      <div className={cn("flex gap-1", className)}>
        {options.map((option) => (
          <Button
            key={option.value}
            variant={value === option.value ? "default" : "outline"}
            size={buttonSize}
            onClick={() => onValueChange(option.value)}
            className={cn(
              "glass-button transition-all duration-200",
              value === option.value && "bg-doge/20 text-doge border-doge/30",
              size === "sm" && "px-2 py-1 text-xs",
              size === "md" && "px-3 py-2 text-sm",
              size === "lg" && "px-4 py-3 text-base"
            )}
            title={option.description}
          >
            {option.label}
          </Button>
        ))}
      </div>
    )
  }

  return (
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger className={cn("glass w-[120px]", className)}>
        <SelectValue placeholder="Select timeframe" />
      </SelectTrigger>
      <SelectContent className="glass">
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            <div className="flex flex-col">
              <span>{option.label}</span>
              {option.description && (
                <span className="text-xs text-white/60">{option.description}</span>
              )}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

/**
 * Hook for managing timeframe state with localStorage persistence
 */
export function useTimeframe(defaultValue: string = "7d", storageKey?: string) {
  const [timeframe, setTimeframe] = React.useState(() => {
    if (typeof window !== "undefined" && storageKey) {
      const stored = localStorage.getItem(storageKey)
      return stored || defaultValue
    }
    return defaultValue
  })

  const handleTimeframeChange = (newTimeframe: string) => {
    setTimeframe(newTimeframe)
    if (typeof window !== "undefined" && storageKey) {
      localStorage.setItem(storageKey, newTimeframe)
    }
  }

  return [timeframe, handleTimeframeChange] as const
}

/**
 * Utility function to get timeframe display info
 */
export function getTimeframeInfo(timeframe: string): {
  label: string
  description: string
  days: number
  pointsPerDay: number
} {
  switch (timeframe) {
    case "24h":
      return {
        label: "24H",
        description: "Last 24 hours",
        days: 1,
        pointsPerDay: 24
      }
    case "7d":
      return {
        label: "7D",
        description: "Last 7 days",
        days: 7,
        pointsPerDay: 1
      }
    case "30d":
      return {
        label: "30D",
        description: "Last 30 days",
        days: 30,
        pointsPerDay: 1
      }
    case "90d":
      return {
        label: "90D",
        description: "Last 90 days",
        days: 90,
        pointsPerDay: 1
      }
    case "1y":
      return {
        label: "1Y",
        description: "Last year",
        days: 365,
        pointsPerDay: 1
      }
    case "all":
      return {
        label: "ALL",
        description: "All time",
        days: 1000,
        pointsPerDay: 1
      }
    default:
      return {
        label: "7D",
        description: "Last 7 days",
        days: 7,
        pointsPerDay: 1
      }
  }
}

/**
 * Utility function to format dates based on timeframe
 */
export function formatDateForTimeframe(date: Date, timeframe: string): string {
  const info = getTimeframeInfo(timeframe)
  
  if (info.pointsPerDay > 1) {
    // Hourly data - show time
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  } else if (info.days <= 30) {
    // Daily data for short periods - show month/day
    return date.toLocaleDateString([], { month: "short", day: "numeric" })
  } else {
    // Longer periods - show month/year
    return date.toLocaleDateString([], { month: "short", year: "2-digit" })
  }
}


