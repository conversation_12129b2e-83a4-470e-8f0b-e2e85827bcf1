"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Heart, MessageCircle, Share2, TrendingUp, Users, Zap } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

interface Post {
  id: string
  author: {
    name: string
    username: string
    avatar?: string
    verified: boolean
    role?: string
  }
  content: string
  timestamp: Date
  likes: number
  comments: number
  shares: number
  isLiked: boolean
  tags?: string[]
  tokenMention?: {
    symbol: string
    price: number
    change: number
  }
}

const mockPosts: Post[] = [
  {
    id: "1",
    author: {
      name: "<PERSON><PERSON> Enthusiast",
      username: "dogehodler",
      verified: true,
      role: "Community Leader"
    },
    content: "Just launched my new memecoin $PAWSOME! 🐕 The community response has been incredible. Already 500+ holders in the first hour! To the moon! 🚀",
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    likes: 142,
    comments: 23,
    shares: 8,
    isLiked: false,
    tags: ["launch", "memecoin"],
    tokenMention: {
      symbol: "PAWSOME",
      price: 0.00012,
      change: 245.6
    }
  },
  {
    id: "2",
    author: {
      name: "Crypto Analyst",
      username: "cryptoanalyst",
      verified: true,
      role: "Analyst"
    },
    content: "Market analysis: The memecoin sector is showing strong momentum. $DOGE leading the charge with 15% gains today. Keep an eye on emerging tokens with strong communities.",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    likes: 89,
    comments: 15,
    shares: 12,
    isLiked: true,
    tags: ["analysis", "market"],
    tokenMention: {
      symbol: "DOGE",
      price: 0.08,
      change: 15.2
    }
  },
  {
    id: "3",
    author: {
      name: "Meme Lord",
      username: "memelord420",
      verified: false
    },
    content: "When you buy the dip but it keeps dipping 😅 Diamond hands though! 💎🙌 This is the way.",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
    likes: 234,
    comments: 45,
    shares: 18,
    isLiked: false,
    tags: ["meme", "hodl"]
  }
]

export function SocialFeed() {
  const [posts, setPosts] = useState(mockPosts)
  const [newPost, setNewPost] = useState("")
  const [isPosting, setIsPosting] = useState(false)

  const handleLike = (postId: string) => {
    setPosts(posts.map(post => 
      post.id === postId 
        ? { 
            ...post, 
            isLiked: !post.isLiked,
            likes: post.isLiked ? post.likes - 1 : post.likes + 1
          }
        : post
    ))
  }

  const handlePost = async () => {
    if (!newPost.trim()) return
    
    setIsPosting(true)
    
    // Simulate posting delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const post: Post = {
      id: Date.now().toString(),
      author: {
        name: "You",
        username: "yourhandle",
        verified: false
      },
      content: newPost,
      timestamp: new Date(),
      likes: 0,
      comments: 0,
      shares: 0,
      isLiked: false
    }
    
    setPosts([post, ...posts])
    setNewPost("")
    setIsPosting(false)
  }

  const getRoleColor = (role?: string) => {
    switch (role) {
      case 'Community Leader': return 'bg-purple-500'
      case 'Analyst': return 'bg-blue-500'
      case 'Developer': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">Community Feed</h1>
        <p className="text-white/70">Connect with the PawPumps community</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-3 gap-4">
        <Card className="glass-card border-white/5">
          <CardContent className="p-4 text-center">
            <Users className="h-6 w-6 text-doge mx-auto mb-2" />
            <p className="text-2xl font-bold text-white">12.5K</p>
            <p className="text-sm text-white/70">Members</p>
          </CardContent>
        </Card>
        
        <Card className="glass-card border-white/5">
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-6 w-6 text-doge mx-auto mb-2" />
            <p className="text-2xl font-bold text-white">1.2K</p>
            <p className="text-sm text-white/70">Active Today</p>
          </CardContent>
        </Card>
        
        <Card className="glass-card border-white/5">
          <CardContent className="p-4 text-center">
            <Zap className="h-6 w-6 text-doge mx-auto mb-2" />
            <p className="text-2xl font-bold text-white">89</p>
            <p className="text-sm text-white/70">Posts Today</p>
          </CardContent>
        </Card>
      </div>

      {/* Create Post */}
      <Card className="glass-card border-white/5">
        <CardContent className="p-6">
          <div className="space-y-4">
            <Textarea
              placeholder="What's happening in the memecoin world?"
              value={newPost}
              onChange={(e) => setNewPost(e.target.value)}
              className="bg-white/5 border-white/10 text-white min-h-[100px]"
            />
            <div className="flex justify-between items-center">
              <div className="text-sm text-white/70">
                {newPost.length}/280 characters
              </div>
              <Button 
                onClick={handlePost}
                disabled={!newPost.trim() || isPosting}
                className="bg-doge hover:bg-doge/90"
              >
                {isPosting ? "Posting..." : "Post"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Posts */}
      <div className="space-y-4">
        {posts.map((post) => (
          <Card key={post.id} className="glass-card border-white/5">
            <CardHeader className="pb-3">
              <div className="flex items-start space-x-3">
                <Avatar>
                  <AvatarImage src={post.author.avatar} />
                  <AvatarFallback className="bg-doge text-black">
                    {post.author.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-semibold text-white">{post.author.name}</h4>
                    {post.author.verified && (
                      <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                    {post.author.role && (
                      <Badge className={`text-xs ${getRoleColor(post.author.role)}`}>
                        {post.author.role}
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-white/70">@{post.author.username}</p>
                  <p className="text-sm text-white/50">
                    {formatDistanceToNow(post.timestamp, { addSuffix: true })}
                  </p>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <p className="text-white mb-4">{post.content}</p>
              
              {/* Token Mention */}
              {post.tokenMention && (
                <Card className="bg-white/5 border-white/10 mb-4">
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-semibold text-white">${post.tokenMention.symbol}</p>
                        <p className="text-sm text-white/70">${post.tokenMention.price}</p>
                      </div>
                      <div className="text-right">
                        <p className={`text-sm font-semibold ${
                          post.tokenMention.change >= 0 ? 'text-green-500' : 'text-red-500'
                        }`}>
                          {post.tokenMention.change >= 0 ? '+' : ''}{post.tokenMention.change.toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
              
              {/* Tags */}
              {post.tags && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {post.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-doge border-doge/50">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              )}
              
              {/* Actions */}
              <div className="flex items-center space-x-6 text-white/70">
                <button 
                  onClick={() => handleLike(post.id)}
                  className={`flex items-center space-x-2 hover:text-red-500 transition-colors ${
                    post.isLiked ? 'text-red-500' : ''
                  }`}
                >
                  <Heart className={`h-4 w-4 ${post.isLiked ? 'fill-current' : ''}`} />
                  <span>{post.likes}</span>
                </button>
                
                <button className="flex items-center space-x-2 hover:text-blue-500 transition-colors">
                  <MessageCircle className="h-4 w-4" />
                  <span>{post.comments}</span>
                </button>
                
                <button className="flex items-center space-x-2 hover:text-green-500 transition-colors">
                  <Share2 className="h-4 w-4" />
                  <span>{post.shares}</span>
                </button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
