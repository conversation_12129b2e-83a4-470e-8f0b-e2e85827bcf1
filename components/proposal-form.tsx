"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { FileText, Users, Clock } from "lucide-react"

const proposalSchema = z.object({
  title: z.string().min(10, "Title must be at least 10 characters").max(100, "Title must be less than 100 characters"),
  description: z.string().min(50, "Description must be at least 50 characters").max(1000, "Description must be less than 1000 characters"),
  category: z.string().min(1, "Please select a category"),
  votingPeriod: z.number().min(1, "Voting period must be at least 1 day").max(30, "Voting period cannot exceed 30 days"),
  minimumQuorum: z.number().min(1, "Minimum quorum must be at least 1%").max(100, "Minimum quorum cannot exceed 100%"),
  executionDelay: z.number().min(0, "Execution delay cannot be negative").max(7, "Execution delay cannot exceed 7 days"),
})

type ProposalFormData = z.infer<typeof proposalSchema>

const categories = [
  { value: "economics", label: "Economics & Tokenomics" },
  { value: "governance", label: "Governance & Protocol" },
  { value: "technical", label: "Technical Upgrades" },
  { value: "partnerships", label: "Partnerships & Integrations" },
  { value: "community", label: "Community & Marketing" },
  { value: "treasury", label: "Treasury Management" },
]

export function ProposalForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)

  const form = useForm<ProposalFormData>({
    resolver: zodResolver(proposalSchema),
    defaultValues: {
      title: "",
      description: "",
      category: "",
      votingPeriod: 7,
      minimumQuorum: 10,
      executionDelay: 2,
    },
  })

  const onSubmit = async (data: ProposalFormData) => {
    setIsSubmitting(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      console.log("Proposal submitted:", data)
      setSubmitSuccess(true)
      form.reset()
    } catch (error) {
      console.error("Error submitting proposal:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (submitSuccess) {
    return (
      <Card className="glass-card border-white/5 max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <div className="mb-4">
            <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileText className="w-8 h-8 text-green-500" />
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">Proposal Submitted!</h2>
            <p className="text-white/70 mb-6">
              Your proposal has been submitted successfully and is now under review. 
              The community will be able to vote on it once it's approved.
            </p>
            <Button 
              onClick={() => setSubmitSuccess(false)}
              className="bg-doge hover:bg-doge/90"
            >
              Submit Another Proposal
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">Create New Proposal</h1>
        <p className="text-white/70">Submit a proposal for the community to vote on</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Info Cards */}
        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="h-5 w-5 text-doge" />
              <h3 className="font-semibold text-white">Community Review</h3>
            </div>
            <p className="text-sm text-white/70">
              Proposals are reviewed by the community before voting begins
            </p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="h-5 w-5 text-doge" />
              <h3 className="font-semibold text-white">Voting Period</h3>
            </div>
            <p className="text-sm text-white/70">
              Set how long the community has to vote on your proposal
            </p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-2">
              <FileText className="h-5 w-5 text-doge" />
              <h3 className="font-semibold text-white">Execution</h3>
            </div>
            <p className="text-sm text-white/70">
              Approved proposals are automatically executed after a delay period
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Form */}
      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Proposal Details</CardTitle>
          <CardDescription className="text-white/70">
            Provide detailed information about your proposal
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Proposal Title</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter a clear, descriptive title"
                        className="bg-white/5 border-white/10 text-white"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription className="text-white/60">
                      A concise title that summarizes your proposal
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Category</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="bg-white/5 border-white/10 text-white">
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription className="text-white/60">
                      Choose the category that best fits your proposal
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Provide a detailed description of your proposal, including rationale, implementation details, and expected outcomes..."
                        className="bg-white/5 border-white/10 text-white min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription className="text-white/60">
                      Detailed explanation of what you're proposing and why
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="votingPeriod"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Voting Period (days)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number"
                          min="1"
                          max="30"
                          className="bg-white/5 border-white/10 text-white"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="minimumQuorum"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Minimum Quorum (%)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number"
                          min="1"
                          max="100"
                          className="bg-white/5 border-white/10 text-white"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="executionDelay"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Execution Delay (days)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number"
                          min="0"
                          max="7"
                          className="bg-white/5 border-white/10 text-white"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end space-x-4">
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => form.reset()}
                >
                  Reset
                </Button>
                <Button 
                  type="submit" 
                  disabled={isSubmitting}
                  className="bg-doge hover:bg-doge/90"
                >
                  {isSubmitting ? "Submitting..." : "Submit Proposal"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
