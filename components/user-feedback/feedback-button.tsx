"use client"

import { useState, useEffect } from "react"
import { MessageSquare } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { FeedbackForm } from "./feedback-form"

export function FeedbackButton() {
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    const handleOpenFeedback = () => setIsOpen(true)
    window.addEventListener("open-feedback", handleOpenFeedback)

    return () => {
      window.removeEventListener("open-feedback", handleOpenFeedback)
    }
  }, [])

  return (
    <>
      <Button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 z-30 rounded-full shadow-lg bg-doge hover:bg-doge/90 text-black"
        size="icon"
        aria-label="Open feedback form"
      >
        <MessageSquare className="h-5 w-5" />
      </Button>

      {isOpen && <FeedbackForm onClose={() => setIsOpen(false)} />}
    </>
  )
}
