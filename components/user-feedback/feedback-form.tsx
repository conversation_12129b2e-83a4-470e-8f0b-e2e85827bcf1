"use client"

import type React from "react"
import { useState } from "react"
import { X } from "lucide-react"
import { saveFeedback } from "@/utils/feedback-storage"

type FeedbackFormProps = {
  onClose: () => void
}

type FeedbackType = "bug" | "feature" | "ux" | "other"

export function FeedbackForm({ onClose }: FeedbackFormProps) {
  const [formData, setFormData] = useState({
    type: "bug" as FeedbackType,
    message: "",
    email: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleTypeChange = (type: FeedbackType) => {
    setFormData((prev) => ({ ...prev, type }))
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Get user metadata
      const metadata = JSON.stringify({
        url: window.location.href,
        userAgent: navigator.userAgent,
        screenSize: `${window.innerWidth}x${window.innerHeight}`,
        timestamp: new Date().toISOString(),
      })

      // Save feedback
      saveFeedback({
        ...formData,
        metadata,
      })

      setIsSubmitted(true)
      setTimeout(() => {
        onClose()
      }, 2000)
    } catch (error) {
      console.error("Error saving feedback:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="glass-card max-w-md w-full p-6 relative rounded-lg">
        <button
          className="absolute right-4 top-4 text-white/80 hover:text-white"
          onClick={onClose}
          aria-label="Close feedback form"
        >
          <X className="h-5 w-5" />
        </button>

        {isSubmitted ? (
          <div className="text-center py-8">
            <h3 className="text-lg font-semibold mb-2 text-white">Thank you for your feedback!</h3>
            <p className="text-gray-300">Your feedback has been submitted successfully.</p>
          </div>
        ) : (
          <>
            <h3 className="text-xl font-semibold mb-6 text-white">Share Your Feedback</h3>

            <form onSubmit={handleSubmit} className="space-y-5">
              <div>
                <label className="block text-sm font-medium mb-2 text-white">Feedback Type</label>
                <div className="grid grid-cols-4 gap-2">
                  <button
                    type="button"
                    onClick={() => handleTypeChange("bug")}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                      formData.type === "bug"
                        ? "bg-doge text-black"
                        : "bg-black/40 text-white hover:bg-black/60 backdrop-blur-sm"
                    }`}
                  >
                    Bug
                  </button>
                  <button
                    type="button"
                    onClick={() => handleTypeChange("feature")}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                      formData.type === "feature"
                        ? "bg-doge text-black"
                        : "bg-black/40 text-white hover:bg-black/60 backdrop-blur-sm"
                    }`}
                  >
                    Feature
                  </button>
                  <button
                    type="button"
                    onClick={() => handleTypeChange("ux")}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                      formData.type === "ux"
                        ? "bg-doge text-black"
                        : "bg-black/40 text-white hover:bg-black/60 backdrop-blur-sm"
                    }`}
                  >
                    UX
                  </button>
                  <button
                    type="button"
                    onClick={() => handleTypeChange("other")}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                      formData.type === "other"
                        ? "bg-doge text-black"
                        : "bg-black/40 text-white hover:bg-black/60 backdrop-blur-sm"
                    }`}
                  >
                    Other
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium mb-2 text-white">
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  rows={4}
                  className="w-full rounded-md bg-black/40 border border-white/10 p-3 text-white placeholder-white/50 backdrop-blur-sm"
                  required
                  placeholder="Please describe the issue you encountered..."
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium mb-2 text-white">
                  Email (optional)
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full rounded-md bg-black/40 border border-white/10 p-3 text-white placeholder-white/50 backdrop-blur-sm"
                  placeholder="<EMAIL>"
                />
                <p className="text-xs text-white/60 mt-1">Provide your email if you'd like us to follow up with you.</p>
              </div>

              <div className="flex justify-end space-x-3 pt-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 rounded-md bg-black/60 text-white hover:bg-black/80 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 rounded-md bg-doge hover:bg-doge/90 text-black font-medium transition-colors"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit Feedback"}
                </button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  )
}
