"use client"

import { Component, type ErrorInfo, type <PERSON>actNode } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Alert<PERSON>riangle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface Props {
  children: ReactNode
  onRetry?: () => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorId: string
}

export class WalletErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { 
      hasError: false, 
      error: null,
      errorId: Math.random().toString(36).substr(2, 9)
    }
  }

  static getDerivedStateFromError(error: Error): State {
    return { 
      hasError: true, 
      error,
      errorId: Math.random().toString(36).substr(2, 9)
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error("Wallet Connection Error:", {
      error: error.message,
      stack: error.stack,
      errorInfo,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      walletType: this.detectWalletType(),
      userAgent: navigator.userAgent
    })
  }

  detectWalletType = (): string => {
    if (typeof window !== 'undefined') {
      if (window.ethereum?.isMetaMask) return 'MetaMask'
      if ((window.ethereum as any)?.isCoinbaseWallet) return 'Coinbase Wallet'
      if (window.ethereum) return 'Unknown Wallet'
    }
    return 'No Wallet Detected'
  }

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null,
      errorId: Math.random().toString(36).substr(2, 9)
    })
    
    if (this.props.onRetry) {
      this.props.onRetry()
    }
  }

  getErrorMessage = (): string => {
    const error = this.state.error
    if (!error) return "Unknown wallet error occurred"

    // Common wallet error patterns
    if (error.message.includes("User rejected")) {
      return "Connection was cancelled. Please try connecting again."
    }
    if (error.message.includes("No Ethereum provider")) {
      return "No wallet detected. Please install MetaMask or another Web3 wallet."
    }
    if (error.message.includes("Chain")) {
      return "Wrong network detected. Please switch to Dogechain Network."
    }
    if (error.message.includes("Unauthorized")) {
      return "Wallet access denied. Please check your wallet permissions."
    }

    return error.message
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return (
        <Card className="glass-card border-yellow-500/20 bg-yellow-500/5">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/20">
                <Wallet className="h-6 w-6 text-yellow-600 dark:text-yellow-500" />
              </div>
            </div>
            <CardTitle className="text-white">Wallet Connection Issue</CardTitle>
            <CardDescription className="text-white/70">
              There was a problem connecting to your wallet.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 rounded-lg bg-black/20 border border-white/10">
              <p className="text-sm text-white/80 mb-2">
                {this.getErrorMessage()}
              </p>
              <p className="text-xs text-white/50">
                Wallet Type: {this.detectWalletType()}
              </p>
              <p className="text-xs text-white/50">
                Error ID: {this.state.errorId}
              </p>
            </div>
            
            <div className="space-y-3">
              <Button
                onClick={this.handleRetry}
                className="w-full pawpumps-button flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Try Connecting Again
              </Button>
              
              <div className="text-center">
                <p className="text-xs text-white/60 mb-2">
                  Need help? Try these steps:
                </p>
                <ul className="text-xs text-white/50 space-y-1">
                  <li>• Make sure your wallet is unlocked</li>
                  <li>• Check you're on the Dogechain Network</li>
                  <li>• Refresh the page and try again</li>
                  <li>• Disable other wallet extensions temporarily</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}
