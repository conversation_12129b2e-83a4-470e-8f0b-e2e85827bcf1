"use client"

import { Component, type ErrorInfo, type <PERSON>actNode } from "react"
import { Database, Refresh<PERSON><PERSON>, <PERSON>ert<PERSON><PERSON>gle, Wifi } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface Props {
  children: ReactNode
  dataSource?: string
  onRetry?: () => void
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorId: string
  retryCount: number
}

export class DataErrorBoundary extends Component<Props, State> {
  private retryTimer: NodeJS.Timeout | null = null

  constructor(props: Props) {
    super(props)
    this.state = { 
      hasError: false, 
      error: null,
      errorId: Math.random().toString(36).substr(2, 9),
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { 
      hasError: true, 
      error,
      errorId: Math.random().toString(36).substr(2, 9)
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error("Data Fetching Error:", {
      dataSource: this.props.dataSource || 'Unknown Source',
      error: error.message,
      stack: error.stack,
      errorInfo,
      errorId: this.state.errorId,
      retryCount: this.state.retryCount,
      timestamp: new Date().toISOString(),
      networkStatus: navigator.onLine ? 'online' : 'offline'
    })
  }

  componentWillUnmount(): void {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer)
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({ 
      hasError: false, 
      error: null,
      errorId: Math.random().toString(36).substr(2, 9),
      retryCount: prevState.retryCount + 1
    }))
    
    if (this.props.onRetry) {
      this.props.onRetry()
    }
  }

  handleAutoRetry = () => {
    if (this.state.retryCount < 3) {
      this.retryTimer = setTimeout(() => {
        this.handleRetry()
      }, 5000)
    }
  }

  getErrorType = (): 'network' | 'server' | 'parsing' | 'unknown' => {
    const error = this.state.error
    if (!error) return 'unknown'

    if (error.message.includes('fetch') || error.message.includes('network')) {
      return 'network'
    }
    if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
      return 'server'
    }
    if (error.message.includes('JSON') || error.message.includes('parse')) {
      return 'parsing'
    }
    return 'unknown'
  }

  getErrorMessage = (): { title: string; description: string; icon: ReactNode } => {
    const errorType = this.getErrorType()
    
    switch (errorType) {
      case 'network':
        return {
          title: 'Connection Problem',
          description: 'Unable to connect to the server. Please check your internet connection.',
          icon: <Wifi className="h-6 w-6 text-blue-500" />
        }
      case 'server':
        return {
          title: 'Server Error',
          description: 'The server is experiencing issues. Please try again in a moment.',
          icon: <Database className="h-6 w-6 text-red-500" />
        }
      case 'parsing':
        return {
          title: 'Data Format Error',
          description: 'The data received is in an unexpected format.',
          icon: <AlertTriangle className="h-6 w-6 text-yellow-500" />
        }
      default:
        return {
          title: 'Data Loading Error',
          description: 'Something went wrong while loading the data.',
          icon: <AlertTriangle className="h-6 w-6 text-red-500" />
        }
    }
  }

  render(): ReactNode {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      const { title, description, icon } = this.getErrorMessage()
      const canAutoRetry = this.state.retryCount < 3 && this.getErrorType() === 'network'

      return (
        <Card className="glass-card border-orange-500/20 bg-orange-500/5">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
                {icon}
              </div>
            </div>
            <CardTitle className="text-white">{title}</CardTitle>
            <CardDescription className="text-white/70">
              {description}
              {this.props.dataSource && (
                <span className="block mt-1 text-sm">
                  Source: {this.props.dataSource}
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 rounded-lg bg-black/20 border border-white/10">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-white/80">Error Details</span>
                <span className="text-xs text-white/50">
                  Attempt {this.state.retryCount + 1}
                </span>
              </div>
              <p className="text-xs text-white/60 mb-1">
                Error ID: {this.state.errorId}
              </p>
              <p className="text-xs text-white/50">
                Network: {navigator.onLine ? 'Online' : 'Offline'}
              </p>
              {this.state.error && (
                <p className="text-xs text-orange-400 mt-2">
                  {this.state.error.message}
                </p>
              )}
            </div>
            
            <div className="flex flex-col gap-3">
              <Button
                onClick={this.handleRetry}
                className="pawpumps-button flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Retry Loading
              </Button>
              
              {canAutoRetry && (
                <Button
                  onClick={this.handleAutoRetry}
                  variant="outline"
                  className="border-white/20 text-white hover:bg-white/10"
                >
                  Auto-retry in 5 seconds
                </Button>
              )}
            </div>

            {this.state.retryCount >= 3 && (
              <div className="text-center p-3 rounded-lg bg-red-500/10 border border-red-500/20">
                <p className="text-sm text-red-400 mb-2">
                  Multiple retry attempts failed
                </p>
                <p className="text-xs text-white/50">
                  Please check your connection or contact support if the problem persists.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}
