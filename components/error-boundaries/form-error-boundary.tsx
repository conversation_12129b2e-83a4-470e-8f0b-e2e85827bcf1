"use client"

import { Component, type ErrorInfo, type <PERSON>actNode } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Alert<PERSON>riangle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface Props {
  children: ReactNode
  formName?: string
  onReset?: () => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorId: string
}

export class FormErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { 
      hasError: false, 
      error: null,
      errorId: Math.random().toString(36).substr(2, 9)
    }
  }

  static getDerivedStateFromError(error: Error): State {
    return { 
      hasError: true, 
      error,
      errorId: Math.random().toString(36).substr(2, 9)
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error("Form Error:", {
      formName: this.props.formName || 'Unknown Form',
      error: error.message,
      stack: error.stack,
      errorInfo,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      formData: this.getFormData()
    })
  }

  getFormData = (): Record<string, any> => {
    try {
      // Attempt to capture form data for debugging (without sensitive info)
      const forms = document.querySelectorAll('form')
      const formData: Record<string, any> = {}
      
      forms.forEach((form, index) => {
        const inputs = form.querySelectorAll('input, select, textarea')
        inputs.forEach((input: any) => {
          if (input.name && !this.isSensitiveField(input.name)) {
            formData[`${input.name}_${index}`] = input.value?.length || 0
          }
        })
      })
      
      return formData
    } catch {
      return {}
    }
  }

  isSensitiveField = (fieldName: string): boolean => {
    const sensitiveFields = ['password', 'private', 'secret', 'key', 'seed']
    return sensitiveFields.some(field => 
      fieldName.toLowerCase().includes(field)
    )
  }

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null,
      errorId: Math.random().toString(36).substr(2, 9)
    })
    
    if (this.props.onReset) {
      this.props.onReset()
    }
  }

  getErrorGuidance = (): string => {
    const error = this.state.error
    if (!error) return "Please try filling out the form again."

    if (error.message.includes("validation")) {
      return "Please check that all required fields are filled correctly."
    }
    if (error.message.includes("network")) {
      return "Network error. Please check your connection and try again."
    }
    if (error.message.includes("timeout")) {
      return "The request timed out. Please try again."
    }
    if (error.message.includes("gas")) {
      return "Transaction failed due to gas issues. Please try with higher gas."
    }

    return "An unexpected error occurred. Please try again."
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return (
        <div className="space-y-4">
          <Alert className="glass border-red-500/20 bg-red-500/5">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <AlertTitle className="text-white">
              {this.props.formName ? `${this.props.formName} Error` : 'Form Error'}
            </AlertTitle>
            <AlertDescription className="text-white/70">
              {this.getErrorGuidance()}
            </AlertDescription>
          </Alert>

          <div className="p-4 rounded-lg bg-black/20 border border-white/10">
            <div className="flex items-center gap-2 mb-2">
              <FileX className="h-4 w-4 text-red-400" />
              <span className="text-sm font-medium text-white">Error Details</span>
            </div>
            <p className="text-xs text-white/60 mb-1">
              Error ID: {this.state.errorId}
            </p>
            {this.state.error && (
              <p className="text-xs text-red-400">
                {this.state.error.message}
              </p>
            )}
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={this.handleRetry}
              className="pawpumps-button flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Reset Form
            </Button>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="border-white/20 text-white hover:bg-white/10"
            >
              Refresh Page
            </Button>
          </div>

          <div className="text-center">
            <p className="text-xs text-white/50">
              If this error persists, please contact support with the Error ID above.
            </p>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}
