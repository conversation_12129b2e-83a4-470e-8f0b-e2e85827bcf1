"use client"

import { Component, type ErrorInfo, type ReactNode } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>resh<PERSON><PERSON>, ArrowLeft, Wallet, TrendingDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { getGlobalErrorHandler, classifyError, ErrorType } from "@/lib/error-handling"

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorId: string
  errorType?: ErrorType
  retryCount: number
}

export class TradingErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorId: Math.random().toString(36).substr(2, 9),
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorId: Math.random().toString(36).substr(2, 9),
      retryCount: 0
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    const appError = classifyError(error, {
      component: 'TradingInterface',
      action: 'trading_operation',
      metadata: {
        errorId: this.state.errorId,
        componentStack: errorInfo.componentStack,
        retryCount: this.state.retryCount,
        userAgent: navigator.userAgent,
        url: window.location.href
      }
    })

    getGlobalErrorHandler().handleError(appError)

    this.setState({ errorType: appError.type })
  }

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null,
      errorId: Math.random().toString(36).substr(2, 9)
    })
  }

  handleGoBack = () => {
    window.history.back()
  }

  render(): ReactNode {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Card className="glass-card border-red-500/20 bg-red-500/5">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-500" />
              </div>
            </div>
            <CardTitle className="text-white">Trading Interface Error</CardTitle>
            <CardDescription className="text-white/70">
              Something went wrong with the trading interface. This could be due to network issues or temporary service problems.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 rounded-lg bg-black/20 border border-white/10">
              <p className="text-sm text-white/60 font-mono">
                Error ID: {this.state.errorId}
              </p>
              {this.state.error && (
                <p className="text-sm text-red-400 mt-2">
                  {this.state.error.message}
                </p>
              )}
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={this.handleRetry}
                className="pawpumps-button flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Try Again
              </Button>
              <Button
                onClick={this.handleGoBack}
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
            </div>

            <div className="text-center">
              <p className="text-xs text-white/50">
                If this problem persists, please contact support with the Error ID above.
              </p>
            </div>
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}
