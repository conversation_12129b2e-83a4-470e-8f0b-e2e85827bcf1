"use client"

import { Component, type ErrorInfo, type <PERSON>actN<PERSON> } from "react"
import { <PERSON>, <PERSON><PERSON>resh<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { getGlobalErrorHandler, classifyError, ErrorType } from "@/lib/error-handling"

interface Props {
  children: ReactNode
  onRetry?: () => void
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorId: string
  errorType?: ErrorType
  retryCount: number
}

export class TokenLaunchErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorId: '',
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `token_launch_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Classify the error for better handling
    const appError = classifyError(error, {
      component: 'TokenLaunchForm',
      action: 'componentDidCatch',
      metadata: {
        errorId: this.state.errorId,
        componentStack: errorInfo.componentStack,
        retryCount: this.state.retryCount,
      }
    })

    // Determine error type based on error message
    let errorType = ErrorType.UNKNOWN
    if (error.message.includes('charAt') || error.message.includes('undefined')) {
      errorType = ErrorType.VALIDATION
    } else if (error.message.includes('network') || error.message.includes('fetch')) {
      errorType = ErrorType.NETWORK
    } else if (error.message.includes('wallet') || error.message.includes('connection')) {
      errorType = ErrorType.WALLET
    }

    this.setState({ errorType })

    // Report error to global handler
    getGlobalErrorHandler().handleError(appError)
  }

  resetError = () => {
    const newRetryCount = this.state.retryCount + 1
    
    if (newRetryCount > 3) {
      // Too many retries, show permanent error state
      return
    }

    this.setState({
      hasError: false,
      error: null,
      errorId: '',
      errorType: undefined,
      retryCount: newRetryCount
    })

    // Call custom retry handler if provided
    this.props.onRetry?.()
  }

  getErrorMessage(): { title: string; description: string; canRetry: boolean } {
    const { error, errorType, retryCount } = this.state

    if (retryCount >= 3) {
      return {
        title: "Multiple Errors Occurred",
        description: "The token launch form has encountered multiple errors. Please refresh the page or contact support.",
        canRetry: false
      }
    }

    switch (errorType) {
      case ErrorType.VALIDATION:
        return {
          title: "Form Validation Error",
          description: "There was an issue with the form data. Please check your inputs and try again.",
          canRetry: true
        }
      case ErrorType.NETWORK:
        return {
          title: "Network Connection Error",
          description: "Unable to connect to the blockchain network. Please check your connection and try again.",
          canRetry: true
        }
      case ErrorType.WALLET:
        return {
          title: "Wallet Connection Error",
          description: "There was an issue connecting to your wallet. Please ensure your wallet is connected and try again.",
          canRetry: true
        }
      default:
        return {
          title: "Token Launch Error",
          description: error?.message || "An unexpected error occurred while preparing the token launch form.",
          canRetry: true
        }
    }
  }

  render(): ReactNode {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      const { title, description, canRetry } = this.getErrorMessage()

      return (
        <Card className="glass-card border-red-500/20 bg-red-500/5">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-500" />
              </div>
            </div>
            <CardTitle className="text-white">{title}</CardTitle>
            <CardDescription className="text-white/70">
              {description}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 rounded-lg bg-black/20 border border-white/10">
              <p className="text-sm text-white/60 font-mono">
                Error ID: {this.state.errorId}
              </p>
              {this.state.retryCount > 0 && (
                <p className="text-sm text-white/60 mt-1">
                  Retry attempt: {this.state.retryCount}/3
                </p>
              )}
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              {canRetry && (
                <Button
                  onClick={this.resetError}
                  className="flex-1 glass-button"
                  variant="outline"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
              )}
              <Button
                onClick={() => window.location.href = '/'}
                className="flex-1 glass-button"
                variant="outline"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Home
              </Button>
            </div>
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}
