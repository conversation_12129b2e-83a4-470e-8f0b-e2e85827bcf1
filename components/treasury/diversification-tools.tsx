"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Chart,
  ResponsiveContainer,
  Pie,
  Cell,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Line,
} from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Download, RefreshCw, Lock, AlertTriangle, Info, CheckCircle } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"

// Mock data for current treasury allocation
const currentAllocation = [
  { name: "$PAW Token", value: 45, color: "#FF6B6B" },
  { name: "Stablecoins", value: 30, color: "#4ECDC4" },
  { name: "ETH", value: 15, color: "#7A77FF" },
  { name: "DO<PERSON>", value: 5, color: "#FFD166" },
  { name: "Other Assets", value: 5, color: "#F9F871" },
]

// Mock data for recommended allocation
const recommendedAllocation = [
  { name: "$PAW Token", value: 35, color: "#FF6B6B" },
  { name: "Stablecoins", value: 40, color: "#4ECDC4" },
  { name: "ETH", value: 15, color: "#7A77FF" },
  { name: "DOGE", value: 5, color: "#FFD166" },
  { name: "Other Assets", value: 5, color: "#F9F871" },
]

// Mock data for risk metrics
const riskMetrics = [
  { name: "Very Low", current: 30, recommended: 40 },
  { name: "Low", current: 15, recommended: 25 },
  { name: "Medium", current: 40, recommended: 25 },
  { name: "High", current: 10, recommended: 8 },
  { name: "Very High", current: 5, recommended: 2 },
]

// Mock data for historical performance
const historicalPerformance = [
  { month: "Jan", current: 100, recommended: 100 },
  { month: "Feb", current: 102, recommended: 103 },
  { month: "Mar", current: 101, recommended: 105 },
  { month: "Apr", current: 104, recommended: 108 },
  { month: "May", current: 103, recommended: 110 },
  { month: "Jun", current: 106, recommended: 112 },
]

export function DiversificationTools() {
  const [customAllocation, setCustomAllocation] = useState([...currentAllocation])
  const [showRebalanceModal, setShowRebalanceModal] = useState(false)

  const handleAllocationChange = (index: number, newValue: number) => {
    // Ensure the total allocation remains 100%
    const currentTotal = customAllocation.reduce((sum, item, i) => sum + (i === index ? 0 : item.value), 0)
    const maxAllowed = 100 - currentTotal

    if (newValue > maxAllowed) newValue = maxAllowed
    if (newValue < 0) newValue = 0

    const newAllocation = [...customAllocation]
    newAllocation[index].value = newValue
    setCustomAllocation(newAllocation)
  }

  const totalAllocation = customAllocation.reduce((sum, item) => sum + item.value, 0)
  const isValidAllocation = totalAllocation === 100

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Treasury Diversification</h2>
          <p className="text-white/70">Optimize treasury allocation for risk and return</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="glass-button">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Data
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Current Allocation</CardTitle>
            <CardDescription className="text-white/70">Treasury asset distribution</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={currentAllocation}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${((percent || 0) * 100).toFixed(0)}%`}
                  >
                    {currentAllocation.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `${value}%`} />
                </PieChart>
              </ResponsiveContainer>
            </div>

            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Total Value:</span>
                <span className="text-white font-medium">$2,450,000</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Risk Profile:</span>
                <span className="text-yellow-500 font-medium">Medium</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Volatility:</span>
                <span className="text-white font-medium">18.5%</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Expected Annual Return:</span>
                <span className="text-white font-medium">12.3%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Recommended Allocation</CardTitle>
            <CardDescription className="text-white/70">Optimized for current market conditions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={recommendedAllocation}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${((percent || 0) * 100).toFixed(0)}%`}
                  >
                    {recommendedAllocation.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `${value}%`} />
                </PieChart>
              </ResponsiveContainer>
            </div>

            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Total Value:</span>
                <span className="text-white font-medium">$2,450,000</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Risk Profile:</span>
                <span className="text-green-500 font-medium">Low-Medium</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Volatility:</span>
                <span className="text-white font-medium">14.2%</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Expected Annual Return:</span>
                <span className="text-white font-medium">10.8%</span>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full doge-button doge-shine" onClick={() => setShowRebalanceModal(true)}>
              Rebalance Treasury
            </Button>
          </CardFooter>
        </Card>
      </div>

      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Custom Allocation</CardTitle>
          <CardDescription className="text-white/70">Create your own treasury allocation strategy</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-6">
              {customAllocation.map((asset, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor={`asset-${index}`} className="text-white">
                      {asset.name}
                    </Label>
                    <span className="text-white font-medium">{asset.value}%</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Slider
                      id={`asset-${index}`}
                      min={0}
                      max={100}
                      step={1}
                      value={[asset.value]}
                      onValueChange={(values) => handleAllocationChange(index, values[0])}
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={asset.value}
                      onChange={(e) => handleAllocationChange(index, Number.parseInt(e.target.value) || 0)}
                      className="glass-input text-white w-16"
                      min={0}
                      max={100}
                    />
                  </div>
                </div>
              ))}

              <div
                className={`p-3 rounded-lg ${isValidAllocation ? "bg-green-500/10 border-green-500/20" : "bg-red-500/10 border-red-500/20"} border flex items-center gap-3`}
              >
                {isValidAllocation ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                )}
                <div>
                  <h4 className={`text-sm font-medium ${isValidAllocation ? "text-green-500" : "text-red-500"}`}>
                    {isValidAllocation ? "Valid Allocation" : "Invalid Allocation"}
                  </h4>
                  <p className="text-xs text-white/70">
                    {isValidAllocation
                      ? "Your allocation totals 100%"
                      : `Your allocation totals ${totalAllocation}%, but must equal 100%`}
                  </p>
                </div>
              </div>
            </div>

            <div>
              <div className="h-[300px] flex items-center justify-center">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={customAllocation}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${((percent || 0) * 100).toFixed(0)}%`}
                    >
                      {customAllocation.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value}%`} />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              <div className="mt-4 space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-white/70">Risk Profile:</span>
                  <span className="text-white font-medium">
                    {customAllocation[1].value >= 40
                      ? "Low"
                      : customAllocation[1].value >= 30
                        ? "Low-Medium"
                        : customAllocation[1].value >= 20
                          ? "Medium"
                          : customAllocation[1].value >= 10
                            ? "Medium-High"
                            : "High"}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-white/70">Estimated Volatility:</span>
                  <span className="text-white font-medium">{(20 - customAllocation[1].value * 0.2).toFixed(1)}%</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-white/70">Expected Annual Return:</span>
                  <span className="text-white font-medium">{(8 + customAllocation[0].value * 0.1).toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex gap-2">
          <Button className="flex-1 doge-button doge-shine" disabled={!isValidAllocation}>
            Save Strategy
          </Button>
          <Button variant="outline" className="flex-1 glass-button">
            Reset to Current
          </Button>
          <Button variant="outline" className="flex-1 glass-button">
            Apply Recommended
          </Button>
        </CardFooter>
      </Card>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Risk Analysis</CardTitle>
            <CardDescription className="text-white/70">
              Comparing risk profiles of different allocations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                current: {
                  label: "Current Allocation",
                  color: "hsl(var(--chart-1))",
                },
                recommended: {
                  label: "Recommended Allocation",
                  color: "hsl(var(--chart-2))",
                },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={riskMetrics}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="name" stroke="rgba(255,255,255,0.5)" />
                  <YAxis stroke="rgba(255,255,255,0.5)" />
                  <ChartTooltip />
                  <Legend />
                  <Bar dataKey="current" fill="var(--color-current)" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="recommended" fill="var(--color-recommended)" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>

            <div className="mt-4 p-4 rounded-lg bg-white/5 border border-white/10 flex items-start gap-3">
              <Info className="h-5 w-5 text-white/70 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-white mb-1">Risk Assessment</h4>
                <p className="text-sm text-white/70">
                  The recommended allocation reduces exposure to medium and high-risk assets, increasing stability while
                  maintaining reasonable returns. This approach is better suited for the current market volatility.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Performance Projection</CardTitle>
            <CardDescription className="text-white/70">Simulated performance comparison</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                current: {
                  label: "Current Allocation",
                  color: "hsl(var(--chart-1))",
                },
                recommended: {
                  label: "Recommended Allocation",
                  color: "hsl(var(--chart-2))",
                },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={historicalPerformance}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="rgba(255,255,255,0.5)" />
                  <YAxis stroke="rgba(255,255,255,0.5)" />
                  <ChartTooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="current"
                    stroke="var(--color-current)"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="recommended"
                    stroke="var(--color-recommended)"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>

            <div className="mt-4 p-4 rounded-lg bg-white/5 border border-white/10 flex items-start gap-3">
              <Info className="h-5 w-5 text-white/70 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-white mb-1">Performance Analysis</h4>
                <p className="text-sm text-white/70">
                  While the current allocation may perform well in bullish markets, the recommended allocation provides
                  more consistent growth with lower drawdowns during market corrections. Over a 6-month period, the
                  recommended allocation is projected to outperform by 6%.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {showRebalanceModal && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card className="glass-card border-white/5 w-full max-w-2xl">
            <CardHeader>
              <CardTitle className="text-white">Treasury Rebalance Confirmation</CardTitle>
              <CardDescription className="text-white/70">
                Review and confirm the proposed treasury rebalance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="p-4 rounded-lg bg-yellow-500/10 border border-yellow-500/20 flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-500 mb-1">Multi-Signature Required</h4>
                  <p className="text-sm text-white/70">
                    This action requires approval from at least 4 out of 7 treasury signers. The transaction will be
                    submitted to the multi-sig wallet for review.
                  </p>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h3 className="text-white font-medium mb-3">Current Allocation</h3>
                  <div className="space-y-2">
                    {currentAllocation.map((asset, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-white/70">{asset.name}</span>
                        <span className="text-white">{asset.value}%</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-white font-medium mb-3">New Allocation</h3>
                  <div className="space-y-2">
                    {recommendedAllocation.map((asset, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-white/70">{asset.name}</span>
                        <span className="text-white">{asset.value}%</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-white font-medium mb-3">Rebalance Summary</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Assets to Sell</span>
                    <span className="text-white">$PAW Token (10%)</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Assets to Buy</span>
                    <span className="text-white">Stablecoins (10%)</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Estimated Transaction Costs</span>
                    <span className="text-white">$2,450 (0.1%)</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Estimated Slippage</span>
                    <span className="text-white">0.2% - 0.5%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Execution Time</span>
                    <span className="text-white">24-48 hours</span>
                  </div>
                </div>
              </div>

              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-white font-medium mb-3">Execution Strategy</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input type="radio" id="immediate" name="execution" className="text-doge" defaultChecked />
                    <Label htmlFor="immediate" className="text-white">
                      Immediate (Higher Slippage)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input type="radio" id="gradual" name="execution" className="text-doge" />
                    <Label htmlFor="gradual" className="text-white">
                      Gradual (24 hours)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input type="radio" id="twap" name="execution" className="text-doge" />
                    <Label htmlFor="twap" className="text-white">
                      TWAP (Time-Weighted Average Price)
                    </Label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex gap-2">
              <Button variant="outline" className="flex-1 glass-button" onClick={() => setShowRebalanceModal(false)}>
                Cancel
              </Button>
              <Button className="flex-1 doge-button doge-shine">
                <Lock className="mr-2 h-4 w-4" />
                Submit for Approval
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}
    </div>
  )
}
