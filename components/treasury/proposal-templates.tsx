"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { FileText, Download, Copy, Plus, X } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Template types
type TemplateCategory = "treasury" | "governance" | "development" | "community" | "custom"

interface Template {
  id: string
  name: string
  description: string
  category: TemplateCategory
  content: string
  parameters: TemplateParameter[]
}

interface TemplateParameter {
  name: string
  type: "text" | "number" | "select" | "textarea"
  label: string
  placeholder?: string
  options?: string[]
  default?: string | number
  required?: boolean
}

// Mock templates
const treasuryTemplates: Template[] = [
  {
    id: "treasury-allocation",
    name: "Treasury Allocation Change",
    description: "Propose changes to the treasury asset allocation",
    category: "treasury",
    content: `# Treasury Allocation Change Proposal

## Summary
This proposal suggests changing the current treasury allocation to optimize for {{objective}}.

## Current Allocation
- $PAW Token: {{current_paw}}%
- Stablecoins: {{current_stables}}%
- ETH: {{current_eth}}%
- Other: {{current_other}}%

## Proposed Allocation
- $PAW Token: {{proposed_paw}}%
- Stablecoins: {{proposed_stables}}%
- ETH: {{proposed_eth}}%
- Other: {{proposed_other}}%

## Rationale
{{rationale}}

## Implementation Timeline
{{timeline}}

## Expected Impact
{{impact}}`,
    parameters: [
      {
        name: "objective",
        type: "select",
        label: "Optimization Objective",
        options: ["Risk Reduction", "Yield Maximization", "Liquidity Improvement", "Long-term Growth"],
        default: "Risk Reduction",
        required: true,
      },
      {
        name: "current_paw",
        type: "number",
        label: "Current $PAW Allocation (%)",
        default: 45,
        required: true,
      },
      {
        name: "current_stables",
        type: "number",
        label: "Current Stablecoins Allocation (%)",
        default: 30,
        required: true,
      },
      {
        name: "current_eth",
        type: "number",
        label: "Current ETH Allocation (%)",
        default: 15,
        required: true,
      },
      {
        name: "current_other",
        type: "number",
        label: "Current Other Assets Allocation (%)",
        default: 10,
        required: true,
      },
      {
        name: "proposed_paw",
        type: "number",
        label: "Proposed $PAW Allocation (%)",
        default: 35,
        required: true,
      },
      {
        name: "proposed_stables",
        type: "number",
        label: "Proposed Stablecoins Allocation (%)",
        default: 40,
        required: true,
      },
      {
        name: "proposed_eth",
        type: "number",
        label: "Proposed ETH Allocation (%)",
        default: 15,
        required: true,
      },
      {
        name: "proposed_other",
        type: "number",
        label: "Proposed Other Assets Allocation (%)",
        default: 10,
        required: true,
      },
      {
        name: "rationale",
        type: "textarea",
        label: "Rationale for Changes",
        placeholder: "Explain why these changes are beneficial...",
        required: true,
      },
      {
        name: "timeline",
        type: "textarea",
        label: "Implementation Timeline",
        placeholder: "Describe the timeline for implementing these changes...",
        required: true,
      },
      {
        name: "impact",
        type: "textarea",
        label: "Expected Impact",
        placeholder: "Describe the expected impact on risk, returns, etc...",
        required: true,
      },
    ],
  },
  {
    id: "treasury-expenditure",
    name: "Treasury Expenditure",
    description: "Propose a new expenditure from the treasury",
    category: "treasury",
    content: `# Treasury Expenditure Proposal

## Summary
This proposal requests {{amount}} {{currency}} from the treasury for {{purpose}}.

## Requested Amount
- Amount: {{amount}} {{currency}}
- Percentage of Treasury: {{percentage}}%

## Purpose
{{purpose_details}}

## Beneficiaries
{{beneficiaries}}

## Expected Return on Investment
{{roi}}

## Payment Schedule
{{schedule}}

## Accountability Measures
{{accountability}}`,
    parameters: [
      {
        name: "amount",
        type: "number",
        label: "Requested Amount",
        required: true,
      },
      {
        name: "currency",
        type: "select",
        label: "Currency",
        options: ["$PAW", "USDC", "ETH", "DOGE"],
        default: "USDC",
        required: true,
      },
      {
        name: "percentage",
        type: "number",
        label: "Percentage of Treasury",
        default: 1,
        required: true,
      },
      {
        name: "purpose",
        type: "text",
        label: "Purpose (Short)",
        placeholder: "E.g., Marketing Campaign, Development Grant",
        required: true,
      },
      {
        name: "purpose_details",
        type: "textarea",
        label: "Detailed Purpose",
        placeholder: "Provide a detailed explanation of how the funds will be used...",
        required: true,
      },
      {
        name: "beneficiaries",
        type: "textarea",
        label: "Beneficiaries",
        placeholder: "List the individuals or entities that will receive the funds...",
        required: true,
      },
      {
        name: "roi",
        type: "textarea",
        label: "Expected Return on Investment",
        placeholder: "Describe the expected benefits and ROI for the community...",
        required: true,
      },
      {
        name: "schedule",
        type: "textarea",
        label: "Payment Schedule",
        placeholder: "Describe the payment schedule and milestones...",
        required: true,
      },
      {
        name: "accountability",
        type: "textarea",
        label: "Accountability Measures",
        placeholder: "Describe how progress will be tracked and reported...",
        required: true,
      },
    ],
  },
]

const governanceTemplates: Template[] = [
  {
    id: "parameter-change",
    name: "Governance Parameter Change",
    description: "Propose changes to governance parameters",
    category: "governance",
    content: `# Governance Parameter Change Proposal

## Summary
This proposal suggests changing the {{parameter_name}} from {{current_value}} to {{proposed_value}}.

## Parameter Details
- Parameter: {{parameter_name}}
- Current Value: {{current_value}}
- Proposed Value: {{proposed_value}}

## Rationale
{{rationale}}

## Impact Analysis
{{impact}}

## Implementation Timeline
{{timeline}}`,
    parameters: [
      {
        name: "parameter_name",
        type: "select",
        label: "Parameter Name",
        options: ["Quorum Requirement", "Voting Period", "Proposal Threshold", "Execution Delay"],
        required: true,
      },
      {
        name: "current_value",
        type: "text",
        label: "Current Value",
        required: true,
      },
      {
        name: "proposed_value",
        type: "text",
        label: "Proposed Value",
        required: true,
      },
      {
        name: "rationale",
        type: "textarea",
        label: "Rationale for Change",
        placeholder: "Explain why this parameter should be changed...",
        required: true,
      },
      {
        name: "impact",
        type: "textarea",
        label: "Impact Analysis",
        placeholder: "Analyze how this change will affect governance...",
        required: true,
      },
      {
        name: "timeline",
        type: "textarea",
        label: "Implementation Timeline",
        placeholder: "Describe when and how this change will be implemented...",
        required: true,
      },
    ],
  },
]

const developmentTemplates: Template[] = [
  {
    id: "feature-request",
    name: "New Feature Request",
    description: "Propose a new platform feature",
    category: "development",
    content: `# Feature Request Proposal

## Summary
This proposal suggests adding {{feature_name}} to the platform.

## Feature Description
{{description}}

## User Stories
{{user_stories}}

## Technical Requirements
{{technical_requirements}}

## Development Resources
- Estimated Development Time: {{dev_time}}
- Required Resources: {{resources}}
- Estimated Cost: {{cost}}

## Success Metrics
{{metrics}}

## Implementation Timeline
{{timeline}}`,
    parameters: [
      {
        name: "feature_name",
        type: "text",
        label: "Feature Name",
        placeholder: "E.g., Mobile App, NFT Integration",
        required: true,
      },
      {
        name: "description",
        type: "textarea",
        label: "Feature Description",
        placeholder: "Describe the feature in detail...",
        required: true,
      },
      {
        name: "user_stories",
        type: "textarea",
        label: "User Stories",
        placeholder: "As a [type of user], I want to [action] so that [benefit]...",
        required: true,
      },
      {
        name: "technical_requirements",
        type: "textarea",
        label: "Technical Requirements",
        placeholder: "List the technical requirements for this feature...",
        required: true,
      },
      {
        name: "dev_time",
        type: "text",
        label: "Estimated Development Time",
        placeholder: "E.g., 4 weeks, 2 months",
        required: true,
      },
      {
        name: "resources",
        type: "textarea",
        label: "Required Resources",
        placeholder: "List the resources needed (developers, designers, etc.)...",
        required: true,
      },
      {
        name: "cost",
        type: "text",
        label: "Estimated Cost",
        placeholder: "E.g., $10,000, 50,000 $PAW",
        required: true,
      },
      {
        name: "metrics",
        type: "textarea",
        label: "Success Metrics",
        placeholder: "How will we measure the success of this feature?",
        required: true,
      },
      {
        name: "timeline",
        type: "textarea",
        label: "Implementation Timeline",
        placeholder: "Outline the development and release timeline...",
        required: true,
      },
    ],
  },
]

const communityTemplates: Template[] = [
  {
    id: "community-initiative",
    name: "Community Initiative",
    description: "Propose a new community program or event",
    category: "community",
    content: `# Community Initiative Proposal

## Summary
This proposal suggests creating a {{initiative_type}} called "{{initiative_name}}".

## Initiative Description
{{description}}

## Goals and Objectives
{{goals}}

## Target Audience
{{audience}}

## Required Resources
- Budget: {{budget}}
- Team Members: {{team}}
- Timeline: {{timeline}}

## Success Metrics
{{metrics}}

## Implementation Plan
{{implementation}}`,
    parameters: [
      {
        name: "initiative_type",
        type: "select",
        label: "Initiative Type",
        options: ["Event", "Program", "Contest", "Educational Series", "Rewards Program"],
        required: true,
      },
      {
        name: "initiative_name",
        type: "text",
        label: "Initiative Name",
        placeholder: "Give your initiative a catchy name",
        required: true,
      },
      {
        name: "description",
        type: "textarea",
        label: "Initiative Description",
        placeholder: "Describe the initiative in detail...",
        required: true,
      },
      {
        name: "goals",
        type: "textarea",
        label: "Goals and Objectives",
        placeholder: "What are the goals of this initiative?",
        required: true,
      },
      {
        name: "audience",
        type: "textarea",
        label: "Target Audience",
        placeholder: "Who is this initiative aimed at?",
        required: true,
      },
      {
        name: "budget",
        type: "text",
        label: "Budget",
        placeholder: "E.g., $5,000, 25,000 $PAW",
        required: true,
      },
      {
        name: "team",
        type: "textarea",
        label: "Team Members",
        placeholder: "Who will be responsible for implementing this initiative?",
        required: true,
      },
      {
        name: "timeline",
        type: "text",
        label: "Timeline",
        placeholder: "E.g., 2 weeks, 3 months",
        required: true,
      },
      {
        name: "metrics",
        type: "textarea",
        label: "Success Metrics",
        placeholder: "How will we measure the success of this initiative?",
        required: true,
      },
      {
        name: "implementation",
        type: "textarea",
        label: "Implementation Plan",
        placeholder: "Outline the steps to implement this initiative...",
        required: true,
      },
    ],
  },
]

// Combine all templates
const allTemplates = [...treasuryTemplates, ...governanceTemplates, ...developmentTemplates, ...communityTemplates]

export function ProposalTemplates() {
  const [activeCategory, setActiveCategory] = useState<TemplateCategory | "all">("all")
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null)
  const [templateValues, setTemplateValues] = useState<Record<string, string | number>>({})
  const [previewMode, setPreviewMode] = useState(false)
  const [customTemplates, setCustomTemplates] = useState<Template[]>([])
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null)

  // Filter templates by category
  const filteredTemplates =
    activeCategory === "all"
      ? [...allTemplates, ...customTemplates]
      : [...allTemplates, ...customTemplates].filter((template) => template.category === activeCategory)

  // Handle template selection
  const handleSelectTemplate = (template: Template) => {
    setSelectedTemplate(template)

    // Initialize template values with defaults
    const initialValues: Record<string, string | number> = {}
    template.parameters.forEach((param) => {
      if (param.default !== undefined) {
        initialValues[param.name] = param.default
      } else {
        initialValues[param.name] = param.type === "number" ? 0 : ""
      }
    })

    setTemplateValues(initialValues)
    setPreviewMode(false)
  }

  // Handle parameter value change
  const handleParameterChange = (name: string, value: string | number) => {
    setTemplateValues((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  // Generate proposal content with filled parameters
  const generateContent = () => {
    if (!selectedTemplate) return ""

    let content = selectedTemplate.content

    // Replace all parameters in the content
    Object.entries(templateValues).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, "g")
      content = content.replace(regex, String(value))
    })

    return content
  }

  // Check if all required parameters are filled
  const isFormValid = () => {
    if (!selectedTemplate) return false

    return selectedTemplate.parameters.every((param) => {
      if (param.required) {
        const value = templateValues[param.name]
        return value !== undefined && value !== "" && value !== 0
      }
      return true
    })
  }

  // Handle copy to clipboard
  const handleCopyToClipboard = () => {
    const content = generateContent()
    navigator.clipboard.writeText(content)
    alert("Proposal content copied to clipboard!")
  }

  // Handle download as markdown
  const handleDownload = () => {
    const content = generateContent()
    const blob = new Blob([content], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `${selectedTemplate?.name.toLowerCase().replace(/\s+/g, "-")}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Proposal Templates</h2>
          <p className="text-white/70">Standardized templates for governance proposals</p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-1">
          <Card className="glass-card border-white/5 h-full">
            <CardHeader>
              <CardTitle className="text-white">Template Categories</CardTitle>
              <CardDescription className="text-white/70">Select a category to view templates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button
                  variant={activeCategory === "all" ? "default" : "outline"}
                  className={activeCategory === "all" ? "w-full doge-button doge-shine" : "w-full glass-button"}
                  onClick={() => setActiveCategory("all")}
                >
                  All Templates
                </Button>
                <Button
                  variant={activeCategory === "treasury" ? "default" : "outline"}
                  className={activeCategory === "treasury" ? "w-full doge-button doge-shine" : "w-full glass-button"}
                  onClick={() => setActiveCategory("treasury")}
                >
                  Treasury
                </Button>
                <Button
                  variant={activeCategory === "governance" ? "default" : "outline"}
                  className={activeCategory === "governance" ? "w-full doge-button doge-shine" : "w-full glass-button"}
                  onClick={() => setActiveCategory("governance")}
                >
                  Governance
                </Button>
                <Button
                  variant={activeCategory === "development" ? "default" : "outline"}
                  className={activeCategory === "development" ? "w-full doge-button doge-shine" : "w-full glass-button"}
                  onClick={() => setActiveCategory("development")}
                >
                  Development
                </Button>
                <Button
                  variant={activeCategory === "community" ? "default" : "outline"}
                  className={activeCategory === "community" ? "w-full doge-button doge-shine" : "w-full glass-button"}
                  onClick={() => setActiveCategory("community")}
                >
                  Community
                </Button>
                <Button
                  variant={activeCategory === "custom" ? "default" : "outline"}
                  className={activeCategory === "custom" ? "w-full doge-button doge-shine" : "w-full glass-button"}
                  onClick={() => setActiveCategory("custom")}
                >
                  Custom Templates
                </Button>
              </div>

              <div className="mt-6">
                <Button className="w-full glass-button">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Custom Template
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-2">
          {selectedTemplate ? (
            <Card className="glass-card border-white/5">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-white">{selectedTemplate.name}</CardTitle>
                    <CardDescription className="text-white/70">{selectedTemplate.description}</CardDescription>
                  </div>
                  <Button variant="outline" className="glass-button" onClick={() => setSelectedTemplate(null)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="edit" className="w-full">
                  <TabsList className="grid w-full grid-cols-2 glass mb-6">
                    <TabsTrigger
                      value="edit"
                      className="data-[state=active]:text-doge data-[state=active]:bg-doge/10"
                      onClick={() => setPreviewMode(false)}
                    >
                      Edit Parameters
                    </TabsTrigger>
                    <TabsTrigger
                      value="preview"
                      className="data-[state=active]:text-dogechain data-[state=active]:bg-dogechain/10"
                      onClick={() => setPreviewMode(true)}
                    >
                      Preview Proposal
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="edit" className="space-y-6">
                    <div className="grid gap-4 sm:grid-cols-2">
                      {selectedTemplate.parameters.map((param) => (
                        <div key={param.name} className="space-y-2">
                          <Label htmlFor={param.name} className="text-white">
                            {param.label} {param.required && <span className="text-red-500">*</span>}
                          </Label>

                          {param.type === "text" && (
                            <Input
                              id={param.name}
                              value={(templateValues[param.name] as string) || ""}
                              onChange={(e) => handleParameterChange(param.name, e.target.value)}
                              placeholder={param.placeholder}
                              className="glass-input text-white"
                              required={param.required}
                            />
                          )}

                          {param.type === "number" && (
                            <Input
                              id={param.name}
                              type="number"
                              value={(templateValues[param.name] as number) || 0}
                              onChange={(e) => handleParameterChange(param.name, Number(e.target.value))}
                              className="glass-input text-white"
                              required={param.required}
                            />
                          )}

                          {param.type === "textarea" && (
                            <Textarea
                              id={param.name}
                              value={(templateValues[param.name] as string) || ""}
                              onChange={(e) => handleParameterChange(param.name, e.target.value)}
                              placeholder={param.placeholder}
                              className="glass-input text-white"
                              required={param.required}
                            />
                          )}

                          {param.type === "select" && (
                            <Select onValueChange={(value) => handleParameterChange(param.name, value)}>
                              <SelectTrigger className="w-full glass-button text-white">
                                <SelectValue placeholder={param.label} />
                              </SelectTrigger>
                              <SelectContent>
                                {param.options?.map((option) => (
                                  <SelectItem key={option} value={option}>
                                    {option}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                        </div>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="preview">
                    <div className="prose dark:prose-invert max-w-none">
                      <pre className="bg-black/20 rounded-md p-4 text-white">{generateContent()}</pre>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
              <CardFooter className="justify-between">
                <Button className="glass-button" onClick={handleCopyToClipboard} disabled={!isFormValid()}>
                  <Copy className="mr-2 h-4 w-4" />
                  Copy to Clipboard
                </Button>
                <Button className="doge-button doge-shine" onClick={handleDownload} disabled={!isFormValid()}>
                  <Download className="mr-2 h-4 w-4" />
                  Download as Markdown
                </Button>
              </CardFooter>
            </Card>
          ) : (
            <Card className="glass-card border-white/5">
              <CardHeader>
                <CardTitle className="text-white">Select a Template</CardTitle>
                <CardDescription className="text-white/70">Choose a template to get started</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {filteredTemplates.length > 0 ? (
                  filteredTemplates.map((template) => (
                    <Button
                      key={template.id}
                      className="w-full glass-button justify-start"
                      onClick={() => handleSelectTemplate(template)}
                    >
                      <FileText className="mr-2 h-4 w-4" />
                      {template.name}
                    </Button>
                  ))
                ) : (
                  <p className="text-white/70">No templates found in this category.</p>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
