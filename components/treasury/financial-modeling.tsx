"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { LineChart, BarChart, ResponsiveContainer, Line, Bar, XAxis, YAxis, CartesianGrid, Legend } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Download, RefreshCw, Calculator, TrendingUp, DollarSign, Percent } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"

// Mock data for treasury projections
const generateProjections = (
  initialValue: number,
  growthRate: number,
  volatility: number,
  months = 12,
  scenarios: string[] = ["base", "bull", "bear"],
) => {
  const data = []

  for (let i = 0; i <= months; i++) {
    const baseValue = initialValue * Math.pow(1 + growthRate / 12, i)

    const entry: any = {
      month: i,
      base: Math.round(baseValue),
    }

    if (scenarios.includes("bull")) {
      entry.bull = Math.round(baseValue * (1 + (volatility * 0.5 * i) / 12))
    }

    if (scenarios.includes("bear")) {
      entry.bear = Math.round(baseValue * (1 - (volatility * 0.3 * i) / 12))
    }

    data.push(entry)
  }

  return data
}

// Mock data for revenue projections
const revenueProjections = [
  { source: "Trading Fees", current: 65000, projected: 78000 },
  { source: "Staking Rewards", current: 25000, projected: 30000 },
  { source: "Token Launches", current: 15000, projected: 22000 },
  { source: "Partnerships", current: 8000, projected: 12000 },
  { source: "Other", current: 5000, projected: 7000 },
]

export function FinancialModeling() {
  const [treasuryValue, setTreasuryValue] = useState(2450000)
  const [growthRate, setGrowthRate] = useState(0.12) // 12% annual growth
  const [volatility, setVolatility] = useState(0.2) // 20% volatility
  const [timeframe, setTimeframe] = useState("12m")
  const [showScenarios, setShowScenarios] = useState(true)

  const months = timeframe === "6m" ? 6 : timeframe === "12m" ? 12 : timeframe === "24m" ? 24 : 36
  const scenarios = showScenarios ? ["base", "bull", "bear"] : ["base"]

  const projectionData = generateProjections(treasuryValue, growthRate, volatility, months, scenarios)

  const handleExport = () => {
    // In a real app, this would export the projections
    alert("Exporting financial projections...")
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Financial Modeling</h2>
          <p className="text-white/70">Treasury projections and scenario analysis</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="glass-button">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Data
          </Button>
          <Button variant="outline" className="glass-button" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Treasury Projection Model</CardTitle>
          <CardDescription className="text-white/70">
            Customize parameters to model future treasury value
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="treasury-value" className="text-white">
                  Initial Treasury Value ($)
                </Label>
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-white/70" />
                  <Input
                    id="treasury-value"
                    type="number"
                    value={treasuryValue}
                    onChange={(e) => setTreasuryValue(Number(e.target.value))}
                    className="glass-input text-white"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="growth-rate" className="text-white">
                    Annual Growth Rate
                  </Label>
                  <span className="text-white font-medium">{(growthRate * 100).toFixed(1)}%</span>
                </div>
                <div className="flex items-center gap-2">
                  <Percent className="h-4 w-4 text-white/70" />
                  <Slider
                    id="growth-rate"
                    min={0}
                    max={0.3}
                    step={0.01}
                    value={[growthRate]}
                    onValueChange={(values) => setGrowthRate(values[0])}
                    className="flex-1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="volatility" className="text-white">
                    Market Volatility
                  </Label>
                  <span className="text-white font-medium">{(volatility * 100).toFixed(1)}%</span>
                </div>
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-white/70" />
                  <Slider
                    id="volatility"
                    min={0.05}
                    max={0.5}
                    step={0.01}
                    value={[volatility]}
                    onValueChange={(values) => setVolatility(values[0])}
                    className="flex-1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeframe" className="text-white">
                  Projection Timeframe
                </Label>
                <Select value={timeframe} onValueChange={setTimeframe}>
                  <SelectTrigger id="timeframe" className="glass-input text-white">
                    <SelectValue placeholder="Select Timeframe" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="6m">6 Months</SelectItem>
                    <SelectItem value="12m">12 Months</SelectItem>
                    <SelectItem value="24m">24 Months</SelectItem>
                    <SelectItem value="36m">36 Months</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="show-scenarios"
                  checked={showScenarios}
                  onChange={(e) => setShowScenarios(e.target.checked)}
                  className="text-doge"
                />
                <Label htmlFor="show-scenarios" className="text-white">
                  Show Bull/Bear Scenarios
                </Label>
              </div>

              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-white font-medium mb-3">Projection Summary</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Initial Value:</span>
                    <span className="text-white">${treasuryValue.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Projected Value (Base):</span>
                    <span className="text-white">
                      ${projectionData[projectionData.length - 1].base.toLocaleString()}
                    </span>
                  </div>
                  {showScenarios && (
                    <>
                      <div className="flex items-center justify-between">
                        <span className="text-white/70">Projected Value (Bull):</span>
                        <span className="text-green-500">
                          ${projectionData[projectionData.length - 1].bull.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-white/70">Projected Value (Bear):</span>
                        <span className="text-red-500">
                          ${projectionData[projectionData.length - 1].bear.toLocaleString()}
                        </span>
                      </div>
                    </>
                  )}
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Projected Growth:</span>
                    <span className="text-white">
                      {((projectionData[projectionData.length - 1].base / treasuryValue - 1) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <ChartContainer
                config={{
                  base: {
                    label: "Base Case",
                    color: "hsl(var(--chart-1))",
                  },
                  ...(showScenarios
                    ? {
                        bull: {
                          label: "Bull Case",
                          color: "hsl(142, 76%, 36%)",
                        },
                        bear: {
                          label: "Bear Case",
                          color: "hsl(346, 84%, 61%)",
                        },
                      }
                    : {}),
                }}
                className="h-[400px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={projectionData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis
                      dataKey="month"
                      stroke="rgba(255,255,255,0.5)"
                      tickFormatter={(value) => {
                        if (value === 0) return "Now"
                        if (value % 3 === 0 || months <= 12) return `${value}m`
                        return ""
                      }}
                    />
                    <YAxis
                      stroke="rgba(255,255,255,0.5)"
                      tickFormatter={(value) =>
                        `$${value >= 1000000 ? (value / 1000000).toFixed(1) + "M" : (value / 1000).toFixed(0) + "K"}`
                      }
                    />
                    <ChartTooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="base"
                      stroke="var(--color-base)"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                      activeDot={{ r: 6 }}
                    />
                    {showScenarios && (
                      <>
                        <Line
                          type="monotone"
                          dataKey="bull"
                          stroke="var(--color-bull)"
                          strokeWidth={2}
                          dot={{ r: 4 }}
                          activeDot={{ r: 6 }}
                        />
                        <Line
                          type="monotone"
                          dataKey="bear"
                          stroke="var(--color-bear)"
                          strokeWidth={2}
                          dot={{ r: 4 }}
                          activeDot={{ r: 6 }}
                        />
                      </>
                    )}
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Revenue Projections</CardTitle>
            <CardDescription className="text-white/70">Projected revenue by source</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                current: {
                  label: "Current Monthly",
                  color: "hsl(var(--chart-1))",
                },
                projected: {
                  label: "Projected (6 months)",
                  color: "hsl(var(--chart-2))",
                },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={revenueProjections} layout="vertical">
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis
                    type="number"
                    stroke="rgba(255,255,255,0.5)"
                    tickFormatter={(value) => `$${value >= 1000 ? (value / 1000).toFixed(0) + "K" : value}`}
                  />
                  <YAxis dataKey="source" type="category" stroke="rgba(255,255,255,0.5)" />
                  <ChartTooltip />
                  <Legend />
                  <Bar dataKey="current" fill="var(--color-current)" radius={[0, 4, 4, 0]} />
                  <Bar dataKey="projected" fill="var(--color-projected)" radius={[0, 4, 4, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>

            <div className="mt-4 p-4 rounded-lg bg-white/5 border border-white/10">
              <h3 className="text-white font-medium mb-3">Revenue Insights</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Current Monthly Revenue:</span>
                  <span className="text-white">
                    ${revenueProjections.reduce((sum, item) => sum + item.current, 0).toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Projected Monthly Revenue:</span>
                  <span className="text-white">
                    ${revenueProjections.reduce((sum, item) => sum + item.projected, 0).toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Growth Rate:</span>
                  <span className="text-green-500">
                    {(
                      (revenueProjections.reduce((sum, item) => sum + item.projected, 0) /
                        revenueProjections.reduce((sum, item) => sum + item.current, 0) -
                        1) *
                      100
                    ).toFixed(1)}
                    %
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Largest Growth Source:</span>
                  <span className="text-white">
                    {
                      revenueProjections.reduce(
                        (max, item) =>
                          item.projected / item.current > max.growth
                            ? { source: item.source, growth: item.projected / item.current }
                            : max,
                        { source: "", growth: 0 },
                      ).source
                    }
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Scenario Analysis</CardTitle>
            <CardDescription className="text-white/70">Impact of different market conditions</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="market" className="w-full">
              <TabsList className="grid w-full grid-cols-3 glass mb-6">
                <TabsTrigger value="market" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                  Market Scenarios
                </TabsTrigger>
                <TabsTrigger
                  value="growth"
                  className="data-[state=active]:text-green-500 data-[state=active]:bg-green-500/10"
                >
                  Growth Rates
                </TabsTrigger>
                <TabsTrigger
                  value="stress"
                  className="data-[state=active]:text-red-500 data-[state=active]:bg-red-500/10"
                >
                  Stress Tests
                </TabsTrigger>
              </TabsList>

              <TabsContent value="market" className="space-y-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Bull Market Scenario</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Market Conditions:</span>
                      <span className="text-green-500">Strong Bullish</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">$PAW Token Price:</span>
                      <span className="text-white">+150% from current</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Trading Volume:</span>
                      <span className="text-white">+200% from current</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Treasury Growth:</span>
                      <span className="text-white">+85% in 12 months</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Base Case Scenario</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Market Conditions:</span>
                      <span className="text-white">Moderate Growth</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">$PAW Token Price:</span>
                      <span className="text-white">+30% from current</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Trading Volume:</span>
                      <span className="text-white">+50% from current</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Treasury Growth:</span>
                      <span className="text-white">+12% in 12 months</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Bear Market Scenario</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Market Conditions:</span>
                      <span className="text-red-500">Bearish</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">$PAW Token Price:</span>
                      <span className="text-white">-40% from current</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Trading Volume:</span>
                      <span className="text-white">-30% from current</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Treasury Growth:</span>
                      <span className="text-white">-15% in 12 months</span>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="growth" className="space-y-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Conservative Growth (5%)</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">12-Month Projection:</span>
                      <span className="text-white">${Math.round(treasuryValue * 1.05).toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">24-Month Projection:</span>
                      <span className="text-white">${Math.round(treasuryValue * 1.05 * 1.05).toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Risk Level:</span>
                      <span className="text-green-500">Very Low</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Moderate Growth (12%)</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">12-Month Projection:</span>
                      <span className="text-white">${Math.round(treasuryValue * 1.12).toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">24-Month Projection:</span>
                      <span className="text-white">${Math.round(treasuryValue * 1.12 * 1.12).toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Risk Level:</span>
                      <span className="text-yellow-500">Medium</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Aggressive Growth (25%)</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">12-Month Projection:</span>
                      <span className="text-white">${Math.round(treasuryValue * 1.25).toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">24-Month Projection:</span>
                      <span className="text-white">${Math.round(treasuryValue * 1.25 * 1.25).toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Risk Level:</span>
                      <span className="text-red-500">High</span>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="stress" className="space-y-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Market Crash (-50%)</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Treasury Impact:</span>
                      <span className="text-red-500">-30% ($1.72M)</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Recovery Time:</span>
                      <span className="text-white">18-24 months</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Mitigation Strategy:</span>
                      <span className="text-white">Increase stablecoin allocation</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Liquidity Crisis</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Treasury Impact:</span>
                      <span className="text-red-500">-15% ($2.08M)</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Recovery Time:</span>
                      <span className="text-white">6-12 months</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Mitigation Strategy:</span>
                      <span className="text-white">Diversify across multiple DEXs</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-3">Regulatory Changes</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Treasury Impact:</span>
                      <span className="text-yellow-500">-10% ($2.21M)</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Recovery Time:</span>
                      <span className="text-white">3-6 months</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Mitigation Strategy:</span>
                      <span className="text-white">Legal compliance reserves</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter>
            <Button className="w-full glass-button">
              <Calculator className="mr-2 h-4 w-4" />
              Run Custom Scenario
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
