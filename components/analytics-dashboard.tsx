"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { PlatformMetrics } from "@/components/platform-metrics"
import { TokenPerformance } from "@/components/token-performance"
import { UserActivity } from "@/components/user-activity"
import { LiquidityAnalytics } from "@/components/liquidity-analytics"
import { TopTokensByVolume } from "@/components/analytics/top-tokens-volume"
import { TimeframeSelector, DEFAULT_TIMEFRAMES } from "@/components/timeframe-selector"

export function AnalyticsDashboard() {
  const [timeframe, setTimeframe] = useState("7d")
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <div className="space-y-8">
      {/* Header with tabs and timeframe selector */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <TabsList className="glass">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="tokens">Tokens</TabsTrigger>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="liquidity">Liquidity</TabsTrigger>
            </TabsList>

            <TimeframeSelector
              value={timeframe}
              onValueChange={setTimeframe}
              options={DEFAULT_TIMEFRAMES}
              variant="buttons"
              size="sm"
            />
          </div>

          <div className="mt-8">
            <TabsContent value="overview" className="space-y-8 mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <MetricCard
                  title="Total Volume"
                  value="$4.2M"
                  change="+12.5%"
                  isPositive={true}
                  description="Trading volume"
                />
                <MetricCard
                  title="Active Users"
                  value="12.5K"
                  change="+8.3%"
                  isPositive={true}
                  description="Unique wallets"
                />
                <MetricCard
                  title="Tokens Created"
                  value="1,234"
                  change="+15.7%"
                  isPositive={true}
                  description="Total tokens"
                />
                <MetricCard
                  title="$PAW Price"
                  value="$0.0042"
                  change="-2.1%"
                  isPositive={false}
                  description="Current price"
                />
              </div>

              <PlatformMetrics timeframe={timeframe} showMetricCards={false} />
              <TopTokensByVolume timeframe={timeframe} />
            </TabsContent>

            <TabsContent value="tokens" className="space-y-8 mt-0">
              <TokenPerformance />
            </TabsContent>

            <TabsContent value="users" className="mt-0">
              <UserActivity timeframe={timeframe} />
            </TabsContent>

            <TabsContent value="liquidity" className="mt-0">
              <LiquidityAnalytics timeframe={timeframe} />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  )
}

function MetricCard({
  title,
  value,
  change,
  isPositive,
  description,
}: {
  title: string
  value: string
  change: string
  isPositive: boolean
  description: string
}) {
  return (
    <Card className="glass-card border-white/5 liquid-glow">
      <CardContent className="p-6">
        <div className="flex flex-col gap-1">
          <p className="text-sm font-medium text-white/60">{title}</p>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold text-white text-glow">{value}</p>
            <div className={`flex items-center text-sm font-medium ${isPositive ? "text-green-500" : "text-red-500"}`}>
              {change}
            </div>
          </div>
          <p className="text-xs text-white/40">{description}</p>
        </div>
      </CardContent>
    </Card>
  )
}
