"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { useMobile } from "@/hooks/use-mobile"
import { Menu, X, ArrowUp } from "lucide-react"

interface MobileOptimizedLayoutProps {
  children: React.ReactNode
  sidebar?: React.ReactNode
  header?: React.ReactNode
  footer?: React.ReactNode
}

export function MobileOptimizedLayout({ children, sidebar, header, footer }: MobileOptimizedLayoutProps) {
  const isMobile = useMobile()
  const [isOpen, setIsOpen] = useState(false)
  const [showScrollTop, setShowScrollTop] = useState(false)

  // Close sidebar when switching to desktop
  useEffect(() => {
    if (!isMobile) {
      setIsOpen(false)
    }
  }, [isMobile])

  // Show/hide scroll to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    })
  }

  return (
    <div className="flex min-h-screen flex-col">
      {/* Header */}
      {header && (
        <header className="sticky top-0 z-40 w-full border-b border-white/10 bg-black/80 backdrop-blur-sm">
          <div className="container flex h-16 items-center justify-between py-4">
            {isMobile && sidebar && (
              <Sheet open={isOpen} onOpenChange={setIsOpen}>
                <SheetTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="mr-2 text-white/80 hover:text-white focus:ring-2 focus:ring-doge focus:ring-offset-2 focus:ring-offset-black"
                  >
                    <Menu className="h-5 w-5" />
                    <span className="sr-only">Toggle menu</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="glass-card border-white/10 w-[80%] max-w-sm p-0">
                  <div className="flex h-16 items-center border-b border-white/10 px-4">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="mr-2 text-white/80 hover:text-white focus:ring-2 focus:ring-doge focus:ring-offset-2 focus:ring-offset-black"
                      onClick={() => setIsOpen(false)}
                    >
                      <X className="h-5 w-5" />
                      <span className="sr-only">Close menu</span>
                    </Button>
                  </div>
                  <div className="py-4 overflow-y-auto max-h-[calc(100vh-4rem)]">{sidebar}</div>
                </SheetContent>
              </Sheet>
            )}
            <div className="flex-1">{header}</div>
          </div>
        </header>
      )}

      {/* Main content */}
      <div className="flex flex-1">
        {/* Sidebar for desktop */}
        {!isMobile && sidebar && (
          <aside className="hidden md:block w-64 shrink-0 border-r border-white/10 bg-black/50">
            <div className="sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto py-6 pr-2 pl-4">{sidebar}</div>
          </aside>
        )}

        {/* Main content */}
        <main className="flex-1">{children}</main>
      </div>

      {/* Footer */}
      {footer && (
        <footer className="border-t border-white/10 bg-black/50">
          <div className="container py-6">{footer}</div>
        </footer>
      )}

      {/* Scroll to top button */}
      {showScrollTop && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-6 right-6 z-50 rounded-full bg-doge/90 text-black hover:bg-doge border-none shadow-lg focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black"
          onClick={scrollToTop}
          aria-label="Scroll to top"
        >
          <ArrowUp className="h-5 w-5" />
        </Button>
      )}
    </div>
  )
}
