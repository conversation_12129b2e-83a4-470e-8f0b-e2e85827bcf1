"use client"

import { useEffect, useRef } from "react"

interface LiveRegionProps {
  message: string
  politeness?: 'polite' | 'assertive' | 'off'
  atomic?: boolean
  relevant?: 'additions' | 'removals' | 'text' | 'all'
  className?: string
}

export function LiveRegion({ 
  message, 
  politeness = 'polite', 
  atomic = true,
  relevant = 'all',
  className = ""
}: LiveRegionProps) {
  const regionRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (regionRef.current && message) {
      // Clear and set the message to ensure screen readers announce it
      regionRef.current.textContent = ''
      setTimeout(() => {
        if (regionRef.current) {
          regionRef.current.textContent = message
        }
      }, 100)
    }
  }, [message])

  return (
    <div
      ref={regionRef}
      aria-live={politeness}
      aria-atomic={atomic}
      aria-relevant={relevant}
      className={`sr-only ${className}`}
      role="status"
    />
  )
}

// Hook for managing live announcements
export function useLiveAnnouncer() {
  const announce = (message: string, politeness: 'polite' | 'assertive' = 'polite') => {
    // Create a temporary live region for the announcement
    const liveRegion = document.createElement('div')
    liveRegion.setAttribute('aria-live', politeness)
    liveRegion.setAttribute('aria-atomic', 'true')
    liveRegion.className = 'sr-only'
    
    document.body.appendChild(liveRegion)
    
    // Announce the message
    setTimeout(() => {
      liveRegion.textContent = message
    }, 100)
    
    // Clean up after announcement
    setTimeout(() => {
      document.body.removeChild(liveRegion)
    }, 1000)
  }

  const announcePolite = (message: string) => announce(message, 'polite')
  const announceAssertive = (message: string) => announce(message, 'assertive')

  return {
    announce,
    announcePolite,
    announceAssertive
  }
}

// Component for trading status announcements
export function TradingStatusAnnouncer({ 
  isConnected, 
  isTrading, 
  lastTradeStatus 
}: {
  isConnected: boolean
  isTrading: boolean
  lastTradeStatus?: string
}) {
  const getStatusMessage = () => {
    if (!isConnected) {
      return "Wallet not connected. Please connect your wallet to start trading."
    }
    
    if (isTrading) {
      return "Trade in progress. Please wait for confirmation."
    }
    
    if (lastTradeStatus) {
      return lastTradeStatus
    }
    
    return ""
  }

  return (
    <LiveRegion 
      message={getStatusMessage()} 
      politeness="assertive"
    />
  )
}

// Component for form validation announcements
export function FormValidationAnnouncer({ 
  errors, 
  isSubmitting,
  submitSuccess 
}: {
  errors: Record<string, string>
  isSubmitting: boolean
  submitSuccess?: boolean
}) {
  const getValidationMessage = () => {
    if (submitSuccess) {
      return "Form submitted successfully!"
    }
    
    if (isSubmitting) {
      return "Submitting form, please wait..."
    }
    
    const errorCount = Object.keys(errors).length
    if (errorCount > 0) {
      if (errorCount === 1) {
        return `Form has 1 error: ${Object.values(errors)[0]}`
      } else {
        return `Form has ${errorCount} errors. Please review and correct them.`
      }
    }
    
    return ""
  }

  return (
    <LiveRegion 
      message={getValidationMessage()} 
      politeness="assertive"
    />
  )
}

// Component for navigation announcements
export function NavigationAnnouncer({ 
  currentPage, 
  isLoading 
}: {
  currentPage: string
  isLoading: boolean
}) {
  const getMessage = () => {
    if (isLoading) {
      return `Loading ${currentPage} page...`
    }
    return `Navigated to ${currentPage} page`
  }

  return (
    <LiveRegion 
      message={getMessage()} 
      politeness="polite"
    />
  )
}
