import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ValidatedInput, ValidatedTextarea } from '../validated-input'

describe('ValidatedInput Component', () => {
  const mockValidator = jest.fn()
  const mockOnChange = jest.fn()
  const mockOnValidation = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders with basic props', () => {
    render(
      <ValidatedInput
        id="test-input"
        label="Test Input"
        value=""
        onChange={mockOnChange}
      />
    )

    expect(screen.getByLabelText(/test input/i)).toBeInTheDocument()
    expect(screen.getByRole('textbox')).toBeInTheDocument()
  })

  it('displays required indicator when required', () => {
    render(
      <ValidatedInput
        id="required-input"
        label="Required Field"
        value=""
        onChange={mockOnChange}
        required
      />
    )

    expect(screen.getByText('*')).toBeInTheDocument()
  })

  it('shows help text when provided', () => {
    render(
      <ValidatedInput
        id="help-input"
        label="Input with Help"
        value=""
        onChange={mockOnChange}
        helpText="This is helpful information"
      />
    )

    expect(screen.getByText(/this is helpful information/i)).toBeInTheDocument()
  })

  it('handles input changes', async () => {
    const user = userEvent.setup()
    
    render(
      <ValidatedInput
        id="change-input"
        label="Change Input"
        value=""
        onChange={mockOnChange}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, 'test value')

    expect(mockOnChange).toHaveBeenCalledWith('t')
    expect(mockOnChange).toHaveBeenCalledWith('e')
    // ... and so on for each character
  })

  it('validates input on change when validator is provided', async () => {
    const user = userEvent.setup()
    mockValidator.mockReturnValue({ isValid: true })

    render(
      <ValidatedInput
        id="validated-input"
        label="Validated Input"
        value=""
        onChange={mockOnChange}
        onValidation={mockOnValidation}
        validator={mockValidator}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, 'valid')

    await waitFor(() => {
      expect(mockValidator).toHaveBeenCalledWith('valid')
      expect(mockOnValidation).toHaveBeenCalledWith(true, undefined)
    })
  })

  it('shows validation error when input is invalid', async () => {
    const user = userEvent.setup()
    mockValidator.mockReturnValue({ 
      isValid: false, 
      error: 'This field is invalid' 
    })

    render(
      <ValidatedInput
        id="invalid-input"
        label="Invalid Input"
        value=""
        onChange={mockOnChange}
        onValidation={mockOnValidation}
        validator={mockValidator}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, 'invalid')
    fireEvent.blur(input)

    await waitFor(() => {
      expect(screen.getByText(/this field is invalid/i)).toBeInTheDocument()
    })
  })

  it('shows success state when input is valid', async () => {
    const user = userEvent.setup()
    mockValidator.mockReturnValue({ isValid: true })

    render(
      <ValidatedInput
        id="valid-input"
        label="Valid Input"
        value=""
        onChange={mockOnChange}
        onValidation={mockOnValidation}
        validator={mockValidator}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, 'valid')
    fireEvent.blur(input)

    await waitFor(() => {
      expect(screen.getByText(/valid/i)).toBeInTheDocument()
    })
  })

  it('shows loading state during validation', async () => {
    const user = userEvent.setup()
    let resolveValidation
    const asyncValidator = () => new Promise(resolve => {
      resolveValidation = resolve
    })

    render(
      <ValidatedInput
        id="loading-input"
        label="Loading Input"
        value=""
        onChange={mockOnChange}
        validator={asyncValidator}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, 'test')

    // Should show loading spinner
    await waitFor(() => {
      expect(screen.getByRole('textbox').parentElement.querySelector('.animate-spin')).toBeInTheDocument()
    })

    // Resolve validation
    resolveValidation({ isValid: true })
  })

  it('handles disabled state', () => {
    render(
      <ValidatedInput
        id="disabled-input"
        label="Disabled Input"
        value="disabled value"
        onChange={mockOnChange}
        disabled
      />
    )

    const input = screen.getByRole('textbox')
    expect(input).toBeDisabled()
  })

  it('supports different input types', () => {
    const { rerender } = render(
      <ValidatedInput
        id="email-input"
        label="Email"
        value=""
        onChange={mockOnChange}
        type="email"
      />
    )

    expect(screen.getByRole('textbox')).toHaveAttribute('type', 'email')

    rerender(
      <ValidatedInput
        id="password-input"
        label="Password"
        value=""
        onChange={mockOnChange}
        type="password"
      />
    )

    expect(screen.getByLabelText(/password/i)).toHaveAttribute('type', 'password')
  })

  it('validates on blur for untouched inputs', async () => {
    mockValidator.mockReturnValue({ isValid: false, error: 'Required field' })

    render(
      <ValidatedInput
        id="blur-input"
        label="Blur Input"
        value="test"
        onChange={mockOnChange}
        validator={mockValidator}
      />
    )

    const input = screen.getByRole('textbox')
    fireEvent.blur(input)

    await waitFor(() => {
      expect(mockValidator).toHaveBeenCalledWith('test')
    })
  })

  it('debounces validation calls', async () => {
    const user = userEvent.setup()
    mockValidator.mockReturnValue({ isValid: true })

    render(
      <ValidatedInput
        id="debounce-input"
        label="Debounce Input"
        value=""
        onChange={mockOnChange}
        validator={mockValidator}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, 'fast typing')

    // Should not call validator for each keystroke immediately
    expect(mockValidator).not.toHaveBeenCalled()

    // Wait for debounce
    await waitFor(() => {
      expect(mockValidator).toHaveBeenCalledWith('fast typing')
    }, { timeout: 500 })
  })
})

describe('ValidatedTextarea Component', () => {
  const mockOnChange = jest.fn()
  const mockValidator = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders textarea element', () => {
    render(
      <ValidatedTextarea
        id="test-textarea"
        label="Test Textarea"
        value=""
        onChange={mockOnChange}
      />
    )

    expect(screen.getByRole('textbox', { multiline: true })).toBeInTheDocument()
  })

  it('handles textarea changes', async () => {
    const user = userEvent.setup()

    render(
      <ValidatedTextarea
        id="change-textarea"
        label="Change Textarea"
        value=""
        onChange={mockOnChange}
      />
    )

    const textarea = screen.getByRole('textbox', { multiline: true })
    await user.type(textarea, 'multiline\ntext')

    expect(mockOnChange).toHaveBeenCalledWith('m')
    expect(mockOnChange).toHaveBeenCalledWith('u')
    // ... and so on
  })

  it('validates textarea content', async () => {
    const user = userEvent.setup()
    mockValidator.mockReturnValue({ 
      isValid: false, 
      error: 'Text too short' 
    })

    render(
      <ValidatedTextarea
        id="validated-textarea"
        label="Validated Textarea"
        value=""
        onChange={mockOnChange}
        validator={mockValidator}
      />
    )

    const textarea = screen.getByRole('textbox', { multiline: true })
    await user.type(textarea, 'short')
    fireEvent.blur(textarea)

    await waitFor(() => {
      expect(screen.getByText(/text too short/i)).toBeInTheDocument()
    })
  })

  it('supports custom rows', () => {
    render(
      <ValidatedTextarea
        id="rows-textarea"
        label="Custom Rows"
        value=""
        onChange={mockOnChange}
        rows={6}
      />
    )

    expect(screen.getByRole('textbox', { multiline: true })).toHaveAttribute('rows', '6')
  })

  it('shows validation icon in correct position', async () => {
    const user = userEvent.setup()
    mockValidator.mockReturnValue({ isValid: true })

    render(
      <ValidatedTextarea
        id="icon-textarea"
        label="Icon Textarea"
        value=""
        onChange={mockOnChange}
        validator={mockValidator}
      />
    )

    const textarea = screen.getByRole('textbox', { multiline: true })
    await user.type(textarea, 'valid text')
    fireEvent.blur(textarea)

    await waitFor(() => {
      const container = textarea.parentElement
      expect(container.querySelector('.absolute.top-2.right-2')).toBeInTheDocument()
    })
  })
})

describe('Validation Integration', () => {
  it('works with email validation', async () => {
    const user = userEvent.setup()
    const emailValidator = (email) => {
      const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
      return {
        isValid,
        error: isValid ? undefined : 'Please enter a valid email address'
      }
    }

    render(
      <ValidatedInput
        id="email-validation"
        label="Email"
        value=""
        onChange={jest.fn()}
        validator={emailValidator}
        type="email"
      />
    )

    const input = screen.getByRole('textbox')
    
    // Test invalid email
    await user.type(input, 'invalid-email')
    fireEvent.blur(input)

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument()
    })

    // Test valid email
    await user.clear(input)
    await user.type(input, '<EMAIL>')

    await waitFor(() => {
      expect(screen.getByText(/valid/i)).toBeInTheDocument()
    })
  })

  it('works with password validation', async () => {
    const user = userEvent.setup()
    const passwordValidator = (password) => {
      if (password.length < 8) {
        return { isValid: false, error: 'Password must be at least 8 characters' }
      }
      if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
        return { isValid: false, error: 'Password must contain uppercase, lowercase, and number' }
      }
      return { isValid: true }
    }

    render(
      <ValidatedInput
        id="password-validation"
        label="Password"
        value=""
        onChange={jest.fn()}
        validator={passwordValidator}
        type="password"
      />
    )

    const input = screen.getByLabelText(/password/i)
    
    // Test short password
    await user.type(input, 'short')
    fireEvent.blur(input)

    await waitFor(() => {
      expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument()
    })

    // Test weak password
    await user.clear(input)
    await user.type(input, 'longpassword')

    await waitFor(() => {
      expect(screen.getByText(/password must contain uppercase, lowercase, and number/i)).toBeInTheDocument()
    })

    // Test strong password
    await user.clear(input)
    await user.type(input, 'StrongPass123')

    await waitFor(() => {
      expect(screen.getByText(/valid/i)).toBeInTheDocument()
    })
  })
})
