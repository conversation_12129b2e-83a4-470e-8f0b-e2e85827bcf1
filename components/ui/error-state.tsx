"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RefreshCw } from "lucide-react"
import { Button } from "./button"

interface ErrorStateProps {
  title: string
  description: string
  action?: {
    label: string
    onClick: () => void
  }
}

export function ErrorState({ title, description, action }: ErrorStateProps) {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
        <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-500" />
      </div>
      <h3 className="mt-4 text-lg font-semibold text-white">{title}</h3>
      <p className="mt-2 text-sm text-white/70">{description}</p>
      {action && (
        <Button
          variant="outline"
          className="mt-4 inline-flex items-center border-doge text-doge hover:bg-doge/20"
          onClick={action.onClick}
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          {action.label}
        </Button>
      )}
    </div>
  )
}
