// Optimized icon imports to reduce bundle size
// Only import the icons we actually use

import React from 'react'
import {
  Loader2,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  TrendingUp,
  TrendingDown,
  ArrowUp,
  ArrowDown,
  Star,
  Heart,
  MessageCircle,
  Share,
  Search,
  Filter,
  Download,
  Upload,
  Copy,
  ExternalLink,
  User,
  Settings,
  Bell,
  Home,
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  BarChart2,
  ArrowLeft,
  ArrowRight,
  RefreshCw,
  Rocket,
  Coins,
  DollarSign,
  Users,
  Vote,
  Shield,
  Lock,
  Unlock,
  ThumbsUp,
  ThumbsDown,
  HelpCircle,
  UserCheck,
  UserPlus,
  Flag,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  Code,
  GitPullRequest,
  FileText,
  Folder,
  FolderOpen,
  PieChart,
  LineChart,
  Activity,
  Wallet,
  CreditCard,
  BellOff,
  Image,
  Video,
  Clock,
  Calendar,
  CheckCircle2,
  ArrowUpRight,
  ArrowDownLeft,
  // Note: Twitter and Github icons are deprecated in newer versions
  Globe,
  BarChart3,
  Database,
  Bookmark,
  Tag,
  Zap,
  Award,
  Trophy,
  Target,
  Compass,
  Map,
  MapPin,
} from "lucide-react"

// Re-export all imported icons
export {
  // Navigation icons
  Home,
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,

  // Trading icons
  BarChart2,
  TrendingUp,
  TrendingDown,
  ArrowDown,
  ArrowUp,
  ArrowLeft,
  ArrowRight,
  RefreshCw,
  Settings,

  // Token launch icons
  Rocket,
  Coins,
  DollarSign,

  // Governance icons
  Users,
  Vote,
  Shield,
  Lock,
  Unlock,

  // Social icons
  MessageCircle,
  Heart,
  Share,
  ThumbsUp,
  ThumbsDown,

  // Utility icons
  Info,
  AlertTriangle,
  CheckCircle,
  HelpCircle,
  Search,
  Filter,
  Download,
  Upload,
  Copy,
  ExternalLink,

  // User icons
  User,
  UserCheck,
  UserPlus,

  // Admin icons
  Flag,
  Eye,
  EyeOff,
  Edit,
  Trash2,

  // Development icons
  Code,
  GitPullRequest,
  FileText,
  Folder,
  FolderOpen,

  // Chart icons
  PieChart,
  LineChart,
  Activity,

  // Wallet icons
  Wallet,
  CreditCard,

  // Notification icons
  Bell,
  BellOff,

  // Loading icons
  Loader2,

  // Media icons
  Image,
  Video,

  // Time icons
  Clock,
  Calendar,

  // Status icons
  CheckCircle2,
  XCircle,
  AlertTriangle as Warning,

  // Navigation arrows
  ArrowUpRight,
  ArrowDownLeft,

  // Social platform icons
  Globe,

  // Analytics icons
  BarChart3,
  Database,

  // Misc icons
  Star,
  Bookmark,
  Tag,
  Zap,
  Award,
  Trophy,
  Target,
  Compass,
  Map,
  MapPin,
}

// Custom icon component for consistent sizing and styling
interface IconProps {
  className?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
}

const sizeClasses = {
  xs: 'h-3 w-3',
  sm: 'h-4 w-4', 
  md: 'h-5 w-5',
  lg: 'h-6 w-6',
  xl: 'h-8 w-8'
}

export function Icon({ 
  children, 
  className = '', 
  size = 'md' 
}: IconProps & { children: React.ReactNode }) {
  return (
    <span className={`inline-flex ${sizeClasses[size]} ${className}`}>
      {children}
    </span>
  )
}

// Commonly used icon combinations
export function LoadingIcon({ size = 'md', className = '' }: IconProps) {
  return (
    <Loader2 className={`animate-spin ${sizeClasses[size]} ${className}`} />
  )
}

export function SuccessIcon({ size = 'md', className = '' }: IconProps) {
  return (
    <Icon size={size} className={`text-green-500 ${className}`}>
      <CheckCircle />
    </Icon>
  )
}

export function ErrorIcon({ size = 'md', className = '' }: IconProps) {
  return (
    <Icon size={size} className={`text-red-500 ${className}`}>
      <XCircle />
    </Icon>
  )
}

export function WarningIcon({ size = 'md', className = '' }: IconProps) {
  return (
    <Icon size={size} className={`text-yellow-500 ${className}`}>
      <AlertTriangle />
    </Icon>
  )
}

export function InfoIcon({ size = 'md', className = '' }: IconProps) {
  return (
    <Icon size={size} className={`text-blue-500 ${className}`}>
      <Info />
    </Icon>
  )
}

// Icon sets for specific features
export const TradingIcons = {
  Chart: BarChart2,
  Trending: TrendingUp,
  Down: TrendingDown,
  Swap: RefreshCw,
  Settings: Settings,
}

export const NavigationIcons = {
  Home: Home,
  Menu: Menu,
  Close: X,
  Expand: ChevronDown,
  Collapse: ChevronUp,
}

export const UserIcons = {
  Profile: User,
  Verified: UserCheck,
  Add: UserPlus,
  Wallet: Wallet,
}

export const ActionIcons = {
  Edit: Edit,
  Delete: Trash2,
  Copy: Copy,
  Share: Share,
  Download: Download,
  Upload: Upload,
}

// Dynamic icon loader for rarely used icons
export async function loadIcon(iconName: string) {
  try {
    const iconModule = await import('lucide-react')
    return iconModule[iconName as keyof typeof iconModule]
  } catch (error) {
    console.warn(`Failed to load icon: ${iconName}`)
    return null
  }
}
