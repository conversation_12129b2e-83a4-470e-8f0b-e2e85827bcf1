"use client"

import { useEffect, useRef, ReactNode } from "react"

interface FocusTrapProps {
  children: ReactNode
  active?: boolean
  restoreFocus?: boolean
  initialFocus?: string // CSS selector for initial focus element
}

export function FocusTrap({ 
  children, 
  active = true, 
  restoreFocus = true,
  initialFocus 
}: FocusTrapProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const previousActiveElement = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (!active) return

    // Store the currently focused element
    previousActiveElement.current = document.activeElement as HTMLElement

    const container = containerRef.current
    if (!container) return

    // Get all focusable elements within the container
    const getFocusableElements = (): HTMLElement[] => {
      const focusableSelectors = [
        'button:not([disabled])',
        'input:not([disabled])',
        'select:not([disabled])',
        'textarea:not([disabled])',
        'a[href]',
        '[tabindex]:not([tabindex="-1"])',
        '[contenteditable="true"]'
      ].join(', ')

      return Array.from(container.querySelectorAll(focusableSelectors))
        .filter((element) => {
          const htmlElement = element as HTMLElement
          return htmlElement.offsetParent !== null && 
                 !htmlElement.hasAttribute('aria-hidden') &&
                 htmlElement.tabIndex !== -1
        }) as HTMLElement[]
    }

    // Focus the initial element or first focusable element
    const setInitialFocus = () => {
      let elementToFocus: HTMLElement | null = null

      if (initialFocus) {
        elementToFocus = container.querySelector(initialFocus) as HTMLElement
      }

      if (!elementToFocus) {
        const focusableElements = getFocusableElements()
        elementToFocus = focusableElements[0] || container
      }

      if (elementToFocus) {
        elementToFocus.focus()
      }
    }

    // Handle tab key navigation
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return

      const focusableElements = getFocusableElements()
      if (focusableElements.length === 0) return

      const firstElement = focusableElements[0]
      const lastElement = focusableElements[focusableElements.length - 1]
      const currentElement = document.activeElement as HTMLElement

      if (event.shiftKey) {
        // Shift + Tab: move to previous element
        if (currentElement === firstElement || !container.contains(currentElement)) {
          event.preventDefault()
          lastElement.focus()
        }
      } else {
        // Tab: move to next element
        if (currentElement === lastElement || !container.contains(currentElement)) {
          event.preventDefault()
          firstElement.focus()
        }
      }
    }

    // Set initial focus
    setInitialFocus()

    // Add event listener
    document.addEventListener('keydown', handleKeyDown)

    // Cleanup function
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      
      // Restore focus to the previously focused element
      if (restoreFocus && previousActiveElement.current) {
        previousActiveElement.current.focus()
      }
    }
  }, [active, initialFocus, restoreFocus])

  return (
    <div ref={containerRef} className="focus-trap">
      {children}
    </div>
  )
}

// Hook for managing focus restoration
export function useFocusRestore() {
  const previousActiveElement = useRef<HTMLElement | null>(null)

  const saveFocus = () => {
    previousActiveElement.current = document.activeElement as HTMLElement
  }

  const restoreFocus = () => {
    if (previousActiveElement.current) {
      previousActiveElement.current.focus()
      previousActiveElement.current = null
    }
  }

  return { saveFocus, restoreFocus }
}

// Hook for managing focus within a component
export function useFocusManagement() {
  const focusFirstElement = (container: HTMLElement | null) => {
    if (!container) return

    const focusableElement = container.querySelector(
      'button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], [tabindex]:not([tabindex="-1"])'
    ) as HTMLElement

    if (focusableElement) {
      focusableElement.focus()
    }
  }

  const focusLastElement = (container: HTMLElement | null) => {
    if (!container) return

    const focusableElements = container.querySelectorAll(
      'button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], [tabindex]:not([tabindex="-1"])'
    )

    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement
    if (lastElement) {
      lastElement.focus()
    }
  }

  const moveFocus = (direction: 'next' | 'previous', container: HTMLElement | null) => {
    if (!container) return

    const focusableElements = Array.from(container.querySelectorAll(
      'button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], [tabindex]:not([tabindex="-1"])'
    )) as HTMLElement[]

    const currentIndex = focusableElements.indexOf(document.activeElement as HTMLElement)
    
    if (currentIndex === -1) return

    let nextIndex: number
    if (direction === 'next') {
      nextIndex = (currentIndex + 1) % focusableElements.length
    } else {
      nextIndex = currentIndex === 0 ? focusableElements.length - 1 : currentIndex - 1
    }

    focusableElements[nextIndex]?.focus()
  }

  return {
    focusFirstElement,
    focusLastElement,
    moveFocus
  }
}
