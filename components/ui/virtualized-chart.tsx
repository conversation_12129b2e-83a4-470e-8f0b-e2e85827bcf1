"use client"

import { useEffect, useRef, useState, useMemo, useCallback } from "react"

interface DataPoint {
  timestamp: string
  price: number
  volume?: number
  high?: number
  low?: number
  open?: number
  close?: number
}

interface VirtualizedChartProps {
  data: DataPoint[]
  width?: number
  height?: number
  showVolume?: boolean
  timeframe?: string
  onDataRequest?: (startIndex: number, endIndex: number) => void
}

export function VirtualizedChart({
  data,
  width,
  height = 400,
  showVolume = false,
  timeframe = '1D',
  onDataRequest
}: VirtualizedChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [viewportStart, setViewportStart] = useState(0)
  const [viewportEnd, setViewportEnd] = useState(100)
  const [isVisible, setIsVisible] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [lastMouseX, setLastMouseX] = useState(0)

  // Calculate visible data points based on viewport
  const visibleData = useMemo(() => {
    const start = Math.max(0, viewportStart)
    const end = Math.min(data.length, viewportEnd)
    return data.slice(start, end)
  }, [data, viewportStart, viewportEnd])

  // Intersection Observer for lazy loading
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting)
      },
      { threshold: 0.1 }
    )

    observer.observe(container)

    return () => observer.disconnect()
  }, [])

  // Optimized drawing function with virtualization
  const drawChart = useCallback((ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement) => {
    if (visibleData.length === 0) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    const padding = 40
    const chartWidth = canvas.width - padding * 2
    const chartHeight = (showVolume ? canvas.height * 0.7 : canvas.height) - padding * 2

    // Calculate price range
    const prices = visibleData.map(d => d.price)
    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)
    const priceRange = maxPrice - minPrice || 1

    // Draw grid
    ctx.strokeStyle = "rgba(255, 255, 255, 0.1)"
    ctx.lineWidth = 1

    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = padding + (chartHeight / 5) * i
      ctx.beginPath()
      ctx.moveTo(padding, y)
      ctx.lineTo(canvas.width - padding, y)
      ctx.stroke()

      // Price labels
      const price = maxPrice - ((maxPrice - minPrice) * i) / 5
      ctx.fillStyle = "rgba(255, 255, 255, 0.6)"
      ctx.font = "12px Inter, sans-serif"
      ctx.textAlign = "right"
      ctx.fillText(price.toFixed(6), padding - 5, y + 4)
    }

    // Vertical grid lines
    const timeSteps = Math.min(5, visibleData.length - 1)
    for (let i = 0; i <= timeSteps; i++) {
      const x = padding + (chartWidth / timeSteps) * i
      ctx.beginPath()
      ctx.moveTo(x, padding)
      ctx.lineTo(x, padding + chartHeight)
      ctx.stroke()

      // Time labels
      if (i < visibleData.length) {
        const dataIndex = Math.floor((visibleData.length - 1) * i / timeSteps)
        const timestamp = new Date(visibleData[dataIndex].timestamp)
        const timeLabel = timestamp.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
        
        ctx.fillStyle = "rgba(255, 255, 255, 0.6)"
        ctx.font = "10px Inter, sans-serif"
        ctx.textAlign = "center"
        ctx.fillText(timeLabel, x, canvas.height - 5)
      }
    }

    // Draw price line
    ctx.strokeStyle = "#D4AF37"
    ctx.lineWidth = 2
    ctx.lineCap = "round"
    ctx.lineJoin = "round"
    ctx.beginPath()

    visibleData.forEach((point, index) => {
      const x = padding + (index / (visibleData.length - 1)) * chartWidth
      const y = padding + ((maxPrice - point.price) / priceRange) * chartHeight

      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })

    ctx.stroke()

    // Add gradient fill
    ctx.globalAlpha = 0.2
    ctx.fillStyle = "#D4AF37"
    ctx.lineTo(canvas.width - padding, padding + chartHeight)
    ctx.lineTo(padding, padding + chartHeight)
    ctx.closePath()
    ctx.fill()
    ctx.globalAlpha = 1

    // Draw volume bars if enabled
    if (showVolume && visibleData.some(d => d.volume)) {
      const volumeHeight = canvas.height * 0.25
      const volumeY = canvas.height - volumeHeight - 20

      const volumes = visibleData.map(d => d.volume || 0)
      const maxVolume = Math.max(...volumes)

      ctx.fillStyle = "rgba(255, 255, 255, 0.1)"
      
      visibleData.forEach((point, index) => {
        if (!point.volume) return

        const x = padding + (index / (visibleData.length - 1)) * chartWidth
        const barHeight = (point.volume / maxVolume) * volumeHeight
        const barWidth = Math.max(1, chartWidth / visibleData.length * 0.8)

        ctx.fillRect(x - barWidth / 2, volumeY + volumeHeight - barHeight, barWidth, barHeight)
      })
    }
  }, [visibleData, showVolume])

  // Handle mouse interactions for panning
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true)
    setLastMouseX(e.clientX)
  }, [])

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging) return

    const deltaX = e.clientX - lastMouseX
    const sensitivity = 0.5
    const dataPointsToMove = Math.round(deltaX * sensitivity)

    if (Math.abs(dataPointsToMove) > 0) {
      setViewportStart(prev => Math.max(0, prev - dataPointsToMove))
      setViewportEnd(prev => Math.min(data.length, prev - dataPointsToMove))
      setLastMouseX(e.clientX)

      // Request more data if needed
      if (onDataRequest && (viewportStart < 10 || viewportEnd > data.length - 10)) {
        onDataRequest(Math.max(0, viewportStart - 50), Math.min(data.length + 50, viewportEnd + 50))
      }
    }
  }, [isDragging, lastMouseX, data.length, viewportStart, viewportEnd, onDataRequest])

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // Handle wheel zoom
  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault()
    
    const zoomFactor = e.deltaY > 0 ? 1.1 : 0.9
    const currentRange = viewportEnd - viewportStart
    const newRange = Math.max(10, Math.min(data.length, currentRange * zoomFactor))
    const center = (viewportStart + viewportEnd) / 2
    
    const newStart = Math.max(0, center - newRange / 2)
    const newEnd = Math.min(data.length, center + newRange / 2)
    
    setViewportStart(newStart)
    setViewportEnd(newEnd)
  }, [viewportStart, viewportEnd, data.length])

  // Canvas setup and drawing
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || !isVisible) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions with device pixel ratio
    const setDimensions = () => {
      const container = containerRef.current
      if (!container) return

      const dpr = window.devicePixelRatio || 1
      const rect = container.getBoundingClientRect()
      
      canvas.width = (width || rect.width) * dpr
      canvas.height = height * dpr
      canvas.style.width = `${width || rect.width}px`
      canvas.style.height = `${height}px`
      
      ctx.scale(dpr, dpr)
    }

    setDimensions()
    drawChart(ctx, canvas)

    window.addEventListener('resize', setDimensions)
    return () => window.removeEventListener('resize', setDimensions)
  }, [visibleData, isVisible, width, height, drawChart])

  if (!isVisible) {
    return (
      <div 
        ref={containerRef}
        className="flex items-center justify-center bg-black/20 rounded-lg"
        style={{ height: `${height}px` }}
      >
        <div className="text-white/60">Loading chart...</div>
      </div>
    )
  }

  return (
    <div 
      ref={containerRef}
      className="relative w-full cursor-grab active:cursor-grabbing"
      style={{ height: `${height}px` }}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onWheel={handleWheel}
    >
      <canvas 
        ref={canvasRef}
        className="w-full h-full rounded-lg"
        style={{ width: width ? `${width}px` : '100%', height: `${height}px` }}
      />
      
      {/* Viewport indicator */}
      <div className="absolute top-2 right-2 text-xs text-white/60 bg-black/50 px-2 py-1 rounded">
        {viewportStart}-{viewportEnd} of {data.length}
      </div>
    </div>
  )
}
