"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, AlertCircle, Info } from "lucide-react"
import { cn } from "@/lib/utils"
import { debounce } from "@/lib/validation"

interface ValidatedInputProps {
  id: string
  label: string
  value: string
  onChange: (value: string) => void
  onValidation?: (isValid: boolean, error?: string) => void
  validator?: (value: string) => { isValid: boolean; error?: string }
  placeholder?: string
  type?: string
  required?: boolean
  helpText?: string
  className?: string
  disabled?: boolean
  onFocus?: () => void
  onBlur?: () => void
}

export function ValidatedInput({
  id,
  label,
  value,
  onChange,
  onValidation,
  validator,
  placeholder,
  type = "text",
  required = false,
  helpText,
  className,
  disabled = false,
  onFocus,
  onBlur,
}: ValidatedInputProps) {
  const [validationState, setValidationState] = useState<{
    isValid: boolean
    error?: string
    isValidating: boolean
  }>({ isValid: true, isValidating: false })

  const [hasBeenTouched, setHasBeenTouched] = useState(false)

  // Debounced validation function
  const debouncedValidate = debounce((inputValue: string) => {
    if (!validator) {
      setValidationState({ isValid: true, isValidating: false })
      onValidation?.(true)
      return
    }

    const result = validator(inputValue)
    setValidationState({
      isValid: result.isValid,
      error: result.error,
      isValidating: false,
    })
    onValidation?.(result.isValid, result.error)
  }, 300)

  useEffect(() => {
    if (hasBeenTouched && value !== undefined) {
      setValidationState(prev => ({ ...prev, isValidating: true }))
      debouncedValidate(value)
    }
  }, [value, hasBeenTouched, debouncedValidate])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    onChange(newValue)
    
    if (!hasBeenTouched) {
      setHasBeenTouched(true)
    }
  }

  const handleBlur = () => {
    if (!hasBeenTouched) {
      setHasBeenTouched(true)
      if (validator && value) {
        setValidationState(prev => ({ ...prev, isValidating: true }))
        debouncedValidate(value)
      }
    }
  }

  const getValidationIcon = () => {
    if (validationState.isValidating) {
      return <div className="animate-spin h-4 w-4 border-2 border-white/20 border-t-white rounded-full" />
    }
    
    if (!hasBeenTouched || !value) {
      return null
    }

    if (validationState.isValid) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    } else {
      return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getInputClassName = () => {
    let baseClass = "glass-input border-white/10 pr-10"
    
    if (!hasBeenTouched || !value) {
      return baseClass
    }

    if (validationState.isValid) {
      baseClass += " border-green-500/50 focus:border-green-500"
    } else {
      baseClass += " border-red-500/50 focus:border-red-500"
    }

    return baseClass
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <Label htmlFor={id} className="text-white/80">
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </Label>
        {helpText && (
          <div className="flex items-center gap-1 text-xs text-white/60">
            <Info className="h-3 w-3" />
            <span>{helpText}</span>
          </div>
        )}
      </div>
      
      <div className="relative">
        <Input
          id={id}
          type={type}
          value={value}
          onChange={handleChange}
          onBlur={() => {
            handleBlur()
            onBlur?.()
          }}
          onFocus={onFocus}
          placeholder={placeholder}
          className={getInputClassName()}
          disabled={disabled}
        />
        
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {getValidationIcon()}
        </div>
      </div>

      {hasBeenTouched && !validationState.isValid && validationState.error && (
        <Alert className="border-red-500/20 bg-red-500/5">
          <AlertCircle className="h-4 w-4 text-red-500" />
          <AlertDescription className="text-red-400 text-sm">
            {validationState.error}
          </AlertDescription>
        </Alert>
      )}

      {hasBeenTouched && validationState.isValid && value && (
        <div className="flex items-center gap-1 text-xs text-green-400">
          <CheckCircle className="h-3 w-3" />
          <span>Valid</span>
        </div>
      )}
    </div>
  )
}

interface ValidatedTextareaProps {
  id: string
  label: string
  value: string
  onChange: (value: string) => void
  onValidation?: (isValid: boolean, error?: string) => void
  validator?: (value: string) => { isValid: boolean; error?: string }
  placeholder?: string
  required?: boolean
  helpText?: string
  className?: string
  disabled?: boolean
  rows?: number
}

export function ValidatedTextarea({
  id,
  label,
  value,
  onChange,
  onValidation,
  validator,
  placeholder,
  required = false,
  helpText,
  className,
  disabled = false,
  rows = 4,
}: ValidatedTextareaProps) {
  const [validationState, setValidationState] = useState<{
    isValid: boolean
    error?: string
    isValidating: boolean
  }>({ isValid: true, isValidating: false })

  const [hasBeenTouched, setHasBeenTouched] = useState(false)

  const debouncedValidate = debounce((inputValue: string) => {
    if (!validator) {
      setValidationState({ isValid: true, isValidating: false })
      onValidation?.(true)
      return
    }

    const result = validator(inputValue)
    setValidationState({
      isValid: result.isValid,
      error: result.error,
      isValidating: false,
    })
    onValidation?.(result.isValid, result.error)
  }, 300)

  useEffect(() => {
    if (hasBeenTouched && value !== undefined) {
      setValidationState(prev => ({ ...prev, isValidating: true }))
      debouncedValidate(value)
    }
  }, [value, hasBeenTouched, debouncedValidate])

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    onChange(newValue)
    
    if (!hasBeenTouched) {
      setHasBeenTouched(true)
    }
  }

  const handleBlur = () => {
    if (!hasBeenTouched) {
      setHasBeenTouched(true)
      if (validator && value) {
        setValidationState(prev => ({ ...prev, isValidating: true }))
        debouncedValidate(value)
      }
    }
  }

  const getTextareaClassName = () => {
    let baseClass = "glass-input border-white/10 resize-none"
    
    if (!hasBeenTouched || !value) {
      return baseClass
    }

    if (validationState.isValid) {
      baseClass += " border-green-500/50 focus:border-green-500"
    } else {
      baseClass += " border-red-500/50 focus:border-red-500"
    }

    return baseClass
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <Label htmlFor={id} className="text-white/80">
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </Label>
        {helpText && (
          <div className="flex items-center gap-1 text-xs text-white/60">
            <Info className="h-3 w-3" />
            <span>{helpText}</span>
          </div>
        )}
      </div>
      
      <div className="relative">
        <textarea
          id={id}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={getTextareaClassName()}
          disabled={disabled}
          rows={rows}
        />
        
        <div className="absolute top-2 right-2">
          {validationState.isValidating && (
            <div className="animate-spin h-4 w-4 border-2 border-white/20 border-t-white rounded-full" />
          )}
          {!validationState.isValidating && hasBeenTouched && value && (
            validationState.isValid ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-500" />
            )
          )}
        </div>
      </div>

      {hasBeenTouched && !validationState.isValid && validationState.error && (
        <Alert className="border-red-500/20 bg-red-500/5">
          <AlertCircle className="h-4 w-4 text-red-500" />
          <AlertDescription className="text-red-400 text-sm">
            {validationState.error}
          </AlertDescription>
        </Alert>
      )}

      {hasBeenTouched && validationState.isValid && value && (
        <div className="flex items-center gap-1 text-xs text-green-400">
          <CheckCircle className="h-3 w-3" />
          <span>Valid</span>
        </div>
      )}
    </div>
  )
}
