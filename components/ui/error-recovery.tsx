"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  RefreshCw, 
  Home, 
  ArrowLeft, 
  ExternalLink, 
  Copy, 
  CheckCircle,
  AlertTriangle,
  Wifi,
  Wallet,
  Settings
} from "lucide-react"
import { ErrorType, type AppError } from "@/lib/error-handling"
import { useToast } from "@/hooks/use-toast"

interface ErrorRecoveryProps {
  error: AppError
  onRetry?: () => void
  onGoHome?: () => void
  onGoBack?: () => void
  className?: string
}

export function ErrorRecovery({ 
  error, 
  onRetry, 
  onGoHome, 
  onGoBack,
  className = "" 
}: ErrorRecoveryProps) {
  const { toast } = useToast()
  const [isRetrying, setIsRetrying] = useState(false)
  const [diagnosticsRun, setDiagnosticsRun] = useState(false)

  const handleRetry = async () => {
    if (!onRetry) return
    
    setIsRetrying(true)
    try {
      await onRetry()
    } finally {
      setIsRetrying(false)
    }
  }

  const handleCopyError = () => {
    const errorDetails = {
      type: error.type,
      message: error.message,
      timestamp: error.timestamp,
      code: error.code,
      details: error.details,
    }
    
    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
    toast({
      title: "Error details copied",
      description: "Error information has been copied to your clipboard.",
    })
  }

  const runDiagnostics = async () => {
    setDiagnosticsRun(true)
    
    // Simulate running diagnostics
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    toast({
      title: "Diagnostics complete",
      description: "System check completed. Try the operation again.",
    })
  }

  const getRecoveryActions = () => {
    switch (error.type) {
      case ErrorType.NETWORK:
        return (
          <div className="space-y-3">
            <Alert>
              <Wifi className="h-4 w-4" />
              <AlertDescription>
                Check your internet connection and try again.
              </AlertDescription>
            </Alert>
            <div className="flex gap-2">
              <Button onClick={handleRetry} disabled={isRetrying} className="flex-1">
                <RefreshCw className={`mr-2 h-4 w-4 ${isRetrying ? 'animate-spin' : ''}`} />
                {isRetrying ? 'Retrying...' : 'Retry Connection'}
              </Button>
              <Button onClick={runDiagnostics} variant="outline" disabled={diagnosticsRun}>
                <Settings className="mr-2 h-4 w-4" />
                {diagnosticsRun ? 'Checked' : 'Check Network'}
              </Button>
            </div>
          </div>
        )

      case ErrorType.WALLET:
        return (
          <div className="space-y-3">
            <Alert>
              <Wallet className="h-4 w-4" />
              <AlertDescription>
                Please check your wallet connection and ensure it's unlocked.
              </AlertDescription>
            </Alert>
            <div className="flex gap-2">
              <Button onClick={handleRetry} disabled={isRetrying} className="flex-1">
                <RefreshCw className={`mr-2 h-4 w-4 ${isRetrying ? 'animate-spin' : ''}`} />
                Reconnect Wallet
              </Button>
              <Button 
                onClick={() => window.open('https://docs.pawpumps.com/wallet-troubleshooting')} 
                variant="outline"
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Help
              </Button>
            </div>
          </div>
        )

      case ErrorType.BLOCKCHAIN:
        return (
          <div className="space-y-3">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Transaction failed. This might be due to network congestion or insufficient gas fees.
              </AlertDescription>
            </Alert>
            <div className="flex gap-2">
              <Button onClick={handleRetry} disabled={isRetrying} className="flex-1">
                <RefreshCw className={`mr-2 h-4 w-4 ${isRetrying ? 'animate-spin' : ''}`} />
                Try Again
              </Button>
              <Button 
                onClick={() => window.open('https://docs.pawpumps.com/gas-fees')} 
                variant="outline"
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Gas Guide
              </Button>
            </div>
          </div>
        )

      case ErrorType.VALIDATION:
        return (
          <div className="space-y-3">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please check your input and try again.
              </AlertDescription>
            </Alert>
            <Button onClick={onGoBack} variant="outline" className="w-full">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back and Fix
            </Button>
          </div>
        )

      default:
        return (
          <div className="space-y-3">
            {error.retryable && (
              <Button onClick={handleRetry} disabled={isRetrying} className="w-full">
                <RefreshCw className={`mr-2 h-4 w-4 ${isRetrying ? 'animate-spin' : ''}`} />
                {isRetrying ? 'Retrying...' : 'Try Again'}
              </Button>
            )}
            <div className="flex gap-2">
              {onGoBack && (
                <Button onClick={onGoBack} variant="outline" className="flex-1">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Go Back
                </Button>
              )}
              <Button onClick={onGoHome || (() => window.location.href = '/')} variant="outline" className="flex-1">
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Button>
            </div>
          </div>
        )
    }
  }

  const getSeverityColor = () => {
    switch (error.severity) {
      case 'CRITICAL':
        return 'border-red-500/40 bg-red-500/5'
      case 'HIGH':
        return 'border-red-500/30 bg-red-500/5'
      case 'MEDIUM':
        return 'border-yellow-500/30 bg-yellow-500/5'
      case 'LOW':
        return 'border-blue-500/30 bg-blue-500/5'
      default:
        return 'border-white/10'
    }
  }

  return (
    <Card className={`glass-card ${getSeverityColor()} ${className}`}>
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <AlertTriangle className="h-5 w-5" />
          Error Recovery
        </CardTitle>
        <CardDescription className="text-white/70">
          {error.message}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {getRecoveryActions()}
        
        {/* Error details section */}
        <details className="mt-4">
          <summary className="cursor-pointer text-sm text-white/60 hover:text-white/80">
            Technical Details
          </summary>
          <div className="mt-2 p-3 bg-black/30 rounded border border-white/10">
            <div className="space-y-2 text-xs text-white/70">
              <div><strong>Type:</strong> {error.type}</div>
              <div><strong>Severity:</strong> {error.severity}</div>
              <div><strong>Time:</strong> {error.timestamp.toLocaleString()}</div>
              {error.code && <div><strong>Code:</strong> {error.code}</div>}
              {error.retryCount && <div><strong>Retry Count:</strong> {error.retryCount}</div>}
            </div>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleCopyError}
              className="mt-3 text-xs"
            >
              <Copy className="mr-1 h-3 w-3" />
              Copy Details
            </Button>
          </div>
        </details>

        {/* Help section */}
        <div className="pt-4 border-t border-white/10">
          <p className="text-xs text-white/50 mb-2">
            Need more help?
          </p>
          <div className="flex gap-2">
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={() => window.open('https://docs.pawpumps.com')}
              className="text-xs"
            >
              <ExternalLink className="mr-1 h-3 w-3" />
              Documentation
            </Button>
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={() => window.open('mailto:<EMAIL>')}
              className="text-xs"
            >
              <ExternalLink className="mr-1 h-3 w-3" />
              Contact Support
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Simplified error recovery for inline use
export function InlineErrorRecovery({ 
  error, 
  onRetry, 
  className = "" 
}: {
  error: AppError
  onRetry?: () => void
  className?: string
}) {
  const [isRetrying, setIsRetrying] = useState(false)

  const handleRetry = async () => {
    if (!onRetry) return
    
    setIsRetrying(true)
    try {
      await onRetry()
    } finally {
      setIsRetrying(false)
    }
  }

  return (
    <Alert className={`border-red-500/20 bg-red-500/5 ${className}`}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <span>{error.message}</span>
        {error.retryable && (
          <Button 
            size="sm" 
            variant="outline" 
            onClick={handleRetry}
            disabled={isRetrying}
            className="ml-2"
          >
            <RefreshCw className={`mr-1 h-3 w-3 ${isRetrying ? 'animate-spin' : ''}`} />
            Retry
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}
