"use client"

import { Loader2, Wallet, Database, Rocket, BarChart3 } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

interface LoadingStateProps {
  type?: 'default' | 'wallet' | 'data' | 'form' | 'chart'
  title?: string
  description?: string
  size?: 'sm' | 'md' | 'lg'
  showSkeleton?: boolean
}

const LoadingIcons = {
  default: Loader2,
  wallet: Wallet,
  data: Database,
  form: Rocket,
  chart: BarChart3,
}

const LoadingMessages = {
  default: { title: "Loading...", description: "Please wait while we load the content." },
  wallet: { title: "Connecting Wallet...", description: "Please confirm the connection in your wallet." },
  data: { title: "Loading Data...", description: "Fetching the latest information from the blockchain." },
  form: { title: "Processing...", description: "Your transaction is being processed." },
  chart: { title: "Loading Chart...", description: "Preparing trading data and analytics." },
}

export function LoadingState({
  type = 'default',
  title,
  description,
  size = 'md',
  showSkeleton = false
}: LoadingStateProps) {
  const Icon = LoadingIcons[type]
  const defaultMessage = LoadingMessages[type]

  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8"
  }

  if (showSkeleton) {
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <Skeleton className="h-4 w-[250px] bg-white/10" />
          <Skeleton className="h-4 w-[200px] bg-white/10" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-full bg-white/10" />
          <Skeleton className="h-4 w-full bg-white/10" />
          <Skeleton className="h-4 w-3/4 bg-white/10" />
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-doge/10 mb-4">
        <Icon className={`${sizeClasses[size]} text-doge animate-spin`} />
      </div>
      <h3 className="text-lg font-semibold text-white mb-2">
        {title || defaultMessage.title}
      </h3>
      <p className="text-sm text-white/70 max-w-md">
        {description || defaultMessage.description}
      </p>
    </div>
  )
}

export function LoadingCard({
  type = 'default',
  title,
  description,
  className = ""
}: LoadingStateProps & { className?: string }) {
  return (
    <Card className={`glass-card border-white/5 ${className}`}>
      <CardContent className="p-6">
        <LoadingState
          type={type}
          title={title}
          description={description}
          size="md"
        />
      </CardContent>
    </Card>
  )
}

export function LoadingSpinner({
  size = 'md',
  className = ""
}: {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8"
  }

  return (
    <Loader2 className={`${sizeClasses[size]} animate-spin text-doge ${className}`} />
  )
}

export function LoadingButton({
  children,
  isLoading = false,
  loadingText = "Loading...",
  ...props
}: {
  children: React.ReactNode
  isLoading?: boolean
  loadingText?: string
  [key: string]: any
}) {
  return (
    <button {...props} disabled={isLoading || props.disabled}>
      {isLoading ? (
        <div className="flex items-center gap-2">
          <LoadingSpinner size="sm" />
          <span>{loadingText}</span>
        </div>
      ) : (
        children
      )}
    </button>
  )
}
