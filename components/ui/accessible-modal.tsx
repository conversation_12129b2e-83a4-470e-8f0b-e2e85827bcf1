"use client"

import { useEffect, useRef, ReactNode } from "react"
import { X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { FocusTrap } from "@/components/ui/focus-trap"
import { cn } from "@/lib/utils"

interface AccessibleModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  description?: string
  children: ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
  closeOnOverlayClick?: boolean
  closeOnEscape?: boolean
  className?: string
}

export function AccessibleModal({
  isOpen,
  onClose,
  title,
  description,
  children,
  size = 'md',
  closeOnOverlayClick = true,
  closeOnEscape = true,
  className = ""
}: AccessibleModalProps) {
  const overlayRef = useRef<HTMLDivElement>(null)
  const modalRef = useRef<HTMLDivElement>(null)

  // Handle escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, closeOnEscape, onClose])

  // Handle body scroll lock
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = 'unset'
      }
    }
  }, [isOpen])

  // Handle overlay click
  const handleOverlayClick = (event: React.MouseEvent) => {
    if (closeOnOverlayClick && event.target === overlayRef.current) {
      onClose()
    }
  }

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl'
  }

  if (!isOpen) return null

  return (
    <div
      ref={overlayRef}
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      aria-describedby={description ? "modal-description" : undefined}
    >
      <FocusTrap active={isOpen} initialFocus="[data-modal-close]">
        <div
          ref={modalRef}
          className={cn(
            "relative w-full glass-card border-white/10 bg-black/90 backdrop-blur-xl",
            sizeClasses[size],
            className
          )}
          role="document"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/10">
            <div>
              <h2 
                id="modal-title" 
                className="text-xl font-semibold text-white"
              >
                {title}
              </h2>
              {description && (
                <p 
                  id="modal-description" 
                  className="mt-1 text-sm text-white/70"
                >
                  {description}
                </p>
              )}
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white/70 hover:text-white hover:bg-white/10"
              aria-label="Close modal"
              data-modal-close
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Content */}
          <div className="p-6">
            {children}
          </div>
        </div>
      </FocusTrap>
    </div>
  )
}

// Confirmation modal with accessible buttons
interface ConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  variant?: 'default' | 'destructive'
}

export function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = 'default'
}: ConfirmationModalProps) {
  const handleConfirm = () => {
    onConfirm()
    onClose()
  }

  return (
    <AccessibleModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="sm"
    >
      <div className="space-y-4">
        <p className="text-white/80">{message}</p>
        
        <div className="flex gap-3 justify-end">
          <Button
            variant="outline"
            onClick={onClose}
            className="border-white/20 text-white hover:bg-white/10"
          >
            {cancelText}
          </Button>
          <Button
            onClick={handleConfirm}
            className={cn(
              "pawpumps-button",
              variant === 'destructive' && "bg-red-600 hover:bg-red-700"
            )}
            data-modal-close
          >
            {confirmText}
          </Button>
        </div>
      </div>
    </AccessibleModal>
  )
}

// Loading modal
interface LoadingModalProps {
  isOpen: boolean
  title?: string
  message?: string
}

export function LoadingModal({
  isOpen,
  title = "Processing...",
  message = "Please wait while we process your request."
}: LoadingModalProps) {
  return (
    <AccessibleModal
      isOpen={isOpen}
      onClose={() => {}} // Cannot close loading modal
      title={title}
      size="sm"
      closeOnOverlayClick={false}
      closeOnEscape={false}
    >
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin h-8 w-8 border-2 border-white/20 border-t-white rounded-full" />
        <p className="text-white/80 text-center">{message}</p>
      </div>
    </AccessibleModal>
  )
}
