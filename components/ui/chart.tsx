"use client"

import * as React from "react"
import { createContext, useContext } from "react"

type ChartConfig = Record<
  string,
  {
    label: string
    color: string
  }
>

type ChartContext = {
  config: ChartConfig
}

const ChartContext = createContext<ChartContext | null>(null)

function useChartContext() {
  const context = useContext(ChartContext)

  if (!context) {
    throw new Error("useChartContext must be used within a ChartProvider")
  }

  return context
}

const ChartContainer = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    config: ChartConfig
  }
>(({ config, children, className, style, ...props }, ref) => {
  // Create CSS variables for each color
  const cssVars = Object.entries(config).reduce(
    (acc, [key, value]) => {
      acc[`--color-${key}`] = value.color
      return acc
    },
    {} as Record<string, string>,
  )

  return (
    <ChartContext.Provider value={{ config }}>
      <div
        ref={ref}
        className={className}
        style={{
          ...cssVars,
          ...style,
        }}
        {...props}
      >
        {children}
      </div>
    </ChartContext.Provider>
  )
})
ChartContainer.displayName = "ChartContainer"

const ChartTooltip = React.forwardRef<
  React.ElementRef<"div">,
  React.ComponentPropsWithoutRef<"div"> & {
    content?: React.ReactNode
  }
>(({ content, ...props }, ref) => {
  if (!content) {
    return null
  }

  return <div ref={ref} {...props} />
})
ChartTooltip.displayName = "ChartTooltip"

const ChartTooltipContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    const { config } = useChartContext()
    const { active, payload, label } = props as any

    if (!active || !payload) {
      return null
    }

    return (
      <div ref={ref} className="rounded-lg border bg-background p-2 shadow-md" {...props}>
        <div className="grid gap-2">
          <div className="text-sm font-medium">{label}</div>
          <div className="grid gap-1">
            {payload.map((item: any) => {
              const dataKey = item.dataKey
              const configItem = config[dataKey]

              if (!configItem) {
                return null
              }

              return (
                <div key={item.dataKey} className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full" style={{ background: configItem.color }} />
                  <div className="text-xs text-muted-foreground">{configItem.label}</div>
                  <div className="ml-auto text-xs font-medium">{item.value}</div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    )
  },
)
ChartTooltipContent.displayName = "ChartTooltipContent"

export { ChartContainer, ChartTooltip, ChartTooltipContent }
