"use client"

import type React from "react"

import { useState } from "react"
import { Plus, Info, RefreshCw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { useWallet } from "@/components/wallet-provider"
import { useNotification } from "@/hooks/use-notification"

type LiquidityPool = {
  id: string
  pair: string
  token1: string
  token2: string
  balance1: string
  balance2: string
  value: string
  share: string
  apy: string
}

const mockPools: LiquidityPool[] = [
  {
    id: "pool1",
    pair: "PAW/wDOGE",
    token1: "PAW",
    token2: "wDOGE",
    balance1: "42,000",
    balance2: "42",
    value: "$176.40",
    share: "0.5%",
    apy: "32.5%",
  },
  {
    id: "pool2",
    pair: "DC/wDOGE",
    token1: "DC",
    token2: "wDOGE",
    balance1: "1,500",
    balance2: "15",
    value: "$63.00",
    share: "0.2%",
    apy: "28.7%",
  },
]

export function LiquidityInterface() {
  const { isConnected } = useWallet()
  const { toast } = useToast()
  const { showNotification } = useNotification()

  const [activeTab, setActiveTab] = useState("add")
  const [isProcessing, setIsProcessing] = useState(false)
  const [formState, setFormState] = useState({
    token1: "wDOGE",
    token2: "PAW",
    amount1: "",
    amount2: "",
    selectedPool: "",
    removePercent: "50",
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormState((prev) => ({ ...prev, [name]: value }))

    // Simulate price calculation for add liquidity
    if (activeTab === "add") {
      if (name === "amount1" && value) {
        const numValue = Number.parseFloat(value)
        if (!isNaN(numValue)) {
          // Mock conversion rate
          const rate = formState.token1 === "wDOGE" ? 1000 : 0.001
          const calculatedAmount = (numValue * rate).toFixed(6)
          setFormState((prev) => ({ ...prev, amount2: calculatedAmount }))
        } else {
          setFormState((prev) => ({ ...prev, amount2: "" }))
        }
      } else if (name === "amount2" && value) {
        const numValue = Number.parseFloat(value)
        if (!isNaN(numValue)) {
          // Mock conversion rate
          const rate = formState.token2 === "wDOGE" ? 0.001 : 1000
          const calculatedAmount = (numValue * rate).toFixed(6)
          setFormState((prev) => ({ ...prev, amount1: calculatedAmount }))
        } else {
          setFormState((prev) => ({ ...prev, amount1: "" }))
        }
      }
    }
  }

  const handleTokenChange = (field: "token1" | "token2", value: string) => {
    setFormState((prev) => {
      // If selecting the same token that's already in the other field, swap them
      if (field === "token1" && value === prev.token2) {
        return { ...prev, token1: value, token2: prev.token1 }
      } else if (field === "token2" && value === prev.token1) {
        return { ...prev, token2: value, token1: prev.token2 }
      }

      return { ...prev, [field]: value }
    })

    // Recalculate amounts if needed
    if (activeTab === "add" && formState.amount1) {
      const numValue = Number.parseFloat(formState.amount1)
      if (!isNaN(numValue)) {
        // Mock conversion rate based on new token selection
        const rate = value === "wDOGE" ? 0.001 : 1000
        const calculatedAmount = (numValue * rate).toFixed(6)
        setFormState((prev) => ({ ...prev, amount2: calculatedAmount }))
      }
    }
  }

  const handlePoolSelect = (poolId: string) => {
    const pool = mockPools.find((p) => p.id === poolId)
    if (pool) {
      setFormState((prev) => ({
        ...prev,
        selectedPool: poolId,
        token1: pool.token1,
        token2: pool.token2,
      }))
    }
  }

  const handleAddLiquidity = async () => {
    if (!isConnected) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to add liquidity",
        variant: "destructive",
      })
      return
    }

    if (!formState.amount1 || !formState.amount2) {
      toast({
        title: "Invalid amounts",
        description: "Please enter valid amounts for both tokens",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)

    try {
      // Simulate adding liquidity
      await new Promise((resolve) => setTimeout(resolve, 2000))

      showNotification({
        title: "Liquidity Added!",
        message: `Added ${formState.amount1} ${formState.token1} and ${formState.amount2} ${formState.token2} to the pool.`,
        type: "success",
        addToCenter: true,
      })

      // Reset form
      setFormState((prev) => ({
        ...prev,
        amount1: "",
        amount2: "",
      }))
    } catch (error) {
      showNotification({
        title: "Failed to Add Liquidity",
        message: "There was an error adding liquidity. Please try again.",
        type: "error",
        addToCenter: true,
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRemoveLiquidity = async () => {
    if (!isConnected) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to remove liquidity",
        variant: "destructive",
      })
      return
    }

    if (!formState.selectedPool) {
      toast({
        title: "No pool selected",
        description: "Please select a liquidity pool",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)

    try {
      // Simulate removing liquidity
      await new Promise((resolve) => setTimeout(resolve, 2000))

      const pool = mockPools.find((p) => p.id === formState.selectedPool)
      const percentRemoved = Number.parseInt(formState.removePercent)

      showNotification({
        title: "Liquidity Removed!",
        message: `Removed ${percentRemoved}% of your liquidity from the ${pool?.pair} pool.`,
        type: "success",
        addToCenter: true,
      })

      // Reset form
      setFormState((prev) => ({
        ...prev,
        selectedPool: "",
        removePercent: "50",
      }))
    } catch (error) {
      showNotification({
        title: "Failed to Remove Liquidity",
        message: "There was an error removing liquidity. Please try again.",
        type: "error",
        addToCenter: true,
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Card className="glass-card border-white/5 liquid-glow">
      <CardHeader>
        <CardTitle className="text-white">Liquidity</CardTitle>
        <CardDescription className="text-white/70">Add or remove liquidity to earn fees</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="add" onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 glass mb-6">
            <TabsTrigger value="add">Add</TabsTrigger>
            <TabsTrigger value="remove">Remove</TabsTrigger>
          </TabsList>

          <TabsContent value="add" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="amount1" className="text-white/80">
                    First Token
                  </Label>
                  <span className="text-xs text-white/60">Balance: 1000</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative flex-1">
                    <Input
                      id="amount1"
                      name="amount1"
                      type="number"
                      placeholder="0.0"
                      value={formState.amount1}
                      onChange={handleInputChange}
                      className="glass-input border-white/10 pr-20"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <Button
                        variant="ghost"
                        className="h-6 text-xs font-medium text-white"
                        onClick={() => {
                          setFormState((prev) => ({
                            ...prev,
                            amount1: "100",
                            amount2: prev.token1 === "wDOGE" ? "100000" : "0.1",
                          }))
                        }}
                      >
                        MAX
                      </Button>
                    </div>
                  </div>
                  <Select value={formState.token1} onValueChange={(value) => handleTokenChange("token1", value)}>
                    <SelectTrigger className="w-[110px] glass-input border-white/10">
                      <SelectValue placeholder="Token" />
                    </SelectTrigger>
                    <SelectContent className="glass">
                      <SelectItem value="wDOGE">wDOGE</SelectItem>
                      <SelectItem value="PAW">PAW</SelectItem>
                      <SelectItem value="DC">DC</SelectItem>
                      <SelectItem value="SHIB">SHIB</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-center">
                <div className="rounded-full bg-white/5 p-2">
                  <Plus className="h-4 w-4 text-white/60" />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="amount2" className="text-white/80">
                    Second Token
                  </Label>
                  <span className="text-xs text-white/60">Balance: 42000</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Input
                    id="amount2"
                    name="amount2"
                    type="number"
                    placeholder="0.0"
                    value={formState.amount2}
                    onChange={handleInputChange}
                    className="flex-1 glass-input border-white/10"
                  />
                  <Select value={formState.token2} onValueChange={(value) => handleTokenChange("token2", value)}>
                    <SelectTrigger className="w-[110px] glass-input border-white/10">
                      <SelectValue placeholder="Token" />
                    </SelectTrigger>
                    <SelectContent className="glass">
                      <SelectItem value="wDOGE">wDOGE</SelectItem>
                      <SelectItem value="PAW">PAW</SelectItem>
                      <SelectItem value="DC">DC</SelectItem>
                      <SelectItem value="SHIB">SHIB</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {formState.amount1 && formState.amount2 && (
                <div className="rounded-md bg-white/5 p-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-white/60">Price</span>
                    <span className="text-white/90">
                      1 {formState.token1} ={" "}
                      {(Number.parseFloat(formState.amount2) / Number.parseFloat(formState.amount1)).toFixed(6)}{" "}
                      {formState.token2}
                    </span>
                  </div>
                  <div className="mt-1 flex items-center justify-between">
                    <span className="text-white/60">Share of Pool</span>
                    <span className="text-white/90">~0.2%</span>
                  </div>
                </div>
              )}

              <div className="flex items-center gap-2 p-3 rounded-md bg-doge/10 border border-doge/20">
                <Info className="h-4 w-4 text-doge flex-shrink-0" />
                <p className="text-xs text-white/80">
                  When you add liquidity, you will receive LP tokens representing your position. These tokens
                  automatically earn fees proportional to your share of the pool.
                </p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="remove" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-white/80">Your Liquidity Positions</Label>
                {mockPools.length > 0 ? (
                  <div className="space-y-2">
                    {mockPools.map((pool) => (
                      <div
                        key={pool.id}
                        className={`p-3 rounded-lg ${
                          formState.selectedPool === pool.id
                            ? "bg-doge/10 border border-doge/20"
                            : "bg-white/5 border border-white/10 hover:bg-white/10"
                        } cursor-pointer transition-colors`}
                        onClick={() => handlePoolSelect(pool.id)}
                      >
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-white">{pool.pair}</span>
                          <span className="text-doge">{pool.apy} APR</span>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div className="text-white/70">
                            {pool.balance1} {pool.token1}
                          </div>
                          <div className="text-white/70">
                            {pool.balance2} {pool.token2}
                          </div>
                        </div>
                        <div className="flex items-center justify-between mt-1 text-sm">
                          <span className="text-white/60">Pool share: {pool.share}</span>
                          <span className="text-white/60">Value: {pool.value}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-6 rounded-lg bg-white/5 border border-white/10 text-center">
                    <p className="text-white/60">You don't have any liquidity positions yet.</p>
                  </div>
                )}
              </div>

              {formState.selectedPool && (
                <>
                  <div className="space-y-2">
                    <Label className="text-white/80">Amount to Remove</Label>
                    <div className="grid grid-cols-4 gap-2">
                      {["25", "50", "75", "100"].map((percent) => (
                        <Button
                          key={percent}
                          variant={formState.removePercent === percent ? "default" : "outline"}
                          className={
                            formState.removePercent === percent
                              ? "doge-button doge-shine"
                              : "glass-button border-white/10"
                          }
                          onClick={() => setFormState((prev) => ({ ...prev, removePercent: percent }))}
                        >
                          {percent}%
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div className="rounded-md bg-white/5 p-3 text-sm">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white/60">You Will Receive</span>
                    </div>
                    <div className="space-y-1">
                      {(() => {
                        const pool = mockPools.find((p) => p.id === formState.selectedPool)
                        if (!pool) return null

                        const percent = Number.parseInt(formState.removePercent) / 100
                        const amount1 = Number.parseFloat(pool.balance1.replace(/,/g, "")) * percent
                        const amount2 = Number.parseFloat(pool.balance2.replace(/,/g, "")) * percent

                        return (
                          <>
                            <div className="flex items-center justify-between">
                              <span className="text-white/90">
                                {amount1.toLocaleString(undefined, { maximumFractionDigits: 6 })} {pool.token1}
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-white/90">
                                {amount2.toLocaleString(undefined, { maximumFractionDigits: 6 })} {pool.token2}
                              </span>
                            </div>
                          </>
                        )
                      })()}
                    </div>
                  </div>
                </>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter>
        {activeTab === "add" ? (
          <Button
            onClick={handleAddLiquidity}
            disabled={isProcessing || !formState.amount1 || !formState.amount2 || !isConnected}
            className="w-full cosmic-button"
          >
            {isProcessing ? (
              <div className="flex items-center">
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                <span>Adding Liquidity...</span>
              </div>
            ) : !isConnected ? (
              <span>Connect Wallet</span>
            ) : !formState.amount1 || !formState.amount2 ? (
              <span>Enter Amounts</span>
            ) : (
              <span>Add Liquidity</span>
            )}
          </Button>
        ) : (
          <Button
            onClick={handleRemoveLiquidity}
            disabled={isProcessing || !formState.selectedPool || !isConnected}
            className="w-full cosmic-button"
          >
            {isProcessing ? (
              <div className="flex items-center">
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                <span>Removing Liquidity...</span>
              </div>
            ) : !isConnected ? (
              <span>Connect Wallet</span>
            ) : !formState.selectedPool ? (
              <span>Select a Position</span>
            ) : (
              <span>Remove {formState.removePercent}% Liquidity</span>
            )}
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
