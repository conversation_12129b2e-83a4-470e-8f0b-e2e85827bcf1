"use client"

import dynamic from "next/dynamic"

// Global background animations for all pages - Client Component
const AnimatedParticles = dynamic(() => import("@/components/animated-particles").then(mod => ({ default: mod.AnimatedParticles })), {
  ssr: false,
  loading: () => null, // No loading state needed for background animation
})

export function GlobalBackground() {
  return <AnimatedParticles />
}
