"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Vote, Users, Clock, TrendingUp } from "lucide-react"
import { GovernanceDashboardLoading } from "@/components/governance/governance-loading-states"
import { useCache, usePerformanceMonitoring } from "@/components/governance/performance-optimizations"

interface Proposal {
  id: string
  title: string
  description: string
  status: 'active' | 'passed' | 'failed' | 'pending'
  votesFor: number
  votesAgainst: number
  totalVotes: number
  endDate: string
  category: string
}

const mockProposals: Proposal[] = [
  {
    id: "1",
    title: "Increase Staking Rewards",
    description: "Proposal to increase staking rewards from 5% to 7% APY",
    status: "active",
    votesFor: 1250,
    votesAgainst: 340,
    totalVotes: 1590,
    endDate: "2024-01-15",
    category: "Economics"
  },
  {
    id: "2", 
    title: "New Token Listing Criteria",
    description: "Establish new criteria for token listings on the platform",
    status: "passed",
    votesFor: 2100,
    votesAgainst: 450,
    totalVotes: 2550,
    endDate: "2024-01-10",
    category: "Governance"
  },
  {
    id: "3",
    title: "Platform Fee Reduction",
    description: "Reduce trading fees from 0.3% to 0.25%",
    status: "active",
    votesFor: 890,
    votesAgainst: 1200,
    totalVotes: 2090,
    endDate: "2024-01-20",
    category: "Economics"
  }
]

export function GovernanceDashboard() {
  const [selectedTab, setSelectedTab] = useState("proposals")
  const [isLoading, setIsLoading] = useState(true)

  // Performance monitoring
  const metrics = usePerformanceMonitoring()

  // Cache governance data
  const { data: governanceData, loading: dataLoading } = useCache(
    "governance-dashboard-data",
    async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      return {
        proposals: mockProposals,
        stats: {
          totalProposals: mockProposals.length,
          activeVotes: mockProposals.filter(p => p.status === 'active').length,
          totalVoters: 1250,
          treasuryBalance: 5000000
        }
      }
    },
    2 * 60 * 1000 // 2 minutes cache
  )

  // Simulate loading data
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1500)
    return () => clearTimeout(timer)
  }, [])

  if (isLoading || dataLoading) {
    return <GovernanceDashboardLoading />
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-blue-500'
      case 'passed': return 'bg-green-500'
      case 'failed': return 'bg-red-500'
      case 'pending': return 'bg-yellow-500'
      default: return 'bg-gray-500'
    }
  }

  const getVotePercentage = (votesFor: number, totalVotes: number) => {
    return totalVotes > 0 ? (votesFor / totalVotes) * 100 : 0
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Governance Dashboard</h1>
          <p className="text-white/70">Participate in platform governance and vote on proposals</p>
        </div>
        <Button className="bg-doge hover:bg-doge/90">
          Create Proposal
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Vote className="h-5 w-5 text-doge" />
              <div>
                <p className="text-sm text-white/70">Active Proposals</p>
                <p className="text-2xl font-bold text-white">12</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-doge" />
              <div>
                <p className="text-sm text-white/70">Total Voters</p>
                <p className="text-2xl font-bold text-white">5,432</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-doge" />
              <div>
                <p className="text-sm text-white/70">Participation Rate</p>
                <p className="text-2xl font-bold text-white">68%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-doge" />
              <div>
                <p className="text-sm text-white/70">Avg. Vote Time</p>
                <p className="text-2xl font-bold text-white">2.3d</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="proposals">Proposals</TabsTrigger>
          <TabsTrigger value="voting">My Votes</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="proposals" className="space-y-4">
          {mockProposals.map((proposal) => (
            <Card key={proposal.id} className="glass-card border-white/5">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CardTitle className="text-white">{proposal.title}</CardTitle>
                    <Badge className={getStatusColor(proposal.status)}>
                      {proposal.status}
                    </Badge>
                  </div>
                  <Badge variant="outline" className="text-white/70">
                    {proposal.category}
                  </Badge>
                </div>
                <CardDescription className="text-white/70">
                  {proposal.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between text-sm text-white/70">
                    <span>Votes For: {proposal.votesFor}</span>
                    <span>Votes Against: {proposal.votesAgainst}</span>
                    <span>Total: {proposal.totalVotes}</span>
                  </div>
                  
                  <Progress 
                    value={getVotePercentage(proposal.votesFor, proposal.totalVotes)} 
                    className="h-2"
                  />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-white/70">
                      Ends: {new Date(proposal.endDate).toLocaleDateString()}
                    </span>
                    <div className="space-x-2">
                      <Button size="sm" variant="outline">
                        Vote Against
                      </Button>
                      <Button size="sm" className="bg-doge hover:bg-doge/90">
                        Vote For
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="voting" className="space-y-4">
          <Card className="glass-card border-white/5">
            <CardContent className="p-6">
              <p className="text-center text-white/70">Your voting history will appear here</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card className="glass-card border-white/5">
            <CardContent className="p-6">
              <p className="text-center text-white/70">Governance analytics will appear here</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
