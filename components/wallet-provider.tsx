"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { WalletErrorBoundary } from "@/components/error-boundaries/wallet-error-boundary"
import {
  walletConnectors,
  getAvailableWallets,
  getWalletConnector,
  WalletConnector,
  WalletConnection,
  DOGECHAIN_NETWORK,
  addTokenToWallet
} from "@/lib/wallet-connectors"
import { getAllTokenBalances, TokenBalance } from "@/lib/token-utils"
import { ethers } from "ethers"

interface WalletContextType {
  address: string | null
  isConnected: boolean
  balance: string | null
  chainId: number
  connector: WalletConnector | null
  provider: ethers.BrowserProvider | null
  signer: ethers.Signer | null
  tokenBalances: TokenBalance[]
  isLoadingBalances: boolean
  connect: (connectorId?: string) => Promise<void>
  disconnect: () => Promise<void>
  switchNetwork: (chainId: number) => Promise<void>
  addToken: (tokenAddress: string, tokenSymbol: string, tokenDecimals: number, tokenImage?: string) => Promise<boolean>
  getAvailableWallets: () => WalletConnector[]
  refreshBalances: () => Promise<void>
  isConnecting: boolean
  error: string | null
}

export const WalletContext = createContext<WalletContextType>({
  address: null,
  isConnected: false,
  balance: null,
  chainId: 2000,
  connector: null,
  provider: null,
  signer: null,
  tokenBalances: [],
  isLoadingBalances: false,
  connect: async () => {},
  disconnect: async () => {},
  switchNetwork: async () => {},
  addToken: async () => false,
  getAvailableWallets: () => [],
  refreshBalances: async () => {},
  isConnecting: false,
  error: null,
})

export function useWallet() {
  return useContext(WalletContext)
}

export function WalletProvider({ children }: { children: ReactNode }) {
  const [address, setAddress] = useState<string | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [balance, setBalance] = useState<string | null>(null)
  const [chainId, setChainId] = useState(2000)
  const [connector, setConnector] = useState<WalletConnector | null>(null)
  const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null)
  const [signer, setSigner] = useState<ethers.Signer | null>(null)
  const [tokenBalances, setTokenBalances] = useState<TokenBalance[]>([])
  const [isLoadingBalances, setIsLoadingBalances] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load saved wallet connection on mount
  useEffect(() => {
    const savedConnectorId = localStorage.getItem("wallet-connector-id")
    const savedAddress = localStorage.getItem("wallet-address")

    if (savedConnectorId && savedAddress) {
      // Try to reconnect automatically
      const savedConnector = getWalletConnector(savedConnectorId)
      if (savedConnector && savedConnector.isAvailable()) {
        connect(savedConnectorId).catch(console.error)
      }
    }
  }, [])

  // Update balance when address or provider changes
  useEffect(() => {
    if (address && provider) {
      updateBalance()
      refreshBalances()
    }
  }, [address, provider])

  const updateBalance = async () => {
    if (!address || !provider) return

    try {
      const balance = await provider.getBalance(address)
      setBalance(ethers.formatEther(balance))
    } catch (error) {
      console.error('Error fetching balance:', error)
    }
  }

  const refreshBalances = async () => {
    if (!address || !provider) return

    setIsLoadingBalances(true)
    try {
      const balances = await getAllTokenBalances(provider, address)
      setTokenBalances(balances)
    } catch (error) {
      console.error('Error fetching token balances:', error)
    } finally {
      setIsLoadingBalances(false)
    }
  }

  const connect = async (connectorId?: string) => {
    setIsConnecting(true)
    setError(null)

    try {
      let selectedConnector: WalletConnector | undefined

      if (connectorId) {
        selectedConnector = getWalletConnector(connectorId)
      } else {
        // Use first available wallet
        const availableWallets = getAvailableWallets()
        selectedConnector = availableWallets[0]
      }

      if (!selectedConnector) {
        throw new Error('No wallet connector available')
      }

      const connection = await selectedConnector.connect()

      setConnector(selectedConnector)
      setAddress(connection.address)
      setChainId(connection.chainId)
      setProvider(connection.provider as ethers.BrowserProvider)
      setSigner(connection.signer)
      setIsConnected(true)

      // Save connection info
      localStorage.setItem("wallet-connector-id", selectedConnector.id)
      localStorage.setItem("wallet-address", connection.address)

      // Set up event listeners
      setupEventListeners(selectedConnector.getProvider())

    } catch (error: any) {
      console.error('Wallet connection error:', error)
      setError(error.message || 'Failed to connect wallet')
      throw error
    } finally {
      setIsConnecting(false)
    }
  }

  const disconnect = async () => {
    try {
      if (connector) {
        await connector.disconnect()
      }

      // Clear state
      setConnector(null)
      setAddress(null)
      setChainId(2000)
      setProvider(null)
      setSigner(null)
      setBalance(null)
      setIsConnected(false)
      setError(null)

      // Clear storage
      localStorage.removeItem("wallet-connector-id")
      localStorage.removeItem("wallet-address")

    } catch (error) {
      console.error('Disconnect error:', error)
    }
  }

  const setupEventListeners = (walletProvider: any) => {
    if (!walletProvider) return

    // Account changed
    walletProvider.on?.('accountsChanged', (accounts: string[]) => {
      if (accounts.length === 0) {
        disconnect()
      } else if (accounts[0] !== address) {
        setAddress(accounts[0])
        localStorage.setItem("wallet-address", accounts[0])
      }
    })

    // Chain changed
    walletProvider.on?.('chainChanged', (chainId: string) => {
      const newChainId = parseInt(chainId, 16)
      setChainId(newChainId)
    })

    // Disconnect
    walletProvider.on?.('disconnect', () => {
      disconnect()
    })
  }

  const switchNetwork = async (targetChainId: number) => {
    if (!connector || !connector.getProvider()) {
      throw new Error('No wallet connected')
    }

    const walletProvider = connector.getProvider() as any

    try {
      await walletProvider.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${targetChainId.toString(16)}` }],
      })
    } catch (switchError: any) {
      // If the chain is not added, add it
      if (switchError.code === 4902) {
        const networkConfig = targetChainId === 2000 ? DOGECHAIN_NETWORK : null
        if (networkConfig) {
          await walletProvider.request({
            method: 'wallet_addEthereumChain',
            params: [
              {
                chainId: `0x${networkConfig.chainId.toString(16)}`,
                chainName: networkConfig.chainName,
                nativeCurrency: networkConfig.nativeCurrency,
                rpcUrls: networkConfig.rpcUrls,
                blockExplorerUrls: networkConfig.blockExplorerUrls,
              },
            ],
          })
        }
      } else {
        throw switchError
      }
    }
  }

  const addToken = async (
    tokenAddress: string,
    tokenSymbol: string,
    tokenDecimals: number,
    tokenImage?: string
  ): Promise<boolean> => {
    if (!connector || !connector.getProvider()) {
      throw new Error('No wallet connected')
    }

    return addTokenToWallet(
      connector.getProvider(),
      tokenAddress,
      tokenSymbol,
      tokenDecimals,
      tokenImage
    )
  }

  return (
    <WalletContext.Provider
      value={{
        address,
        isConnected,
        balance,
        chainId,
        connector,
        provider,
        signer,
        tokenBalances,
        isLoadingBalances,
        connect,
        disconnect,
        switchNetwork,
        addToken,
        getAvailableWallets,
        refreshBalances,
        isConnecting,
        error,
      }}
    >
      <WalletErrorBoundary onRetry={() => connect()}>
        {children}
      </WalletErrorBoundary>
    </WalletContext.Provider>
  )
}
