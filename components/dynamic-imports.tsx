// Dynamic imports for code splitting and lazy loading
import dynamic from 'next/dynamic'
import { ComponentType } from 'react'

// Loading components
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-doge"></div>
  </div>
)

const LoadingChart = () => (
  <div className="h-[300px] w-full flex items-center justify-center bg-black/20 rounded-lg">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-doge mx-auto mb-2"></div>
      <div className="text-white/60 text-sm">Loading chart...</div>
    </div>
  </div>
)

const LoadingTable = () => (
  <div className="w-full p-4">
    <div className="space-y-3">
      <div className="h-4 bg-white/10 rounded w-3/4"></div>
      <div className="h-4 bg-white/10 rounded w-1/2"></div>
      <div className="h-4 bg-white/10 rounded w-5/6"></div>
    </div>
  </div>
)

// Trading components (heavy)
export const TradingChart = dynamic(
  () => import('./trading-chart').then(mod => ({ default: mod.TradingChart })),
  {
    loading: LoadingChart,
    ssr: false, // Charts don't need SSR
  }
)

export const VirtualizedChart = dynamic(
  () => import('./ui/virtualized-chart').then(mod => ({ default: mod.VirtualizedChart })),
  {
    loading: LoadingChart,
    ssr: false,
  }
)

export const TradingInterface = dynamic(
  () => import('./trading-interface').then(mod => ({ default: mod.TradingInterface })),
  {
    loading: LoadingSpinner,
    ssr: false, // Trading interface is interactive
  }
)

// Analytics components (heavy with charts)
export const AnalyticsDashboard = dynamic(
  () => import('./analytics-dashboard').then(mod => ({ default: mod.AnalyticsDashboard })),
  {
    loading: LoadingSpinner,
    ssr: false,
  }
)



// Social features (can be lazy loaded)
export const SocialFeed = dynamic(
  () => import('./social-feed').then(mod => ({ default: mod.SocialFeed })),
  {
    loading: LoadingSpinner,
    ssr: false,
  }
)

export const UserProfile = dynamic(
  () => import('./user-profile').then(mod => ({ default: mod.UserProfile })),
  {
    loading: LoadingSpinner,
    ssr: true, // User profiles benefit from SSR for SEO
  }
)

export const AdminDashboard = dynamic(
  () => import('./admin-dashboard').then(mod => ({ default: mod.AdminDashboard })),
  {
    loading: LoadingSpinner,
    ssr: false, // Admin tools don't need SSR
  }
)

// Form components (can be heavy with validation)
export const TokenLaunchForm = dynamic(
  () => import('./token-launch-form').then(mod => ({ default: mod.TokenLaunchForm })),
  {
    loading: LoadingSpinner,
    ssr: false, // Forms are interactive
  }
)

// Modal components (only load when needed)
export const WalletConnectModal = dynamic(
  () => import('./wallet-connect-modal').then(mod => ({ default: mod.WalletConnectModal })),
  {
    loading: () => <div className="bg-white/10 rounded-lg h-64 w-96"></div>,
    ssr: false,
  }
)



// Utility function for conditional dynamic imports
export function createConditionalDynamicImport<T>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  condition: () => boolean,
  fallback?: ComponentType<T>
) {
  if (condition()) {
    return dynamic(importFn, {
      loading: LoadingSpinner,
      ssr: false,
    })
  }
  
  return fallback || (() => null)
}

// Route-based dynamic imports
export const TradePageComponents = {
  TradingInterface: dynamic(() => import('./trading-interface').then(mod => ({ default: mod.TradingInterface })), {
    loading: LoadingSpinner,
    ssr: false,
  }),
  TradingChart: dynamic(() => import('./trading-chart').then(mod => ({ default: mod.TradingChart })), {
    loading: LoadingChart,
    ssr: false,
  }),
}

export const LaunchPageComponents = {
  TokenLaunchForm: dynamic(() => import('./token-launch-form').then(mod => ({ default: mod.TokenLaunchForm })), {
    loading: LoadingSpinner,
    ssr: false,
  }),
}



// Feature flag based dynamic imports
export function createFeatureFlaggedComponent<T>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  featureFlag: string,
  fallback?: ComponentType<T>
) {
  // In a real app, you'd check your feature flag service
  const isEnabled = process.env[`FEATURE_${featureFlag}`] === 'true'
  
  if (isEnabled) {
    return dynamic(importFn, {
      loading: LoadingSpinner,
      ssr: false,
    })
  }
  
  return fallback || (() => null)
}

// Bundle size monitoring
export function withBundleTracking<T extends Record<string, any>>(
  Component: ComponentType<T>,
  componentName: string
) {
  return function TrackedComponent(props: T) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Loading component: ${componentName}`)
    }

    return <Component {...(props as any)} />
  }
}

// Preload utilities for critical components
export const preloadCriticalComponents = () => {
  if (typeof window !== 'undefined') {
    // Preload components that are likely to be needed soon
    const preloadPromises = [
      import('./trading-interface'),
      import('./wallet-connect-modal'),
    ]
    
    Promise.allSettled(preloadPromises).then(() => {
      console.log('Critical components preloaded')
    })
  }
}

// Component size estimation (for development)
export const componentSizeEstimates = {
  TradingInterface: '~45KB',
  TradingChart: '~25KB',
  AnalyticsDashboard: '~60KB',
  AdminDashboard: '~40KB',
  SocialFeed: '~30KB',
  TokenLaunchForm: '~35KB',
  VirtualizedChart: '~20KB',
}

// Export all for easy access
export default {
  TradingChart,
  VirtualizedChart,
  TradingInterface,
  AnalyticsDashboard,
  SocialFeed,
  TokenLaunchForm,
  AdminDashboard,
  UserProfile,
  WalletConnectModal,
  preloadCriticalComponents,
  componentSizeEstimates,
}
