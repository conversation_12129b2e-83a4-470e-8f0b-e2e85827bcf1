"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  BarChart2,
  FileText,
  GitPullRequest,
  LayoutDashboard,
  Lock,
  Settings,
  Shield,
  Users,
  Workflow,
  HelpCircle,
  Landmark,
  Map,
  TrendingUp,
  Trophy,
  Flag,
  AlertTriangle,
  Wallet,
  UserCheck,
  FolderKanban,
  Activity,
  Gift,
  Coins,
  MessageSquare,
  Bell,
  Gauge,
  Cog,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface NavItem {
  title: string
  href: string
  icon: React.ReactNode
  description: string
  section: "governance" | "development" | "admin"
}

export function UnifiedGovernanceNav() {
  const pathname = usePathname()
  const [activeSection, setActiveSection] = useState<"governance" | "development" | "admin">("governance")

  // Determine initial active section based on current path
  useEffect(() => {
    if (
      pathname?.includes("/development") ||
      pathname?.includes("/roadmap") ||
      pathname?.includes("/progress-report")
    ) {
      setActiveSection("development")
    } else if (pathname?.includes("/governance/admin")) {
      setActiveSection("admin")
    } else if (pathname?.includes("/governance")) {
      setActiveSection("governance")
    }
  }, [pathname])

  const navItems: NavItem[] = [
    // Governance Section
    {
      title: "Proposals",
      href: "/governance/proposals",
      icon: <GitPullRequest className="h-5 w-5" />,
      description: "View and vote on governance proposals",
      section: "governance",
    },
    {
      title: "Staking",
      href: "/governance/staking",
      icon: <Lock className="h-5 w-5" />,
      description: "Stake tokens to increase voting power",
      section: "governance",
    },
    {
      title: "Rewards",
      href: "/governance/rewards",
      icon: <Trophy className="h-5 w-5" />,
      description: "Earn rewards for participation",
      section: "governance",
    },
    {
      title: "Analytics",
      href: "/governance/analytics",
      icon: <BarChart2 className="h-5 w-5" />,
      description: "Governance metrics and reports",
      section: "governance",
    },
    {
      title: "Treasury",
      href: "/governance/treasury",
      icon: <Landmark className="h-5 w-5" />,
      description: "Treasury management and tools",
      section: "governance",
    },
    {
      title: "Achievements",
      href: "/governance/achievements",
      icon: <Trophy className="h-5 w-5" />,
      description: "Governance participation rewards",
      section: "governance",
    },
    {
      title: "Delegation",
      href: "/governance/delegation",
      icon: <UserCheck className="h-5 w-5" />,
      description: "Delegate your voting power",
      section: "governance",
    },
    {
      title: "Working Groups",
      href: "/governance/working-groups",
      icon: <FolderKanban className="h-5 w-5" />,
      description: "Specialized committees",
      section: "governance",
    },
    {
      title: "Health",
      href: "/governance/health",
      icon: <Activity className="h-5 w-5" />,
      description: "Governance health metrics",
      section: "governance",
    },

    // Development Section
    {
      title: "Overview",
      href: "/development-overview",
      icon: <LayoutDashboard className="h-5 w-5" />,
      description: "Development overview dashboard",
      section: "development",
    },
    {
      title: "Development Tracker",
      href: "/development-tracker",
      icon: <Workflow className="h-5 w-5" />,
      description: "Track development progress",
      section: "development",
    },
    {
      title: "Development Staking",
      href: "/development-staking",
      icon: <Shield className="h-5 w-5" />,
      description: "Stake tokens to influence development",
      section: "development",
    },
    {
      title: "Roadmap",
      href: "/roadmap",
      icon: <Map className="h-5 w-5" />,
      description: "View the development roadmap",
      section: "development",
    },
    {
      title: "Progress Report",
      href: "/progress-report",
      icon: <TrendingUp className="h-5 w-5" />,
      description: "Detailed development metrics",
      section: "development",
    },
    {
      title: "Documentation",
      href: "/docs/development-dao",
      icon: <FileText className="h-5 w-5" />,
      description: "Development DAO documentation",
      section: "development",
    },

    // Admin Section
    {
      title: "Admin Dashboard",
      href: "/governance/admin/dashboard",
      icon: <Settings className="h-5 w-5" />,
      description: "Platform administration overview",
      section: "admin",
    },
    {
      title: "Analytics",
      href: "/governance/admin/analytics",
      icon: <BarChart2 className="h-5 w-5" />,
      description: "Detailed platform analytics and reports",
      section: "admin",
    },
    {
      title: "Development DAO",
      href: "/governance/admin/development-dao",
      icon: <FolderKanban className="h-5 w-5" />,
      description: "Manage development governance and tasks",
      section: "admin",
    },
    {
      title: "Proposals Management",
      href: "/governance/admin/proposals",
      icon: <FileText className="h-5 w-5" />,
      description: "Administrative proposal management",
      section: "admin",
    },
    {
      title: "Content Moderation",
      href: "/governance/admin/moderation",
      icon: <Flag className="h-5 w-5" />,
      description: "Manage reported content",
      section: "admin",
    },
    {
      title: "Emergency Controls",
      href: "/governance/admin/emergency",
      icon: <AlertTriangle className="h-5 w-5" />,
      description: "Emergency system controls",
      section: "admin",
    },
    {
      title: "Treasury Management",
      href: "/governance/admin/treasury",
      icon: <Wallet className="h-5 w-5" />,
      description: "Manage treasury funds",
      section: "admin",
    },
    {
      title: "Audit Logs",
      href: "/governance/admin/audit-logs",
      icon: <FileText className="h-5 w-5" />,
      description: "System audit logs",
      section: "admin",
    },
    {
      title: "Audit Analytics",
      href: "/governance/admin/audit-analytics",
      icon: <BarChart2 className="h-5 w-5" />,
      description: "System audit analytics",
      section: "admin",
    },
    {
      title: "Users Management",
      href: "/governance/admin/users",
      icon: <Users className="h-5 w-5" />,
      description: "Manage user accounts and permissions",
      section: "admin",
    },
    {
      title: "Rewards Management",
      href: "/governance/admin/rewards",
      icon: <Gift className="h-5 w-5" />,
      description: "Configure reward distribution",
      section: "admin",
    },
    {
      title: "Staking Management",
      href: "/governance/admin/staking",
      icon: <Coins className="h-5 w-5" />,
      description: "Configure staking parameters",
      section: "admin",
    },
    {
      title: "Feedback Management",
      href: "/governance/admin/feedback",
      icon: <MessageSquare className="h-5 w-5" />,
      description: "Review user feedback",
      section: "admin",
    },
    {
      title: "Notifications",
      href: "/governance/admin/notifications",
      icon: <Bell className="h-5 w-5" />,
      description: "Manage system notifications",
      section: "admin",
    },
    {
      title: "Security Settings",
      href: "/governance/admin/security",
      icon: <Shield className="h-5 w-5" />,
      description: "Configure security settings",
      section: "admin",
    },
    {
      title: "Performance Monitoring",
      href: "/governance/admin/performance",
      icon: <Gauge className="h-5 w-5" />,
      description: "Monitor system performance",
      section: "admin",
    },
    {
      title: "System Settings",
      href: "/governance/admin/settings",
      icon: <Cog className="h-5 w-5" />,
      description: "Configure platform settings",
      section: "admin",
    },
  ]

  return (
    <div className="mb-8">
      <div className="flex flex-col space-y-3 mb-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
          <Button
            variant="outline"
            className={cn(
              "border-white/10 bg-white/5 hover:bg-white/10 text-xs sm:text-sm px-2 sm:px-3",
              activeSection === "governance" && "bg-doge/10 text-doge border-doge/20 hover:bg-doge/20",
            )}
            onClick={() => setActiveSection("governance")}
          >
            <Users className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            Governance
          </Button>
          <Button
            variant="outline"
            className={cn(
              "border-white/10 bg-white/5 hover:bg-white/10 text-xs sm:text-sm px-2 sm:px-3",
              activeSection === "development" &&
                "bg-dogechain/10 text-dogechain border-dogechain/20 hover:bg-dogechain/20",
            )}
            onClick={() => setActiveSection("development")}
          >
            <Workflow className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            Development
          </Button>
          <Button
            variant="outline"
            className={cn(
              "border-white/10 bg-white/5 hover:bg-white/10 text-xs sm:text-sm px-2 sm:px-3",
              activeSection === "admin" &&
                "bg-purple-500/10 text-purple-500 border-purple-500/20 hover:bg-purple-500/20",
            )}
            onClick={() => setActiveSection("admin")}
          >
            <Settings className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            Admin
          </Button>
        </div>
        <div className="flex justify-center sm:justify-end">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" className="border-white/10 bg-white/5 hover:bg-white/10 h-8 w-8 sm:h-10 sm:w-10">
                  <HelpCircle className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="sr-only">Help</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Learn about governance and development</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
        {navItems
          .filter((item) => item.section === activeSection)
          .map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center p-4 rounded-lg border border-white/10 bg-white/5 hover:bg-white/10 transition-colors",
                pathname === item.href &&
                  (activeSection === "governance"
                    ? "bg-doge/10 border-doge/20 hover:bg-doge/20"
                    : activeSection === "development"
                      ? "bg-dogechain/10 border-dogechain/20 hover:bg-dogechain/20"
                      : "bg-purple-500/10 border-purple-500/20 hover:bg-purple-500/20"),
              )}
            >
              <div
                className={cn(
                  "p-2 rounded-full mb-2",
                  pathname === item.href
                    ? activeSection === "governance"
                      ? "bg-doge/10 text-doge"
                      : activeSection === "development"
                        ? "bg-dogechain/10 text-dogechain"
                        : "bg-purple-500/10 text-purple-500"
                    : "bg-white/5 text-white/70",
                )}
              >
                {item.icon}
              </div>
              <span className="text-sm font-medium text-center">{item.title}</span>
              <span className="text-xs text-white/50 text-center mt-1 hidden md:block">{item.description}</span>
            </Link>
          ))}
      </div>
    </div>
  )
}
