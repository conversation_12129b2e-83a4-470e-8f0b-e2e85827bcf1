"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { FileText, Code, Settings, Users, Lock } from "lucide-react"

export function DevelopmentDAODocumentation() {
  return (
    <div className="space-y-6">
      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Development DAO Documentation</CardTitle>
          <CardDescription className="text-white/70">Comprehensive guide to the Development DAO system</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="glass mb-6 grid w-full grid-cols-5">
              <TabsTrigger value="overview" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                <FileText className="mr-2 h-4 w-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="technical" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                <Code className="mr-2 h-4 w-4" />
                Technical
              </TabsTrigger>
              <TabsTrigger value="governance" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                <Users className="mr-2 h-4 w-4" />
                Governance
              </TabsTrigger>
              <TabsTrigger value="staking" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                <Lock className="mr-2 h-4 w-4" />
                Staking
              </TabsTrigger>
              <TabsTrigger value="admin" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                <Settings className="mr-2 h-4 w-4" />
                Admin
              </TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-4">
              <div className="prose prose-invert max-w-none">
                <h2>Development DAO System Overview</h2>
                <p>
                  The Development DAO is a decentralized governance system that allows PawPumps token holders to
                  participate in the platform's development process. By staking $PAW tokens, users gain voting power to
                  influence development priorities, propose new features, and earn rewards for their participation.
                </p>

                <h3>Core Components</h3>
                <ul>
                  <li>
                    <strong>Community Priorities:</strong> Users vote on which development tasks should be prioritized
                  </li>
                  <li>
                    <strong>Feature Proposals:</strong> Community members can propose new features for development
                  </li>
                  <li>
                    <strong>Development Progress:</strong> Transparent tracking of milestone completion
                  </li>
                  <li>
                    <strong>Staking System:</strong> Stake $PAW tokens to increase voting power and earn rewards
                  </li>
                  <li>
                    <strong>Rewards System:</strong> Earn $PAW tokens for participation in governance
                  </li>
                </ul>

                <h3>Benefits</h3>
                <ul>
                  <li>Creates a community-driven development process</li>
                  <li>Ensures the platform evolves according to user needs</li>
                  <li>Provides transparency into the development roadmap</li>
                  <li>Rewards active participation in platform governance</li>
                  <li>Builds a stronger community around the platform</li>
                </ul>

                <h3>Key Metrics</h3>
                <ul>
                  <li>Total staked: Amount of $PAW tokens staked for development governance</li>
                  <li>Active stakers: Number of users participating in development governance</li>
                  <li>Proposal success rate: Percentage of proposals that pass voting</li>
                  <li>Implementation rate: Percentage of passed proposals that get implemented</li>
                  <li>Rewards distributed: Total $PAW tokens distributed for participation</li>
                </ul>
              </div>
            </TabsContent>

            {/* Technical Tab */}
            <TabsContent value="technical" className="space-y-4">
              <div className="prose prose-invert max-w-none">
                <h2>Technical Documentation</h2>
                <p>
                  The Development DAO system is built on a modular architecture that integrates with the existing
                  governance system while providing specialized functionality for development-related activities.
                </p>

                <h3>System Architecture</h3>
                <ul>
                  <li>
                    <strong>Frontend Components:</strong> React components for user interaction with the Development DAO
                  </li>
                  <li>
                    <strong>Smart Contracts:</strong> Solidity contracts for on-chain governance and staking
                  </li>
                  <li>
                    <strong>Backend Services:</strong> Node.js services for off-chain processing and data aggregation
                  </li>
                  <li>
                    <strong>Database:</strong> Storage for proposals, tasks, votes, and user participation data
                  </li>
                </ul>

                <h3>Data Models</h3>
                <p>The system uses the following core data models:</p>

                <h4>Task</h4>
                <pre>
                  {`{
  id: string
  title: string
  description: string
  status: "completed" | "in-progress" | "planned" | "blocked"
  priority: "critical" | "high" | "medium" | "low"
  category: string
  estimatedHours: number
  assignedTo?: string
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  dependencies?: string[]
  notes?: string
  votes?: {
    for: number
    against: number
  }
  communityPriority?: number
}`}
                </pre>

                <h4>CommunityProposal</h4>
                <pre>
                  {`{
  id: string
  title: string
  description: string
  proposedBy: string
  createdAt: Date
  status: "active" | "passed" | "failed" | "implemented"
  category: string
  votesFor: number
  votesAgainst: number
  quorum: number
  implementationTask?: string
}`}
                </pre>

                <h4>StakingConfig</h4>
                <pre>
                  {`{
  baseAPR: number
  maxAPR: number
  minStake: number
  lockPeriods: {
    name: string
    days: number
    multiplier: number
  }[]
  tiers: {
    id: string
    name: string
    threshold: number
    benefits: string[]
    color?: string
  }[]
}`}
                </pre>

                <h3>API Endpoints</h3>
                <p>The system exposes the following API endpoints:</p>
                <ul>
                  <li>
                    <code>/api/development/tasks</code> - CRUD operations for development tasks
                  </li>
                  <li>
                    <code>/api/development/proposals</code> - CRUD operations for community proposals
                  </li>
                  <li>
                    <code>/api/development/votes</code> - Endpoints for voting on tasks and proposals
                  </li>
                  <li>
                    <code>/api/development/staking</code> - Endpoints for staking operations
                  </li>
                  <li>
                    <code>/api/development/rewards</code> - Endpoints for reward distribution
                  </li>
                  <li>
                    <code>/api/development/admin</code> - Admin-only endpoints for system configuration
                  </li>
                </ul>

                <h3>Integration Points</h3>
                <p>The Development DAO integrates with the following platform systems:</p>
                <ul>
                  <li>Main Governance System - Shares user roles and permissions</li>
                  <li>Wallet Provider - For authentication and transaction signing</li>
                  <li>Notification System - For alerting users about votes and rewards</li>
                  <li>Token Contract - For staking and reward distribution</li>
                </ul>
              </div>
            </TabsContent>

            {/* Governance Tab */}
            <TabsContent value="governance" className="space-y-4">
              <div className="prose prose-invert max-w-none">
                <h2>Governance Documentation</h2>
                <p>
                  The Development DAO governance system allows token holders to participate in decision-making related
                  to platform development through voting and proposal mechanisms.
                </p>

                <h3>Voting Mechanism</h3>
                <p>
                  Voting power is determined by the amount of $PAW tokens staked for development governance. Each staked
                  token represents one vote. The system uses a simple majority voting mechanism with the following
                  rules:
                </p>
                <ul>
                  <li>Proposals require a quorum of at least 10% of total staked tokens to be valid</li>
                  <li>A proposal passes if it receives more "For" votes than "Against" votes</li>
                  <li>Task prioritization is calculated based on the ratio of "For" to total votes</li>
                  <li>Voting periods last for 7 days by default</li>
                </ul>

                <h3>Proposal Process</h3>
                <ol>
                  <li>
                    <strong>Submission:</strong> Any user who meets the minimum staking requirement can submit a
                    proposal
                  </li>
                  <li>
                    <strong>Review:</strong> Admins review proposals to ensure they meet basic quality standards
                  </li>
                  <li>
                    <strong>Voting:</strong> Approved proposals enter a 7-day voting period
                  </li>
                  <li>
                    <strong>Results:</strong> After the voting period, the proposal is marked as passed or failed
                  </li>
                  <li>
                    <strong>Implementation:</strong> Passed proposals are added to the development roadmap
                  </li>
                </ol>

                <h3>Task Prioritization</h3>
                <p>Development tasks are prioritized based on a combination of factors:</p>
                <ul>
                  <li>Community votes (60% weight)</li>
                  <li>Technical feasibility (20% weight)</li>
                  <li>Strategic alignment (20% weight)</li>
                </ul>
                <p>The community priority score is calculated as:</p>
                <pre>{`communityPriority = (votesFor / (votesFor + votesAgainst)) * 100`}</pre>

                <h3>Governance Tiers</h3>
                <p>
                  Users are assigned governance tiers based on their staked amount, with higher tiers providing
                  additional benefits:
                </p>
                <ul>
                  <li>
                    <strong>Contributor (1,000+ $PAW):</strong> Basic voting rights, proposal voting
                  </li>
                  <li>
                    <strong>Developer (5,000+ $PAW):</strong> Increased voting weight, create proposals, early access to
                    features
                  </li>
                  <li>
                    <strong>Architect (25,000+ $PAW):</strong> Major voting weight, priority feature requests,
                    development team access
                  </li>
                  <li>
                    <strong>Founder (100,000+ $PAW):</strong> Maximum voting weight, direct influence on roadmap,
                    private development channel
                  </li>
                </ul>
              </div>
            </TabsContent>

            {/* Staking Tab */}
            <TabsContent value="staking" className="space-y-4">
              <div className="prose prose-invert max-w-none">
                <h2>Staking System Documentation</h2>
                <p>
                  The Development DAO staking system allows users to lock their $PAW tokens to earn rewards and gain
                  voting power in development governance.
                </p>

                <h3>Staking Mechanics</h3>
                <ul>
                  <li>
                    <strong>Minimum Stake:</strong> 1,000 $PAW tokens
                  </li>
                  <li>
                    <strong>Base APR:</strong> 15% annual yield on staked tokens
                  </li>
                  <li>
                    <strong>Lock Periods:</strong> Optional lock periods for higher APR
                    <ul>
                      <li>Flexible (no lock): 1.0x multiplier (15% APR)</li>
                      <li>30 Days: 1.2x multiplier (18% APR)</li>
                      <li>90 Days: 1.5x multiplier (22.5% APR)</li>
                      <li>180 Days: 2.0x multiplier (30% APR)</li>
                    </ul>
                  </li>
                  <li>
                    <strong>Early Unstaking:</strong> Possible with a 10% penalty on rewards
                  </li>
                  <li>
                    <strong>Auto-compounding:</strong> Optional automatic reinvestment of rewards
                  </li>
                </ul>

                <h3>Reward Distribution</h3>
                <p>Staking rewards are distributed on a weekly basis. The reward calculation is as follows:</p>
                <pre>{`weeklyReward = (stakedAmount * baseAPR * lockMultiplier) / 52`}</pre>
                <p>Additional rewards are earned through participation:</p>
                <ul>
                  <li>Voting on proposals: 5 $PAW per vote</li>
                  <li>Creating proposals: 100 $PAW per proposal</li>
                  <li>Approved proposals: 1,000 $PAW per approved proposal</li>
                  <li>Bug reports: 50-5,000 $PAW depending on severity</li>
                </ul>

                <h3>Voting Power</h3>
                <p>
                  Voting power is directly proportional to the amount of tokens staked for development governance. The
                  formula is:
                </p>
                <pre>{`votingPower = stakedAmount`}</pre>
                <p>
                  This creates a 1:1 relationship between staked tokens and votes, ensuring that users with more skin in
                  the game have more influence over development decisions.
                </p>

                <h3>Staking Tiers</h3>
                <p>Users are assigned to tiers based on their staked amount:</p>
                <table>
                  <thead>
                    <tr>
                      <th>Tier</th>
                      <th>Minimum Stake</th>
                      <th>Benefits</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Contributor</td>
                      <td>1,000 $PAW</td>
                      <td>Basic voting rights, Proposal voting</td>
                    </tr>
                    <tr>
                      <td>Developer</td>
                      <td>5,000 $PAW</td>
                      <td>Increased voting weight, Create proposals, Early access to features</td>
                    </tr>
                    <tr>
                      <td>Architect</td>
                      <td>25,000 $PAW</td>
                      <td>Major voting weight, Priority feature requests, Development team access</td>
                    </tr>
                    <tr>
                      <td>Founder</td>
                      <td>100,000 $PAW</td>
                      <td>Maximum voting weight, Direct influence on roadmap, Private development channel</td>
                    </tr>
                  </tbody>
                </table>

                <div className="bg-white/5 p-4 rounded-md border border-white/10 mt-6">
                  <h4 className="text-doge">Important Note on Configuration</h4>
                  <p className="mb-0">
                    All staking parameters including APR percentages, tier thresholds, and lock periods are configurable
                    by administrators through the admin panel. These values can be adjusted to optimize participation
                    and reward distribution based on platform growth and token economics.
                  </p>
                </div>
              </div>
            </TabsContent>

            {/* Admin Tab */}
            <TabsContent value="admin" className="space-y-4">
              <div className="prose prose-invert max-w-none">
                <h2>Administration Documentation</h2>
                <p>
                  The Development DAO admin system provides tools for managing all aspects of the development governance
                  system, including staking parameters, rewards, proposals, and tasks.
                </p>

                <h3>Admin Dashboard</h3>
                <p>The admin dashboard provides an overview of key metrics:</p>
                <ul>
                  <li>Total staked amount and number of stakers</li>
                  <li>Voting activity and proposal statistics</li>
                  <li>Task status distribution</li>
                  <li>Rewards distributed</li>
                  <li>Recent activity log</li>
                </ul>

                <h3>Task Management</h3>
                <p>Administrators can manage development tasks through the following actions:</p>
                <ul>
                  <li>Create new tasks</li>
                  <li>Edit task details (title, description, category, etc.)</li>
                  <li>Update task status (planned, in-progress, completed, blocked)</li>
                  <li>Assign tasks to team members</li>
                  <li>Link tasks to proposals</li>
                  <li>View community voting on task priorities</li>
                </ul>

                <h3>Proposal Management</h3>
                <p>Administrators can manage community proposals through the following actions:</p>
                <ul>
                  <li>Review and approve/reject new proposals</li>
                  <li>Edit proposal details if necessary</li>
                  <li>Update proposal status (active, passed, failed, implemented)</li>
                  <li>Create implementation tasks for approved proposals</li>
                  <li>Monitor voting progress and results</li>
                </ul>

                <h3>Staking Configuration</h3>
                <p>The staking system can be configured through the admin panel:</p>
                <ul>
                  <li>Adjust base and maximum APR rates</li>
                  <li>Set minimum staking requirements</li>
                  <li>Configure lock periods and their multipliers</li>
                  <li>Manage staking tiers (thresholds, benefits, colors)</li>
                  <li>Enable/disable auto-compounding and early unstaking</li>
                </ul>

                <h3>Rewards Configuration</h3>
                <p>The rewards system can be configured through the admin panel:</p>
                <ul>
                  <li>Set reward amounts for different activities (voting, proposals, etc.)</li>
                  <li>Configure reward distribution schedule</li>
                  <li>Set bug bounty reward ranges</li>
                  <li>Enable/disable automatic distribution</li>
                  <li>Manually trigger reward distribution</li>
                </ul>

                <h3>Emergency Controls</h3>
                <p>In case of issues, administrators have access to emergency controls:</p>
                <ul>
                  <li>Pause the entire Development DAO system</li>
                  <li>Freeze staking/unstaking operations</li>
                  <li>Halt reward distribution</li>
                  <li>Suspend voting on proposals</li>
                </ul>

                <div className="bg-red-500/10 p-4 rounded-md border border-red-500/20 mt-6">
                  <h4 className="text-red-500">Security Note</h4>
                  <p className="mb-0">
                    Access to the admin panel is restricted to authorized administrators only. All admin actions are
                    logged for accountability and transparency. Multi-signature approval is required for critical
                    operations such as changing APR rates or pausing the system.
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
