"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>,
  <PERSON>,
  Cell,
} from "recharts"

// Weekly progress data
const weeklyProgressData = [
  { week: "Week 1", completed: 3, inProgress: 5, planned: 42, blocked: 0 },
  { week: "Week 2", completed: 7, inProgress: 8, planned: 35, blocked: 0 },
  { week: "Week 3", completed: 12, inProgress: 10, planned: 28, blocked: 0 },
  { week: "Week 4", completed: 15, inProgress: 12, planned: 23, blocked: 0 },
  { week: "Week 5", completed: 18, inProgress: 15, planned: 17, blocked: 0 },
  { week: "Week 6", completed: 22, inProgress: 12, planned: 16, blocked: 0 },
]

// Category progress data
const categoryProgressData = [
  { name: "Core Functionality", completed: 2, inProgress: 3, planned: 4 },
  { name: "User Experience", completed: 1, inProgress: 2, planned: 6 },
  { name: "Visual Design", completed: 3, inProgress: 1, planned: 2 },
  { name: "Mobile", completed: 0, inProgress: 1, planned: 2 },
  { name: "Performance", completed: 1, inProgress: 1, planned: 4 },
  { name: "Accessibility", completed: 0, inProgress: 2, planned: 4 },
  { name: "Content", completed: 0, inProgress: 0, planned: 6 },
  { name: "Technical", completed: 0, inProgress: 1, planned: 8 },
  { name: "Security", completed: 0, inProgress: 0, planned: 3 },
  { name: "Social", completed: 0, inProgress: 0, planned: 3 },
]

// Priority distribution data
const priorityData = [
  { name: "Critical", value: 9, color: "#ef4444" },
  { name: "High", value: 18, color: "#f97316" },
  { name: "Medium", value: 28, color: "#3b82f6" },
  { name: "Low", value: 15, color: "#6b7280" },
]

// Recent activity data
const recentActivityData = [
  {
    id: 1,
    task: "CF-007",
    title: "Integrate Multiple Wallet Providers",
    action: "started",
    date: "2023-05-28",
    user: "Alex Chen",
  },
  {
    id: 2,
    task: "CF-008",
    title: "Implement Dogechain Integration",
    action: "started",
    date: "2023-05-27",
    user: "Sarah Johnson",
  },
  {
    id: 3,
    task: "MR-002",
    title: "Improve Chart Touch Interactions",
    action: "started",
    date: "2023-05-26",
    user: "Michael Wong",
  },
  {
    id: 4,
    task: "TC-004",
    title: "Standardize State Management",
    action: "started",
    date: "2023-05-25",
    user: "Emily Davis",
  },
  {
    id: 5,
    task: "PF-004",
    title: "Optimize Images",
    action: "started",
    date: "2023-05-24",
    user: "James Wilson",
  },
]

// Milestone progress data
const milestoneProgressData = [
  { name: "Core Infrastructure", completed: 20, total: 100 },
  { name: "Token Launch Experience", completed: 10, total: 100 },
  { name: "Trading Enhancements", completed: 0, total: 100 },
  { name: "Mobile & Accessibility", completed: 15, total: 100 },
  { name: "Performance & Technical Debt", completed: 5, total: 100 },
  { name: "Security & Compliance", completed: 0, total: 100 },
  { name: "Community & Social", completed: 0, total: 100 },
]

export function ProgressReport() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="milestones">Milestones</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="pt-4 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Progress</CardTitle>
              <CardDescription>Task completion progress over the past 6 weeks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={weeklyProgressData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="week" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="completed" stroke="#10b981" name="Completed" />
                    <Line type="monotone" dataKey="inProgress" stroke="#3b82f6" name="In Progress" />
                    <Line type="monotone" dataKey="planned" stroke="#6b7280" name="Planned" />
                    <Line type="monotone" dataKey="blocked" stroke="#ef4444" name="Blocked" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Priority Distribution</CardTitle>
                <CardDescription>Tasks by priority level</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={priorityData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                      >
                        {priorityData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Overall Progress</CardTitle>
                <CardDescription>Current development progress</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Tasks Completed</span>
                      <span className="text-sm text-muted-foreground">22/70 (31%)</span>
                    </div>
                    <Progress value={31} />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Estimated Hours</span>
                      <span className="text-sm text-muted-foreground">320/1200 (27%)</span>
                    </div>
                    <Progress value={27} />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Milestones Completed</span>
                      <span className="text-sm text-muted-foreground">0/7 (0%)</span>
                    </div>
                    <Progress value={0} />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Critical Tasks</span>
                      <span className="text-sm text-muted-foreground">2/9 (22%)</span>
                    </div>
                    <Progress value={22} className="bg-red-100 dark:bg-red-900">
                      <div className="bg-red-500 h-full" style={{ width: "22%" }} />
                    </Progress>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="categories" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Progress by Category</CardTitle>
              <CardDescription>Task completion status across different categories</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={categoryProgressData}
                    layout="vertical"
                    margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis type="category" dataKey="name" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="completed" stackId="a" fill="#10b981" name="Completed" />
                    <Bar dataKey="inProgress" stackId="a" fill="#3b82f6" name="In Progress" />
                    <Bar dataKey="planned" stackId="a" fill="#6b7280" name="Planned" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="milestones" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Milestone Progress</CardTitle>
              <CardDescription>Progress towards major development milestones</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {milestoneProgressData.map((milestone) => (
                  <div key={milestone.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{milestone.name}</span>
                      <span className="text-sm text-muted-foreground">{milestone.completed}% complete</span>
                    </div>
                    <Progress value={milestone.completed} />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest updates on development tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {recentActivityData.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-4">
                    <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-500">
                      {activity.user.charAt(0)}
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{activity.user}</span>
                        <Badge variant="outline">{activity.action}</Badge>
                        <span className="font-mono text-xs">{activity.task}</span>
                      </div>
                      <p className="text-sm">{activity.title}</p>
                      <p className="text-xs text-muted-foreground">{activity.date}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
