"use client"

import { use<PERSON>allback } from "react"

import { useState, useEffect, useMemo } from "react"

import { CheckCircle2, Circle, Clock, AlertTriangle } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { usePerformanceMonitor } from "@/hooks/use-performance-monitor"
import { ErrorState } from "@/components/ui/error-state"
import { LoadingState } from "@/components/ui/loading-state"
import { useAsync } from "@/hooks/use-async"
import { useMobile } from "@/hooks/use-mobile"

// Task status types
type TaskStatus = "completed" | "in-progress" | "planned" | "blocked"

// Priority levels
type PriorityLevel = "critical" | "high" | "medium" | "low"

// Category types
type TaskCategory =
  | "core-functionality"
  | "user-experience"
  | "visual-design"
  | "mobile"
  | "performance"
  | "accessibility"
  | "content"
  | "technical"
  | "security"
  | "social"

// Task interface
interface Task {
  id: string
  title: string
  description: string
  status: TaskStatus
  priority: PriorityLevel
  category: TaskCategory
  estimatedHours: number
  assignedTo?: string
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  dependencies?: string[]
  notes?: string
}

// Mock data - in a real app, this would come from an API
const mockTasks = [
  {
    id: 1,
    title: "Smart Contract Audit",
    status: "completed",
    progress: 100,
    priority: "high",
    assignee: "0x123...456",
    dueDate: "2023-06-15",
  },
  {
    id: 2,
    title: "Frontend Development",
    status: "in-progress",
    progress: 65,
    priority: "medium",
    assignee: "0x789...012",
    dueDate: "2023-07-01",
  },
  {
    id: 3,
    title: "Backend Integration",
    status: "in-progress",
    progress: 40,
    priority: "high",
    assignee: "0x345...678",
    dueDate: "2023-07-15",
  },
  {
    id: 4,
    title: "Documentation",
    status: "not-started",
    progress: 0,
    priority: "low",
    assignee: "Unassigned",
    dueDate: "2023-08-01",
  },
  {
    id: 5,
    title: "Community Testing",
    status: "not-started",
    progress: 0,
    priority: "medium",
    assignee: "Unassigned",
    dueDate: "2023-08-15",
  },
]

const mockMilestones = [
  {
    id: 1,
    title: "Alpha Release",
    status: "completed",
    date: "2023-05-01",
    description: "Initial release with basic functionality",
  },
  {
    id: 2,
    title: "Beta Release",
    status: "in-progress",
    date: "2023-07-01",
    description: "Feature complete with known issues",
  },
  { id: 3, title: "V1 Release", status: "not-started", date: "2023-09-01", description: "Production ready release" },
]

// Generate a comprehensive task list based on the audit
const generateTasks = (): Task[] => {
  const now = new Date()

  return [
    // Core Functionality Tasks
    {
      id: "CF-001",
      title: "Implement Token Launch Confirmation Flow",
      description: "Add a confirmation step before token launch to review parameters and confirm deployment",
      status: "planned",
      priority: "critical",
      category: "core-functionality",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CF-002",
      title: "Add Transaction Simulation",
      description: "Implement transaction simulation to preview gas costs and transaction details",
      status: "planned",
      priority: "high",
      category: "core-functionality",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CF-003",
      title: "Create Token Deployment Status Tracker",
      description: "Add progress tracking during token deployment with status updates",
      status: "in-progress",
      priority: "high",
      category: "core-functionality",
      estimatedHours: 12,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CF-004",
      title: "Implement Limit Orders",
      description: "Add limit order functionality to the trading interface",
      status: "planned",
      priority: "medium",
      category: "core-functionality",
      estimatedHours: 40,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CF-005",
      title: "Create Trade History View",
      description: "Implement personal trading history with filtering and sorting",
      status: "planned",
      priority: "medium",
      category: "core-functionality",
      estimatedHours: 20,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CF-006",
      title: "Develop Portfolio Tracking",
      description: "Create a dedicated portfolio view to track holdings and performance",
      status: "planned",
      priority: "high",
      category: "core-functionality",
      estimatedHours: 32,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CF-007",
      title: "Integrate Multiple Wallet Providers",
      description: "Add support for multiple wallet providers (MetaMask, WalletConnect, etc.)",
      status: "in-progress",
      priority: "critical",
      category: "core-functionality",
      estimatedHours: 40,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CF-008",
      title: "Implement Dogechain Integration",
      description: "Connect to actual Dogechain network for real transactions",
      status: "in-progress",
      priority: "critical",
      category: "core-functionality",
      estimatedHours: 60,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CF-009",
      title: "Add Transaction Signing Process",
      description: "Implement secure transaction signing process with wallet providers",
      status: "planned",
      priority: "critical",
      category: "core-functionality",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
      dependencies: ["CF-007", "CF-008"],
    },

    // User Experience Tasks
    {
      id: "UX-001",
      title: "Add Breadcrumb Navigation",
      description: "Implement breadcrumb navigation for deeper pages",
      status: "planned",
      priority: "low",
      category: "user-experience",
      estimatedHours: 8,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "UX-002",
      title: "Implement Global Search",
      description: "Add global search functionality across the platform",
      status: "planned",
      priority: "medium",
      category: "user-experience",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "UX-003",
      title: "Create Help/Support Section",
      description: "Develop dedicated help and support pages with documentation",
      status: "planned",
      priority: "medium",
      category: "user-experience",
      estimatedHours: 32,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "UX-004",
      title: "Design User Onboarding Flow",
      description: "Create guided tour for new users to learn platform features",
      status: "planned",
      priority: "high",
      category: "user-experience",
      estimatedHours: 40,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "UX-005",
      title: "Develop Educational Content",
      description: "Create educational content explaining complex features and concepts",
      status: "planned",
      priority: "medium",
      category: "user-experience",
      estimatedHours: 60,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "UX-006",
      title: "Add Contextual Tooltips",
      description: "Implement tooltips for complex UI elements",
      status: "in-progress",
      priority: "medium",
      category: "user-experience",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "UX-007",
      title: "Improve Error Handling",
      description: "Enhance error messages with specific guidance and solutions",
      status: "planned",
      priority: "high",
      category: "user-experience",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "UX-008",
      title: "Add Success Confirmations",
      description: "Implement clear success states for user actions",
      status: "planned",
      priority: "medium",
      category: "user-experience",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "UX-009",
      title: "Create User Feedback Collection",
      description: "Add mechanisms for users to provide feedback on the platform",
      status: "planned",
      priority: "low",
      category: "user-experience",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },

    // Visual Design Tasks
    {
      id: "VD-001",
      title: "Standardize Card Styling",
      description: "Create consistent card styling patterns across the platform",
      status: "in-progress",
      priority: "medium",
      category: "visual-design",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "VD-002",
      title: "Unify Button Styles",
      description: "Ensure consistent button styles throughout the application",
      status: "planned",
      priority: "medium",
      category: "visual-design",
      estimatedHours: 12,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "VD-003",
      title: "Standardize Spacing System",
      description: "Implement consistent spacing system for padding and margins",
      status: "planned",
      priority: "medium",
      category: "visual-design",
      estimatedHours: 20,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "VD-004",
      title: "Enhance Primary Action Visibility",
      description: "Improve visual hierarchy to make primary actions stand out",
      status: "planned",
      priority: "high",
      category: "visual-design",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "VD-005",
      title: "Optimize Information Density",
      description: "Improve layout to balance information density across the platform",
      status: "planned",
      priority: "medium",
      category: "visual-design",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "VD-006",
      title: "Add Visual Cues",
      description: "Implement visual indicators for important information and actions",
      status: "planned",
      priority: "medium",
      category: "visual-design",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },

    // Mobile Responsiveness Tasks
    {
      id: "MR-001",
      title: "Optimize Tables for Mobile",
      description: "Redesign tables for better display on small screens",
      status: "planned",
      priority: "high",
      category: "mobile",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "MR-002",
      title: "Improve Chart Touch Interactions",
      description: "Enhance charts for better touch interaction on mobile devices",
      status: "in-progress",
      priority: "high",
      category: "mobile",
      estimatedHours: 32,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "MR-003",
      title: "Optimize Form Fields for Mobile",
      description: "Improve form field sizing and interaction on mobile devices",
      status: "planned",
      priority: "high",
      category: "mobile",
      estimatedHours: 20,
      createdAt: now,
      updatedAt: now,
    },

    // Performance Tasks
    {
      id: "PF-001",
      title: "Add Skeleton Loaders",
      description: "Implement skeleton loading states for asynchronous content",
      status: "planned",
      priority: "high",
      category: "performance",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "PF-002",
      title: "Improve Loading Indicators",
      description: "Add better loading feedback for all asynchronous actions",
      status: "planned",
      priority: "high",
      category: "performance",
      estimatedHours: 12,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "PF-003",
      title: "Implement Offline Support",
      description: "Add offline fallback and caching strategy",
      status: "planned",
      priority: "medium",
      category: "performance",
      estimatedHours: 40,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "PF-004",
      title: "Optimize Images",
      description: "Further optimize images for better performance",
      status: "in-progress",
      priority: "medium",
      category: "performance",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "PF-005",
      title: "Implement Code Splitting",
      description: "Add code splitting for better initial load times",
      status: "planned",
      priority: "medium",
      category: "performance",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "PF-006",
      title: "Add Lazy Loading",
      description: "Implement lazy loading for off-screen content",
      status: "planned",
      priority: "medium",
      category: "performance",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },

    // Accessibility Tasks
    {
      id: "AC-001",
      title: "Standardize Focus States",
      description: "Implement consistent focus indicators across the platform",
      status: "planned",
      priority: "high",
      category: "accessibility",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "AC-002",
      title: "Optimize Tab Order",
      description: "Improve tab order for logical keyboard navigation",
      status: "planned",
      priority: "high",
      category: "accessibility",
      estimatedHours: 12,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "AC-003",
      title: "Add Keyboard Shortcuts",
      description: "Implement keyboard shortcuts for power users",
      status: "planned",
      priority: "medium",
      category: "accessibility",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "AC-004",
      title: "Complete ARIA Attributes",
      description: "Add missing ARIA roles and attributes to components",
      status: "planned",
      priority: "high",
      category: "accessibility",
      estimatedHours: 20,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "AC-005",
      title: "Add Alternative Text",
      description: "Ensure all images have proper alternative text",
      status: "in-progress",
      priority: "high",
      category: "accessibility",
      estimatedHours: 8,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "AC-006",
      title: "Fix Form Label Associations",
      description: "Ensure all form controls are properly associated with labels",
      status: "planned",
      priority: "high",
      category: "accessibility",
      estimatedHours: 12,
      createdAt: now,
      updatedAt: now,
    },

    // Content Tasks
    {
      id: "CT-001",
      title: "Create API Documentation",
      description: "Develop comprehensive API documentation",
      status: "planned",
      priority: "medium",
      category: "content",
      estimatedHours: 40,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CT-002",
      title: "Add Developer Resources",
      description: "Create resources for developers building on the platform",
      status: "planned",
      priority: "medium",
      category: "content",
      estimatedHours: 32,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CT-003",
      title: "Expand FAQ Section",
      description: "Add more comprehensive FAQs covering common questions",
      status: "planned",
      priority: "medium",
      category: "content",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CT-004",
      title: "Create Terms of Service",
      description: "Develop detailed terms of service documentation",
      status: "planned",
      priority: "high",
      category: "content",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CT-005",
      title: "Develop Privacy Policy",
      description: "Create comprehensive privacy policy",
      status: "planned",
      priority: "high",
      category: "content",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "CT-006",
      title: "Add Compliance Information",
      description: "Include regulatory compliance information",
      status: "planned",
      priority: "high",
      category: "content",
      estimatedHours: 32,
      createdAt: now,
      updatedAt: now,
    },

    // Technical Tasks
    {
      id: "TC-001",
      title: "Implement Error Boundaries",
      description: "Add React error boundaries for graceful failures",
      status: "planned",
      priority: "high",
      category: "technical",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "TC-002",
      title: "Create Error Logging System",
      description: "Implement centralized error logging system",
      status: "planned",
      priority: "high",
      category: "technical",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "TC-003",
      title: "Add Fallback UI",
      description: "Create fallback UI for failed component rendering",
      status: "planned",
      priority: "medium",
      category: "technical",
      estimatedHours: 16,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "TC-004",
      title: "Standardize State Management",
      description: "Implement consistent state management approach",
      status: "in-progress",
      priority: "high",
      category: "technical",
      estimatedHours: 40,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "TC-005",
      title: "Create Global Store",
      description: "Implement centralized store for shared application state",
      status: "planned",
      priority: "high",
      category: "technical",
      estimatedHours: 32,
      createdAt: now,
      updatedAt: now,
      dependencies: ["TC-004"],
    },
    {
      id: "TC-006",
      title: "Develop Caching Strategy",
      description: "Implement clear strategy for data caching",
      status: "planned",
      priority: "medium",
      category: "technical",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "TC-007",
      title: "Add Unit Tests",
      description: "Implement unit tests for critical components",
      status: "planned",
      priority: "high",
      category: "technical",
      estimatedHours: 60,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "TC-008",
      title: "Create Integration Tests",
      description: "Develop integration tests for critical flows",
      status: "planned",
      priority: "high",
      category: "technical",
      estimatedHours: 40,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "TC-009",
      title: "Implement E2E Testing",
      description: "Set up end-to-end testing infrastructure",
      status: "planned",
      priority: "medium",
      category: "technical",
      estimatedHours: 40,
      createdAt: now,
      updatedAt: now,
    },

    // Security Tasks
    {
      id: "SC-001",
      title: "Implement Two-Factor Authentication",
      description: "Add 2FA support for account security",
      status: "planned",
      priority: "high",
      category: "security",
      estimatedHours: 40,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "SC-002",
      title: "Create Security Settings Page",
      description: "Develop dedicated security settings page",
      status: "planned",
      priority: "high",
      category: "security",
      estimatedHours: 24,
      createdAt: now,
      updatedAt: now,
      dependencies: ["SC-001"],
    },
    {
      id: "SC-003",
      title: "Add User Activity Logs",
      description: "Implement activity logs for security monitoring",
      status: "planned",
      priority: "medium",
      category: "security",
      estimatedHours: 32,
      createdAt: now,
      updatedAt: now,
    },

    // Social Features
    {
      id: "SF-001",
      title: "Implement Chat/Messaging",
      description: "Add direct messaging between users",
      status: "planned",
      priority: "low",
      category: "social",
      estimatedHours: 60,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "SF-002",
      title: "Enhance Social Feed",
      description: "Add more interaction options to social feed",
      status: "planned",
      priority: "low",
      category: "social",
      estimatedHours: 40,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: "SF-003",
      title: "Create Community Forums",
      description: "Implement dedicated discussion forums",
      status: "planned",
      priority: "low",
      category: "social",
      estimatedHours: 80,
      createdAt: now,
      updatedAt: now,
    },
  ]
}

// Helper function to get status icon
const getStatusIcon = (status: TaskStatus) => {
  switch (status) {
    case "completed":
      return <CheckCircle2 className="h-4 w-4 text-green-500" />
    case "in-progress":
      return <Clock className="h-4 w-4 text-blue-500" />
    case "planned":
      return <Circle className="h-4 w-4 text-gray-400" />
    case "blocked":
      return <AlertTriangle className="h-4 w-4 text-red-500" />
    default:
      return <Circle className="h-4 w-4 text-gray-400" />
  }
}

// Helper function to get priority badge
const getPriorityBadge = (priority: PriorityLevel) => {
  switch (priority) {
    case "critical":
      return <Badge variant="destructive">Critical</Badge>
    case "high":
      return <Badge className="bg-orange-500">High</Badge>
    case "medium":
      return <Badge className="bg-blue-500">Medium</Badge>
    case "low":
      return <Badge className="bg-gray-500">Low</Badge>
    default:
      return <Badge className="bg-gray-500">Low</Badge>
  }
}

// Helper function to get category label
const getCategoryLabel = (category: TaskCategory) => {
  const labels: Record<TaskCategory, string> = {
    "core-functionality": "Core Functionality",
    "user-experience": "User Experience",
    "visual-design": "Visual Design",
    mobile: "Mobile",
    performance: "Performance",
    accessibility: "Accessibility",
    content: "Content",
    technical: "Technical",
    security: "Security",
    social: "Social",
  }

  return labels[category] || category
}

export function DevelopmentTracker() {
  const { isMobile } = useMobile()
  const { trackRender, trackOperation } = usePerformanceMonitor("DevelopmentTracker")
  const [activeTab, setActiveTab] = useState("tasks")

  // Define the async functions outside of useAsync to prevent recreation on each render
  const fetchTasksAsync = useCallback(async () => {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))
    return mockTasks
  }, [])

  const fetchMilestonesAsync = useCallback(async () => {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))
    return mockMilestones
  }, [])

  // Use our custom async hook for data fetching with error handling
  const { data: tasks, loading: tasksLoading, error: tasksError, execute: fetchTasks } = useAsync(fetchTasksAsync, true)

  const {
    data: milestones,
    loading: milestonesLoading,
    error: milestonesError,
    execute: fetchMilestones,
  } = useAsync(fetchMilestonesAsync, true)

  // Track component render
  useEffect(() => {
    trackRender()
  }, [trackRender])

  // Calculate overall progress - memoized for performance
  const overallProgress = useMemo(() => {
    if (!tasks) return 0

    const trackCalculation = trackOperation("calculateProgress")
    const total = tasks.length
    const completed = tasks.filter((task) => task.status === "completed").length
    const inProgress = tasks.filter((task) => task.status === "in-progress").length
    const progress = Math.round(((completed + inProgress * 0.5) / total) * 100)
    trackCalculation()

    return progress
  }, [tasks, trackOperation])

  // Handle tab change
  const handleTabChange = (value: string) => {
    trackOperation("tabChange")()
    setActiveTab(value)
  }

  // If there's an error loading tasks or milestones
  if (tasksError || milestonesError) {
    return (
      <ErrorState
        title="Failed to load development tracker"
        description="There was an error loading the development data. Please try again later."
        action={{
          label: "Try Again",
          onClick: () => {
            fetchTasks()
            fetchMilestones()
          },
        }}
      />
    )
  }

  // If data is still loading
  if (tasksLoading || milestonesLoading) {
    return <LoadingState title="Loading development tracker..." />
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Development Tracker</CardTitle>
          <CardDescription>Track the progress of development tasks and milestones</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm font-medium">{overallProgress}%</span>
            </div>
            <Progress value={overallProgress} className="h-2" />
          </div>

          <Tabs defaultValue="tasks" onValueChange={handleTabChange} className="w-full">
            <TabsList className={isMobile ? "grid w-full grid-cols-3" : "flex"}>
              <TabsTrigger value="tasks" className={isMobile ? "text-xs py-1" : ""}>
                Tasks
              </TabsTrigger>
              <TabsTrigger value="timeline" className={isMobile ? "text-xs py-1" : ""}>
                Timeline
              </TabsTrigger>
              <TabsTrigger value="milestones" className={isMobile ? "text-xs py-1" : ""}>
                Milestones
              </TabsTrigger>
            </TabsList>

            <TabsContent value="tasks" className="space-y-4 mt-4">
              {tasks &&
                tasks.map((task) => (
                  <Card key={task.id} className="overflow-hidden">
                    <CardContent className="p-4">
                      <div
                        className={`flex ${isMobile ? "flex-col space-y-2" : "flex-row items-center justify-between"}`}
                      >
                        <div className="space-y-1">
                          <h3 className="font-medium">{task.title}</h3>
                          <div className="flex items-center space-x-2">
                            <span
                              className={`px-2 py-1 text-xs rounded-full ${
                                task.status === "completed"
                                  ? "bg-green-100 text-green-800"
                                  : task.status === "in-progress"
                                    ? "bg-blue-100 text-blue-800"
                                    : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {task.status.replace("-", " ")}
                            </span>
                            <span
                              className={`px-2 py-1 text-xs rounded-full ${
                                task.priority === "high"
                                  ? "bg-red-100 text-red-800"
                                  : task.priority === "medium"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : "bg-green-100 text-green-800"
                              }`}
                            >
                              {task.priority}
                            </span>
                          </div>
                        </div>

                        <div className={`${isMobile ? "w-full" : "w-1/3"}`}>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-xs">{task.progress}%</span>
                            <span className="text-xs">Due: {task.dueDate}</span>
                          </div>
                          <Progress value={task.progress} className="h-1.5" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </TabsContent>

            <TabsContent value="timeline" className="mt-4">
              <div className="relative">
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                <div className="space-y-8 pl-10">
                  {tasks &&
                    tasks.map((task) => (
                      <div key={task.id} className="relative">
                        <div
                          className={`absolute left-[-30px] top-1 w-4 h-4 rounded-full border-2 ${
                            task.status === "completed"
                              ? "bg-green-500 border-green-500"
                              : task.status === "in-progress"
                                ? "bg-blue-500 border-blue-500"
                                : "bg-white border-gray-300"
                          }`}
                        ></div>
                        <div className="space-y-1">
                          <h3 className="font-medium">{task.title}</h3>
                          <p className="text-sm text-gray-500">Due: {task.dueDate}</p>
                          <div className="flex items-center space-x-2">
                            <span
                              className={`px-2 py-1 text-xs rounded-full ${
                                task.status === "completed"
                                  ? "bg-green-100 text-green-800"
                                  : task.status === "in-progress"
                                    ? "bg-blue-100 text-blue-800"
                                    : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {task.status.replace("-", " ")}
                            </span>
                            <span className="text-xs">{task.progress}% complete</span>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="milestones" className="space-y-4 mt-4">
              {milestones &&
                milestones.map((milestone) => (
                  <Card key={milestone.id} className="overflow-hidden">
                    <CardContent className="p-4">
                      <div className={`${isMobile ? "space-y-2" : "flex items-center justify-between"}`}>
                        <div className="space-y-1">
                          <h3 className="font-medium">{milestone.title}</h3>
                          <p className="text-sm text-gray-500">{milestone.description}</p>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span className="text-sm">Target: {milestone.date}</span>
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${
                              milestone.status === "completed"
                                ? "bg-green-100 text-green-800"
                                : milestone.status === "in-progress"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {milestone.status.replace("-", " ")}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
