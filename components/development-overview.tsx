"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { RoadmapView } from "@/components/roadmap-view"
import { ProgressReport } from "@/components/progress-report"
import {
  LineChart,
  Line,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from "recharts"
import Link from "next/link"
import { ArrowRight, Calendar, CheckCircle, Clock, FileText, Map, TrendingUp } from "lucide-react"

// Milestone progress data
const milestoneProgressData = [
  { name: "Core Infrastructure", completed: 20, total: 100 },
  { name: "Token Launch Experience", completed: 10, total: 100 },
  { name: "Trading Enhancements", completed: 0, total: 100 },
  { name: "Mobile & Accessibility", completed: 15, total: 100 },
  { name: "Performance & Technical Debt", completed: 5, total: 100 },
  { name: "Security & Compliance", completed: 0, total: 100 },
  { name: "Community & Social", completed: 0, total: 100 },
]

// Weekly progress data
const weeklyProgressData = [
  { week: "Week 1", completed: 3, inProgress: 5, planned: 42, blocked: 0 },
  { week: "Week 2", completed: 7, inProgress: 8, planned: 35, blocked: 0 },
  { week: "Week 3", completed: 12, inProgress: 10, planned: 28, blocked: 0 },
  { week: "Week 4", completed: 15, inProgress: 12, planned: 23, blocked: 0 },
  { week: "Week 5", completed: 18, inProgress: 15, planned: 17, blocked: 0 },
  { week: "Week 6", completed: 22, inProgress: 12, planned: 16, blocked: 0 },
]

// Priority distribution data
const priorityData = [
  { name: "Critical", value: 9, color: "#ef4444" },
  { name: "High", value: 18, color: "#f97316" },
  { name: "Medium", value: 28, color: "#3b82f6" },
  { name: "Low", value: 15, color: "#6b7280" },
]

// Recent activity data
const recentActivityData = [
  {
    id: 1,
    task: "CF-007",
    title: "Integrate Multiple Wallet Providers",
    action: "started",
    date: "2023-05-28",
    user: "Alex Chen",
  },
  {
    id: 2,
    task: "CF-008",
    title: "Implement Dogechain Integration",
    action: "started",
    date: "2023-05-27",
    user: "Sarah Johnson",
  },
  {
    id: 3,
    task: "MR-002",
    title: "Improve Chart Touch Interactions",
    action: "started",
    date: "2023-05-26",
    user: "Michael Wong",
  },
]

export function DevelopmentOverview() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="roadmap">Roadmap</TabsTrigger>
          <TabsTrigger value="progress">Progress</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="pt-4 space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Overall Progress</CardTitle>
                <CardDescription>Current development progress</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Tasks Completed</span>
                      <span className="text-sm text-muted-foreground">22/70 (31%)</span>
                    </div>
                    <Progress value={31} />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Estimated Hours</span>
                      <span className="text-sm text-muted-foreground">320/1200 (27%)</span>
                    </div>
                    <Progress value={27} />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Milestones Completed</span>
                      <span className="text-sm text-muted-foreground">0/7 (0%)</span>
                    </div>
                    <Progress value={0} />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Critical Tasks</span>
                      <span className="text-sm text-muted-foreground">2/9 (22%)</span>
                    </div>
                    <Progress value={22} className="bg-red-100 dark:bg-red-900">
                      <div className="bg-red-500 h-full" style={{ width: "22%" }} />
                    </Progress>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Weekly Progress</CardTitle>
                <CardDescription>Task completion over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={weeklyProgressData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="week" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="completed" stroke="#10b981" name="Completed" />
                      <Line type="monotone" dataKey="inProgress" stroke="#3b82f6" name="In Progress" />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Next Milestone</CardTitle>
                <CardDescription>Core Infrastructure</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 mb-4">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Target: June 30, 2023</span>
                </div>
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Progress</span>
                    <span className="text-sm text-muted-foreground">20%</span>
                  </div>
                  <Progress value={20} />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <div className="h-2 w-2 rounded-full bg-blue-500" />
                    <span>Integrate Multiple Wallet Providers</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <div className="h-2 w-2 rounded-full bg-blue-500" />
                    <span>Implement Dogechain Integration</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <div className="h-2 w-2 rounded-full bg-gray-500" />
                    <span>Add Transaction Signing Process</span>
                  </div>
                </div>
                <Button variant="link" className="p-0 h-auto mt-4" asChild>
                  <Link href="/roadmap">
                    View Roadmap <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest updates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivityData.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-2">
                      <div className="h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-500 text-xs">
                        {activity.user.charAt(0)}
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center gap-1">
                          <span className="text-xs font-mono">{activity.task}</span>
                          <Badge variant="outline" className="text-xs">
                            {activity.action}
                          </Badge>
                        </div>
                        <p className="text-sm">{activity.title}</p>
                        <p className="text-xs text-muted-foreground">{activity.date}</p>
                      </div>
                    </div>
                  ))}
                </div>
                <Button variant="link" className="p-0 h-auto mt-4" asChild>
                  <Link href="/progress-report">
                    View All Activity <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Priority Distribution</CardTitle>
                <CardDescription>Tasks by priority</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[180px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={priorityData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={70}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                      >
                        {priorityData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-4">
            <Card className="col-span-full md:col-span-1">
              <CardHeader>
                <CardTitle>Quick Links</CardTitle>
                <CardDescription>Development resources</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/roadmap">
                      <Map className="mr-2 h-4 w-4" />
                      Roadmap
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/progress-report">
                      <TrendingUp className="mr-2 h-4 w-4" />
                      Progress Report
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/development-tracker">
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Development Tracker
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/development-staking">
                      <Clock className="mr-2 h-4 w-4" />
                      Development Staking
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/docs/development-dao">
                      <FileText className="mr-2 h-4 w-4" />
                      Documentation
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-full md:col-span-3">
              <CardHeader>
                <CardTitle>Milestone Progress</CardTitle>
                <CardDescription>Progress towards major development milestones</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {milestoneProgressData.slice(0, 4).map((milestone) => (
                    <div key={milestone.name} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{milestone.name}</span>
                        <span className="text-sm text-muted-foreground">{milestone.completed}% complete</span>
                      </div>
                      <Progress value={milestone.completed} />
                    </div>
                  ))}
                </div>
                <Button variant="link" className="p-0 h-auto mt-4" asChild>
                  <Link href="/progress-report">
                    View All Milestones <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="roadmap" className="pt-4">
          <RoadmapView />
        </TabsContent>

        <TabsContent value="progress" className="pt-4">
          <ProgressReport />
        </TabsContent>
      </Tabs>
    </div>
  )
}
