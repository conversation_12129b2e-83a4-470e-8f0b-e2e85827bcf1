export function ShimmerTextDemo() {
  return (
    <div className="py-12 space-y-12">
      <div className="text-center space-y-6">
        <h2 className="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl shimmer-text">Shimmer Text Effect</h2>
        <p className="mx-auto max-w-[700px] text-white/70">
          A premium text style with a subtle, continuous shimmer effect that flows across the text
        </p>
      </div>

      <div className="grid gap-8 md:grid-cols-2 mx-auto max-w-4xl">
        <div className="glass-card p-6 flex flex-col items-center justify-center text-center">
          <h3 className="shimmer-text text-2xl md:text-3xl font-bold mb-4">Launch Your Memecoin</h3>
          <p className="text-white/70">The shimmer effect adds a premium feel to important calls-to-action</p>
        </div>

        <div className="glass-card p-6 flex flex-col items-center justify-center text-center">
          <h3 className="shimmer-text text-2xl md:text-3xl font-bold mb-4">Join the Revolution</h3>
          <p className="text-white/70">Perfect for highlighting key marketing messages or platform benefits</p>
        </div>
      </div>

      <div className="mx-auto max-w-4xl glass-card p-8 text-center">
        <h3 className="shimmer-text text-3xl md:text-4xl font-bold mb-6">The PawPumps Advantage</h3>
        <p className="text-lg text-white/70 max-w-2xl mx-auto">
          This subtle shimmer creates an elegant, premium feel. It works best on larger heading text and should be used
          to highlight the most important messages.
        </p>
      </div>
    </div>
  )
}
