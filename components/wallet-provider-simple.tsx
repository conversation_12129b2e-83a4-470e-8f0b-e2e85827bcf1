"use client"

import { createContext, use<PERSON>ontext, useState, useEffect, type ReactNode } from "react"

interface WalletContextType {
  address: string | null
  isConnected: boolean
  balance: string | null
  chainId: number | null
  isConnecting: boolean
  error: string | null
  connect: (walletId?: string) => Promise<void>
  disconnect: () => void
  switchNetwork: (chainId: number) => Promise<void>
  getAvailableWallets: () => Array<{ id: string; name: string; installed: boolean }>
}

const WalletContext = createContext<WalletContextType | undefined>(undefined)

export function useWallet() {
  const context = useContext(WalletContext)
  if (context === undefined) {
    throw new Error("useWallet must be used within a WalletProvider")
  }
  return context
}

interface WalletProviderProps {
  children: ReactNode
}

export function WalletProvider({ children }: WalletProviderProps) {
  const [address, setAddress] = useState<string | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [balance, setBalance] = useState<string | null>(null)
  const [chainId, setChainId] = useState<number | null>(null)
  const [isConnecting, setIsConnecting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Check if wallet is already connected on mount
  useEffect(() => {
    checkConnection()
  }, [])

  const checkConnection = async () => {
    try {
      if (typeof window !== 'undefined' && window.ethereum) {
        const accounts = await window.ethereum.request({ method: 'eth_accounts' })
        if (accounts.length > 0) {
          setAddress(accounts[0])
          setIsConnected(true)
          await updateWalletInfo()
        }
      }
    } catch (err) {
      console.error('Error checking wallet connection:', err)
    }
  }

  const updateWalletInfo = async () => {
    try {
      if (window.ethereum && address) {
        // Get balance
        const balance = await window.ethereum.request({
          method: 'eth_getBalance',
          params: [address, 'latest']
        })
        setBalance((parseInt(balance, 16) / 1e18).toFixed(4))

        // Get chain ID
        const chainId = await window.ethereum.request({ method: 'eth_chainId' })
        setChainId(parseInt(chainId, 16))
      }
    } catch (err) {
      console.error('Error updating wallet info:', err)
    }
  }

  const connect = async (walletId?: string) => {
    setIsConnecting(true)
    setError(null)

    try {
      if (typeof window === 'undefined' || !window.ethereum) {
        throw new Error('MetaMask is not installed')
      }

      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      })

      if (accounts.length > 0) {
        setAddress(accounts[0])
        setIsConnected(true)
        await updateWalletInfo()
      }
    } catch (err: any) {
      setError(err.message || 'Failed to connect wallet')
      console.error('Error connecting wallet:', err)
    } finally {
      setIsConnecting(false)
    }
  }

  const disconnect = () => {
    setAddress(null)
    setIsConnected(false)
    setBalance(null)
    setChainId(null)
    setError(null)
  }

  const switchNetwork = async (targetChainId: number) => {
    try {
      if (!window.ethereum) {
        throw new Error('MetaMask is not installed')
      }

      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${targetChainId.toString(16)}` }]
      })

      setChainId(targetChainId)
    } catch (err: any) {
      setError(err.message || 'Failed to switch network')
      console.error('Error switching network:', err)
    }
  }

  const getAvailableWallets = () => {
    const wallets = [
      {
        id: 'metamask',
        name: 'MetaMask',
        installed: typeof window !== 'undefined' && !!window.ethereum
      },
      {
        id: 'walletconnect',
        name: 'WalletConnect',
        installed: true // Always available
      },
      {
        id: 'coinbase',
        name: 'Coinbase Wallet',
        installed: false // Simplified for now
      },
      {
        id: 'trust',
        name: 'Trust Wallet',
        installed: false // Simplified for now
      }
    ]
    return wallets
  }

  // Listen for account changes
  useEffect(() => {
    if (typeof window !== 'undefined' && window.ethereum) {
      const handleAccountsChanged = (accounts: string[]) => {
        if (accounts.length === 0) {
          disconnect()
        } else {
          setAddress(accounts[0])
          updateWalletInfo()
        }
      }

      const handleChainChanged = (chainId: string) => {
        setChainId(parseInt(chainId, 16))
      }

      window.ethereum.on('accountsChanged', handleAccountsChanged)
      window.ethereum.on('chainChanged', handleChainChanged)

      return () => {
        if (window.ethereum) {
          window.ethereum.removeListener('accountsChanged', handleAccountsChanged)
          window.ethereum.removeListener('chainChanged', handleChainChanged)
        }
      }
    }
  }, [address])

  const value: WalletContextType = {
    address,
    isConnected,
    balance,
    chainId,
    isConnecting,
    error,
    connect,
    disconnect,
    switchNetwork,
    getAvailableWallets
  }

  return (
    <WalletContext.Provider value={value}>
      {children}
    </WalletContext.Provider>
  )
}


