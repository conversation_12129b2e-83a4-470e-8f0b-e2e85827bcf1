"use client"

import { useState } from 'react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useWallet } from '@/components/wallet-provider'
import { WalletConnector } from '@/lib/wallet-connectors'
import { Loader2, ExternalLink, AlertTriangle } from 'lucide-react'

interface WalletConnectModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function WalletConnectModal({ open, onOpenChange }: WalletConnectModalProps) {
  const { connect, getAvailableWallets, isConnecting, error } = useWallet()
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null)
  const availableWallets = getAvailableWallets()

  const handleConnect = async (connectorId: string) => {
    setSelectedWallet(connectorId)
    try {
      await connect(connectorId)
      onOpenChange(false)
    } catch (error) {
      console.error('Connection failed:', error)
      setSelectedWallet(null)
    }
  }

  const getWalletIcon = (connector: WalletConnector) => {
    // Return appropriate icon based on wallet type
    switch (connector.id) {
      case 'metamask':
        return '🦊'
      case 'walletconnect':
        return '🔗'
      case 'coinbase':
        return '🔵'
      case 'trust':
        return '🛡️'
      default:
        return '👛'
    }
  }

  const getInstallUrl = (connectorId: string) => {
    switch (connectorId) {
      case 'metamask':
        return 'https://metamask.io/download/'
      case 'trust':
        return 'https://trustwallet.com/download'
      case 'coinbase':
        return 'https://www.coinbase.com/wallet'
      default:
        return null
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Connect Wallet</DialogTitle>
          <DialogDescription>
            Choose a wallet to connect to PawPumps and start trading memecoins on Dogechain.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {error && (
            <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-destructive" />
              <p className="text-sm text-destructive">{error}</p>
            </div>
          )}

          {availableWallets.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">
                No compatible wallets detected. Please install a supported wallet.
              </p>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  onClick={() => window.open('https://metamask.io/download/', '_blank')}
                  className="w-full"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Install MetaMask
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.open('https://trustwallet.com/download', '_blank')}
                  className="w-full"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Install Trust Wallet
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {availableWallets.map((connector) => (
                <Card
                  key={connector.id}
                  className="cursor-pointer hover:bg-accent/50 transition-colors"
                  onClick={() => handleConnect(connector.id)}
                >
                  <CardContent className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{getWalletIcon(connector)}</div>
                      <div>
                        <h3 className="font-medium">{connector.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {connector.id === 'metamask' && 'Most popular wallet'}
                          {connector.id === 'walletconnect' && 'Scan with mobile wallet'}
                          {connector.id === 'coinbase' && 'Coinbase users'}
                          {connector.id === 'trust' && 'Mobile-first wallet'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        Available
                      </Badge>
                      {isConnecting && selectedWallet === connector.id && (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Show unavailable wallets */}
          {availableWallets.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground">Not Installed</h4>
              {['metamask', 'trust', 'coinbase'].map((walletId) => {
                const isAvailable = availableWallets.some(w => w.id === walletId)
                const installUrl = getInstallUrl(walletId)
                
                if (isAvailable || !installUrl) return null
                
                return (
                  <Card key={walletId} className="opacity-60">
                    <CardContent className="flex items-center justify-between p-4">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl grayscale">{getWalletIcon({ id: walletId } as WalletConnector)}</div>
                        <div>
                          <h3 className="font-medium capitalize">{walletId}</h3>
                          <p className="text-sm text-muted-foreground">Not installed</p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(installUrl, '_blank')}
                      >
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Install
                      </Button>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}

          <div className="pt-4 border-t">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Secure connection via HTTPS</span>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              By connecting a wallet, you agree to our Terms of Service and Privacy Policy.
              Your wallet will be used to interact with smart contracts on the Dogechain network.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Wallet status indicator component
export function WalletStatus() {
  const { isConnected, address, balance, chainId, disconnect, switchNetwork } = useWallet()
  const [showDropdown, setShowDropdown] = useState(false)

  if (!isConnected || !address) {
    return null
  }

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`
  }

  const isCorrectNetwork = chainId === 2000 // Dogechain

  return (
    <div className="relative">
      <Button
        variant="outline"
        onClick={() => setShowDropdown(!showDropdown)}
        className="flex items-center gap-2"
      >
        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
        <span>{formatAddress(address)}</span>
        {balance && <span className="text-xs text-muted-foreground">({parseFloat(balance).toFixed(4)} DOGE)</span>}
      </Button>

      {showDropdown && (
        <div className="absolute top-full right-0 mt-2 w-64 bg-background border rounded-lg shadow-lg z-50">
          <div className="p-4 space-y-3">
            <div>
              <p className="text-sm font-medium">Connected Wallet</p>
              <p className="text-xs text-muted-foreground font-mono">{address}</p>
            </div>
            
            {balance && (
              <div>
                <p className="text-sm font-medium">Balance</p>
                <p className="text-xs text-muted-foreground">{parseFloat(balance).toFixed(6)} DOGE</p>
              </div>
            )}

            <div>
              <p className="text-sm font-medium">Network</p>
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${isCorrectNetwork ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                <p className="text-xs text-muted-foreground">
                  {isCorrectNetwork ? 'Dogechain Mainnet' : `Chain ID: ${chainId}`}
                </p>
              </div>
            </div>

            {!isCorrectNetwork && (
              <Button
                size="sm"
                onClick={() => switchNetwork(2000)}
                className="w-full"
              >
                Switch to Dogechain
              </Button>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={disconnect}
              className="w-full"
            >
              Disconnect
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
