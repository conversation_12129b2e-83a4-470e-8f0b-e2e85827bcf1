"use client"

import type React from "react"
import dynamic from "next/dynamic"

import { useState, useRef, memo, useCallback } from "react"
import { ArrowDown, Settings, Info, RefreshCw, X, Zap, Shield, Clock } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { useToast } from "@/components/ui/use-toast"
import { useWallet } from "@/components/wallet-provider"
import { LoadingSpinner } from "@/components/ui/loading-state"
import { TradingStatusAnnouncer } from "@/components/ui/live-region"
import { useTrading<PERSON>rror<PERSON><PERSON><PERSON> } from "@/hooks/use-error-handler"
import { InlineErrorRecovery } from "@/components/ui/error-recovery"
import { useCachedPriceData, useCachedChartData } from "@/hooks/use-cached-data"
import { useNotification } from "@/hooks/use-notification"

// Dynamically import heavy components
const TradingChart = dynamic(() => import("@/components/trading-chart").then(mod => ({ default: mod.TradingChart })), {
  ssr: false,
  loading: () => <div className="h-64 bg-white/5 rounded-lg flex items-center justify-center">Loading Chart...</div>
})

const EnhancedTradingChart = dynamic(() => import("@/components/enhanced-trading-chart").then(mod => ({ default: mod.EnhancedTradingChart })), {
  ssr: false,
  loading: () => <div className="h-64 bg-white/5 rounded-lg animate-pulse flex items-center justify-center">Loading Enhanced Chart...</div>
})

const TokenList = dynamic(() => import("@/components/token-list").then(mod => ({ default: mod.TokenList })), {
  ssr: false,
  loading: () => <div className="h-32 bg-white/5 rounded-lg animate-pulse flex items-center justify-center">Loading Tokens...</div>
})

const LiquidityInterface = dynamic(() => import("@/components/liquidity-interface").then(mod => ({ default: mod.LiquidityInterface })), {
  ssr: false,
  loading: () => <div className="h-96 bg-white/5 rounded-lg animate-pulse flex items-center justify-center">Loading Liquidity Interface...</div>
})

export function TradingInterface() {
  const { isConnected } = useWallet()
  const { toast } = useToast()
  const { showNotification } = useNotification()

  const [swapState, setSwapState] = useState({
    fromToken: "wDOGE",
    toToken: "PAW",
    fromAmount: "",
    toAmount: "",
    slippage: 0.5,
  })

  const [advancedSettings, setAdvancedSettings] = useState({
    maxSlippage: 100,
    customSlippage: "",
    gasSpeed: "standard", // "slow", "standard", "fast"
    mevProtection: true,
    deadline: 20, // minutes
    expertMode: false
  })

  const [isSwapping, setIsSwapping] = useState(false)
  const [lastTradeStatus, setLastTradeStatus] = useState<string>("")
  const [showSettings, setShowSettings] = useState(false)
  const [isSwapAnimating, setIsSwapAnimating] = useState(false)
  const { error, executeWithRetry, clearError } = useTradingErrorHandler()

  // Use cached price data
  const { data: fromTokenPrice, loading: fromPriceLoading } = useCachedPriceData(swapState.fromToken)
  const { data: toTokenPrice, loading: toPriceLoading } = useCachedPriceData(swapState.toToken)

  // Use cached chart data
  const { data: chartData, loading: chartLoading } = useCachedChartData(swapState.fromToken, '1D')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    // Update the state
    setSwapState((prev) => ({ ...prev, [name]: value }))

    // Simulate price calculation
    if (name === "fromAmount" && value) {
      const numValue = Number.parseFloat(value)
      if (!isNaN(numValue)) {
        // Mock conversion rate
        const rate = swapState.fromToken === "wDOGE" ? 1000 : 0.001
        const calculatedAmount = (numValue * rate).toFixed(6)
        setSwapState((prev) => ({ ...prev, toAmount: calculatedAmount }))
      } else {
        setSwapState((prev) => ({ ...prev, toAmount: "" }))
      }
    } else if (name === "toAmount" && value) {
      const numValue = Number.parseFloat(value)
      if (!isNaN(numValue)) {
        // Mock conversion rate
        const rate = swapState.toToken === "wDOGE" ? 0.001 : 1000
        const calculatedAmount = (numValue * rate).toFixed(6)
        setSwapState((prev) => ({ ...prev, fromAmount: calculatedAmount }))
      } else {
        setSwapState((prev) => ({ ...prev, fromAmount: "" }))
      }
    }
  }

  const handleTokenChange = (field: "fromToken" | "toToken", value: string) => {
    setSwapState((prev) => {
      // If selecting the same token that's already in the other field, swap them
      if (field === "fromToken" && value === prev.toToken) {
        return { ...prev, fromToken: value, toToken: prev.fromToken }
      } else if (field === "toToken" && value === prev.fromToken) {
        return { ...prev, toToken: value, fromToken: prev.toToken }
      }

      return { ...prev, [field]: value }
    })

    // Recalculate amounts if needed
    if (swapState.fromAmount) {
      const numValue = Number.parseFloat(swapState.fromAmount)
      if (!isNaN(numValue)) {
        // Mock conversion rate based on new token selection
        const rate = value === "wDOGE" ? 0.001 : 1000
        const calculatedAmount = (numValue * rate).toFixed(6)
        setSwapState((prev) => ({ ...prev, toAmount: calculatedAmount }))
      }
    }
  }

  const handleSlippageChange = (value: number[]) => {
    setSwapState((prev) => ({ ...prev, slippage: value[0] }))
  }

  const handleSwapTokens = () => {
    // Trigger animation
    setIsSwapAnimating(true)
    setTimeout(() => setIsSwapAnimating(false), 500)

    setSwapState((prev) => {
      const newState = {
        ...prev,
        fromToken: prev.toToken,
        toToken: prev.fromToken,
        fromAmount: prev.toAmount,
        toAmount: prev.fromAmount,
      }

      // Recalculate conversion if there's a fromAmount
      if (newState.fromAmount && !isNaN(Number.parseFloat(newState.fromAmount))) {
        const numValue = Number.parseFloat(newState.fromAmount)
        const rate = newState.fromToken === "wDOGE" ? 1000 : 0.001
        const calculatedAmount = (numValue * rate).toFixed(6)
        newState.toAmount = calculatedAmount
      }

      return newState
    })
  }

  const handleSwap = async () => {
    if (!isConnected) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to swap tokens",
        variant: "destructive",
      })
      return
    }

    if (!swapState.fromAmount || !swapState.toAmount) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid amount to swap",
        variant: "destructive",
      })
      return
    }

    clearError() // Clear any previous errors
    setIsSwapping(true)
    setLastTradeStatus("Initiating swap transaction...")

    const result = await executeWithRetry(async () => {
      // Simulate swap with potential failure
      await new Promise((resolve, reject) => {
        setTimeout(() => {
          // Simulate random failures for demo
          if (Math.random() < 0.3) {
            reject(new Error("Transaction failed: Insufficient gas fees"))
          } else {
            resolve(undefined)
          }
        }, 2000)
      })

      return {
        fromAmount: swapState.fromAmount,
        fromToken: swapState.fromToken,
        toAmount: swapState.toAmount,
        toToken: swapState.toToken,
      }
    }, { action: 'swap_tokens' })

    if (result) {
      const successMessage = `Successfully swapped ${result.fromAmount} ${result.fromToken} for ${result.toAmount} ${result.toToken}`

      showNotification({
        title: "Swap Successful!",
        message: successMessage,
        type: "success",
        addToCenter: true,
      })

      setLastTradeStatus(successMessage)

      // Reset form
      setSwapState((prev) => ({
        ...prev,
        fromAmount: "",
        toAmount: "",
      }))
    }

    setIsSwapping(false)
  }

  return (
    <div className="flex flex-col gap-8 lg:grid lg:grid-cols-3 lg:gap-8">
      {/* Chart Section - Full width on mobile, 2/3 on desktop */}
      <div className="w-full lg:col-span-2">
        <EnhancedTradingChart
          token={swapState.fromToken}
          height={400}
          showVolume={true}
          showRealtime={true}
        />
      </div>

      {/* Swap Section - Below chart on mobile, right side on desktop */}
      <div className="w-full">
        <Tabs defaultValue="swap" className="w-full">
          <TabsList className="grid w-full grid-cols-2 glass">
            <TabsTrigger value="swap">Swap</TabsTrigger>
            <TabsTrigger value="liquidity">Liquidity</TabsTrigger>
          </TabsList>
          <TabsContent value="swap">
            <Card className="glass-card border-white/5 liquid-glow">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white">Swap Tokens</CardTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowSettings(!showSettings)}
                    className={`text-white/70 hover:text-white transition-all duration-300 ${
                      showSettings ? 'rotate-90 text-doge' : 'hover:rotate-12'
                    }`}
                  >
                    <Settings className="h-4 w-4" />
                    <span className="sr-only">Settings</span>
                  </Button>
                </div>
                <CardDescription className="text-white/70">Trade tokens with bonding curve pricing</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="fromAmount" className="text-white/80">
                        From
                      </Label>
                      <span className="text-xs text-white/60">Balance: 1000</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="relative flex-1">
                        <Input
                          id="fromAmount"
                          name="fromAmount"
                          type="number"
                          placeholder="0.0"
                          value={swapState.fromAmount}
                          onChange={handleInputChange}
                          className="glass-input border-white/10 pr-20"
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                          <Button
                            variant="ghost"
                            className="h-6 text-xs font-medium text-white"
                            onClick={() => {
                              setSwapState((prev) => ({
                                ...prev,
                                fromAmount: "1000",
                                toAmount: prev.fromToken === "wDOGE" ? "1000000" : "1",
                              }))
                            }}
                          >
                            MAX
                          </Button>
                        </div>
                      </div>
                      <Select
                        value={swapState.fromToken}
                        onValueChange={(value) => handleTokenChange("fromToken", value)}
                      >
                        <SelectTrigger className="w-[110px] glass-input border-white/10">
                          <SelectValue placeholder="Token" />
                        </SelectTrigger>
                        <SelectContent className="glass">
                          <SelectItem value="wDOGE">wDOGE</SelectItem>
                          <SelectItem value="PAW">PAW</SelectItem>
                          <SelectItem value="DC">DC</SelectItem>
                          <SelectItem value="SHIB">SHIB</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex justify-center">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handleSwapTokens}
                      className="rounded-full bg-white/5 hover:bg-white/10 transition-all duration-300 hover:scale-110 active:scale-95 group"
                    >
                      <ArrowDown className={`h-4 w-4 transition-transform duration-500 group-hover:rotate-180 group-active:scale-110 ${isSwapAnimating ? 'rotate-180' : ''}`} />
                      <span className="sr-only">Swap tokens</span>
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="toAmount" className="text-white/80">
                        To
                      </Label>
                      <span className="text-xs text-white/60">Balance: 0</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="toAmount"
                        name="toAmount"
                        type="number"
                        placeholder="0.0"
                        value={swapState.toAmount}
                        onChange={handleInputChange}
                        className="flex-1 glass-input border-white/10"
                      />
                      <Select value={swapState.toToken} onValueChange={(value) => handleTokenChange("toToken", value)}>
                        <SelectTrigger className="w-[110px] glass-input border-white/10">
                          <SelectValue placeholder="Token" />
                        </SelectTrigger>
                        <SelectContent className="glass">
                          <SelectItem value="wDOGE">wDOGE</SelectItem>
                          <SelectItem value="PAW">PAW</SelectItem>
                          <SelectItem value="DC">DC</SelectItem>
                          <SelectItem value="SHIB">SHIB</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {swapState.fromAmount && swapState.toAmount && (
                    <div className="rounded-md bg-white/5 p-3 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-white/60">Price</span>
                        <span className="text-white/90">
                          1 {swapState.fromToken} ={" "}
                          {(Number.parseFloat(swapState.toAmount) / Number.parseFloat(swapState.fromAmount)).toFixed(6)}{" "}
                          {swapState.toToken}
                        </span>
                      </div>
                      <div className="mt-1 flex items-center justify-between">
                        <span className="text-white/60">Fee (0.5%)</span>
                        <span className="text-white/90">
                          {(Number.parseFloat(swapState.fromAmount) * 0.005).toFixed(6)} {swapState.fromToken}
                        </span>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="flex items-center gap-1 text-white/80">
                        Slippage Tolerance
                        <Info className="h-3 w-3 text-white/60" />
                      </Label>
                      <span className="text-sm font-medium text-white/90">{swapState.slippage}%</span>
                    </div>
                    <Slider
                      min={0.1}
                      max={advancedSettings.maxSlippage}
                      step={0.1}
                      value={[swapState.slippage]}
                      onValueChange={handleSlippageChange}
                    />
                    <div className="flex items-center justify-between text-xs text-white/60">
                      <span>0.1%</span>
                      <span>{advancedSettings.maxSlippage}%</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        placeholder="Custom %"
                        value={advancedSettings.customSlippage}
                        onChange={(e) => {
                          const value = e.target.value
                          setAdvancedSettings(prev => ({ ...prev, customSlippage: value }))
                          if (value && !isNaN(Number(value))) {
                            const numValue = Math.min(Number(value), advancedSettings.maxSlippage)
                            setSwapState(prev => ({ ...prev, slippage: numValue }))
                          }
                        }}
                        className="flex-1 glass-input border-white/10 text-xs"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          if (advancedSettings.customSlippage && !isNaN(Number(advancedSettings.customSlippage))) {
                            const numValue = Math.min(Number(advancedSettings.customSlippage), advancedSettings.maxSlippage)
                            setSwapState(prev => ({ ...prev, slippage: numValue }))
                          }
                        }}
                        className="glass-button text-xs"
                      >
                        Apply
                      </Button>
                    </div>
                  </div>

                  {/* Advanced Settings Panel */}
                  <div className={`overflow-hidden transition-all duration-500 ease-in-out ${
                    showSettings ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
                  }`}>
                    <div className="pt-4 border-t border-white/10 space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-white flex items-center gap-2">
                          <Settings className="h-4 w-4" />
                          Advanced Settings
                        </h4>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowSettings(false)}
                          className="text-white/60 hover:text-white"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>

                      {/* Max Slippage Setting */}
                      <div className="space-y-2">
                        <Label className="text-white/80 text-xs">Maximum Slippage Limit</Label>
                        <div className="flex items-center gap-2">
                          <Slider
                            min={5}
                            max={100}
                            step={5}
                            value={[advancedSettings.maxSlippage]}
                            onValueChange={(value) => setAdvancedSettings(prev => ({ ...prev, maxSlippage: value[0] }))}
                            className="flex-1"
                          />
                          <span className="text-xs text-white/60 w-12">{advancedSettings.maxSlippage}%</span>
                        </div>
                      </div>

                      {/* Gas Speed Setting */}
                      <div className="space-y-2">
                        <Label className="text-white/80 text-xs flex items-center gap-1">
                          <Zap className="h-3 w-3" />
                          Transaction Speed
                        </Label>
                        <div className="grid grid-cols-3 gap-1">
                          {['slow', 'standard', 'fast'].map((speed) => (
                            <Button
                              key={speed}
                              variant={advancedSettings.gasSpeed === speed ? "default" : "outline"}
                              size="sm"
                              onClick={() => setAdvancedSettings(prev => ({ ...prev, gasSpeed: speed }))}
                              className="text-xs glass-button"
                            >
                              {speed.charAt(0).toUpperCase() + speed.slice(1)}
                            </Button>
                          ))}
                        </div>
                      </div>

                      {/* MEV Protection */}
                      <div className="flex items-center justify-between">
                        <Label className="text-white/80 text-xs flex items-center gap-1">
                          <Shield className="h-3 w-3" />
                          MEV Protection
                        </Label>
                        <Button
                          variant={advancedSettings.mevProtection ? "default" : "outline"}
                          size="sm"
                          onClick={() => setAdvancedSettings(prev => ({ ...prev, mevProtection: !prev.mevProtection }))}
                          className="text-xs glass-button"
                        >
                          {advancedSettings.mevProtection ? 'ON' : 'OFF'}
                        </Button>
                      </div>

                      {/* Transaction Deadline */}
                      <div className="space-y-2">
                        <Label className="text-white/80 text-xs flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          Transaction Deadline (minutes)
                        </Label>
                        <div className="flex items-center gap-2">
                          <Input
                            type="number"
                            min="1"
                            max="60"
                            value={advancedSettings.deadline}
                            onChange={(e) => setAdvancedSettings(prev => ({
                              ...prev,
                              deadline: Math.max(1, Math.min(60, Number(e.target.value) || 20))
                            }))}
                            className="glass-input border-white/10 text-xs"
                          />
                          <span className="text-xs text-white/60">min</span>
                        </div>
                      </div>

                      {/* Expert Mode */}
                      <div className="flex items-center justify-between pt-3 pb-2 border-t border-white/5 min-h-[70px]">
                        <div className="flex-1 pr-3">
                          <Label className="text-white/80 text-xs block mb-1">Expert Mode</Label>
                          <p className="text-xs text-white/50 leading-relaxed">Disable transaction confirmations</p>
                        </div>
                        <Button
                          variant={advancedSettings.expertMode ? "destructive" : "outline"}
                          size="sm"
                          onClick={() => setAdvancedSettings(prev => ({ ...prev, expertMode: !prev.expertMode }))}
                          className="text-xs flex-shrink-0"
                        >
                          {advancedSettings.expertMode ? 'ON' : 'OFF'}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleSwap}
                  disabled={isSwapping || !swapState.fromAmount || !swapState.toAmount || !isConnected}
                  className="w-full cosmic-button"
                >
                  {isSwapping ? (
                    <div className="flex items-center">
                      <LoadingSpinner size="sm" className="mr-2" />
                      <span>Swapping...</span>
                    </div>
                  ) : !isConnected ? (
                    <span>Connect Wallet</span>
                  ) : !swapState.fromAmount || !swapState.toAmount ? (
                    <span>Enter an amount</span>
                  ) : (
                    <span>Swap</span>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          <TabsContent value="liquidity">
            <LiquidityInterface />
          </TabsContent>
        </Tabs>
      </div>

      {/* Top Tokens Section - Below swap on mobile, spans full width */}
      <div className="w-full lg:col-span-3">
        <Card className="glass-card border-white/5 liquid-glow">
          <CardHeader>
            <CardTitle className="text-white">Top Tokens</CardTitle>
            <CardDescription className="text-white/70">Trending memecoins on PawPumps</CardDescription>
          </CardHeader>
          <CardContent>
            <TokenList />
          </CardContent>
        </Card>
      </div>

      {/* Error recovery */}
      {error && (
        <div className="w-full lg:col-span-3">
          <InlineErrorRecovery
            error={error}
            onRetry={() => handleSwap()}
          />
        </div>
      )}

      {/* Trading status announcements for screen readers */}
      <TradingStatusAnnouncer
        isConnected={isConnected}
        isTrading={isSwapping}
        lastTradeStatus={lastTradeStatus}
      />
    </div>
  )
}
