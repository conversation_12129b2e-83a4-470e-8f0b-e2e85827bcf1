"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { useNotification } from "@/hooks/use-notification"
import { Bell, Calendar, Mail, Save } from "lucide-react"

export type NotificationPreference = {
  id: string
  type: string
  channel: "in-app" | "email" | "calendar"
  enabled: boolean
  description: string
}

export function NotificationSettings() {
  const { showNotification } = useNotification()
  const [preferences, setPreferences] = useState<NotificationPreference[]>([
    {
      id: "governance-proposal",
      type: "Governance Proposals",
      channel: "in-app",
      enabled: true,
      description: "New proposals and voting reminders",
    },
    {
      id: "governance-result",
      type: "Governance Results",
      channel: "in-app",
      enabled: true,
      description: "Results of proposal votes",
    },
    {
      id: "token-launch",
      type: "Token Launches",
      channel: "in-app",
      enabled: true,
      description: "New token launches on the platform",
    },
    {
      id: "price-alert",
      type: "Price Alerts",
      channel: "in-app",
      enabled: false,
      description: "Significant price movements for watched tokens",
    },
    {
      id: "governance-proposal-email",
      type: "Governance Proposals",
      channel: "email",
      enabled: false,
      description: "New proposals and voting reminders",
    },
    {
      id: "governance-result-email",
      type: "Governance Results",
      channel: "email",
      enabled: false,
      description: "Results of proposal votes",
    },
    {
      id: "token-launch-email",
      type: "Token Launches",
      channel: "email",
      enabled: false,
      description: "New token launches on the platform",
    },
    {
      id: "governance-deadline-calendar",
      type: "Governance Deadlines",
      channel: "calendar",
      enabled: false,
      description: "Add voting deadlines to your calendar",
    },
    {
      id: "token-launch-calendar",
      type: "Token Launch Events",
      channel: "calendar",
      enabled: false,
      description: "Add token launch events to your calendar",
    },
  ])

  const togglePreference = (id: string) => {
    setPreferences(preferences.map((pref) => (pref.id === id ? { ...pref, enabled: !pref.enabled } : pref)))
  }

  const savePreferences = () => {
    // In a real app, this would save to a database
    showNotification({
      title: "Preferences Saved",
      message: "Your notification preferences have been updated",
      type: "success",
    })
  }

  const getChannelIcon = (channel: "in-app" | "email" | "calendar") => {
    switch (channel) {
      case "in-app":
        return <Bell className="h-5 w-5 text-white/70" />
      case "email":
        return <Mail className="h-5 w-5 text-white/70" />
      case "calendar":
        return <Calendar className="h-5 w-5 text-white/70" />
    }
  }

  return (
    <Card className="glass-card border-white/5">
      <CardHeader>
        <CardTitle className="text-white">Notification Preferences</CardTitle>
        <CardDescription className="text-white/70">Customize how and when you receive notifications</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-white">In-App Notifications</h3>
            <Bell className="h-5 w-5 text-white/70" />
          </div>
          <div className="space-y-2">
            {preferences
              .filter((pref) => pref.channel === "in-app")
              .map((pref) => (
                <div key={pref.id} className="flex items-center justify-between py-2">
                  <div className="space-y-0.5">
                    <Label className="text-white">{pref.type}</Label>
                    <p className="text-sm text-white/70">{pref.description}</p>
                  </div>
                  <Switch
                    checked={pref.enabled}
                    onCheckedChange={() => togglePreference(pref.id)}
                    aria-label={`${pref.type} notifications`}
                  />
                </div>
              ))}
          </div>
        </div>

        <Separator className="bg-white/10" />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-white">Email Notifications</h3>
            <Mail className="h-5 w-5 text-white/70" />
          </div>
          <div className="space-y-2">
            {preferences
              .filter((pref) => pref.channel === "email")
              .map((pref) => (
                <div key={pref.id} className="flex items-center justify-between py-2">
                  <div className="space-y-0.5">
                    <Label className="text-white">{pref.type}</Label>
                    <p className="text-sm text-white/70">{pref.description}</p>
                  </div>
                  <Switch
                    checked={pref.enabled}
                    onCheckedChange={() => togglePreference(pref.id)}
                    aria-label={`${pref.type} email notifications`}
                  />
                </div>
              ))}
          </div>
        </div>

        <Separator className="bg-white/10" />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-white">Calendar Integration</h3>
            <Calendar className="h-5 w-5 text-white/70" />
          </div>
          <div className="space-y-2">
            {preferences
              .filter((pref) => pref.channel === "calendar")
              .map((pref) => (
                <div key={pref.id} className="flex items-center justify-between py-2">
                  <div className="space-y-0.5">
                    <Label className="text-white">{pref.type}</Label>
                    <p className="text-sm text-white/70">{pref.description}</p>
                  </div>
                  <Switch
                    checked={pref.enabled}
                    onCheckedChange={() => togglePreference(pref.id)}
                    aria-label={`${pref.type} calendar integration`}
                  />
                </div>
              ))}
          </div>
        </div>

        <Button onClick={savePreferences} className="w-full doge-button doge-shine">
          <Save className="mr-2 h-4 w-4" />
          Save Preferences
        </Button>
      </CardContent>
    </Card>
  )
}
