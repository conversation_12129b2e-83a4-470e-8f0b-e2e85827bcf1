"use client"

import { useEffect, useRef, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"

type StatData = {
  label: string
  value: string
  endValue: number
  prefix?: string
  suffix?: string
}

export function StatsSection() {
  const stats: StatData[] = [
    { label: "Total Tokens", value: "0", endValue: 1234, suffix: "" },
    { label: "Total Volume", value: "$0", endValue: 4.2, prefix: "$", suffix: "M" },
    { label: "Active Users", value: "0", endValue: 12.5, suffix: "K" },
    { label: "$PAW Price", value: "$0", endValue: 0.0042, prefix: "$" },
  ]

  return (
    <section className="border-b border-white/5 bg-black/20 backdrop-blur-sm py-12">
      <div className="container">
        <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
          {stats.map((stat) => (
            <StatCard key={stat.label} stat={stat} />
          ))}
        </div>
      </div>
    </section>
  )
}

function StatCard({ stat }: { stat: StatData }) {
  const [value, setValue] = useState(stat.value)
  const cardRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<number | null>(null)
  const startTimeRef = useRef<number | null>(null)
  const hasAnimated = useRef(false)
  const animationDuration = 2000 // 2 seconds

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !hasAnimated.current) {
          hasAnimated.current = true

          const animate = (timestamp: number) => {
            if (!startTimeRef.current) startTimeRef.current = timestamp
            const elapsed = timestamp - startTimeRef.current
            const progress = Math.min(elapsed / animationDuration, 1)

            // Easing function for smoother animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4)

            // Calculate current value based on progress
            let currentValue: number | string = stat.endValue * easeOutQuart

            // Format the value based on the type
            if (stat.endValue < 1) {
              // For small decimal values like 0.0042
              currentValue = currentValue.toFixed(4)
            } else if (Number.isInteger(stat.endValue)) {
              // For integers
              currentValue = Math.floor(currentValue).toString()
            } else {
              // For decimals
              currentValue = currentValue.toFixed(1)
            }

            // Add prefix and suffix
            const formattedValue = `${stat.prefix || ""}${currentValue}${stat.suffix || ""}`
            setValue(formattedValue)

            if (progress < 1) {
              animationRef.current = requestAnimationFrame(animate)
            }
          }

          animationRef.current = requestAnimationFrame(animate)
        }
      },
      { threshold: 0.1 },
    )

    if (cardRef.current) {
      observer.observe(cardRef.current)
    }

    return () => {
      if (cardRef.current) {
        observer.unobserve(cardRef.current)
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [stat])

  return (
    <Card ref={cardRef} className="glass-card border-white/5 liquid-glow">
      <CardContent className="flex flex-col items-center justify-center p-4">
        <p className="text-center text-sm font-medium text-white/60">{stat.label}</p>
        <p className="text-center text-2xl font-bold pawpumps-text">{value}</p>
      </CardContent>
    </Card>
  )
}
