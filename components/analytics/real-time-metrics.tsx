"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Download, RefreshCw, TrendingUp, Users, Vote, Activity } from "lucide-react"
import { Line, LineChart, Bar, Bar<PERSON>hart, XAxis, YAxis, CartesianGrid, Legend, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

// Mock data for real-time metrics
const generateMockData = (timeframe: string) => {
  const now = new Date()
  const data = []

  let interval = 1
  let dataPoints = 24
  let format = (date: Date) => `${date.getHours()}:00`

  if (timeframe === "day") {
    interval = 1
    dataPoints = 24
    format = (date: Date) => `${date.getHours()}:00`
  } else if (timeframe === "week") {
    interval = 24
    dataPoints = 7
    format = (date: Date) => ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"][date.getDay()]
  } else if (timeframe === "month") {
    interval = 24
    dataPoints = 30
    format = (date: Date) => `${date.getDate()}`
  }

  for (let i = dataPoints - 1; i >= 0; i--) {
    const time = new Date(now.getTime() - i * interval * 60 * 60 * 1000)
    data.push({
      time: format(time),
      activeUsers: Math.floor(Math.random() * 500) + 100,
      proposalVotes: Math.floor(Math.random() * 200) + 50,
      treasuryValue: Math.floor(Math.random() * 50000) + 2000000,
      stakingParticipation: Math.floor(Math.random() * 15) + 40,
    })
  }

  return data
}

// Mock historical comparison data
const generateHistoricalData = (timeframe: string) => {
  const currentData = generateMockData(timeframe)
  const previousData = currentData.map((item) => ({
    ...item,
    activeUsers: Math.floor(item.activeUsers * (0.7 + Math.random() * 0.4)),
    proposalVotes: Math.floor(item.proposalVotes * (0.7 + Math.random() * 0.4)),
    treasuryValue: Math.floor(item.treasuryValue * (0.85 + Math.random() * 0.2)),
    stakingParticipation: Math.floor(item.stakingParticipation * (0.9 + Math.random() * 0.2)),
  }))

  return { currentData, previousData }
}

export default function RealTimeMetrics() {
  const [timeframe, setTimeframe] = useState("day")
  const [refreshInterval, setRefreshInterval] = useState<number | null>(30)
  const [data, setData] = useState(generateMockData(timeframe))
  const [showComparison, setShowComparison] = useState(false)
  const [comparisonData, setComparisonData] = useState(generateHistoricalData(timeframe))

  // Simulate real-time updates
  useEffect(() => {
    if (!refreshInterval) return

    const interval = setInterval(() => {
      setData(generateMockData(timeframe))
      if (showComparison) {
        setComparisonData(generateHistoricalData(timeframe))
      }
    }, refreshInterval * 1000)

    return () => clearInterval(interval)
  }, [refreshInterval, timeframe, showComparison])

  // Update data when timeframe changes
  useEffect(() => {
    setData(generateMockData(timeframe))
    if (showComparison) {
      setComparisonData(generateHistoricalData(timeframe))
    }
  }, [timeframe, showComparison])

  const handleRefresh = () => {
    setData(generateMockData(timeframe))
    if (showComparison) {
      setComparisonData(generateHistoricalData(timeframe))
    }
  }

  const handleExport = () => {
    // In a real app, this would generate a CSV or PDF
    alert("Exporting data...")
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Real-Time Analytics</h2>
          <p className="text-white/70">Live platform metrics and governance participation</p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="glass-input text-white w-[120px]">
              <SelectValue placeholder="Timeframe" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Last 24h</SelectItem>
              <SelectItem value="week">Last Week</SelectItem>
              <SelectItem value="month">Last Month</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={refreshInterval?.toString() || "off"}
            onValueChange={(val) => setRefreshInterval(val === "off" ? null : Number.parseInt(val))}
          >
            <SelectTrigger className="glass-input text-white w-[140px]">
              <RefreshCw className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Refresh Rate" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10 seconds</SelectItem>
              <SelectItem value="30">30 seconds</SelectItem>
              <SelectItem value="60">1 minute</SelectItem>
              <SelectItem value="300">5 minutes</SelectItem>
              <SelectItem value="off">Manual only</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" className="glass-button" onClick={handleRefresh}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>

          <Button variant="outline" className="glass-button" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-4">
        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Active Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">{data[data.length - 1].activeUsers}</div>
                <p className="text-sm text-green-500">+12% from previous period</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <Users className="h-6 w-6 text-doge" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Proposal Votes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">{data[data.length - 1].proposalVotes}</div>
                <p className="text-sm text-green-500">+8% from previous period</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <Vote className="h-6 w-6 text-dogechain" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Treasury Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">
                  ${(data[data.length - 1].treasuryValue / 1000000).toFixed(2)}M
                </div>
                <p className="text-sm text-green-500">+5.2% from previous period</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <TrendingUp className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Staking Participation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-white">{data[data.length - 1].stakingParticipation}%</div>
                <p className="text-sm text-green-500">+3.5% from previous period</p>
              </div>
              <div className="rounded-full bg-white/5 p-2">
                <Activity className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="glass-card border-white/5">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-white">Active Users</CardTitle>
              <CardDescription className="text-white/70">Real-time platform usage</CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="glass-button"
              onClick={() => setShowComparison(!showComparison)}
            >
              {showComparison ? "Hide Comparison" : "Show Comparison"}
            </Button>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                activeUsers: {
                  label: "Current Period",
                  color: "hsl(var(--chart-1))",
                },
                ...(showComparison
                  ? {
                      previousPeriod: {
                        label: "Previous Period",
                        color: "hsl(var(--chart-2))",
                      },
                    }
                  : {}),
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={
                    showComparison
                      ? comparisonData.currentData.map((item, index) => ({
                          ...item,
                          previousPeriod: comparisonData.previousData[index].activeUsers,
                        }))
                      : data
                  }
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="time" stroke="rgba(255,255,255,0.5)" />
                  <YAxis stroke="rgba(255,255,255,0.5)" />
                  <ChartTooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="activeUsers"
                    stroke="var(--color-activeUsers)"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  {showComparison && (
                    <Line
                      type="monotone"
                      dataKey="previousPeriod"
                      stroke="var(--color-previousPeriod)"
                      strokeWidth={2}
                      strokeDasharray="5 5"
                      dot={{ r: 4 }}
                    />
                  )}
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-white">Proposal Votes</CardTitle>
              <CardDescription className="text-white/70">Governance participation</CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="glass-button"
              onClick={() => setShowComparison(!showComparison)}
            >
              {showComparison ? "Hide Comparison" : "Show Comparison"}
            </Button>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                proposalVotes: {
                  label: "Current Period",
                  color: "hsl(var(--chart-1))",
                },
                ...(showComparison
                  ? {
                      previousPeriod: {
                        label: "Previous Period",
                        color: "hsl(var(--chart-2))",
                      },
                    }
                  : {}),
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={
                    showComparison
                      ? comparisonData.currentData.map((item, index) => ({
                          ...item,
                          previousPeriod: comparisonData.previousData[index].proposalVotes,
                        }))
                      : data
                  }
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="time" stroke="rgba(255,255,255,0.5)" />
                  <YAxis stroke="rgba(255,255,255,0.5)" />
                  <ChartTooltip />
                  <Legend />
                  <Bar dataKey="proposalVotes" fill="var(--color-proposalVotes)" radius={[4, 4, 0, 0]} />
                  {showComparison && (
                    <Bar dataKey="previousPeriod" fill="var(--color-previousPeriod)" radius={[4, 4, 0, 0]} />
                  )}
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Governance Metrics</CardTitle>
          <CardDescription className="text-white/70">Comprehensive platform analytics</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="participation" className="w-full">
            <TabsList className="grid w-full grid-cols-4 glass mb-6">
              <TabsTrigger
                value="participation"
                className="data-[state=active]:text-doge data-[state=active]:bg-doge/10"
              >
                Participation
              </TabsTrigger>
              <TabsTrigger
                value="treasury"
                className="data-[state=active]:text-green-500 data-[state=active]:bg-green-500/10"
              >
                Treasury
              </TabsTrigger>
              <TabsTrigger
                value="staking"
                className="data-[state=active]:text-dogechain data-[state=active]:bg-dogechain/10"
              >
                Staking
              </TabsTrigger>
              <TabsTrigger
                value="proposals"
                className="data-[state=active]:text-purple-500 data-[state=active]:bg-purple-500/10"
              >
                Proposals
              </TabsTrigger>
            </TabsList>

            <TabsContent value="participation" className="space-y-4">
              <ChartContainer
                config={{
                  stakingParticipation: {
                    label: "Staking Participation (%)",
                    color: "hsl(var(--chart-1))",
                  },
                  activeUsers: {
                    label: "Active Users (scaled)",
                    color: "hsl(var(--chart-2))",
                  },
                  proposalVotes: {
                    label: "Proposal Votes (scaled)",
                    color: "hsl(var(--chart-3))",
                  },
                }}
                className="h-[400px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={data.map((item) => ({
                      ...item,
                      activeUsers: item.activeUsers / 10, // Scale down for better visualization
                      proposalVotes: item.proposalVotes / 5, // Scale down for better visualization
                    }))}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis dataKey="time" stroke="rgba(255,255,255,0.5)" />
                    <YAxis stroke="rgba(255,255,255,0.5)" />
                    <ChartTooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="stakingParticipation"
                      stroke="var(--color-stakingParticipation)"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                      activeDot={{ r: 6 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="activeUsers"
                      stroke="var(--color-activeUsers)"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                      activeDot={{ r: 6 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="proposalVotes"
                      stroke="var(--color-proposalVotes)"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                      activeDot={{ r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Participation Insights</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>Peak activity occurs between 18:00-22:00 UTC</li>
                    <li>Weekend participation is 15% higher than weekdays</li>
                    <li>New proposal announcements drive 3x normal engagement</li>
                    <li>Mobile users account for 45% of all participation</li>
                  </ul>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Growth Metrics</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>Monthly active users: +18% MoM</li>
                    <li>New governance participants: +22% MoM</li>
                    <li>Average voting power per user: +5% MoM</li>
                    <li>Proposal creation rate: +12% MoM</li>
                  </ul>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Recommendations</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>Increase mobile optimization for governance UI</li>
                    <li>Schedule important proposals during peak hours</li>
                    <li>Implement push notifications for proposal updates</li>
                    <li>Create educational content for new governance participants</li>
                  </ul>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="treasury" className="space-y-4">
              <ChartContainer
                config={{
                  treasuryValue: {
                    label: "Treasury Value ($)",
                    color: "hsl(var(--chart-1))",
                  },
                }}
                className="h-[400px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={data}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis dataKey="time" stroke="rgba(255,255,255,0.5)" />
                    <YAxis stroke="rgba(255,255,255,0.5)" />
                    <ChartTooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="treasuryValue"
                      stroke="var(--color-treasuryValue)"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                      activeDot={{ r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Treasury Composition</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>$PAW Token: 45%</li>
                    <li>Stablecoins: 30%</li>
                    <li>ETH: 15%</li>
                    <li>DOGE: 5%</li>
                    <li>Other Assets: 5%</li>
                  </ul>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Revenue Sources</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>Trading fees: 65%</li>
                    <li>Staking rewards: 20%</li>
                    <li>Token launches: 10%</li>
                    <li>Partnerships: 5%</li>
                  </ul>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Expenditures</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>Development: 40%</li>
                    <li>Marketing: 25%</li>
                    <li>Liquidity incentives: 20%</li>
                    <li>Operations: 10%</li>
                    <li>Community rewards: 5%</li>
                  </ul>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="staking" className="space-y-4">
              {/* Staking metrics content */}
              <ChartContainer
                config={{
                  stakingParticipation: {
                    label: "Staking Participation (%)",
                    color: "hsl(var(--chart-1))",
                  },
                }}
                className="h-[400px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={data}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis dataKey="time" stroke="rgba(255,255,255,0.5)" />
                    <YAxis stroke="rgba(255,255,255,0.5)" />
                    <ChartTooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="stakingParticipation"
                      stroke="var(--color-stakingParticipation)"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                      activeDot={{ r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Staking Statistics</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>Total staked $PAW: 45M (45% of supply)</li>
                    <li>Average stake duration: 3.5 months</li>
                    <li>Average APY: 12.5%</li>
                    <li>Number of stakers: 3,250</li>
                  </ul>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Staker Distribution</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>Whales ({">"}100k $PAW): 5% of stakers, 40% of stake</li>
                    <li>Large (10k-100k $PAW): 15% of stakers, 30% of stake</li>
                    <li>Medium (1k-10k $PAW): 35% of stakers, 20% of stake</li>
                    <li>Small ({"<"}1k $PAW): 45% of stakers, 10% of stake</li>
                  </ul>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Staking Trends</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>New stakers: +8% MoM</li>
                    <li>Average stake size: +5% MoM</li>
                    <li>Unstaking rate: -2% MoM</li>
                    <li>Governance participation among stakers: 65%</li>
                  </ul>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="proposals" className="space-y-4">
              {/* Proposals metrics content */}
              <ChartContainer
                config={{
                  proposalVotes: {
                    label: "Proposal Votes",
                    color: "hsl(var(--chart-1))",
                  },
                }}
                className="h-[400px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={data}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis dataKey="time" stroke="rgba(255,255,255,0.5)" />
                    <YAxis stroke="rgba(255,255,255,0.5)" />
                    <ChartTooltip />
                    <Legend />
                    <Bar dataKey="proposalVotes" fill="var(--color-proposalVotes)" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Proposal Statistics</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>Total proposals: 42</li>
                    <li>Active proposals: 6</li>
                    <li>Pass rate: 72%</li>
                    <li>Average voting period: 7 days</li>
                  </ul>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Proposal Categories</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>Core functionality: 35%</li>
                    <li>User experience: 25%</li>
                    <li>Technical: 20%</li>
                    <li>Treasury: 15%</li>
                    <li>Other: 5%</li>
                  </ul>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-white font-medium mb-2">Voting Patterns</h3>
                  <ul className="space-y-2 text-sm text-white/70">
                    <li>Average quorum reached: 65%</li>
                    <li>Average approval rate: 78%</li>
                    <li>Whale influence on outcomes: 35%</li>
                    <li>Proposals with discussion: 85%</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
