"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { ArrowUp, ArrowDown } from "lucide-react"
import { useSorting, createSortableHeader, createSortableHeaderRight } from "@/hooks/use-sorting"

export interface TokenVolumeData {
  id: string
  name: string
  symbol: string
  price: number
  change24h: number
  volume24h: number
  marketCap?: number
}

// Enhanced mock data for top tokens by volume
const generateTopTokensData = (timeframe: string): TokenVolumeData[] => {
  const baseTokens = [
    { id: "paw", name: "PawPumps", symbol: "PAW", price: 0.004200, change24h: 12.50, volume24h: 1250000, marketCap: 4200000 },
    { id: "dc", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", symbol: "DC", price: 0.010500, change24h: 3.20, volume24h: 3450000, marketCap: 10500000 },
    { id: "rdog<PERSON>", name: "<PERSON><PERSON><PERSON><PERSON>", symbol: "<PERSON><PERSON><PERSON><PERSON>", price: 0.000780, change24h: 28.40, volume24h: 2100000, marketCap: 780000 },
    { id: "dgk", name: "<PERSON><PERSON><PERSON><PERSON>", symbol: "DG<PERSON>", price: 0.000230, change24h: -5.70, volume24h: 890000, marketCap: 230000 },
    { id: "moon", name: "MoonShot", symbol: "MOON", price: 0.000560, change24h: -2.10, volume24h: 560000, marketCap: 560000 },
    { id: "shibdoge", name: "ShibaDoge", symbol: "SHIBDOGE", price: 0.000120, change24h: 15.30, volume24h: 420000, marketCap: 120000 },
    { id: "dmoon", name: "DogeMoon", symbol: "DMOON", price: 0.000340, change24h: 7.80, volume24h: 340000, marketCap: 340000 },
    { id: "pawt", name: "PawToken", symbol: "PAWT", price: 0.000090, change24h: -8.20, volume24h: 290000, marketCap: 90000 },
    { id: "shib", name: "ShibaInu", symbol: "SHIB", price: 0.000015, change24h: 4.60, volume24h: 1800000, marketCap: 8900000 },
    { id: "floki", name: "Floki", symbol: "FLOKI", price: 0.000032, change24h: -1.80, volume24h: 750000, marketCap: 320000 }
  ]

  // Apply timeframe-based variations
  return baseTokens.map(token => {
    let volumeMultiplier = 1
    let priceMultiplier = 1
    let changeMultiplier = 1

    switch (timeframe) {
      case "24h":
        volumeMultiplier = 1
        break
      case "7d":
        volumeMultiplier = 0.85
        changeMultiplier = 0.7
        break
      case "30d":
        volumeMultiplier = 0.6
        changeMultiplier = 0.5
        break
      case "90d":
        volumeMultiplier = 0.4
        changeMultiplier = 0.3
        break
    }

    return {
      ...token,
      volume24h: Math.round(token.volume24h * volumeMultiplier),
      change24h: Number((token.change24h * changeMultiplier).toFixed(2)),
      price: Number((token.price * priceMultiplier).toFixed(6))
    }
  })
}

interface TopTokensByVolumeProps {
  timeframe: string
  showChart?: boolean
}

export function TopTokensByVolume({ timeframe, showChart = false }: TopTokensByVolumeProps) {
  const tokensData = generateTopTokensData(timeframe)

  const {
    sortedData,
    handleSort,
    getSortIcon
  } = useSorting({
    initialField: 'volume24h',
    initialDirection: 'desc',
    data: tokensData
  })

  const formatPrice = (price: number) => {
    return `$${price.toFixed(6)}`
  }

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `$${(volume / 1000000).toFixed(2)}M`
    }
    return `$${(volume / 1000).toFixed(1)}K`
  }

  const formatChange = (change: number) => {
    const isPositive = change >= 0
    return (
      <div className={`flex items-center justify-end gap-1 ${isPositive ? "text-green-500" : "text-red-500"}`}>
        {isPositive ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />}
        {Math.abs(change).toFixed(2)}%
      </div>
    )
  }

  return (
    <Card className="glass-card border-white/5 liquid-glow">
      <CardHeader>
        <CardTitle className="text-white">Top Tokens by Volume</CardTitle>
        <CardDescription className="text-white/70">
          24-hour trading volume leaderboard for top tokens
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-white/10">
                {createSortableHeader<TokenVolumeData>("Token", "name", getSortIcon, handleSort, "text-left", true)}
                {createSortableHeaderRight<TokenVolumeData>("Price", "price", getSortIcon, handleSort)}
                {createSortableHeaderRight<TokenVolumeData>("24h", "change24h", getSortIcon, handleSort)}
                {createSortableHeaderRight<TokenVolumeData>("Volume", "volume24h", getSortIcon, handleSort)}
              </tr>
            </thead>
            <tbody>
              {sortedData.map((token, index) => (
                <tr
                  key={token.id}
                  className="border-b border-white/5 hover:bg-white/5 transition-colors duration-200"
                >
                  <td className="py-3">
                    <div className="flex items-center gap-2">
                      <div
                        className={`flex h-8 w-8 items-center justify-center rounded-full ${
                          index % 2 === 0 ? "bg-doge/10 text-doge" : "bg-dogechain/10 text-dogechain"
                        } text-xs font-bold`}
                      >
                        {token.symbol.slice(0, 2)}
                      </div>
                      <div>
                        <div className="font-medium text-white">{token.name}</div>
                        <div className="text-xs text-white/60">{token.symbol}</div>
                      </div>
                    </div>
                  </td>
                  <td className="py-3 text-right font-medium text-white">
                    {formatPrice(token.price)}
                  </td>
                  <td className="py-3 text-right">
                    {formatChange(token.change24h)}
                  </td>
                  <td className="py-3 text-right text-white/60">
                    {formatVolume(token.volume24h)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )
}
