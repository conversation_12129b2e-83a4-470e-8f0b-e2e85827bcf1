"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Download, Calendar, ArrowUpDown } from "lucide-react"
import { Line, LineChart, Bar, BarChart, XAxis, YAxis, CartesianGrid, Legend, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Mock data for historical comparison
const generateHistoricalData = (period: string) => {
  const data = []
  const periods = period === "weekly" ? 12 : period === "monthly" ? 12 : 4
  const labels =
    period === "weekly"
      ? [
          "Week 1",
          "Week 2",
          "Week 3",
          "Week 4",
          "Week 5",
          "Week 6",
          "Week 7",
          "Week 8",
          "Week 9",
          "Week 10",
          "Week 11",
          "Week 12",
        ]
      : period === "monthly"
        ? ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
        : ["Q1", "Q2", "Q3", "Q4"]

  for (let i = 0; i < periods; i++) {
    data.push({
      period: labels[i],
      currentPeriod: {
        activeUsers: Math.floor(Math.random() * 500) + 300,
        proposalVotes: Math.floor(Math.random() * 200) + 100,
        treasuryValue: Math.floor(Math.random() * 500000) + 2000000,
        stakingParticipation: Math.floor(Math.random() * 15) + 40,
      },
      previousPeriod: {
        activeUsers: Math.floor(Math.random() * 400) + 200,
        proposalVotes: Math.floor(Math.random() * 150) + 80,
        treasuryValue: Math.floor(Math.random() * 400000) + 1800000,
        stakingParticipation: Math.floor(Math.random() * 12) + 35,
      },
    })
  }

  return data
}

export function HistoricalComparison() {
  const [period, setPeriod] = useState("monthly")
  const [data, setData] = useState(generateHistoricalData(period))
  const [metric, setMetric] = useState("activeUsers")

  const handlePeriodChange = (value: string) => {
    setPeriod(value)
    setData(generateHistoricalData(value))
  }

  const metricLabels = {
    activeUsers: "Active Users",
    proposalVotes: "Proposal Votes",
    treasuryValue: "Treasury Value ($)",
    stakingParticipation: "Staking Participation (%)",
  }

  const formatValue = (value: number, metricType: string) => {
    if (metricType === "treasuryValue") {
      return `$${(value / 1000000).toFixed(2)}M`
    }
    if (metricType === "stakingParticipation") {
      return `${value}%`
    }
    return value.toString()
  }

  const calculateGrowth = (current: number, previous: number) => {
    const growth = ((current - previous) / previous) * 100
    return growth.toFixed(1)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Historical Comparison</h2>
          <p className="text-white/70">Compare metrics across different time periods</p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Select value={period} onValueChange={handlePeriodChange}>
            <SelectTrigger className="glass-input text-white w-[120px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
            </SelectContent>
          </Select>

          <Select value={metric} onValueChange={setMetric}>
            <SelectTrigger className="glass-input text-white w-[150px]">
              <ArrowUpDown className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Metric" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="activeUsers">Active Users</SelectItem>
              <SelectItem value="proposalVotes">Proposal Votes</SelectItem>
              <SelectItem value="treasuryValue">Treasury Value</SelectItem>
              <SelectItem value="stakingParticipation">Staking %</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" className="glass-button">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">
            {metricLabels[metric as keyof typeof metricLabels]} - Historical Comparison
          </CardTitle>
          <CardDescription className="text-white/70">Current period vs previous period</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              current: {
                label: "Current Period",
                color: "hsl(var(--chart-1))",
              },
              previous: {
                label: "Previous Period",
                color: "hsl(var(--chart-2))",
              },
            }}
            className="h-[400px]"
          >
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={data.map((item) => ({
                  period: item.period,
                  current: item.currentPeriod[metric as keyof typeof item.currentPeriod],
                  previous: item.previousPeriod[metric as keyof typeof item.previousPeriod],
                }))}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis dataKey="period" stroke="rgba(255,255,255,0.5)" />
                <YAxis stroke="rgba(255,255,255,0.5)" />
                <ChartTooltip />
                <Legend />
                <Bar dataKey="current" fill="var(--color-current)" radius={[4, 4, 0, 0]} />
                <Bar dataKey="previous" fill="var(--color-previous)" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Period-over-Period Growth</CardTitle>
            <CardDescription className="text-white/70">Percentage change in key metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                growth: {
                  label: "Growth (%)",
                  color: "hsl(var(--chart-1))",
                },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={data.map((item) => ({
                    period: item.period,
                    growth: Number.parseFloat(
                      calculateGrowth(
                        item.currentPeriod[metric as keyof typeof item.currentPeriod] as number,
                        item.previousPeriod[metric as keyof typeof item.previousPeriod] as number,
                      ),
                    ),
                  }))}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="period" stroke="rgba(255,255,255,0.5)" />
                  <YAxis stroke="rgba(255,255,255,0.5)" />
                  <ChartTooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="growth"
                    stroke="var(--color-growth)"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Metric Breakdown</CardTitle>
            <CardDescription className="text-white/70">Detailed analysis by period</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-left py-3 text-white/70">Period</th>
                    <th className="text-right py-3 text-white/70">Current</th>
                    <th className="text-right py-3 text-white/70">Previous</th>
                    <th className="text-right py-3 text-white/70">Growth</th>
                  </tr>
                </thead>
                <tbody>
                  {data.map((item, index) => (
                    <tr key={index} className="border-b border-white/5">
                      <td className="py-3 text-white">{item.period}</td>
                      <td className="py-3 text-right text-white">
                        {formatValue(item.currentPeriod[metric as keyof typeof item.currentPeriod] as number, metric)}
                      </td>
                      <td className="py-3 text-right text-white">
                        {formatValue(item.previousPeriod[metric as keyof typeof item.previousPeriod] as number, metric)}
                      </td>
                      <td
                        className={`py-3 text-right ${
                          Number.parseFloat(
                            calculateGrowth(
                              item.currentPeriod[metric as keyof typeof item.currentPeriod] as number,
                              item.previousPeriod[metric as keyof typeof item.previousPeriod] as number,
                            ),
                          ) >= 0
                            ? "text-green-500"
                            : "text-red-500"
                        }`}
                      >
                        {calculateGrowth(
                          item.currentPeriod[metric as keyof typeof item.currentPeriod] as number,
                          item.previousPeriod[metric as keyof typeof item.previousPeriod] as number,
                        )}
                        %
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="glass-card border-white/5">
        <CardHeader>
          <CardTitle className="text-white">Comprehensive Metrics Comparison</CardTitle>
          <CardDescription className="text-white/70">All metrics across time periods</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="activeUsers" className="w-full">
            <TabsList className="grid w-full grid-cols-4 glass mb-6">
              <TabsTrigger value="activeUsers" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                Active Users
              </TabsTrigger>
              <TabsTrigger
                value="proposalVotes"
                className="data-[state=active]:text-dogechain data-[state=active]:bg-dogechain/10"
              >
                Proposal Votes
              </TabsTrigger>
              <TabsTrigger
                value="treasuryValue"
                className="data-[state=active]:text-green-500 data-[state=active]:bg-green-500/10"
              >
                Treasury Value
              </TabsTrigger>
              <TabsTrigger
                value="stakingParticipation"
                className="data-[state=active]:text-purple-500 data-[state=active]:bg-purple-500/10"
              >
                Staking %
              </TabsTrigger>
            </TabsList>

            {(["activeUsers", "proposalVotes", "treasuryValue", "stakingParticipation"] as const).map((metricKey) => (
              <TabsContent key={metricKey} value={metricKey} className="space-y-4">
                <ChartContainer
                  config={{
                    current: {
                      label: "Current Period",
                      color: "hsl(var(--chart-1))",
                    },
                    previous: {
                      label: "Previous Period",
                      color: "hsl(var(--chart-2))",
                    },
                  }}
                  className="h-[400px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={data.map((item) => ({
                        period: item.period,
                        current: item.currentPeriod[metricKey],
                        previous: item.previousPeriod[metricKey],
                      }))}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis dataKey="period" stroke="rgba(255,255,255,0.5)" />
                      <YAxis stroke="rgba(255,255,255,0.5)" />
                      <ChartTooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="current"
                        stroke="var(--color-current)"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="previous"
                        stroke="var(--color-previous)"
                        strokeWidth={2}
                        strokeDasharray="5 5"
                        dot={{ r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <h3 className="text-white font-medium mb-2">Key Insights</h3>
                    <ul className="space-y-2 text-sm text-white/70">
                      <li>
                        Average growth:{" "}
                        {(
                          data.reduce(
                            (acc, item) =>
                              acc +
                              Number.parseFloat(
                                calculateGrowth(item.currentPeriod[metricKey], item.previousPeriod[metricKey]),
                              ),
                            0,
                          ) / data.length
                        ).toFixed(1)}
                        %
                      </li>
                      <li>
                        Peak period:{" "}
                        {
                          data.reduce(
                            (max, item) =>
                              item.currentPeriod[metricKey] > max.value
                                ? { period: item.period, value: item.currentPeriod[metricKey] }
                                : max,
                            { period: "", value: 0 },
                          ).period
                        }
                      </li>
                      <li>
                        Lowest period:{" "}
                        {
                          data.reduce(
                            (min, item) =>
                              item.currentPeriod[metricKey] < min.value || min.value === 0
                                ? { period: item.period, value: item.currentPeriod[metricKey] }
                                : min,
                            { period: "", value: 0 },
                          ).period
                        }
                      </li>
                    </ul>
                  </div>

                  <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <h3 className="text-white font-medium mb-2">Trend Analysis</h3>
                    <ul className="space-y-2 text-sm text-white/70">
                      <li>
                        Overall trend:{" "}
                        {data[data.length - 1].currentPeriod[metricKey] > data[0].currentPeriod[metricKey]
                          ? "Upward"
                          : "Downward"}
                      </li>
                      <li>
                        Volatility:{" "}
                        {Math.abs(
                          Number.parseFloat(
                            calculateGrowth(
                              data.reduce((max, item) => Math.max(max, item.currentPeriod[metricKey]), 0),
                              data.reduce(
                                (min, item) => Math.min(min, item.currentPeriod[metricKey]),
                                Number.POSITIVE_INFINITY,
                              ),
                            ),
                          ),
                        ) > 30
                          ? "High"
                          : "Low"}
                      </li>
                      <li>
                        Consistency:{" "}
                        {data.filter(
                          (item, i) =>
                            i > 0 &&
                            item.currentPeriod[metricKey] > data[i - 1].currentPeriod[metricKey] ===
                              data[i - 1].currentPeriod[metricKey] > (i > 1 ? data[i - 2].currentPeriod[metricKey] : 0),
                        ).length >
                        data.length / 2
                          ? "High"
                          : "Low"}
                      </li>
                    </ul>
                  </div>

                  <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <h3 className="text-white font-medium mb-2">Recommendations</h3>
                    <ul className="space-y-2 text-sm text-white/70">
                      <li>
                        {data[data.length - 1].currentPeriod[metricKey] <
                        data[data.length - 2]?.currentPeriod[metricKey]
                          ? "Investigate recent decline"
                          : "Maintain growth strategy"}
                      </li>
                      <li>
                        {data.filter(
                          (item) =>
                            Number.parseFloat(
                              calculateGrowth(item.currentPeriod[metricKey], item.previousPeriod[metricKey]),
                            ) < 0,
                        ).length >
                        data.length / 3
                          ? "Address negative growth periods"
                          : "Focus on peak performance periods"}
                      </li>
                      <li>
                        Set target for next period:{" "}
                        {formatValue(Math.round(data[data.length - 1].currentPeriod[metricKey] * 1.1), metricKey)}
                      </li>
                    </ul>
                  </div>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
