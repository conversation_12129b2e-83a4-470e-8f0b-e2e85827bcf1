"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Download, FileText, Calendar, Filter, Clock, Share2 } from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Mock report templates
const reportTemplates = [
  {
    id: "governance-summary",
    name: "Governance Summary",
    description: "Overview of governance activity and participation",
    metrics: ["activeUsers", "proposalVotes", "stakingParticipation"],
    formats: ["pdf", "csv", "json"],
    frequency: "weekly",
  },
  {
    id: "treasury-report",
    name: "Treasury Report",
    description: "Detailed breakdown of treasury assets and transactions",
    metrics: ["treasuryValue", "treasuryTransactions", "assetAllocation"],
    formats: ["pdf", "csv", "json"],
    frequency: "monthly",
  },
  {
    id: "staking-analytics",
    name: "Staking Analytics",
    description: "Analysis of staking behavior and rewards distribution",
    metrics: ["stakingParticipation", "stakingRewards", "stakingDuration"],
    formats: ["pdf", "csv"],
    frequency: "monthly",
  },
  {
    id: "proposal-metrics",
    name: "Proposal Metrics",
    description: "Detailed metrics on proposal creation, voting, and execution",
    metrics: ["proposalVotes", "proposalCreation", "proposalExecution"],
    formats: ["pdf", "csv", "json"],
    frequency: "weekly",
  },
  {
    id: "user-engagement",
    name: "User Engagement",
    description: "Analysis of user activity and engagement patterns",
    metrics: ["activeUsers", "sessionDuration", "featureUsage"],
    formats: ["pdf", "csv"],
    frequency: "weekly",
  },
  {
    id: "performance-report",
    name: "Platform Performance",
    description: "Technical performance metrics and system health",
    metrics: ["responseTime", "errorRate", "uptime"],
    formats: ["pdf", "json"],
    frequency: "daily",
  },
]

// Mock scheduled reports
const scheduledReports = [
  {
    id: "weekly-governance",
    name: "Weekly Governance Summary",
    template: "governance-summary",
    schedule: "Every Monday at 9:00 AM",
    format: "pdf",
    recipients: ["<EMAIL>", "<EMAIL>"],
    lastRun: "2023-05-01T09:00:00Z",
  },
  {
    id: "monthly-treasury",
    name: "Monthly Treasury Report",
    template: "treasury-report",
    schedule: "1st of every month at 12:00 PM",
    format: "csv",
    recipients: ["<EMAIL>", "<EMAIL>"],
    lastRun: "2023-05-01T12:00:00Z",
  },
  {
    id: "weekly-proposals",
    name: "Weekly Proposal Analytics",
    template: "proposal-metrics",
    schedule: "Every Friday at 5:00 PM",
    format: "pdf",
    recipients: ["<EMAIL>"],
    lastRun: "2023-04-28T17:00:00Z",
  },
]

// Mock recent reports
const recentReports = [
  {
    id: "gov-summary-2023-05-01",
    name: "Governance Summary - May 1, 2023",
    template: "governance-summary",
    generated: "2023-05-01T09:00:00Z",
    format: "pdf",
    size: "2.4 MB",
  },
  {
    id: "treasury-2023-05-01",
    name: "Treasury Report - May 1, 2023",
    template: "treasury-report",
    generated: "2023-05-01T12:00:00Z",
    format: "csv",
    size: "1.8 MB",
  },
  {
    id: "proposals-2023-04-28",
    name: "Proposal Metrics - April 28, 2023",
    template: "proposal-metrics",
    generated: "2023-04-28T17:00:00Z",
    format: "pdf",
    size: "3.2 MB",
  },
  {
    id: "staking-2023-04-15",
    name: "Staking Analytics - April 15, 2023",
    template: "staking-analytics",
    generated: "2023-04-15T10:30:00Z",
    format: "pdf",
    size: "2.7 MB",
  },
  {
    id: "users-2023-04-24",
    name: "User Engagement - April 24, 2023",
    template: "user-engagement",
    generated: "2023-04-24T14:15:00Z",
    format: "csv",
    size: "4.1 MB",
  },
]

export function ExportableReports() {
  const [selectedTemplate, setSelectedTemplate] = useState("")
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([])
  const [selectedFormat, setSelectedFormat] = useState("pdf")
  const [dateRange, setDateRange] = useState("last-30-days")

  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId)
    const template = reportTemplates.find((t) => t.id === templateId)
    if (template) {
      setSelectedMetrics(template.metrics)
      setSelectedFormat(template.formats[0])
    } else {
      setSelectedMetrics([])
      setSelectedFormat("pdf")
    }
  }

  const handleGenerateReport = () => {
    // In a real app, this would generate the report
    alert(`Generating ${selectedFormat.toUpperCase()} report for template: ${selectedTemplate}`)
  }

  const handleDownloadReport = (reportId: string) => {
    // In a real app, this would download the report
    alert(`Downloading report: ${reportId}`)
  }

  const handleScheduleReport = () => {
    // In a real app, this would open a scheduling dialog
    alert(`Scheduling report for template: ${selectedTemplate}`)
  }

  const handleShareReport = (reportId: string) => {
    // In a real app, this would open a sharing dialog
    alert(`Sharing report: ${reportId}`)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Exportable Reports</h2>
          <p className="text-white/70">Generate, schedule, and manage analytics reports</p>
        </div>
      </div>

      <Tabs defaultValue="generate" className="w-full">
        <TabsList className="grid w-full grid-cols-3 glass mb-6">
          <TabsTrigger value="generate" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
            Generate Report
          </TabsTrigger>
          <TabsTrigger
            value="scheduled"
            className="data-[state=active]:text-dogechain data-[state=active]:bg-dogechain/10"
          >
            Scheduled Reports
          </TabsTrigger>
          <TabsTrigger
            value="recent"
            className="data-[state=active]:text-green-500 data-[state=active]:bg-green-500/10"
          >
            Recent Reports
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-4">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle className="text-white">Generate Custom Report</CardTitle>
              <CardDescription className="text-white/70">Select a template and customize your report</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="template" className="text-white">
                      Report Template
                    </Label>
                    <Select value={selectedTemplate} onValueChange={handleTemplateChange}>
                      <SelectTrigger id="template" className="glass-input text-white mt-1">
                        <FileText className="mr-2 h-4 w-4" />
                        <SelectValue placeholder="Select a template" />
                      </SelectTrigger>
                      <SelectContent>
                        {reportTemplates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {selectedTemplate && (
                      <p className="text-sm text-white/70 mt-1">
                        {reportTemplates.find((t) => t.id === selectedTemplate)?.description}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="format" className="text-white">
                      Export Format
                    </Label>
                    <Select value={selectedFormat} onValueChange={setSelectedFormat}>
                      <SelectTrigger id="format" className="glass-input text-white mt-1">
                        <SelectValue placeholder="Select format" />
                      </SelectTrigger>
                      <SelectContent>
                        {(selectedTemplate &&
                          reportTemplates
                            .find((t) => t.id === selectedTemplate)
                            ?.formats.map((format) => (
                              <SelectItem key={format} value={format}>
                                {format.toUpperCase()}
                              </SelectItem>
                            ))) || (
                          <>
                            <SelectItem value="pdf">PDF</SelectItem>
                            <SelectItem value="csv">CSV</SelectItem>
                            <SelectItem value="json">JSON</SelectItem>
                          </>
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="date-range" className="text-white">
                      Date Range
                    </Label>
                    <Select value={dateRange} onValueChange={setDateRange}>
                      <SelectTrigger id="date-range" className="glass-input text-white mt-1">
                        <Calendar className="mr-2 h-4 w-4" />
                        <SelectValue placeholder="Select date range" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="last-7-days">Last 7 days</SelectItem>
                        <SelectItem value="last-30-days">Last 30 days</SelectItem>
                        <SelectItem value="last-90-days">Last 90 days</SelectItem>
                        <SelectItem value="year-to-date">Year to date</SelectItem>
                        <SelectItem value="custom">Custom range</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <Label className="text-white">Included Metrics</Label>
                  <div className="space-y-2 max-h-[200px] overflow-y-auto pr-2">
                    {selectedTemplate ? (
                      reportTemplates
                        .find((t) => t.id === selectedTemplate)
                        ?.metrics.map((metric) => (
                          <div key={metric} className="flex items-center space-x-2">
                            <Checkbox
                              id={metric}
                              checked={selectedMetrics.includes(metric)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedMetrics((prev) => [...prev, metric])
                                } else {
                                  setSelectedMetrics((prev) => prev.filter((m) => m !== metric))
                                }
                              }}
                            />
                            <Label htmlFor={metric} className="text-white">
                              {metric.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())}
                            </Label>
                          </div>
                        ))
                    ) : (
                      <p className="text-white/70">Select a template to see available metrics</p>
                    )}
                  </div>

                  <div className="pt-4">
                    <Label className="text-white">Additional Options</Label>
                    <div className="space-y-2 mt-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="include-charts" defaultChecked />
                        <Label htmlFor="include-charts" className="text-white">
                          Include charts and visualizations
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="include-raw-data" />
                        <Label htmlFor="include-raw-data" className="text-white">
                          Include raw data tables
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="include-recommendations" defaultChecked />
                        <Label htmlFor="include-recommendations" className="text-white">
                          Include recommendations
                        </Label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" className="glass-button" onClick={handleScheduleReport}>
                <Clock className="mr-2 h-4 w-4" />
                Schedule
              </Button>
              <Button className="bg-doge hover:bg-doge/90" onClick={handleGenerateReport}>
                <Download className="mr-2 h-4 w-4" />
                Generate Report
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-4">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle className="text-white">Scheduled Reports</CardTitle>
              <CardDescription className="text-white/70">Manage your automated report generation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {scheduledReports.map((report) => (
                  <div key={report.id} className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <div className="flex flex-col md:flex-row justify-between">
                      <div>
                        <h3 className="text-white font-medium">{report.name}</h3>
                        <p className="text-sm text-white/70">
                          Template: {reportTemplates.find((t) => t.id === report.template)?.name}
                        </p>
                        <p className="text-sm text-white/70">Schedule: {report.schedule}</p>
                        <p className="text-sm text-white/70">
                          Format: {report.format.toUpperCase()} • Recipients: {report.recipients.length}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 mt-4 md:mt-0">
                        <Button variant="outline" size="sm" className="glass-button">
                          Edit
                        </Button>
                        <Button variant="outline" size="sm" className="glass-button">
                          Run Now
                        </Button>
                        <Button variant="outline" size="sm" className="glass-button text-red-500 hover:text-red-400">
                          Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}

                <Button className="w-full glass-button">+ Create New Scheduled Report</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <Card className="glass-card border-white/5">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-white">Recent Reports</CardTitle>
                <CardDescription className="text-white/70">
                  Access and download previously generated reports
                </CardDescription>
              </div>
              <Button variant="outline" size="sm" className="glass-button">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentReports.map((report) => (
                  <div key={report.id} className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <div className="flex flex-col md:flex-row justify-between">
                      <div>
                        <h3 className="text-white font-medium">{report.name}</h3>
                        <p className="text-sm text-white/70">
                          Generated: {new Date(report.generated).toLocaleString()}
                        </p>
                        <p className="text-sm text-white/70">
                          Format: {report.format.toUpperCase()} • Size: {report.size}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 mt-4 md:mt-0">
                        <Button
                          variant="outline"
                          size="sm"
                          className="glass-button"
                          onClick={() => handleShareReport(report.id)}
                        >
                          <Share2 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="glass-button"
                          onClick={() => handleDownloadReport(report.id)}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
