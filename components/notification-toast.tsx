"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { X } from "lucide-react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const notificationVariants = cva(
  "fixed z-50 flex items-center gap-3 rounded-lg p-4 shadow-lg transition-all duration-300 ease-in-out",
  {
    variants: {
      position: {
        "top-right": "top-4 right-4",
        "top-left": "top-4 left-4",
        "bottom-right": "bottom-4 right-4",
        "bottom-left": "bottom-4 left-4",
        "top-center": "top-4 left-1/2 -translate-x-1/2",
        "bottom-center": "bottom-4 left-1/2 -translate-x-1/2",
      },
      variant: {
        default: "glass-card border-white/5",
        success: "glass-card border-green-500/20 bg-green-500/10",
        error: "glass-card border-red-500/20 bg-red-500/10",
        warning: "glass-card border-yellow-500/20 bg-yellow-500/10",
        info: "glass-card border-doge/20 bg-doge/10",
      },
    },
    defaultVariants: {
      position: "top-right",
      variant: "default",
    },
  },
)

export interface NotificationToastProps extends VariantProps<typeof notificationVariants> {
  title: string
  message: string
  duration?: number
  onClose?: () => void
  icon?: React.ReactNode
}

export function NotificationToast({
  title,
  message,
  duration = 5000,
  position,
  variant,
  onClose,
  icon,
}: NotificationToastProps) {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
      setTimeout(() => {
        onClose?.()
      }, 300) // Wait for the fade-out animation to complete
    }, duration)

    return () => clearTimeout(timer)
  }, [duration, onClose])

  return (
    <div
      className={cn(
        notificationVariants({ position, variant }),
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2",
      )}
    >
      {icon}
      <div className="flex-1">
        <h4 className="font-medium text-white">{title}</h4>
        <p className="text-sm text-white/80">{message}</p>
      </div>
      <button
        className="text-white/60 hover:text-white"
        onClick={() => {
          setIsVisible(false)
          setTimeout(() => {
            onClose?.()
          }, 300)
        }}
      >
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </button>
    </div>
  )
}
