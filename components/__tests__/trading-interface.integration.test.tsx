import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { TradingInterface } from '../trading-interface'

// Mock the wallet context
const mockWalletContext = {
  isConnected: true,
  address: '******************************************',
  connect: jest.fn(),
  disconnect: jest.fn(),
}

// Mock the notification context
const mockNotificationContext = {
  showNotification: jest.fn(),
  notifications: [],
  unreadCount: 0,
  markAsRead: jest.fn(),
  clearAll: jest.fn(),
}

// Mock the toast hook
const mockToast = jest.fn()

jest.mock('@/hooks/use-wallet', () => ({
  useWallet: () => mockWalletContext,
}))

jest.mock('@/hooks/use-notifications', () => ({
  useNotifications: () => mockNotificationContext,
}))

jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: mockToast }),
}))

// Mock the error handler hook
const mockErrorHandler = {
  error: null,
  executeWithRetry: jest.fn(),
  clearError: jest.fn(),
}

jest.mock('@/hooks/use-error-handler', () => ({
  useTradingErrorHandler: () => mockErrorHandler,
}))

describe('TradingInterface Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockErrorHandler.error = null
    mockErrorHandler.executeWithRetry.mockImplementation(async (operation) => {
      return await operation()
    })
  })

  it('renders all trading interface components', () => {
    render(<TradingInterface />)

    // Check for main sections
    expect(screen.getByText(/chart/i)).toBeInTheDocument()
    expect(screen.getByText(/swap/i)).toBeInTheDocument()
    expect(screen.getByText(/liquidity/i)).toBeInTheDocument()

    // Check for form elements
    expect(screen.getByText(/from/i)).toBeInTheDocument()
    expect(screen.getByText(/to/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /swap tokens/i })).toBeInTheDocument()
  })

  it('handles successful token swap', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    // Fill in swap form
    const fromAmountInput = screen.getByPlaceholderText(/0\.0/i)
    await user.type(fromAmountInput, '100')

    // Click swap button
    const swapButton = screen.getByRole('button', { name: /swap tokens/i })
    await user.click(swapButton)

    // Wait for swap to complete
    await waitFor(() => {
      expect(mockNotificationContext.showNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Swap Successful!',
          type: 'success',
        })
      )
    })

    // Form should be reset
    expect(fromAmountInput).toHaveValue('')
  })

  it('handles swap validation errors', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    // Try to swap without amount
    const swapButton = screen.getByRole('button', { name: /swap tokens/i })
    await user.click(swapButton)

    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Invalid amount',
        variant: 'destructive',
      })
    )
  })

  it('handles wallet not connected error', async () => {
    const user = userEvent.setup()
    mockWalletContext.isConnected = false

    render(<TradingInterface />)

    const fromAmountInput = screen.getByPlaceholderText(/0\.0/i)
    await user.type(fromAmountInput, '100')

    const swapButton = screen.getByRole('button', { name: /swap tokens/i })
    await user.click(swapButton)

    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Wallet not connected',
        variant: 'destructive',
      })
    )
  })

  it('handles swap failures with retry', async () => {
    const user = userEvent.setup()
    mockErrorHandler.executeWithRetry.mockRejectedValueOnce(new Error('Transaction failed'))

    render(<TradingInterface />)

    const fromAmountInput = screen.getByPlaceholderText(/0\.0/i)
    await user.type(fromAmountInput, '100')

    const swapButton = screen.getByRole('button', { name: /swap tokens/i })
    await user.click(swapButton)

    await waitFor(() => {
      expect(mockErrorHandler.executeWithRetry).toHaveBeenCalled()
    })
  })

  it('shows loading state during swap', async () => {
    const user = userEvent.setup()
    let resolveSwap
    mockErrorHandler.executeWithRetry.mockImplementation(() => {
      return new Promise(resolve => {
        resolveSwap = resolve
      })
    })

    render(<TradingInterface />)

    const fromAmountInput = screen.getByPlaceholderText(/0\.0/i)
    await user.type(fromAmountInput, '100')

    const swapButton = screen.getByRole('button', { name: /swap tokens/i })
    await user.click(swapButton)

    // Should show loading state
    expect(swapButton).toBeDisabled()
    expect(screen.getByText(/swapping/i)).toBeInTheDocument()

    // Resolve the swap
    resolveSwap({ success: true })
  })

  it('allows token selection', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    // Click on token selector
    const tokenSelector = screen.getAllByText(/doge/i)[0]
    await user.click(tokenSelector)

    // Should show token selection options
    await waitFor(() => {
      expect(screen.getByText(/select token/i)).toBeInTheDocument()
    })
  })

  it('calculates swap amounts correctly', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    const fromAmountInput = screen.getByPlaceholderText(/0\.0/i)
    await user.type(fromAmountInput, '100')

    // Should calculate and display estimated output
    await waitFor(() => {
      const toAmountInput = screen.getAllByPlaceholderText(/0\.0/i)[1]
      expect(toAmountInput).toHaveValue('99.7') // Assuming 0.3% fee
    })
  })

  it('handles token swap direction reversal', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    const fromAmountInput = screen.getByPlaceholderText(/0\.0/i)
    await user.type(fromAmountInput, '100')

    // Click swap direction button
    const swapDirectionButton = screen.getByRole('button', { name: /swap direction/i })
    await user.click(swapDirectionButton)

    // Amounts should be swapped
    const toAmountInput = screen.getAllByPlaceholderText(/0\.0/i)[1]
    expect(toAmountInput).toHaveValue('100')
    expect(fromAmountInput).toHaveValue('')
  })

  it('shows price impact warnings', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    // Enter large amount that would cause high price impact
    const fromAmountInput = screen.getByPlaceholderText(/0\.0/i)
    await user.type(fromAmountInput, '1000000')

    await waitFor(() => {
      expect(screen.getByText(/high price impact/i)).toBeInTheDocument()
    })
  })

  it('displays transaction details', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    const fromAmountInput = screen.getByPlaceholderText(/0\.0/i)
    await user.type(fromAmountInput, '100')

    // Should show transaction details
    expect(screen.getByText(/estimated gas/i)).toBeInTheDocument()
    expect(screen.getByText(/minimum received/i)).toBeInTheDocument()
    expect(screen.getByText(/price impact/i)).toBeInTheDocument()
  })

  it('handles slippage tolerance settings', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    // Click settings button
    const settingsButton = screen.getByRole('button', { name: /settings/i })
    await user.click(settingsButton)

    // Should show slippage settings
    await waitFor(() => {
      expect(screen.getByText(/slippage tolerance/i)).toBeInTheDocument()
    })

    // Change slippage
    const slippageInput = screen.getByDisplayValue(/0\.5/i)
    await user.clear(slippageInput)
    await user.type(slippageInput, '1.0')

    // Settings should be applied
    expect(slippageInput).toHaveValue('1.0')
  })

  it('shows error recovery when swap fails', async () => {
    const user = userEvent.setup()
    const mockError = {
      type: 'BLOCKCHAIN',
      message: 'Transaction failed',
      retryable: true,
    }
    mockErrorHandler.error = mockError

    render(<TradingInterface />)

    // Should show error recovery component
    expect(screen.getByText(/transaction failed/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument()
  })

  it('integrates with chart timeframe selection', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    // Click timeframe selector
    const timeframeSelector = screen.getByText(/1d/i)
    await user.click(timeframeSelector)

    // Should show timeframe options
    await waitFor(() => {
      expect(screen.getByText(/1h/i)).toBeInTheDocument()
      expect(screen.getByText(/4h/i)).toBeInTheDocument()
      expect(screen.getByText(/1w/i)).toBeInTheDocument()
    })
  })

  it('handles liquidity tab switching', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    // Click liquidity tab
    const liquidityTab = screen.getByRole('tab', { name: /liquidity/i })
    await user.click(liquidityTab)

    // Should show liquidity interface
    await waitFor(() => {
      expect(screen.getByText(/add liquidity/i)).toBeInTheDocument()
    })
  })

  it('maintains state across tab switches', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    // Enter amount in swap tab
    const fromAmountInput = screen.getByPlaceholderText(/0\.0/i)
    await user.type(fromAmountInput, '100')

    // Switch to liquidity tab
    const liquidityTab = screen.getByRole('tab', { name: /liquidity/i })
    await user.click(liquidityTab)

    // Switch back to swap tab
    const swapTab = screen.getByRole('tab', { name: /swap/i })
    await user.click(swapTab)

    // Amount should be preserved
    expect(fromAmountInput).toHaveValue('100')
  })

  it('provides accessibility features', () => {
    render(<TradingInterface />)

    // Check for proper ARIA labels
    expect(screen.getByRole('tablist')).toBeInTheDocument()
    expect(screen.getByRole('tab', { name: /swap/i })).toHaveAttribute('aria-selected', 'true')

    // Check for screen reader announcements
    expect(screen.getByRole('status')).toBeInTheDocument()
  })

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup()
    render(<TradingInterface />)

    // Tab through form elements
    await user.tab()
    expect(screen.getByPlaceholderText(/0\.0/i)).toHaveFocus()

    await user.tab()
    expect(screen.getByRole('button', { name: /select token/i })).toHaveFocus()

    // Enter key should activate buttons
    await user.keyboard('{Enter}')
    // Should open token selector
  })

  it('handles responsive design', () => {
    // Mock window.matchMedia for mobile
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query.includes('max-width'),
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    })

    render(<TradingInterface />)

    // Should render mobile-friendly layout
    expect(screen.getByRole('main')).toHaveClass('grid')
  })
})
