"use client"

import { useEffect, useRef, useState, useMemo, useCallback } from "react"

interface ChartDataPoint {
  timestamp: string
  price: number
  volume: number
  high: number
  low: number
  open: number
  close: number
}

interface ChartProps {
  data?: ChartDataPoint[]
  width?: number
  height?: number
  showVolume?: boolean
  timeframe?: string
}

export function TradingChart({
  data: externalData,
  width,
  height = 300,
  showVolume = false,
  timeframe = '1D'
}: ChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [internalData, setInternalData] = useState<ChartDataPoint[]>([])
  const animationRef = useRef<number | null>(null)
  const lastUpdateTimeRef = useRef<number>(0)
  const isInitializedRef = useRef<boolean>(false)
  const [isVisible, setIsVisible] = useState(false)
  const observerRef = useRef<IntersectionObserver | null>(null)

  // Use external data if provided, otherwise generate internal data
  const chartData = useMemo(() => {
    return externalData || internalData
  }, [externalData, internalData])

  // Memoized data generation function
  const generateChartData = useCallback((points: number = 100) => {
    const initialData: ChartDataPoint[] = []
    let value = 100

    // Generate more realistic price data with smaller changes
    for (let i = 0; i < points; i++) {
      // Random walk with smaller changes (0.2% - 0.5% per point)
      const change = value * (Math.random() * 0.003 + 0.002) * (Math.random() > 0.5 ? 1 : -1)
      value += change
      // Add slight upward bias
      if (Math.random() > 0.6) value += value * 0.001

      const date = new Date()
      date.setMinutes(date.getMinutes() - (points - i))

      initialData.push({
        timestamp: date.toISOString(),
        price: value,
        volume: Math.random() * 1000000,
        high: value * 1.02,
        low: value * 0.98,
        open: value * 0.99,
        close: value
      })
    }

    return initialData
  }, [])

  // Initialize data only once and only when visible
  useEffect(() => {
    if (isInitializedRef.current || !isVisible || externalData) return

    const dataPoints = 100
    const initialData = generateChartData(dataPoints)
    setInternalData(initialData)
    isInitializedRef.current = true
  }, [isVisible, externalData, generateChartData])

  // Intersection Observer for lazy loading
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting)
      },
      { threshold: 0.1 }
    )

    observerRef.current.observe(canvas)

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [])

  // Memoized drawing function
  const drawChart = useCallback((ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement, data: ChartDataPoint[]) => {
    if (data.length === 0) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Calculate dimensions and scaling
    const padding = 40
    const chartWidth = canvas.width - padding * 2
    const chartHeight = canvas.height - padding * 2

    // Extract price values from data points
    const prices = data.map(point => point.price)
    const minValue = Math.min(...prices)
    const maxValue = Math.max(...prices)
    const valueRange = maxValue - minValue || 1

    // Set up drawing styles
    ctx.strokeStyle = "#D4AF37"
    ctx.lineWidth = 2
    ctx.lineCap = "round"
    ctx.lineJoin = "round"

    // Draw grid lines (optimized)
    ctx.strokeStyle = "rgba(255, 255, 255, 0.1)"
    ctx.lineWidth = 1

    // Horizontal grid lines
    for (let i = 0; i <= 4; i++) {
      const y = padding + (chartHeight / 4) * i
      ctx.beginPath()
      ctx.moveTo(padding, y)
      ctx.lineTo(canvas.width - padding, y)
      ctx.stroke()
    }

    // Vertical grid lines
    for (let i = 0; i <= 4; i++) {
      const x = padding + (chartWidth / 4) * i
      ctx.beginPath()
      ctx.moveTo(x, padding)
      ctx.lineTo(x, canvas.height - padding)
      ctx.stroke()
    }

    // Draw price line with path optimization
    ctx.strokeStyle = "#D4AF37"
    ctx.lineWidth = 2
    ctx.beginPath()

    // Use requestAnimationFrame for smooth rendering
    const drawPath = () => {
      for (let i = 0; i < prices.length; i++) {
        const x = padding + (i / (prices.length - 1)) * chartWidth
        const y = padding + ((maxValue - prices[i]) / valueRange) * chartHeight

        if (i === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      }
      ctx.stroke()

      // Add gradient fill for better visual appeal
      ctx.globalAlpha = 0.1
      ctx.fillStyle = "#D4AF37"
      ctx.lineTo(canvas.width - padding, canvas.height - padding)
      ctx.lineTo(padding, canvas.height - padding)
      ctx.closePath()
      ctx.fill()
      ctx.globalAlpha = 1
    }

    drawPath()
  }, [])

  // Handle canvas setup and drawing
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || !isVisible) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    const setDimensions = () => {
      const parent = canvas.parentElement
      if (!parent) return

      const dpr = window.devicePixelRatio || 1
      const rect = parent.getBoundingClientRect()

      canvas.width = (width || rect.width) * dpr
      canvas.height = height * dpr
      canvas.style.width = `${width || rect.width}px`
      canvas.style.height = `${height}px`

      ctx.scale(dpr, dpr)
    }

    // Chart drawing function
    const drawChart = (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement, data: ChartDataPoint[]) => {
      if (data.length === 0) return

      const padding = 20
      const chartWidth = canvas.width / (window.devicePixelRatio || 1) - padding * 2
      const chartHeight = canvas.height / (window.devicePixelRatio || 1) - padding * 2

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Extract price values and calculate min and max
      const prices = data.map(point => point.price)
      const min = Math.min(...prices)
      const max = Math.max(...prices)

      // Draw line chart - Using Dogecoin yellow
      ctx.strokeStyle = "rgba(255, 193, 7, 0.8)" // Dogecoin yellow
      ctx.lineWidth = 2
      ctx.beginPath()

      for (let i = 0; i < prices.length; i++) {
        const x = padding + (chartWidth * i) / (prices.length - 1)
        const y = padding + chartHeight - ((prices[i] - min) / (max - min)) * chartHeight

        if (i === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      }

      ctx.stroke()

      // Add gradient fill with Dogecoin yellow
      const gradient = ctx.createLinearGradient(0, padding, 0, padding + chartHeight)
      gradient.addColorStop(0, "rgba(255, 193, 7, 0.2)") // Dogecoin yellow with opacity
      gradient.addColorStop(1, "rgba(255, 193, 7, 0)") // Transparent Dogecoin yellow

      ctx.fillStyle = gradient
      ctx.beginPath()

      // Start from bottom left
      ctx.moveTo(padding, padding + chartHeight)

      // Draw the same line as above
      for (let i = 0; i < prices.length; i++) {
        const x = padding + (chartWidth * i) / (prices.length - 1)
        const y = padding + chartHeight - ((prices[i] - min) / (max - min)) * chartHeight

        if (i === 0) {
          ctx.lineTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      }

      // Complete the rectangle
      ctx.lineTo(padding + chartWidth, padding + chartHeight)
      ctx.closePath()
      ctx.fill()

      // Add glow effect with Dogecoin yellow
      ctx.shadowColor = "rgba(255, 193, 7, 0.5)" // Dogecoin yellow glow
      ctx.shadowBlur = 10
      ctx.strokeStyle = "rgba(255, 193, 7, 0.8)" // Dogecoin yellow
      ctx.lineWidth = 2
      ctx.beginPath()

      for (let i = 0; i < prices.length; i++) {
        const x = padding + (chartWidth * i) / (prices.length - 1)
        const y = padding + chartHeight - ((prices[i] - min) / (max - min)) * chartHeight

        if (i === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      }

      ctx.stroke()
      ctx.shadowBlur = 0
    }

    setDimensions()
    window.addEventListener("resize", setDimensions)

    // Throttled drawing function
    let drawTimeout: NodeJS.Timeout | null = null
    const throttledDraw = () => {
      if (drawTimeout) clearTimeout(drawTimeout)
      drawTimeout = setTimeout(() => {
        drawChart(ctx, canvas, chartData)
      }, 16) // ~60fps
    }

    // Initial draw
    throttledDraw()

    // Update data with smoother transitions (only if using internal data)
    const updateData = (timestamp: number) => {
      if (externalData) return // Don't update if using external data

      // Limit updates to once every 100ms for ultra-smooth animation
      if (timestamp - lastUpdateTimeRef.current < 100) {
        animationRef.current = requestAnimationFrame(updateData)
        return
      }

      lastUpdateTimeRef.current = timestamp

      setInternalData((prevData) => {
        if (prevData.length === 0) return prevData

        const newData = [...prevData]
        // Remove first point and add a new one
        newData.shift()

        // Calculate new value based on the last value with smaller, more realistic changes
        const lastPoint = newData[newData.length - 1]
        const lastValue = lastPoint.price
        // Smaller percentage change (0.1% - 0.3%)
        const change = lastValue * (Math.random() * 0.002 + 0.001) * (Math.random() > 0.5 ? 1 : -1)
        // Add slight upward bias occasionally
        const bias = Math.random() > 0.7 ? lastValue * 0.0005 : 0
        const newValue = lastValue + change + bias

        const newPoint: ChartDataPoint = {
          timestamp: new Date().toISOString(),
          price: newValue,
          volume: Math.random() * 1000000,
          high: newValue * 1.02,
          low: newValue * 0.98,
          open: lastValue,
          close: newValue
        }

        newData.push(newPoint)
        return newData
      })

      animationRef.current = requestAnimationFrame(updateData)
    }

    // Start animation with controlled timing (only for internal data)
    if (!externalData) {
      animationRef.current = requestAnimationFrame(updateData)
    }

    return () => {
      window.removeEventListener("resize", setDimensions)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [chartData, isVisible, drawChart, externalData]) // Optimized dependencies

  // Show loading state when not visible
  if (!isVisible) {
    return (
      <div className="h-[300px] w-full flex items-center justify-center bg-black/20 rounded-lg">
        <div className="text-white/60">Chart loading...</div>
      </div>
    )
  }

  return (
    <div className="w-full" style={{ height: `${height}px` }}>
      <canvas
        ref={canvasRef}
        className="h-full w-full rounded-lg"
        style={{ width: width ? `${width}px` : '100%', height: `${height}px` }}
      />
    </div>
  )
}
