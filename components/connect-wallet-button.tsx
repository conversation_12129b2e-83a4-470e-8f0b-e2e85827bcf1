"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useWallet } from "./wallet-provider"
import { LoadingSpinner } from "@/components/ui/loading-state"
import { Wallet } from "lucide-react"

interface ConnectWalletButtonProps {
  className?: string
}

export function ConnectWalletButton({ className }: ConnectWalletButtonProps) {
  const { connect, disconnect, isConnected } = useWallet()
  const [isConnecting, setIsConnecting] = useState(false)

  const handleClick = async () => {
    if (isConnected) {
      await disconnect()
      return
    }

    setIsConnecting(true)
    try {
      await connect()
    } catch (error) {
      console.error("Failed to connect wallet:", error)
    } finally {
      setIsConnecting(false)
    }
  }

  return (
    <Button 
      variant="outline"
      className={`cosmic-button flex items-center justify-center gap-2 px-4 transition-none ${isConnected ? 'border-yellow-500 text-yellow-500' : 'border-white/20 text-white/70'} ${className || ''}`} 
      onClick={handleClick}
      disabled={isConnecting}
    >
      {isConnecting ? (
        <>
          <LoadingSpinner size="sm" className="mr-2" />
          Connecting...
        </>
      ) : isConnected ? (
        <>
          <Wallet className="h-4 w-4" />
          <span className="group-hover:hidden">Wallet Connected</span>
          <span className="hidden group-hover:inline">Disconnect</span>
        </>
      ) : (
        <>
          <Wallet className="h-4 w-4" />
          <span>Connect Wallet</span>
        </>
      )}
    </Button>
  )
}
