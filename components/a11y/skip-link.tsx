"use client"

import { useState, useEffect } from "react"

export function SkipLink() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Tab" && !e.shiftKey) {
        setIsVisible(true)
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [])

  return (
    <a
      href="#main-content"
      className={`fixed top-4 left-4 z-50 bg-doge text-black px-4 py-2 rounded-md transition-transform ${
        isVisible ? "translate-y-0" : "-translate-y-20"
      } focus:translate-y-0`}
      onBlur={() => setIsVisible(false)}
    >
      Skip to main content
    </a>
  )
}
