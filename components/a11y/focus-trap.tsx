"use client"

import type React from "react"

import { useRef, useEffect } from "react"
import { useIsomorphicLayoutEffect } from "@/hooks/use-isomorphic-layout-effect"

type FocusTrapProps = {
  children: React.ReactNode
  active?: boolean
  initialFocus?: React.RefObject<HTMLElement>
}

export function FocusTrap({ children, active = true, initialFocus }: FocusTrapProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const focusableElementsRef = useRef<HTMLElement[]>([])

  // Get all focusable elements when the component mounts or updates
  useIsomorphicLayoutEffect(() => {
    if (!containerRef.current || !active) return

    const focusableElements = containerRef.current.querySelectorAll<HTMLElement>(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
    )

    focusableElementsRef.current = Array.from(focusableElements)

    // Set initial focus
    if (initialFocus?.current) {
      initialFocus.current.focus()
    } else if (focusableElementsRef.current.length > 0) {
      focusableElementsRef.current[0].focus()
    }
  }, [active, initialFocus])

  // Handle tab key to trap focus
  useEffect(() => {
    if (!active) return

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== "Tab") return

      const focusableElements = focusableElementsRef.current
      if (focusableElements.length === 0) return

      const firstElement = focusableElements[0]
      const lastElement = focusableElements[focusableElements.length - 1]

      // If shift+tab on first element, move to last element
      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault()
        lastElement.focus()
      }
      // If tab on last element, move to first element
      else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault()
        firstElement.focus()
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [active])

  return (
    <div ref={containerRef} className="outline-none">
      {children}
    </div>
  )
}
