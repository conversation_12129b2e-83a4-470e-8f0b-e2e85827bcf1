"use client"

import { useState, useEffect, useRef } from "react"
import { createPortal } from "react-dom"
import Link from "next/link"
import { usePathname } from "next/navigation"
import Image from "next/image"
import { Menu, X, ChevronDown, User, Award, BarChart3, Wallet } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { ConnectWalletButton } from "@/components/connect-wallet-button"
import { NotificationDropdown } from "@/components/notification-dropdown"
import { NavigationAnnouncer } from "@/components/ui/live-region"
import { useWallet } from "@/components/wallet-provider"
import { useNotifications } from "@/components/notification-provider-simple"
import { useOnClickOutside } from "@/hooks/use-click-outside"
import { ShimmerText } from "@/components/shimmer-text"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export function Navbar() {
  const pathname = usePathname()
  const { isConnected, address } = useWallet()
  const { unreadCount } = useNotifications()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const profileDropdownRef = useRef<HTMLDivElement>(null)
  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false)
  const [isNavigating, setIsNavigating] = useState(false)

  // Get current page name for announcements
  const getCurrentPageName = () => {
    if (pathname === '/') return 'Home'
    if (pathname === '/launch') return 'Token Launch'
    if (pathname === '/trade') return 'Trading'
    if (pathname === '/governance') return 'Governance'
    if (pathname === '/analytics') return 'Analytics'
    if (pathname === '/social') return 'Social'
    if (pathname === '/profile') return 'Profile'
    if (pathname === '/docs') return 'Documentation'
    return 'Page'
  }

  // Admin access check (would be properly gated in production)
  const isAdmin = true

  // Update the navLinks array to include the governance admin section for admin users
  const navLinks = [
    { name: "Home", href: "/" },
    { name: "Trade", href: "/trade" },
    { name: "Launch", href: "/launch" },
    { name: "Analytics", href: "/analytics" },
    { name: "Social", href: "/social" },
    {
      name: "Governance",
      href: "/governance",
      children: [
        { name: "Dashboard", href: "/governance" },
        { name: "Proposals", href: "/governance/proposals" },
        { name: "Staking", href: "/governance/staking" },
        { name: "Treasury", href: "/governance/treasury" },
        { name: "Analytics", href: "/governance/analytics" },
        { name: "Achievements", href: "/governance/achievements" },
        { name: "Admin", href: "/governance/admin/dashboard" },
        { name: "Moderation", href: "/governance/admin/moderation" },
        { name: "Emergency Controls", href: "/governance/admin/emergency" },
        { name: "Treasury Management", href: "/governance/admin/treasury" },
        { name: "Audit Logs", href: "/governance/admin/audit-logs" },
        { name: "Audit Analytics", href: "/governance/admin/audit-analytics" },
      ],
    },
  ]

  useEffect(() => {
    // Close mobile menu when route changes
    setIsMenuOpen(false)
    setActiveDropdown(null)
    setProfileDropdownOpen(false)
  }, [pathname])

  // Close dropdown when clicking outside (only for desktop)
  useOnClickOutside(dropdownRef, () => {
    if (!isMenuOpen) {
      setActiveDropdown(null)
    }
  })
  useOnClickOutside(profileDropdownRef, () => setProfileDropdownOpen(false))

  const toggleDropdown = (name: string) => {
    setActiveDropdown(activeDropdown === name ? null : name)
  }

  const toggleProfileDropdown = () => {
    setProfileDropdownOpen(!profileDropdownOpen)
  }

  return (
    <header className="sticky top-0 z-40 w-full backdrop-blur-md bg-black/80">
      <div className="container flex h-16 items-center justify-between px-4 md:px-6">
        <div className="flex items-center gap-6 md:gap-8 lg:gap-10">
          <Link href="/" className="flex items-center gap-2">
            <Image
              src="/images/pawpumps-icon.png"
              alt="PawPumps logo"
              width={32}
              height={32}
              priority
            />
            <span className="text-lg font-bold text-white">
              <ShimmerText>PawPumps</ShimmerText>
            </span>
          </Link>

          <nav className="hidden md:flex items-center gap-6" role="navigation" aria-label="Main navigation">
            {navLinks.map((link) =>
              link.children ? (
                <div key={link.name} className="relative group" ref={activeDropdown === link.name ? dropdownRef : null}>
                  <button
                    className="flex items-center gap-1 text-sm text-white/80 hover:text-white focus:outline-none focus:ring-2 focus:ring-doge/50 rounded px-2 py-1"
                    onClick={() => toggleDropdown(link.name)}
                    aria-expanded={activeDropdown === link.name}
                    aria-haspopup="true"
                    aria-controls={`dropdown-${link.name.toLowerCase()}`}
                    id={`button-${link.name.toLowerCase()}`}
                  >
                    {link.name}
                    <ChevronDown className="h-4 w-4" />
                  </button>
                  <div
                    className={`absolute left-0 top-full mt-2 w-56 rounded-md glass-card-solid shadow-lg ring-1 ring-white/10 transition-all duration-200 ${
                      activeDropdown === link.name
                        ? "opacity-100 visible"
                        : "opacity-0 invisible group-hover:opacity-100 group-hover:visible"
                    }`}
                    style={{ maxHeight: "calc(100vh - 80px)", overflowY: "auto" }}
                    role="menu"
                    aria-labelledby={`button-${link.name.toLowerCase()}`}
                    id={`dropdown-${link.name.toLowerCase()}`}
                  >
                    <div className="py-1">
                      {link.children.map((child) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          className={`block px-4 py-2 text-sm focus:outline-none focus:bg-white/10 focus:text-white ${
                            pathname === child.href
                              ? "bg-white/10 text-white"
                              : "text-white/70 hover:bg-white/5 hover:text-white"
                          }`}
                          role="menuitem"
                          tabIndex={activeDropdown === link.name ? 0 : -1}
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <Link
                  key={link.name}
                  href={link.href}
                  className={`text-sm focus:outline-none focus:ring-2 focus:ring-doge/50 rounded px-2 py-1 ${
                    pathname === link.href ? "text-white font-medium" : "text-white/80 hover:text-white"
                  }`}
                  aria-current={pathname === link.href ? "page" : undefined}
                >
                  {link.name}
                </Link>
              ),
            )}
          </nav>
        </div>

        <div className="flex items-center gap-4">
          <div className="hidden md:flex items-center gap-2">
            <NotificationDropdown />

            {isConnected && (
              <div className="relative" ref={profileDropdownRef}>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white/80 hover:text-white relative"
                  onClick={toggleProfileDropdown}
                >
                  <Avatar className="h-8 w-8 border border-white/20">
                    <AvatarImage src="/placeholder.svg?key=jg7nh" alt="Profile" />
                    <AvatarFallback className="bg-doge/20 text-doge">DW</AvatarFallback>
                  </Avatar>
                  <span className="absolute -top-1 -right-1 flex h-3 w-3">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-doge opacity-75"></span>
                    <span className="relative inline-flex rounded-full h-3 w-3 bg-doge"></span>
                  </span>
                </Button>

                <div
                  className={`absolute right-0 top-full mt-2 w-56 rounded-md glass-card shadow-lg ring-1 ring-white/10 transition-all duration-200 ${
                    profileDropdownOpen ? "opacity-100 visible" : "opacity-0 invisible"
                  }`}
                >
                  <div className="p-2 border-b border-white/10">
                    <p className="text-sm font-medium text-white">DogeWhale</p>
                    <p className="text-xs text-white/60 truncate">
                      {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : ""}
                    </p>
                  </div>
                  <div className="py-1">
                    <Link
                      href="/profile"
                      className="flex items-center px-4 py-2 text-sm text-white/70 hover:bg-white/5 hover:text-white"
                    >
                      <User className="h-4 w-4 mr-2" />
                      My Profile
                    </Link>
                    <Link
                      href="/governance/achievements"
                      className="flex items-center px-4 py-2 text-sm text-white/70 hover:bg-white/5 hover:text-white"
                    >
                      <Award className="h-4 w-4 mr-2" />
                      Achievements
                    </Link>
                    <Link
                      href="/governance/analytics"
                      className="flex items-center px-4 py-2 text-sm text-white/70 hover:bg-white/5 hover:text-white"
                    >
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Analytics
                    </Link>
                    <Link
                      href="/governance/treasury"
                      className="flex items-center px-4 py-2 text-sm text-white/70 hover:bg-white/5 hover:text-white"
                    >
                      <Wallet className="h-4 w-4 mr-2" />
                      Treasury
                    </Link>
                  </div>
                </div>
              </div>
            )}

            <ConnectWalletButton />
          </div>

          <Button
            variant="ghost"
            size="icon"
            className="md:hidden text-white/80 hover:text-white focus:outline-none focus:ring-2 focus:ring-doge/50"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-expanded={isMenuOpen}
            aria-controls="mobile-menu"
            aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>
      </div>

      {/* Mobile menu using Portal */}
      {isMenuOpen && typeof window !== 'undefined' && window.innerWidth < 768 && createPortal(
        <div
          style={{
            position: 'fixed',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            backgroundColor: 'black',
            zIndex: 999999,
            overflowY: 'auto',
            padding: '24px'
          }}
          id="mobile-menu"
          role="navigation"
          aria-label="Mobile navigation"
          onClick={(e) => {
            // Close menu if clicking on the background (not on menu content)
            if (e.target === e.currentTarget) {
              setIsMenuOpen(false);
            }
          }}
        >
          {/* Close button */}
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '24px' }}>
            <button
              onClick={() => setIsMenuOpen(false)}
              style={{
                background: 'none',
                border: 'none',
                color: 'white',
                fontSize: '24px',
                cursor: 'pointer',
                padding: '8px',
                borderRadius: '4px',
                backgroundColor: 'rgba(255, 255, 255, 0.1)'
              }}
              aria-label="Close menu"
            >
              <X style={{ width: '24px', height: '24px' }} />
            </button>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', width: '100%', maxWidth: '400px', margin: '0 auto' }}>
            {navLinks.map((link) =>
              link.children ? (
                <div key={link.name} style={{ marginBottom: '8px' }}>
                  <button
                    style={{
                      fontWeight: '500',
                      color: 'white',
                      width: '100%',
                      textAlign: 'left',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '14px 16px',
                      borderRadius: '8px',
                      backgroundColor: 'rgba(255, 255, 255, 0.05)',
                      border: 'none',
                      fontSize: '18px',
                      cursor: 'pointer'
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setActiveDropdown(prev => prev === link.name ? null : link.name);
                    }}
                  >
                    <span>{link.name}</span>
                    <ChevronDown
                      style={{
                        height: '20px',
                        width: '20px',
                        transform: activeDropdown === link.name ? 'rotate(180deg)' : 'rotate(0deg)',
                        transition: 'transform 0.2s'
                      }}
                    />
                  </button>
                  {activeDropdown === link.name && (
                    <div style={{ marginTop: '8px', paddingLeft: '16px', backgroundColor: 'rgba(255, 255, 255, 0.02)', borderRadius: '8px', padding: '8px' }}>
                      {link.children.map((child) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          style={{
                            display: 'block',
                            padding: '10px 12px',
                            fontSize: '16px',
                            color: pathname === child.href ? 'white' : 'rgba(255, 255, 255, 0.7)',
                            backgroundColor: pathname === child.href ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
                            borderRadius: '6px',
                            textDecoration: 'none',
                            marginBottom: '2px'
                          }}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <Link
                  key={link.name}
                  href={link.href}
                  style={{
                    fontSize: '18px',
                    color: pathname === link.href ? 'white' : 'rgba(255, 255, 255, 0.8)',
                    fontWeight: pathname === link.href ? '500' : '400',
                    padding: '14px 16px',
                    borderRadius: '8px',
                    backgroundColor: pathname === link.href ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.02)',
                    textDecoration: 'none',
                    display: 'block',
                    marginBottom: '8px'
                  }}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {link.name}
                </Link>
              ),
            )}

            {/* User section */}
            <div style={{ marginTop: '24px', paddingTop: '16px', borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
              {isConnected && (
                <>
                  <Link href="/profile" onClick={() => setIsMenuOpen(false)} style={{ display: 'block', marginBottom: '8px' }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      color: 'rgba(255, 255, 255, 0.8)',
                      padding: '14px 16px',
                      fontSize: '16px',
                      borderRadius: '8px',
                      backgroundColor: 'rgba(255, 255, 255, 0.02)',
                      textDecoration: 'none'
                    }}>
                      <User style={{ height: '20px', width: '20px', marginRight: '12px' }} />
                      My Profile
                    </div>
                  </Link>
                  <Link href="/governance/achievements" onClick={() => setIsMenuOpen(false)} style={{ display: 'block', marginBottom: '8px' }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      color: 'rgba(255, 255, 255, 0.8)',
                      padding: '14px 16px',
                      fontSize: '16px',
                      borderRadius: '8px',
                      backgroundColor: 'rgba(255, 255, 255, 0.02)',
                      textDecoration: 'none'
                    }}>
                      <Award style={{ height: '20px', width: '20px', marginRight: '12px' }} />
                      Achievements
                    </div>
                  </Link>
                </>
              )}
              <div style={{ marginTop: '12px' }}>
                <ConnectWalletButton />
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}



      {/* Navigation announcements for screen readers */}
      <NavigationAnnouncer
        currentPage={getCurrentPageName()}
        isLoading={isNavigating}
      />
    </header>
  )
}
