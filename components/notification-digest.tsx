"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, Download, Filter, Inbox, Mail } from "lucide-react"
import { useNotifications } from "@/components/notification-provider"
import { format } from "date-fns"

export function NotificationDigest() {
  const { notifications } = useNotifications()
  const [timeframe, setTimeframe] = useState<"daily" | "weekly">("daily")
  const [category, setCategory] = useState<"all" | "governance" | "token" | "price" | "system">("all")

  // Group notifications by date
  const groupedNotifications = notifications.reduce(
    (groups, notification) => {
      const date = format(notification.timestamp, "yyyy-MM-dd")
      if (!groups[date]) {
        groups[date] = []
      }
      groups[date].push(notification)
      return groups
    },
    {} as Record<string, typeof notifications>,
  )

  // Sort dates in descending order
  const sortedDates = Object.keys(groupedNotifications).sort((a, b) => new Date(b).getTime() - new Date(a).getTime())

  // Filter notifications by category if needed
  const filteredDates = sortedDates
    .map((date) => {
      if (category === "all") {
        return { date, notifications: groupedNotifications[date] }
      }
      return {
        date,
        notifications: groupedNotifications[date].filter((n) => n.category === category),
      }
    })
    .filter((group) => group.notifications.length > 0)

  // Get notification count by category
  const getCategoryCount = (cat: string) => {
    return notifications.filter((n) => n.category === cat).length
  }

  return (
    <Card className="glass-card border-white/5">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle className="text-white">Notification Digest</CardTitle>
            <CardDescription className="text-white/70">Summary of your recent notifications</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="border-white/10 bg-white/5">
              <Mail className="h-4 w-4 mr-2" />
              Email Digest
            </Button>
            <Button variant="outline" size="sm" className="border-white/10 bg-white/5">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <Tabs
            defaultValue="daily"
            value={timeframe}
            onValueChange={(value) => setTimeframe(value as any)}
            className="flex-1"
          >
            <TabsList className="grid w-full grid-cols-2 glass">
              <TabsTrigger value="daily">Daily</TabsTrigger>
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
            </TabsList>
          </Tabs>

          <Tabs
            defaultValue="all"
            value={category}
            onValueChange={(value) => setCategory(value as any)}
            className="flex-1"
          >
            <TabsList className="grid w-full grid-cols-5 glass">
              <TabsTrigger value="all" className="text-xs">
                All
              </TabsTrigger>
              <TabsTrigger value="governance" className="text-xs">
                Gov ({getCategoryCount("governance")})
              </TabsTrigger>
              <TabsTrigger value="token" className="text-xs">
                Token ({getCategoryCount("token")})
              </TabsTrigger>
              <TabsTrigger value="price" className="text-xs">
                Price ({getCategoryCount("price")})
              </TabsTrigger>
              <TabsTrigger value="system" className="text-xs">
                Sys ({getCategoryCount("system")})
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {filteredDates.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Inbox className="h-12 w-12 text-white/20 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No notifications found</h3>
            <p className="text-white/70 max-w-md">There are no notifications matching your selected filters.</p>
          </div>
        ) : (
          <div className="space-y-6">
            {filteredDates.map((group) => (
              <div key={group.date} className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-white/60" />
                  <h3 className="text-sm font-medium text-white">
                    {format(new Date(group.date), "EEEE, MMMM d, yyyy")}
                  </h3>
                </div>
                <div className="space-y-2 pl-6">
                  {group.notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className="flex items-start gap-3 p-2 rounded-md bg-white/5 border border-white/10"
                    >
                      <div className="flex-shrink-0">
                        <Badge
                          className={`h-8 w-8 rounded-full flex items-center justify-center ${
                            notification.type === "governance"
                              ? "bg-blue-500/20 text-blue-500"
                              : notification.type === "token"
                                ? "bg-purple-500/20 text-purple-500"
                                : notification.type === "price"
                                  ? "bg-green-500/20 text-green-500"
                                  : "bg-yellow-500/20 text-yellow-500"
                          }`}
                        >
                          {notification.type === "governance" ? (
                            <Calendar className="h-4 w-4" />
                          ) : notification.type === "token" ? (
                            <Filter className="h-4 w-4" />
                          ) : notification.type === "price" ? (
                            <Clock className="h-4 w-4" />
                          ) : (
                            <Mail className="h-4 w-4" />
                          )}
                        </Badge>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <p className="text-sm font-medium text-white">{notification.title}</p>
                          <span className="text-xs text-white/60 whitespace-nowrap ml-2">
                            {format(notification.timestamp, "HH:mm")}
                          </span>
                        </div>
                        <p className="text-xs text-white/70 mt-1">{notification.message}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
