"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Rocket, AlertCircle, Info, HelpCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useWallet } from "@/components/wallet-provider"
import { cn } from "@/lib/utils"
import { LoadingSpinner } from "@/components/ui/loading-state"
import { ValidatedInput, ValidatedTextarea } from "@/components/ui/validated-input"
import { tokenLaunchSchema, validate<PERSON>ield, formatValidationErrors, type TokenLaunchFormData } from "@/lib/validation"
import { z } from "zod"
import { FormValidationAnnouncer } from "@/components/ui/live-region"
import { useNotification } from "@/hooks/use-notification"
import { BondingCurveVisualization } from "@/components/bonding-curve-visualization"
import { ShimmerText } from "@/components/shimmer-text"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

type BondingCurve = "linear" | "exponential" | "logarithmic"

export function TokenLaunchForm() {
  const { isConnected } = useWallet()
  const { toast } = useToast()
  const { showNotification } = useNotification()
  const [chartSize, setChartSize] = useState({ width: 0, height: 250 })
  const [chartContainerRef, setChartContainerRef] = useState<HTMLDivElement | null>(null)

  const [formState, setFormState] = useState<Partial<TokenLaunchFormData>>({
    name: "",
    symbol: "",
    totalSupply: "**********", // 1 billion default
    initialPrice: "0.000001",
    description: "",
    bondingCurveType: "linear",
    liquidityPercentage: 50,
    website: "",
    twitter: "",
    telegram: "",
  })

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [isFormValid, setIsFormValid] = useState(false)

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)

  // Validation functions for individual fields
  const validateName = (value: string) => validateField(tokenLaunchSchema, 'name', value)
  const validateSymbol = (value: string) => validateField(tokenLaunchSchema, 'symbol', value)
  const validateDescription = (value: string) => validateField(tokenLaunchSchema, 'description', value)
  const validateTotalSupply = (value: string) => validateField(tokenLaunchSchema, 'totalSupply', value)
  const validateInitialPrice = (value: string) => validateField(tokenLaunchSchema, 'initialPrice', value)
  const validateWebsite = (value: string) => validateField(tokenLaunchSchema, 'website', value)
  const validateTwitter = (value: string) => validateField(tokenLaunchSchema, 'twitter', value)
  const validateTelegram = (value: string) => validateField(tokenLaunchSchema, 'telegram', value)

  // Handle field validation
  const handleFieldValidation = (field: string, isValid: boolean, error?: string) => {
    setValidationErrors(prev => {
      const newErrors = { ...prev }
      if (isValid) {
        delete newErrors[field]
      } else {
        newErrors[field] = error || 'Invalid value'
      }
      return newErrors
    })
  }

  // Check if form is valid
  useEffect(() => {
    const hasErrors = Object.keys(validationErrors).length > 0
    const hasRequiredFields = formState.name && formState.symbol && formState.description && formState.totalSupply && formState.initialPrice
    setIsFormValid(!hasErrors && !!hasRequiredFields)
  }, [validationErrors, formState])

  // Handle form field changes
  const handleFieldChange = (field: keyof TokenLaunchFormData, value: string | number) => {
    setFormState(prev => ({ ...prev, [field]: value }))
  }

  // Update chart size when window resizes
  useEffect(() => {
    if (!chartContainerRef) return

    const updateChartSize = () => {
      try {
        if (chartContainerRef && chartContainerRef.clientWidth > 0) {
          setChartSize({
            width: chartContainerRef.clientWidth,
            height: 250,
          })
        }
      } catch (error) {
        console.warn("Error updating chart size:", error)
        // Fallback to default size
        setChartSize({ width: 400, height: 250 })
      }
    }

    // Initial size with delay to ensure DOM is ready
    const timeoutId = setTimeout(updateChartSize, 100)

    // Update on resize
    window.addEventListener("resize", updateChartSize)
    return () => {
      window.removeEventListener("resize", updateChartSize)
      clearTimeout(timeoutId)
    }
  }, [chartContainerRef])

  const handleBondingCurveChange = (value: string) => {
    setFormState((prev) => ({ ...prev, bondingCurveType: value as "linear" | "exponential" | "logarithmic" }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!isConnected) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to launch a token",
        variant: "destructive",
      })
      return
    }

    // Validate entire form before submission
    try {
      // Add additional null checks before validation
      const safeFormState = {
        ...formState,
        name: formState.name || "",
        symbol: formState.symbol || "",
        description: formState.description || "",
        totalSupply: formState.totalSupply || "**********",
        initialPrice: formState.initialPrice || "0.000001",
        bondingCurveType: formState.bondingCurveType || "linear",
        liquidityPercentage: formState.liquidityPercentage || 50,
        website: formState.website || "",
        twitter: formState.twitter || "",
        telegram: formState.telegram || "",
      }

      const validatedData = tokenLaunchSchema.parse(safeFormState)
      setIsSubmitting(true)

      // Simulate token deployment with error handling
      await new Promise((resolve, reject) => {
        setTimeout(() => {
          // Simulate occasional network errors for testing
          if (Math.random() < 0.1) {
            reject(new Error("Network timeout - please try again"))
          } else {
            resolve(undefined)
          }
        }, 2000)
      })

      // Show toast notification
      showNotification({
        title: "Token Launched!",
        message: `Your token ${validatedData.name} (${validatedData.symbol}) has been deployed to the Dogechain Network.`,
        type: "success",
        addToCenter: true,
      })

      // Reset form
      setFormState({
        name: "",
        symbol: "",
        totalSupply: "**********",
        initialPrice: "0.000001",
        description: "",
        bondingCurveType: "linear",
        liquidityPercentage: 50,
        website: "",
        twitter: "",
        telegram: "",
      })
      setValidationErrors({})
      setSubmitSuccess(true)

      // Reset success state after a delay
      setTimeout(() => setSubmitSuccess(false), 3000)
    } catch (error) {
      console.error("Token launch error:", error)

      if (error instanceof z.ZodError) {
        const formattedErrors = formatValidationErrors(error)
        setValidationErrors(formattedErrors)
        toast({
          title: "Validation Error",
          description: "Please fix the errors in the form before submitting.",
          variant: "destructive",
        })
      } else if (error instanceof Error) {
        // Handle specific error types
        let errorMessage = "There was an error deploying your token. Please try again."

        if (error.message.includes("timeout") || error.message.includes("network")) {
          errorMessage = "Network timeout occurred. Please check your connection and try again."
        } else if (error.message.includes("gas")) {
          errorMessage = "Transaction failed due to insufficient gas. Please try again."
        } else if (error.message.includes("rejected")) {
          errorMessage = "Transaction was rejected. Please try again."
        }

        showNotification({
          title: "Error Launching Token",
          message: errorMessage,
          type: "error",
          addToCenter: true,
        })
      } else {
        // Fallback for unknown errors
        showNotification({
          title: "Unknown Error",
          message: "An unexpected error occurred. Please refresh the page and try again.",
          type: "error",
          addToCenter: true,
        })
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatSupply = (value: number) => {
    if (value >= **********) {
      return `${(value / **********).toFixed(1)}B`
    } else if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    }
    return value.toString()
  }

  const getBondingCurveInfo = (type: BondingCurve | undefined) => {
    // Provide safe fallback for undefined type
    const safeType = type || "linear"
    switch (safeType) {
      case "linear":
        return {
          title: "Linear Curve",
          description: "Price increases at a constant rate as tokens are sold.",
          initialPrice: "Low",
          priceGrowth: "Steady",
          bestFor: "Predictability",
          benefits: [
            "Predictable price growth",
            "Fair distribution across all buyers",
            "Balanced incentives for early and late adopters",
          ],
        }
      case "exponential":
        return {
          title: "Exponential Curve",
          description: "Price starts low and increases rapidly as more tokens are sold.",
          initialPrice: "Very Low",
          priceGrowth: "Accelerating",
          bestFor: "Long Term",
          benefits: [
            "Strong incentives for early adoption",
            "Potential for significant price appreciation",
            "Creates FOMO (Fear Of Missing Out) effect",
          ],
        }
      case "logarithmic":
        return {
          title: "Logarithmic Curve",
          description: "Price rises quickly at first, then stabilizes as more tokens are sold.",
          initialPrice: "High",
          priceGrowth: "Diminishing",
          bestFor: "Quick Start",
          benefits: [
            "Quick initial price discovery",
            "Stabilizes as market matures",
            "Balances short-term excitement with long-term stability",
          ],
        }
      default:
        // Fallback for any unexpected values
        return {
          title: "Linear Curve",
          description: "Price increases at a constant rate as tokens are sold.",
          initialPrice: "Low",
          priceGrowth: "Steady",
          bestFor: "Predictability",
          benefits: [
            "Predictable price growth",
            "Fair distribution across all buyers",
            "Balanced incentives for early and late adopters",
          ],
        }
    }
  }

  const curveInfo = getBondingCurveInfo(formState.bondingCurveType || "linear")

  return (
    <Card className="glass-card border-white/5 liquid-glow">
      <CardHeader>
        <CardTitle className="text-white">
          <ShimmerText>Create Your Memecoin</ShimmerText>
        </CardTitle>
        <CardDescription className="text-white/70">
          Fill in the details below to launch your own memecoin on the Dogechain Network
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <ValidatedInput
              id="name"
              label="Token Name"
              value={formState.name || ""}
              onChange={(value) => handleFieldChange('name', value)}
              onValidation={(isValid, error) => handleFieldValidation('name', isValid, error)}
              validator={validateName}
              placeholder="e.g. DogeKing"
              required
              helpText="2-50 characters, letters, numbers, and spaces only"
            />

            <ValidatedInput
              id="symbol"
              label="Token Symbol"
              value={formState.symbol || ""}
              onChange={(value) => handleFieldChange('symbol', value.toUpperCase())}
              onValidation={(isValid, error) => handleFieldValidation('symbol', isValid, error)}
              validator={validateSymbol}
              placeholder="e.g. DGK"
              required
              helpText="2-10 characters, uppercase letters and numbers only"
            />

            <ValidatedInput
              id="totalSupply"
              label="Total Supply"
              value={formState.totalSupply || ""}
              onChange={(value) => handleFieldChange('totalSupply', value)}
              onValidation={(isValid, error) => handleFieldValidation('totalSupply', isValid, error)}
              validator={validateTotalSupply}
              placeholder="**********"
              type="number"
              required
              helpText="Minimum 1,000,000 tokens, maximum 1 trillion"
            />

            <ValidatedInput
              id="initialPrice"
              label="Initial Price (wDOGE)"
              value={formState.initialPrice || ""}
              onChange={(value) => handleFieldChange('initialPrice', value)}
              onValidation={(isValid, error) => handleFieldValidation('initialPrice', isValid, error)}
              validator={validateInitialPrice}
              placeholder="0.000001"
              type="number"
              required
              helpText="Starting price per token in wDOGE"
            />

            <ValidatedTextarea
              id="description"
              label="Description"
              value={formState.description || ""}
              onChange={(value) => handleFieldChange('description', value)}
              onValidation={(isValid, error) => handleFieldValidation('description', isValid, error)}
              validator={validateDescription}
              placeholder="Describe your token's purpose or mission"
              required
              helpText="10-500 characters describing your token"
              rows={4}
            />

            <div className="space-y-2">
              <Label className="text-white/80">Bonding Curve Type</Label>
              <RadioGroup
                value={formState.bondingCurveType}
                onValueChange={handleBondingCurveChange}
                className="grid grid-cols-1 gap-4 pt-2 md:grid-cols-3"
              >
                <BondingCurveOption
                  id="linear"
                  value="linear"
                  title="Linear"
                  description="Price increases linearly with supply sold"
                  selected={formState.bondingCurveType === "linear"}
                />
                <BondingCurveOption
                  id="exponential"
                  value="exponential"
                  title="Exponential"
                  description="Price rises faster as supply diminishes"
                  selected={formState.bondingCurveType === "exponential"}
                />
                <BondingCurveOption
                  id="logarithmic"
                  value="logarithmic"
                  title="Logarithmic"
                  description="Price stabilizes as supply grows"
                  selected={formState.bondingCurveType === "logarithmic"}
                />
              </RadioGroup>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">Social Links (Optional)</h3>

              <ValidatedInput
                id="website"
                label="Website"
                value={formState.website || ""}
                onChange={(value) => handleFieldChange('website', value)}
                onValidation={(isValid, error) => handleFieldValidation('website', isValid, error)}
                validator={validateWebsite}
                placeholder="https://yourtoken.com"
                helpText="Your project's official website"
              />

              <ValidatedInput
                id="twitter"
                label="Twitter"
                value={formState.twitter || ""}
                onChange={(value) => handleFieldChange('twitter', value)}
                onValidation={(isValid, error) => handleFieldValidation('twitter', isValid, error)}
                validator={validateTwitter}
                placeholder="@yourtoken or https://twitter.com/yourtoken"
                helpText="Twitter handle or URL"
              />

              <ValidatedInput
                id="telegram"
                label="Telegram"
                value={formState.telegram || ""}
                onChange={(value) => handleFieldChange('telegram', value)}
                onValidation={(isValid, error) => handleFieldValidation('telegram', isValid, error)}
                validator={validateTelegram}
                placeholder="@yourtoken or https://t.me/yourtoken"
                helpText="Telegram channel or group"
              />
            </div>

            <div className="mt-6 rounded-lg bg-black border border-white/10 overflow-hidden">
              <div className="p-4 border-b border-white/10 bg-black">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <h3 className="text-sm font-medium text-white/80">Bonding Curve Preview</h3>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full">
                            <HelpCircle className="h-3 w-3 text-white/60" />
                            <span className="sr-only">Bonding Curve Info</span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-xs">
                          <p>
                            This curve determines how your token price will change as more tokens are bought and sold.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <span className="px-2 py-1 text-xs rounded-full bg-white/10 text-white/80">
                    {(formState.bondingCurveType || "linear").charAt(0).toUpperCase() + (formState.bondingCurveType || "linear").slice(1)}
                  </span>
                </div>
                <p className="text-xs text-white/60 mt-1">{curveInfo.description}</p>
              </div>

              <div className="flex flex-col md:flex-row">
                <div
                  className="h-[250px] w-full md:w-2/3 border-b md:border-b-0 md:border-r border-white/10"
                  ref={setChartContainerRef}
                >
                  <BondingCurveVisualization
                    type={formState.bondingCurveType || "linear"}
                    width={chartSize.width}
                    height={chartSize.height}
                  />
                </div>

                <div className="p-4 w-full md:w-1/3 bg-black">
                  <h4 className="text-sm font-medium text-white/80 mb-3">{curveInfo.title}</h4>

                  <div className="space-y-3">
                    <div>
                      <span className="text-xs text-white/60 block">Initial Price</span>
                      <span className="text-sm text-white/90 font-medium">{curveInfo.initialPrice}</span>
                    </div>

                    <div>
                      <span className="text-xs text-white/60 block">Price Growth</span>
                      <span className="text-sm text-white/90 font-medium">{curveInfo.priceGrowth}</span>
                    </div>

                    <div>
                      <span className="text-xs text-white/60 block">Best For</span>
                      <span className="text-sm text-white/90 font-medium">{curveInfo.bestFor}</span>
                    </div>

                    <div>
                      <span className="text-xs text-white/60 block">Benefits</span>
                      <ul className="list-disc list-inside text-xs text-white/80 mt-1 space-y-1">
                        {curveInfo.benefits.map((benefit, index) => (
                          <li key={index}>{benefit}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-black border-t border-white/10">
                <p className="text-xs text-white/70 italic">
                  <Info className="h-3 w-3 inline mr-1" />
                  Hover over the chart to see exact price points at different supply levels
                </p>
              </div>
            </div>
          </div>

          <Alert className="glass border-white/10 bg-white/5">
            <AlertCircle className="h-4 w-4 text-white" />
            <AlertTitle className="text-white">Gas Fee Required</AlertTitle>
            <AlertDescription className="text-white/70">
              Launching a token requires approximately 0.1 wDOGE (~$0.02) in gas fees
            </AlertDescription>
          </Alert>
        </form>
      </CardContent>
      <CardFooter>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={isSubmitting || !isConnected || !isFormValid}
          className="w-full glass-button liquid-shine"
        >
          {isSubmitting ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Deploying Token...
            </>
          ) : (
            <>
              <Rocket className="mr-2 h-4 w-4" />
              Launch Token
            </>
          )}
        </Button>
      </CardFooter>

      {/* Form validation announcements for screen readers */}
      <FormValidationAnnouncer
        errors={validationErrors}
        isSubmitting={isSubmitting}
        submitSuccess={submitSuccess}
      />
    </Card>
  )
}

function BondingCurveOption({
  id,
  value,
  title,
  description,
  selected,
}: {
  id: string
  value: string
  title: string
  description: string
  selected: boolean
}) {
  return (
    <div className="relative">
      <RadioGroupItem value={value} id={id} className="peer sr-only" />
      <Label
        htmlFor={id}
        className={cn(
          "flex h-full cursor-pointer flex-col rounded-md glass-card border border-white/5 p-4 hover:border-white/10",
          selected && "border-white/20 border-glow",
        )}
      >
        <span className="mb-1 font-medium text-white">{title}</span>
        <span className="text-xs text-white/60">{description}</span>
      </Label>
    </div>
  )
}
