"use client"

import { useState, useEffect } from "react"
import { <PERSON>, FileText, <PERSON><PERSON>hart2, Landmark, Trophy } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useWallet } from "@/components/wallet-provider"
import { useNotification } from "@/hooks/use-notification"
import { ProposalCard } from "@/components/governance/proposal-card"
import { VotingModal } from "@/components/governance/voting-modal"
import Link from "next/link"

type ProposalStatus = "active" | "passed" | "failed" | "pending"

type Proposal = {
  id: string
  title: string
  description: string
  creator: string
  createdAt: string
  endTime: string
  status: ProposalStatus
  votesFor: number
  votesAgainst: number
  quorum: number
}

const mockProposals: Proposal[] = [
  {
    id: "PROP-001",
    title: "Add Perpetual Trading",
    description: "Integrate perpetual trading functionality to allow leveraged trading of memecoin pairs.",
    creator: "0x1234...5678",
    createdAt: "2025-05-01",
    endTime: "2025-05-08",
    status: "active",
    votesFor: 1250000,
    votesAgainst: 450000,
    quorum: 2000000,
  },
  {
    id: "PROP-002",
    title: "Reduce Trading Fees",
    description: "Reduce trading fees from 0.5% to 0.3% to increase volume and attract more traders.",
    creator: "0x8765...4321",
    createdAt: "2025-04-28",
    endTime: "2025-05-05",
    status: "passed",
    votesFor: 1800000,
    votesAgainst: 200000,
    quorum: 1500000,
  },
  {
    id: "PROP-003",
    title: "Add New Token Pair",
    description: "Add SHIB/wDOGE as a new trading pair with initial liquidity incentives.",
    creator: "0x5678...1234",
    createdAt: "2025-04-25",
    endTime: "2025-05-02",
    status: "failed",
    votesFor: 800000,
    votesAgainst: 1200000,
    quorum: 1500000,
  },
  {
    id: "PROP-004",
    title: "Implement Token Burning Mechanism",
    description: "Implement a 0.1% token burn on each transaction to create deflationary pressure on $PAW.",
    creator: "0x4321...8765",
    createdAt: "2025-05-02",
    endTime: "2025-05-09",
    status: "pending",
    votesFor: 0,
    votesAgainst: 0,
    quorum: 2000000,
  },
]

export function GovernanceInterface() {
  const { isConnected } = useWallet()
  const { showNotification } = useNotification()
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(null)

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (selectedProposal) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = ""
    }

    return () => {
      document.body.style.overflow = ""
    }
  }, [selectedProposal])

  const formatVotes = (votes: number) => {
    if (votes >= 1000000) {
      return `${(votes / 1000000).toFixed(1)}M`
    } else if (votes >= 1000) {
      return `${(votes / 1000).toFixed(1)}K`
    }
    return votes.toString()
  }

  const calculateProgress = (votesFor: number, votesAgainst: number) => {
    const total = votesFor + votesAgainst
    if (total === 0) return 0
    return (votesFor / total) * 100
  }

  const handleVote = (vote: "for" | "against") => {
    if (!selectedProposal) return

    showNotification({
      title: "Vote Submitted",
      message: `You voted ${vote} the proposal "${selectedProposal.title}"`,
      type: "success",
      addToCenter: true,
    })

    setSelectedProposal(null)
  }

  return (
    <div className="grid gap-8 lg:grid-cols-3">
      <div className="lg:col-span-2">
        <Card className="glass-card border-white/5 doge-glow">
          <CardHeader>
            <CardTitle className="text-white">Governance Proposals</CardTitle>
            <CardDescription className="text-white/70">
              Vote on proposals with your $PAW tokens to shape the future of the platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <Link href="/docs/development-dao" className="flex items-center text-doge hover:underline">
                <FileText className="mr-2 h-4 w-4" />
                View Development DAO Documentation
              </Link>
            </div>
            <Tabs defaultValue="active" className="w-full">
              <TabsList className="grid w-full grid-cols-4 glass mb-6">
                <TabsTrigger value="active" className="data-[state=active]:text-doge data-[state=active]:bg-doge/10">
                  Active
                </TabsTrigger>
                <TabsTrigger
                  value="passed"
                  className="data-[state=active]:text-green-500 data-[state=active]:bg-green-500/10"
                >
                  Passed
                </TabsTrigger>
                <TabsTrigger
                  value="failed"
                  className="data-[state=active]:text-red-500 data-[state=active]:bg-red-500/10"
                >
                  Failed
                </TabsTrigger>
                <TabsTrigger value="pending" className="data-[state=active]:text-white data-[state=active]:bg-white/10">
                  Pending
                </TabsTrigger>
              </TabsList>

              {["active", "passed", "failed", "pending"].map((status) => (
                <TabsContent key={status} value={status} className="space-y-4">
                  {mockProposals
                    .filter((proposal) => proposal.status === status)
                    .map((proposal) => (
                      <ProposalCard
                        key={proposal.id}
                        id={proposal.id}
                        title={proposal.title}
                        description={proposal.description}
                        status={proposal.status as "active" | "passed" | "failed" | "pending"}
                        category="core-functionality"
                        votesFor={proposal.votesFor}
                        votesAgainst={proposal.votesAgainst}
                        quorum={proposal.quorum}
                        creator={proposal.creator}
                        createdAt={proposal.createdAt}
                        endTime={proposal.endTime}
                        onClick={() => setSelectedProposal(proposal)}
                      />
                    ))}
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button className="doge-button doge-shine">Create Proposal</Button>
          </CardFooter>
        </Card>
      </div>

      <div>
        <Card className="glass-card border-white/5 dogechain-glow sticky top-20">
          <CardHeader>
            <CardTitle className="text-white">Your Voting Power</CardTitle>
            <CardDescription className="text-white/70">Stake $PAW tokens to increase your voting power</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {isConnected ? (
              <>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">$PAW Balance</span>
                    <span className="text-white font-medium">10,000</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Staked $PAW</span>
                    <span className="text-doge font-medium doge-text-glow">5,000</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Voting Power</span>
                    <span className="text-dogechain font-medium dogechain-text-glow">5,000</span>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h4 className="text-sm font-medium text-white mb-2">Staking Rewards</h4>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs text-white/70">APR</span>
                    <span className="text-xs text-doge">12.5%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-white/70">Earned</span>
                    <span className="text-xs text-doge">+125 $PAW</span>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button className="flex-1 doge-button doge-shine">Stake</Button>
                  <Button className="flex-1 glass-button">Unstake</Button>
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center py-6 text-center">
                <Users className="h-12 w-12 text-white/20 mb-4" />
                <p className="text-white/70 mb-4">
                  Connect your wallet to view your voting power and participate in governance
                </p>
                <Button className="doge-button doge-shine">Connect Wallet</Button>
              </div>
            )}
          </CardContent>
        </Card>
        <div className="mt-6 space-y-4">
          <Link href="/governance/analytics" className="block">
            <Card className="glass-card border-white/5 hover:border-white/10 transition-colors">
              <CardContent className="flex items-center p-4">
                <div className="mr-4 rounded-full bg-white/5 p-2 text-white">
                  <BarChart2 className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-medium text-white">Real-time Analytics</h3>
                  <p className="text-sm text-white/60">View platform metrics and reports</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/governance/treasury" className="block">
            <Card className="glass-card border-white/5 hover:border-white/10 transition-colors">
              <CardContent className="flex items-center p-4">
                <div className="mr-4 rounded-full bg-white/5 p-2 text-white">
                  <Landmark className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-medium text-white">Treasury Management</h3>
                  <p className="text-sm text-white/60">Advanced treasury tools and models</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/governance/achievements" className="block">
            <Card className="glass-card border-white/5 hover:border-white/10 transition-colors">
              <CardContent className="flex items-center p-4">
                <div className="mr-4 rounded-full bg-white/5 p-2 text-white">
                  <Trophy className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-medium text-white">Governance Achievements</h3>
                  <p className="text-sm text-white/60">View your reputation and rewards</p>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>

      {selectedProposal && (
        <VotingModal
          id={selectedProposal.id}
          title={selectedProposal.title}
          description={selectedProposal.description}
          status={selectedProposal.status as "active" | "passed" | "failed" | "pending"}
          category="core-functionality"
          votesFor={selectedProposal.votesFor}
          votesAgainst={selectedProposal.votesAgainst}
          quorum={selectedProposal.quorum}
          creator={selectedProposal.creator}
          createdAt={selectedProposal.createdAt}
          endTime={selectedProposal.endTime}
          onClose={() => setSelectedProposal(null)}
          onVote={handleVote}
          userVotingPower={5000}
        />
      )}
    </div>
  )
}
