"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { useWallet } from '@/components/wallet-provider'
import { useRealtimeNotifications, useConnectionStatus } from '@/hooks/use-realtime-data'
import { NotificationUpdate } from '@/lib/websocket-manager'

interface RealtimeDataContextType {
  notifications: NotificationUpdate[]
  unreadCount: number
  connectionStatus: {
    price: string
    trade: string
    notification: string
  }
  isConnected: boolean
  markNotificationAsRead: (id: string) => void
  markAllNotificationsAsRead: () => void
}

const RealtimeDataContext = createContext<RealtimeDataContextType | null>(null)

interface RealtimeDataProviderProps {
  children: ReactNode
}

export function RealtimeDataProvider({ children }: RealtimeDataProviderProps) {
  const { address, isConnected: walletConnected } = useWallet()
  const [isInitialized, setIsInitialized] = useState(false)
  
  // Real-time notifications
  const {
    notifications,
    unreadCount,
    isConnected: notificationConnected,
    markAs<PERSON>ead,
    markAllAsRead,
  } = useRealtimeNotifications(address || undefined)
  
  // Connection status monitoring
  const connectionStatus = useConnectionStatus()
  
  // Overall connection status
  const isConnected = notificationConnected && connectionStatus.price === 'connected'

  useEffect(() => {
    // Initialize real-time connections
    setIsInitialized(true)
    
    // Show connection status in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Real-time data provider initialized', {
        walletConnected,
        notificationConnected,
        connectionStatus,
      })
    }
  }, [walletConnected, notificationConnected, connectionStatus])

  const value: RealtimeDataContextType = {
    notifications,
    unreadCount,
    connectionStatus,
    isConnected,
    markNotificationAsRead: markAsRead,
    markAllNotificationsAsRead: markAllAsRead,
  }

  return (
    <RealtimeDataContext.Provider value={value}>
      {children}
      {process.env.NODE_ENV === 'development' && <RealtimeDebugPanel />}
    </RealtimeDataContext.Provider>
  )
}

export function useRealtimeData() {
  const context = useContext(RealtimeDataContext)
  if (!context) {
    throw new Error('useRealtimeData must be used within a RealtimeDataProvider')
  }
  return context
}

// Debug panel for development
function RealtimeDebugPanel() {
  const [isOpen, setIsOpen] = useState(false)
  const { connectionStatus, notifications, unreadCount, isConnected } = useRealtimeData()

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-20 right-4 bg-blue-600 text-white px-3 py-2 rounded-lg text-xs z-50"
      >
        Real-time Debug
      </button>
    )
  }

  return (
    <div className="fixed bottom-20 right-4 bg-black/90 text-white p-4 rounded-lg text-xs z-50 max-w-sm">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-semibold">Real-time Debug</h3>
        <button
          onClick={() => setIsOpen(false)}
          className="text-white/60 hover:text-white"
        >
          ×
        </button>
      </div>

      <div className="space-y-3">
        <div>
          <h4 className="font-medium mb-1">Connection Status</h4>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Overall:</span>
              <span className={isConnected ? 'text-green-400' : 'text-red-400'}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Price:</span>
              <span className={connectionStatus.price === 'connected' ? 'text-green-400' : 'text-red-400'}>
                {connectionStatus.price}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Trade:</span>
              <span className={connectionStatus.trade === 'connected' ? 'text-green-400' : 'text-red-400'}>
                {connectionStatus.trade}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Notifications:</span>
              <span className={connectionStatus.notification === 'connected' ? 'text-green-400' : 'text-red-400'}>
                {connectionStatus.notification}
              </span>
            </div>
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-1">Notifications</h4>
          <div className="flex justify-between">
            <span>Total:</span>
            <span>{notifications.length}</span>
          </div>
          <div className="flex justify-between">
            <span>Unread:</span>
            <span className="text-yellow-400">{unreadCount}</span>
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-1">Recent Notifications</h4>
          <div className="max-h-32 overflow-y-auto space-y-1">
            {notifications.slice(0, 5).map((notification) => (
              <div key={notification.id} className="text-xs p-2 bg-white/10 rounded">
                <div className="font-medium">{notification.title}</div>
                <div className="text-white/70">{notification.message}</div>
                <div className="text-white/50">
                  {new Date(notification.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Real-time price ticker component
export function PriceTicker({ symbols }: { symbols: string[] }) {
  const [prices, setPrices] = useState<Map<string, any>>(new Map())
  
  useEffect(() => {
    // This would use the useMarketData hook
    // For now, simulate with mock data
    const interval = setInterval(() => {
      symbols.forEach(symbol => {
        setPrices(prev => {
          const currentPrice = prev.get(symbol)?.price || 0.000001
          const change = (Math.random() - 0.5) * 0.00001
          const newPrice = Math.max(currentPrice + change, 0.0000001)
          
          return new Map(prev.set(symbol, {
            price: newPrice,
            change24h: (Math.random() - 0.5) * 20,
            timestamp: Date.now(),
          }))
        })
      })
    }, 2000)

    return () => clearInterval(interval)
  }, [symbols])

  return (
    <div className="flex gap-4 overflow-x-auto py-2">
      {symbols.map(symbol => {
        const data = prices.get(symbol)
        if (!data) return null

        return (
          <div key={symbol} className="flex items-center gap-2 whitespace-nowrap">
            <span className="font-medium">{symbol}</span>
            <span className="text-sm">${data.price.toFixed(8)}</span>
            <span className={`text-xs ${data.change24h >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {data.change24h >= 0 ? '+' : ''}{data.change24h.toFixed(2)}%
            </span>
          </div>
        )
      })}
    </div>
  )
}

// Real-time notification badge
export function NotificationBadge() {
  const { unreadCount } = useRealtimeData()

  if (unreadCount === 0) return null

  return (
    <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
      {unreadCount > 99 ? '99+' : unreadCount}
    </div>
  )
}

// Connection status indicator
export function ConnectionStatusIndicator() {
  const { isConnected, connectionStatus } = useRealtimeData()

  return (
    <div className="flex items-center gap-2">
      <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
      <span className="text-xs text-muted-foreground">
        {isConnected ? 'Live' : 'Offline'}
      </span>
    </div>
  )
}

// Real-time trade feed component
export function TradeFeed({ symbol, limit = 10 }: { symbol: string; limit?: number }) {
  const [trades, setTrades] = useState<any[]>([])

  useEffect(() => {
    // This would use the useRealtimeTrades hook
    // For now, simulate with mock data
    const interval = setInterval(() => {
      const newTrade = {
        id: Date.now(),
        price: 0.000001 + Math.random() * 0.000001,
        amount: Math.random() * 1000000,
        side: Math.random() > 0.5 ? 'buy' : 'sell',
        timestamp: Date.now(),
      }

      setTrades(prev => [newTrade, ...prev].slice(0, limit))
    }, 3000)

    return () => clearInterval(interval)
  }, [symbol, limit])

  return (
    <div className="space-y-1">
      <h3 className="text-sm font-medium">Recent Trades</h3>
      <div className="space-y-1 max-h-48 overflow-y-auto">
        {trades.map(trade => (
          <div key={trade.id} className="flex justify-between text-xs">
            <span className={trade.side === 'buy' ? 'text-green-400' : 'text-red-400'}>
              {trade.side.toUpperCase()}
            </span>
            <span>${trade.price.toFixed(8)}</span>
            <span>{trade.amount.toFixed(0)}</span>
            <span className="text-muted-foreground">
              {new Date(trade.timestamp).toLocaleTimeString()}
            </span>
          </div>
        ))}
      </div>
    </div>
  )
}
