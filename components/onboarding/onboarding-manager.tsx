"use client"

import { useEffect, useState } from 'react'
import { OnboardingFlow, useOnboarding } from './onboarding-flow'
import { InteractiveTutorial, tradingTutorial, launchTutorial, socialTutorial } from './interactive-tutorial'
import { UserGuidance, SmartGuidance } from './user-guidance'
import { usePathname } from 'next/navigation'

export function OnboardingManager() {
  const pathname = usePathname()
  const {
    showOnboarding,
    hasCompletedOnboarding,
    completeOnboarding,
    closeOnboarding,
  } = useOnboarding()
  
  const [activeTutorial, setActiveTutorial] = useState<string | null>(null)
  const [tutorialSteps, setTutorialSteps] = useState<any[]>([])

  // Auto-start tutorials based on route
  useEffect(() => {
    if (!hasCompletedOnboarding) return

    const tutorialKey = `tutorial-${pathname}-completed`
    const hasCompletedTutorial = localStorage.getItem(tutorialKey) === 'true'
    
    if (!hasCompletedTutorial) {
      // Delay tutorial start to let page load
      const timer = setTimeout(() => {
        switch (pathname) {
          case '/trade':
            setTutorialSteps(tradingTutorial)
            setActiveTutorial('trading')
            break
          case '/launch':
            setTutorialSteps(launchTutorial)
            setActiveTutorial('launch')
            break
          case '/social':
            setTutorialSteps(socialTutorial)
            setActiveTutorial('social')
            break
        }
      }, 2000)
      
      return () => clearTimeout(timer)
    }
  }, [pathname, hasCompletedOnboarding])

  const handleTutorialComplete = () => {
    if (activeTutorial) {
      localStorage.setItem(`tutorial-${pathname}-completed`, 'true')
    }
    setActiveTutorial(null)
    setTutorialSteps([])
  }

  const handleTutorialClose = () => {
    setActiveTutorial(null)
    setTutorialSteps([])
  }

  return (
    <>
      {/* Main onboarding flow */}
      <OnboardingFlow
        open={showOnboarding}
        onComplete={completeOnboarding}
        onClose={closeOnboarding}
      />
      
      {/* Interactive tutorials */}
      {activeTutorial && tutorialSteps.length > 0 && (
        <InteractiveTutorial
          steps={tutorialSteps}
          onComplete={handleTutorialComplete}
          onClose={handleTutorialClose}
          isActive={true}
        />
      )}

      {/* Contextual user guidance */}
      <UserGuidance />

      {/* Smart guidance based on user state */}
      <SmartGuidance />
    </>
  )
}
