"use client"

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { X, ArrowRight, Target, CheckCircle } from 'lucide-react'

interface TutorialStep {
  id: string
  title: string
  description: string
  target: string // CSS selector
  position: 'top' | 'bottom' | 'left' | 'right'
  action?: 'click' | 'hover' | 'input'
  actionText?: string
  optional?: boolean
}

interface InteractiveTutorialProps {
  steps: TutorialStep[]
  onComplete: () => void
  onClose: () => void
  isActive: boolean
}

export function InteractiveTutorial({ steps, onComplete, onClose, isActive }: InteractiveTutorialProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set())
  const [highlightedElement, setHighlightedElement] = useState<Element | null>(null)
  const overlayRef = useRef<HTMLDivElement>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)

  const step = steps[currentStep]

  useEffect(() => {
    if (!isActive || !step) return

    const targetElement = document.querySelector(step.target)
    if (targetElement) {
      setHighlightedElement(targetElement)
      scrollToElement(targetElement)
      positionTooltip(targetElement)
    }

    // Listen for the required action
    if (step.action) {
      const handleAction = () => {
        handleStepComplete()
      }

      if (step.action === 'click') {
        targetElement?.addEventListener('click', handleAction)
      } else if (step.action === 'hover') {
        targetElement?.addEventListener('mouseenter', handleAction)
      } else if (step.action === 'input') {
        targetElement?.addEventListener('input', handleAction)
      }

      return () => {
        if (step.action === 'click') {
          targetElement?.removeEventListener('click', handleAction)
        } else if (step.action === 'hover') {
          targetElement?.removeEventListener('mouseenter', handleAction)
        } else if (step.action === 'input') {
          targetElement?.removeEventListener('input', handleAction)
        }
      }
    }
  }, [currentStep, isActive, step])

  const scrollToElement = (element: Element) => {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'center',
    })
  }

  const positionTooltip = (element: Element) => {
    if (!tooltipRef.current) return

    const rect = element.getBoundingClientRect()
    const tooltip = tooltipRef.current
    const tooltipRect = tooltip.getBoundingClientRect()

    let top = 0
    let left = 0

    switch (step.position) {
      case 'top':
        top = rect.top - tooltipRect.height - 10
        left = rect.left + (rect.width - tooltipRect.width) / 2
        break
      case 'bottom':
        top = rect.bottom + 10
        left = rect.left + (rect.width - tooltipRect.width) / 2
        break
      case 'left':
        top = rect.top + (rect.height - tooltipRect.height) / 2
        left = rect.left - tooltipRect.width - 10
        break
      case 'right':
        top = rect.top + (rect.height - tooltipRect.height) / 2
        left = rect.right + 10
        break
    }

    // Ensure tooltip stays within viewport
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    if (left < 10) left = 10
    if (left + tooltipRect.width > viewportWidth - 10) {
      left = viewportWidth - tooltipRect.width - 10
    }
    if (top < 10) top = 10
    if (top + tooltipRect.height > viewportHeight - 10) {
      top = viewportHeight - tooltipRect.height - 10
    }

    tooltip.style.top = `${top}px`
    tooltip.style.left = `${left}px`
  }

  const handleStepComplete = () => {
    setCompletedSteps(prev => new Set([...prev, step.id]))
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1)
    } else {
      handleComplete()
    }
  }

  const handleSkip = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1)
    } else {
      handleComplete()
    }
  }

  const handleComplete = () => {
    setHighlightedElement(null)
    onComplete()
  }

  const getHighlightStyle = (element: Element) => {
    const rect = element.getBoundingClientRect()
    return {
      top: rect.top - 4,
      left: rect.left - 4,
      width: rect.width + 8,
      height: rect.height + 8,
    }
  }

  if (!isActive || !step) return null

  return (
    <>
      {/* Overlay */}
      <div
        ref={overlayRef}
        className="fixed inset-0 bg-black/50 z-50 pointer-events-none"
        style={{
          background: highlightedElement
            ? `radial-gradient(circle at ${
                highlightedElement.getBoundingClientRect().left +
                highlightedElement.getBoundingClientRect().width / 2
              }px ${
                highlightedElement.getBoundingClientRect().top +
                highlightedElement.getBoundingClientRect().height / 2
              }px, transparent ${Math.max(
                highlightedElement.getBoundingClientRect().width,
                highlightedElement.getBoundingClientRect().height
              ) / 2 + 20}px, rgba(0,0,0,0.7) ${Math.max(
                highlightedElement.getBoundingClientRect().width,
                highlightedElement.getBoundingClientRect().height
              ) / 2 + 21}px)`
            : 'rgba(0,0,0,0.7)',
        }}
      />

      {/* Highlight border */}
      {highlightedElement && (
        <div
          className="fixed border-2 border-doge rounded-lg pointer-events-none z-50 transition-all duration-300"
          style={getHighlightStyle(highlightedElement)}
        />
      )}

      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className="fixed z-50 pointer-events-auto"
        style={{ top: 0, left: 0 }}
      >
        <Card className="max-w-sm shadow-lg">
          <CardContent className="p-4 space-y-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {currentStep + 1}/{steps.length}
                </Badge>
                <Target className="h-4 w-4 text-doge" />
              </div>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div>
              <h3 className="font-semibold mb-2">{step.title}</h3>
              <p className="text-sm text-muted-foreground">{step.description}</p>
            </div>

            {step.action && (
              <div className="p-3 bg-doge/10 rounded-lg">
                <p className="text-sm font-medium text-doge">
                  {step.actionText || `${step.action} the highlighted element`}
                </p>
              </div>
            )}

            <div className="flex justify-between">
              <div className="flex gap-2">
                {step.optional && (
                  <Button variant="ghost" size="sm" onClick={handleSkip}>
                    Skip
                  </Button>
                )}
              </div>
              
              {!step.action && (
                <Button size="sm" onClick={handleStepComplete} className="gap-1">
                  {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
                  <ArrowRight className="h-3 w-3" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}

// Predefined tutorials
export const tradingTutorial: TutorialStep[] = [
  {
    id: 'welcome-trading',
    title: 'Welcome to Trading',
    description: 'Let\'s learn how to swap tokens on PawPumps! This tutorial will guide you through your first trade.',
    target: '[data-testid="trading-interface"]',
    position: 'bottom',
  },
  {
    id: 'select-from-token',
    title: 'Select From Token',
    description: 'Choose the token you want to swap from. Popular options include DOGE, SHIB, and PEPE.',
    target: '[data-testid="from-token-select"]',
    position: 'bottom',
    action: 'click',
    actionText: 'Click to select a token',
  },
  {
    id: 'enter-amount',
    title: 'Enter Amount',
    description: 'Type the amount you want to swap',
    target: '[data-testid="from-amount-input"]',
    position: 'bottom',
    action: 'input',
    actionText: 'Enter an amount to continue',
  },
  {
    id: 'select-to-token',
    title: 'Select To Token',
    description: 'Choose the token you want to receive',
    target: '[data-testid="to-token-select"]',
    position: 'bottom',
    action: 'click',
    actionText: 'Click to select the destination token',
  },
  {
    id: 'review-swap',
    title: 'Review Swap Details',
    description: 'Check the exchange rate and fees before confirming',
    target: '[data-testid="swap-details"]',
    position: 'top',
  },
  {
    id: 'confirm-swap',
    title: 'Confirm Swap',
    description: 'Click the swap button to execute the trade. You\'ll need to confirm the transaction in your wallet.',
    target: '[data-testid="swap-button"]',
    position: 'top',
    action: 'click',
    actionText: 'Click to confirm the swap',
  },
  {
    id: 'trading-complete',
    title: 'Trading Complete!',
    description: 'Congratulations! You\'ve completed your first trade. You can now explore advanced features like limit orders and portfolio tracking.',
    target: '[data-testid="trading-interface"]',
    position: 'top',
  },
]

export const launchTutorial: TutorialStep[] = [
  {
    id: 'token-name',
    title: 'Token Name',
    description: 'Enter a catchy name for your memecoin',
    target: '[data-testid="token-name-input"]',
    position: 'bottom',
    action: 'input',
    actionText: 'Enter your token name',
  },
  {
    id: 'token-symbol',
    title: 'Token Symbol',
    description: 'Create a unique symbol (2-6 characters)',
    target: '[data-testid="token-symbol-input"]',
    position: 'bottom',
    action: 'input',
    actionText: 'Enter your token symbol',
  },
  {
    id: 'total-supply',
    title: 'Total Supply',
    description: 'Set the total number of tokens to create',
    target: '[data-testid="total-supply-input"]',
    position: 'bottom',
    action: 'input',
    actionText: 'Enter the total supply',
  },
  {
    id: 'upload-image',
    title: 'Upload Image',
    description: 'Add an image for your token (optional)',
    target: '[data-testid="image-upload"]',
    position: 'bottom',
    optional: true,
  },
  {
    id: 'launch-token',
    title: 'Launch Token',
    description: 'Review and launch your memecoin',
    target: '[data-testid="launch-button"]',
    position: 'top',
    action: 'click',
    actionText: 'Click to launch your token',
  },
]

export const socialTutorial: TutorialStep[] = [
  {
    id: 'create-post',
    title: 'Create Post',
    description: 'Share your thoughts with the community',
    target: '[data-testid="create-post-button"]',
    position: 'bottom',
    action: 'click',
    actionText: 'Click to create a new post',
  },
  {
    id: 'write-content',
    title: 'Write Content',
    description: 'Share your market insights or thoughts',
    target: '[data-testid="post-content-input"]',
    position: 'bottom',
    action: 'input',
    actionText: 'Write your post content',
  },
  {
    id: 'add-sentiment',
    title: 'Add Sentiment',
    description: 'Tag your post with market sentiment',
    target: '[data-testid="sentiment-select"]',
    position: 'bottom',
    optional: true,
  },
  {
    id: 'publish-post',
    title: 'Publish Post',
    description: 'Share your post with the community',
    target: '[data-testid="publish-button"]',
    position: 'top',
    action: 'click',
    actionText: 'Click to publish your post',
  },
]
