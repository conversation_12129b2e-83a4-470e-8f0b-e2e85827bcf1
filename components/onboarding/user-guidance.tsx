"use client"

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { usePathname } from 'next/navigation'
import { useWallet } from '@/components/wallet-provider'
import { 
  Lightbulb, 
  X, 
  ChevronRight, 
  ChevronLeft,
  Target,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react'

interface GuidanceTip {
  id: string
  title: string
  description: string
  type: 'tip' | 'warning' | 'info' | 'success'
  target?: string
  position?: 'top' | 'bottom' | 'left' | 'right'
  condition?: () => boolean
  action?: {
    text: string
    onClick: () => void
  }
  dismissible?: boolean
  priority: number
}

interface ContextualGuidance {
  route: string
  tips: GuidanceTip[]
}

const guidanceData: ContextualGuidance[] = [
  {
    route: '/trade',
    tips: [
      {
        id: 'first-trade-tip',
        title: 'Start Small',
        description: 'For your first trade, start with a small amount to get familiar with the process.',
        type: 'tip',
        target: '[data-testid="from-amount-input"]',
        position: 'bottom',
        condition: () => {
          const tradeCount = localStorage.getItem('trade-count')
          return !tradeCount || parseInt(tradeCount) < 3
        },
        priority: 1,
        dismissible: true,
      },
      {
        id: 'slippage-warning',
        title: 'Check Slippage',
        description: 'High slippage can result in unexpected prices. Consider adjusting your slippage tolerance.',
        type: 'warning',
        target: '[data-testid="slippage-settings"]',
        position: 'top',
        condition: () => {
          // This would check if slippage is high
          return true
        },
        priority: 2,
        dismissible: true,
      },
      {
        id: 'gas-optimization',
        title: 'Optimize Gas Fees',
        description: 'Trade during off-peak hours to save on gas fees. Current network activity is moderate.',
        type: 'info',
        priority: 3,
        dismissible: true,
      },
    ],
  },
  {
    route: '/launch',
    tips: [
      {
        id: 'token-name-tip',
        title: 'Choose a Memorable Name',
        description: 'Pick a catchy, unique name that represents your token\'s theme or community.',
        type: 'tip',
        target: '[data-testid="token-name-input"]',
        position: 'bottom',
        priority: 1,
        dismissible: true,
      },
      {
        id: 'supply-warning',
        title: 'Consider Token Supply',
        description: 'Total supply affects token economics. Higher supply = lower individual token value.',
        type: 'warning',
        target: '[data-testid="total-supply-input"]',
        position: 'bottom',
        priority: 2,
        dismissible: true,
      },
      {
        id: 'launch-cost-info',
        title: 'Launch Costs',
        description: 'Token deployment costs ~100 DOGE plus gas fees. Make sure you have enough balance.',
        type: 'info',
        priority: 3,
        dismissible: true,
      },
    ],
  },
  {
    route: '/social',
    tips: [
      {
        id: 'first-post-tip',
        title: 'Introduce Yourself',
        description: 'Share your trading experience and what tokens you\'re interested in to connect with the community.',
        type: 'tip',
        target: '[data-testid="create-post-button"]',
        position: 'bottom',
        condition: () => {
          const postCount = localStorage.getItem('post-count')
          return !postCount || parseInt(postCount) === 0
        },
        priority: 1,
        dismissible: true,
      },
      {
        id: 'sentiment-tip',
        title: 'Add Market Sentiment',
        description: 'Tag your posts with bullish/bearish sentiment to help others understand your market view.',
        type: 'tip',
        target: '[data-testid="sentiment-select"]',
        position: 'bottom',
        priority: 2,
        dismissible: true,
      },
    ],
  },
  {
    route: '/governance',
    tips: [
      {
        id: 'voting-power-info',
        title: 'Voting Power',
        description: 'Your voting power is based on your PAWS token holdings. Stake more to increase influence.',
        type: 'info',
        priority: 1,
        dismissible: true,
      },
      {
        id: 'proposal-deadline',
        title: 'Voting Deadline',
        description: 'Don\'t forget to vote! Proposals close in 24 hours.',
        type: 'warning',
        condition: () => {
          // This would check if there are active proposals
          return true
        },
        priority: 2,
        dismissible: true,
      },
    ],
  },
]

export function UserGuidance() {
  const pathname = usePathname()
  const { isConnected, address } = useWallet()
  const [activeTips, setActiveTips] = useState<GuidanceTip[]>([])
  const [currentTipIndex, setCurrentTipIndex] = useState(0)
  const [dismissedTips, setDismissedTips] = useState<Set<string>>(new Set())
  const [showGuidance, setShowGuidance] = useState(false)
  const tooltipRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Load dismissed tips from localStorage
    const dismissed = localStorage.getItem('dismissed-tips')
    if (dismissed) {
      setDismissedTips(new Set(JSON.parse(dismissed)))
    }
  }, [])

  useEffect(() => {
    // Get tips for current route
    const routeGuidance = guidanceData.find(g => g.route === pathname)
    if (!routeGuidance) {
      setActiveTips([])
      setShowGuidance(false)
      return
    }

    // Filter tips based on conditions and dismissal status
    const validTips = routeGuidance.tips
      .filter(tip => {
        if (dismissedTips.has(tip.id)) return false
        if (tip.condition && !tip.condition()) return false
        return true
      })
      .sort((a, b) => a.priority - b.priority)

    setActiveTips(validTips)
    setCurrentTipIndex(0)
    setShowGuidance(validTips.length > 0)

    // Position tooltip for first tip
    if (validTips.length > 0) {
      setTimeout(() => positionTooltip(validTips[0]), 100)
    }
  }, [pathname, dismissedTips, isConnected])

  const positionTooltip = (tip: GuidanceTip) => {
    if (!tip.target || !tooltipRef.current) return

    const targetElement = document.querySelector(tip.target)
    if (!targetElement) return

    const rect = targetElement.getBoundingClientRect()
    const tooltip = tooltipRef.current
    const tooltipRect = tooltip.getBoundingClientRect()

    let top = 0
    let left = 0

    switch (tip.position || 'bottom') {
      case 'top':
        top = rect.top - tooltipRect.height - 10
        left = rect.left + (rect.width - tooltipRect.width) / 2
        break
      case 'bottom':
        top = rect.bottom + 10
        left = rect.left + (rect.width - tooltipRect.width) / 2
        break
      case 'left':
        top = rect.top + (rect.height - tooltipRect.height) / 2
        left = rect.left - tooltipRect.width - 10
        break
      case 'right':
        top = rect.top + (rect.height - tooltipRect.height) / 2
        left = rect.right + 10
        break
    }

    // Keep tooltip within viewport
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    if (left < 10) left = 10
    if (left + tooltipRect.width > viewportWidth - 10) {
      left = viewportWidth - tooltipRect.width - 10
    }
    if (top < 10) top = 10
    if (top + tooltipRect.height > viewportHeight - 10) {
      top = viewportHeight - tooltipRect.height - 10
    }

    tooltip.style.top = `${top}px`
    tooltip.style.left = `${left}px`
  }

  const dismissTip = (tipId: string) => {
    const newDismissed = new Set([...dismissedTips, tipId])
    setDismissedTips(newDismissed)
    localStorage.setItem('dismissed-tips', JSON.stringify([...newDismissed]))

    // Move to next tip or hide guidance
    if (currentTipIndex < activeTips.length - 1) {
      setCurrentTipIndex(prev => prev + 1)
      setTimeout(() => positionTooltip(activeTips[currentTipIndex + 1]), 100)
    } else {
      setShowGuidance(false)
    }
  }

  const nextTip = () => {
    if (currentTipIndex < activeTips.length - 1) {
      setCurrentTipIndex(prev => prev + 1)
      setTimeout(() => positionTooltip(activeTips[currentTipIndex + 1]), 100)
    }
  }

  const prevTip = () => {
    if (currentTipIndex > 0) {
      setCurrentTipIndex(prev => prev - 1)
      setTimeout(() => positionTooltip(activeTips[currentTipIndex - 1]), 100)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'tip':
        return <Lightbulb className="h-4 w-4 text-yellow-500" />
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-orange-500" />
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'tip':
        return 'border-yellow-500/20 bg-yellow-500/5'
      case 'warning':
        return 'border-orange-500/20 bg-orange-500/5'
      case 'info':
        return 'border-blue-500/20 bg-blue-500/5'
      case 'success':
        return 'border-green-500/20 bg-green-500/5'
      default:
        return 'border-gray-500/20 bg-gray-500/5'
    }
  }

  if (!showGuidance || activeTips.length === 0) return null

  const currentTip = activeTips[currentTipIndex]

  return (
    <>
      {/* Highlight target element */}
      {currentTip.target && (
        <style jsx global>{`
          ${currentTip.target} {
            position: relative;
            z-index: 40;
          }
          ${currentTip.target}::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border: 2px solid #D4AF37;
            border-radius: 8px;
            pointer-events: none;
            animation: pulse 2s infinite;
          }
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }
        `}</style>
      )}

      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className="fixed z-50 pointer-events-auto"
        style={{ top: 0, left: 0 }}
      >
        <Card className={`max-w-sm shadow-lg border-2 ${getTypeColor(currentTip.type)}`}>
          <CardContent className="p-4 space-y-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                {getTypeIcon(currentTip.type)}
                <Badge variant="secondary" className="text-xs">
                  {currentTipIndex + 1}/{activeTips.length}
                </Badge>
              </div>
              {currentTip.dismissible && (
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6"
                  onClick={() => dismissTip(currentTip.id)}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>

            <div>
              <h3 className="font-semibold mb-1">{currentTip.title}</h3>
              <p className="text-sm text-muted-foreground">{currentTip.description}</p>
            </div>

            {currentTip.action && (
              <Button 
                size="sm" 
                onClick={currentTip.action.onClick}
                className="w-full"
              >
                {currentTip.action.text}
              </Button>
            )}

            <div className="flex justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={prevTip}
                disabled={currentTipIndex === 0}
                className="gap-1"
              >
                <ChevronLeft className="h-3 w-3" />
                Previous
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={nextTip}
                disabled={currentTipIndex === activeTips.length - 1}
                className="gap-1"
              >
                Next
                <ChevronRight className="h-3 w-3" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}

// Smart guidance that appears based on user actions
export function SmartGuidance() {
  const { isConnected, balance } = useWallet()
  const [smartTips, setSmartTips] = useState<GuidanceTip[]>([])

  useEffect(() => {
    const tips: GuidanceTip[] = []

    // Wallet not connected
    if (!isConnected) {
      tips.push({
        id: 'connect-wallet',
        title: 'Connect Your Wallet',
        description: 'Connect a wallet to start trading and accessing all features.',
        type: 'info',
        priority: 1,
        action: {
          text: 'Connect Wallet',
          onClick: () => {
            // Trigger wallet connection
            (document.querySelector('[data-testid="connect-wallet-button"]') as HTMLElement)?.click()
          }
        }
      })
    }

    // Low balance warning
    if (isConnected && balance && parseFloat(balance) < 0.1) {
      tips.push({
        id: 'low-balance',
        title: 'Low Balance',
        description: 'You have a low DOGE balance. You may need more for gas fees and trading.',
        type: 'warning',
        priority: 2,
        dismissible: true,
      })
    }

    setSmartTips(tips)
  }, [isConnected, balance])

  if (smartTips.length === 0) return null

  return (
    <div className="fixed bottom-20 right-4 z-40 space-y-2">
      {smartTips.map(tip => (
        <Card key={tip.id} className={`max-w-sm ${getTypeColor(tip.type)}`}>
          <CardContent className="p-3">
            <div className="flex items-start gap-2">
              {getTypeIcon(tip.type)}
              <div className="flex-1">
                <h4 className="font-medium text-sm">{tip.title}</h4>
                <p className="text-xs text-muted-foreground">{tip.description}</p>
                {tip.action && (
                  <Button size="sm" className="mt-2 w-full" onClick={tip.action.onClick}>
                    {tip.action.text}
                  </Button>
                )}
              </div>
              {tip.dismissible && (
                <Button variant="ghost" size="icon" className="h-6 w-6">
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

// Helper function for type colors (used in both components)
function getTypeColor(type: string) {
  switch (type) {
    case 'tip':
      return 'border-yellow-500/20 bg-yellow-500/5'
    case 'warning':
      return 'border-orange-500/20 bg-orange-500/5'
    case 'info':
      return 'border-blue-500/20 bg-blue-500/5'
    case 'success':
      return 'border-green-500/20 bg-green-500/5'
    default:
      return 'border-gray-500/20 bg-gray-500/5'
  }
}

function getTypeIcon(type: string) {
  switch (type) {
    case 'tip':
      return <Lightbulb className="h-4 w-4 text-yellow-500" />
    case 'warning':
      return <AlertCircle className="h-4 w-4 text-orange-500" />
    case 'info':
      return <Info className="h-4 w-4 text-blue-500" />
    case 'success':
      return <CheckCircle className="h-4 w-4 text-green-500" />
    default:
      return <Info className="h-4 w-4" />
  }
}
