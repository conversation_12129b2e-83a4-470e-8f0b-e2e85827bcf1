"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  HelpCircle, 
  Search, 
  BookOpen, 
  Video, 
  MessageCircle, 
  ExternalLink,
  ChevronRight,
  Star,
  Clock
} from 'lucide-react'

interface HelpArticle {
  id: string
  title: string
  description: string
  category: string
  content: string
  tags: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  readTime: number
  helpful: number
  lastUpdated: string
}

interface FAQ {
  id: string
  question: string
  answer: string
  category: string
  helpful: number
}

const helpArticles: HelpArticle[] = [
  {
    id: 'getting-started',
    title: 'Getting Started with PawPumps',
    description: 'Learn the basics of using PawPumps for memecoin trading',
    category: 'basics',
    content: `
# Getting Started with PawPumps

Welcome to PawPumps, the premier memecoin launchpad and DEX on Dogechain!

## What is PawPumps?

PawPumps is a decentralized platform that allows you to:
- Trade memecoins with low fees
- Launch your own memecoin
- Connect with the memecoin community

## First Steps

1. **Connect Your Wallet**: Use MetaMask, WalletConnect, or other supported wallets
2. **Get DOGE**: You'll need DOGE tokens for gas fees and trading
3. **Start Trading**: Swap between different memecoins
4. **Join the Community**: Follow other traders and share your insights

## Safety Tips

- Always verify contract addresses
- Start with small amounts
- Never share your private keys
- Use hardware wallets for large amounts
    `,
    tags: ['basics', 'wallet', 'trading'],
    difficulty: 'beginner',
    readTime: 3,
    helpful: 45,
    lastUpdated: '2024-01-15',
  },
  {
    id: 'how-to-trade',
    title: 'How to Trade Memecoins',
    description: 'Step-by-step guide to swapping tokens on PawPumps',
    category: 'trading',
    content: `
# How to Trade Memecoins

Trading on PawPumps is simple and secure. Follow these steps:

## Step 1: Select Tokens
- Choose the token you want to swap from
- Select the token you want to receive

## Step 2: Enter Amount
- Type the amount you want to trade
- Review the exchange rate and slippage

## Step 3: Confirm Trade
- Check the transaction details
- Confirm in your wallet
- Wait for confirmation

## Advanced Features

### Slippage Tolerance
Adjust slippage tolerance based on market conditions:
- Low volatility: 0.5-1%
- High volatility: 2-5%

### Gas Optimization
- Trade during off-peak hours
- Use appropriate gas settings
- Consider batching transactions
    `,
    tags: ['trading', 'swap', 'slippage'],
    difficulty: 'beginner',
    readTime: 5,
    helpful: 38,
    lastUpdated: '2024-01-14',
  },
  {
    id: 'launch-token',
    title: 'How to Launch Your Memecoin',
    description: 'Complete guide to creating and launching your own token',
    category: 'launch',
    content: `
# How to Launch Your Memecoin

Create your own memecoin in minutes with PawPumps!

## Requirements

- Connected wallet with DOGE for gas fees
- Token name and symbol
- Total supply amount
- Optional: Token image and description

## Launch Process

### 1. Token Details
- **Name**: Choose a memorable name
- **Symbol**: 2-6 character ticker
- **Supply**: Total number of tokens

### 2. Customization
- Upload token image
- Write description
- Set initial liquidity

### 3. Deploy
- Review all details
- Pay deployment fee
- Confirm transaction

## Post-Launch

- Add liquidity to enable trading
- Share on social media
- Engage with the community
- Monitor token performance

## Best Practices

- Choose unique names and symbols
- Create engaging visuals
- Build community before launch
- Provide clear tokenomics
    `,
    tags: ['launch', 'token', 'deployment'],
    difficulty: 'intermediate',
    readTime: 7,
    helpful: 52,
    lastUpdated: '2024-01-13',
  },
]

const faqs: FAQ[] = [
  {
    id: 'what-is-pawpumps',
    question: 'What is PawPumps?',
    answer: 'PawPumps is a decentralized memecoin launchpad and DEX built on Dogechain. It allows users to create, trade, and discover memecoins in a secure and user-friendly environment.',
    category: 'general',
    helpful: 23,
  },
  {
    id: 'supported-wallets',
    question: 'Which wallets are supported?',
    answer: 'PawPumps supports MetaMask, WalletConnect, Coinbase Wallet, and Trust Wallet. Make sure your wallet is connected to the Dogechain network.',
    category: 'wallet',
    helpful: 18,
  },
  {
    id: 'gas-fees',
    question: 'What are the gas fees?',
    answer: 'Gas fees on Dogechain are typically very low, usually less than $0.01 per transaction. Fees may vary based on network congestion.',
    category: 'fees',
    helpful: 31,
  },
  {
    id: 'token-launch-cost',
    question: 'How much does it cost to launch a token?',
    answer: 'Token deployment costs approximately 100 DOGE plus gas fees. This covers the smart contract deployment and initial setup.',
    category: 'launch',
    helpful: 27,
  },
]

export function HelpSystem() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [filteredArticles, setFilteredArticles] = useState(helpArticles)
  const [filteredFAQs, setFilteredFAQs] = useState(faqs)

  useEffect(() => {
    let filtered = helpArticles

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(article => article.category === selectedCategory)
    }

    if (searchQuery) {
      filtered = filtered.filter(article =>
        article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        article.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    setFilteredArticles(filtered)

    // Filter FAQs
    let filteredFAQList = faqs
    if (searchQuery) {
      filteredFAQList = filteredFAQList.filter(faq =>
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }
    setFilteredFAQs(filteredFAQList)
  }, [searchQuery, selectedCategory])

  const categories = [
    { id: 'all', label: 'All' },
    { id: 'basics', label: 'Basics' },
    { id: 'trading', label: 'Trading' },
    { id: 'launch', label: 'Launch' },
    { id: 'wallet', label: 'Wallet' },
  ]

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-500'
      case 'intermediate':
        return 'bg-yellow-500'
      case 'advanced':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" className="fixed bottom-4 left-4 z-40">
          <HelpCircle className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Help Center
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search help articles and FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Categories */}
          <div className="flex gap-2 overflow-x-auto">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="whitespace-nowrap"
              >
                {category.label}
              </Button>
            ))}
          </div>

          {/* Content */}
          <Tabs defaultValue="articles" className="h-[60vh]">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="articles">Articles</TabsTrigger>
              <TabsTrigger value="faqs">FAQs</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
            </TabsList>

            <TabsContent value="articles" className="h-full overflow-y-auto">
              <div className="space-y-4">
                {filteredArticles.map((article) => (
                  <Card key={article.id} className="cursor-pointer hover:bg-accent/50">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <CardTitle className="text-lg">{article.title}</CardTitle>
                          <CardDescription>{article.description}</CardDescription>
                        </div>
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <div className={`w-2 h-2 rounded-full ${getDifficultyColor(article.difficulty)}`} />
                        <span className="capitalize">{article.difficulty}</span>
                        <span>•</span>
                        <Clock className="h-3 w-3" />
                        <span>{article.readTime} min read</span>
                        <span>•</span>
                        <Star className="h-3 w-3" />
                        <span>{article.helpful} helpful</span>
                      </div>
                      
                      <div className="flex gap-1">
                        {article.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardHeader>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="faqs" className="h-full overflow-y-auto">
              <div className="space-y-4">
                {filteredFAQs.map((faq) => (
                  <Card key={faq.id}>
                    <CardHeader>
                      <CardTitle className="text-lg">{faq.question}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground mb-3">{faq.answer}</p>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Star className="h-3 w-3" />
                        <span>{faq.helpful} people found this helpful</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="contact" className="h-full">
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2">Need More Help?</h3>
                  <p className="text-muted-foreground">
                    Can't find what you're looking for? Get in touch with our community and support team.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-6 text-center">
                      <MessageCircle className="h-8 w-8 mx-auto mb-3 text-doge" />
                      <h4 className="font-semibold mb-2">Community Discord</h4>
                      <p className="text-sm text-muted-foreground mb-4">
                        Join our Discord server for real-time help and community discussions
                      </p>
                      <Button variant="outline" className="gap-2">
                        <ExternalLink className="h-4 w-4" />
                        Join Discord
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6 text-center">
                      <Video className="h-8 w-8 mx-auto mb-3 text-doge" />
                      <h4 className="font-semibold mb-2">Video Tutorials</h4>
                      <p className="text-sm text-muted-foreground mb-4">
                        Watch step-by-step video guides on our YouTube channel
                      </p>
                      <Button variant="outline" className="gap-2">
                        <ExternalLink className="h-4 w-4" />
                        Watch Videos
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardContent className="p-6">
                    <h4 className="font-semibold mb-4">Quick Links</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <Button variant="ghost" className="justify-start">
                        📖 Documentation
                      </Button>
                      <Button variant="ghost" className="justify-start">
                        🐛 Report Bug
                      </Button>
                      <Button variant="ghost" className="justify-start">
                        💡 Feature Request
                      </Button>
                      <Button variant="ghost" className="justify-start">
                        📧 Contact Support
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  )
}
