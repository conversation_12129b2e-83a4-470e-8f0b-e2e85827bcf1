"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent } from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useWallet } from '@/components/wallet-provider'
import { 
  Wallet, 
  TrendingUp, 
  Users, 
  Shield, 
  Rocket, 
  CheckCircle, 
  ArrowRight,
  ArrowLeft,
  X
} from 'lucide-react'

interface OnboardingStep {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  component: React.ComponentType<OnboardingStepProps>
  required: boolean
}

interface OnboardingStepProps {
  onNext: () => void
  onPrev: () => void
  onSkip?: () => void
  isFirst: boolean
  isLast: boolean
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to PawPumps',
    description: 'Your gateway to the memecoin universe',
    icon: <Rocket className="h-6 w-6" />,
    component: WelcomeStep,
    required: true,
  },
  {
    id: 'wallet',
    title: 'Connect Your Wallet',
    description: 'Secure connection to start trading',
    icon: <Wallet className="h-6 w-6" />,
    component: WalletStep,
    required: true,
  },
  {
    id: 'trading',
    title: 'Learn to Trade',
    description: 'Master the basics of memecoin trading',
    icon: <TrendingUp className="h-6 w-6" />,
    component: TradingStep,
    required: false,
  },
  {
    id: 'social',
    title: 'Join the Community',
    description: 'Connect with other traders',
    icon: <Users className="h-6 w-6" />,
    component: SocialStep,
    required: false,
  },
  {
    id: 'security',
    title: 'Security Best Practices',
    description: 'Keep your assets safe',
    icon: <Shield className="h-6 w-6" />,
    component: SecurityStep,
    required: false,
  },
]

interface OnboardingFlowProps {
  open: boolean
  onComplete: () => void
  onClose: () => void
}

// Onboarding provider and hook
export function useOnboarding() {
  const [showOnboarding, setShowOnboarding] = useState(false)
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false)

  useEffect(() => {
    const completed = localStorage.getItem('pawpumps-onboarding-completed')
    setHasCompletedOnboarding(completed === 'true')

    // Show onboarding for new users
    if (!completed) {
      const timer = setTimeout(() => {
        setShowOnboarding(true)
      }, 2000) // Delay to let the app load

      return () => clearTimeout(timer)
    }
  }, [])

  const startOnboarding = () => setShowOnboarding(true)
  const completeOnboarding = () => {
    setShowOnboarding(false)
    setHasCompletedOnboarding(true)
    localStorage.setItem('pawpumps-onboarding-completed', 'true')
  }
  const closeOnboarding = () => setShowOnboarding(false)

  return {
    showOnboarding,
    hasCompletedOnboarding,
    startOnboarding,
    completeOnboarding,
    closeOnboarding,
  }
}

export function OnboardingFlow({ open, onComplete, onClose }: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set())
  const { isConnected } = useWallet()

  const step = onboardingSteps[currentStep]
  const progress = ((currentStep + 1) / onboardingSteps.length) * 100

  const handleNext = () => {
    setCompletedSteps(prev => new Set([...prev, step.id]))
    
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(prev => prev + 1)
    } else {
      handleComplete()
    }
  }

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const handleSkip = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(prev => prev + 1)
    } else {
      handleComplete()
    }
  }

  const handleComplete = () => {
    localStorage.setItem('pawpumps-onboarding-completed', 'true')
    onComplete()
  }

  const StepComponent = step.component

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-doge/10 rounded-lg">
                {step.icon}
              </div>
              <div>
                <h2 className="text-xl font-semibold">{step.title}</h2>
                <p className="text-sm text-muted-foreground">{step.description}</p>
              </div>
            </div>
            
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Step {currentStep + 1} of {onboardingSteps.length}</span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Step content */}
          <div className="min-h-[300px]">
            <StepComponent
              onNext={handleNext}
              onPrev={handlePrev}
              onSkip={step.required ? undefined : handleSkip}
              isFirst={currentStep === 0}
              isLast={currentStep === onboardingSteps.length - 1}
            />
          </div>

          {/* Step indicators */}
          <div className="flex justify-center gap-2">
            {onboardingSteps.map((s, index) => (
              <div
                key={s.id}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentStep
                    ? 'bg-doge'
                    : completedSteps.has(s.id)
                    ? 'bg-green-500'
                    : 'bg-muted'
                }`}
              />
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Welcome Step
function WelcomeStep({ onNext, isFirst, isLast }: OnboardingStepProps) {
  return (
    <div className="text-center space-y-6">
      <div className="mx-auto w-32 h-32 bg-gradient-to-br from-doge to-dogechain rounded-full flex items-center justify-center">
        <Rocket className="h-16 w-16 text-black" />
      </div>
      
      <div className="space-y-4">
        <h3 className="text-2xl font-bold">Welcome to PawPumps!</h3>
        <p className="text-muted-foreground max-w-md mx-auto">
          The premier memecoin launchpad and DEX on Dogechain. Create, trade, and discover 
          the next big memecoin with our community-driven platform.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 mx-auto mb-2 text-doge" />
            <h4 className="font-semibold">Trade</h4>
            <p className="text-xs text-muted-foreground">
              Swap memecoins with low fees
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Rocket className="h-8 w-8 mx-auto mb-2 text-doge" />
            <h4 className="font-semibold">Launch</h4>
            <p className="text-xs text-muted-foreground">
              Create your own memecoin
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Users className="h-8 w-8 mx-auto mb-2 text-doge" />
            <h4 className="font-semibold">Connect</h4>
            <p className="text-xs text-muted-foreground">
              Join the community
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end">
        <Button onClick={onNext} className="gap-2">
          Get Started
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

// Wallet Step
function WalletStep({ onNext, onPrev, isFirst, isLast }: OnboardingStepProps) {
  const { isConnected, connect, getAvailableWallets } = useWallet()
  const availableWallets = getAvailableWallets()

  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="mx-auto w-20 h-20 bg-doge/10 rounded-full flex items-center justify-center">
          <Wallet className="h-10 w-10 text-doge" />
        </div>
        
        <div>
          <h3 className="text-xl font-semibold">Connect Your Wallet</h3>
          <p className="text-muted-foreground">
            Connect a wallet to start trading on PawPumps. Your wallet is your key to the memecoin universe.
          </p>
        </div>
      </div>

      {isConnected ? (
        <div className="text-center space-y-4">
          <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
            <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <h4 className="font-semibold text-green-500">Wallet Connected!</h4>
            <p className="text-sm text-muted-foreground">
              You're ready to start trading on PawPumps
            </p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="grid gap-3">
            {availableWallets.map((wallet) => (
              <Button
                key={wallet.id}
                variant="outline"
                onClick={() => connect(wallet.id)}
                className="justify-start gap-3 h-auto p-4"
              >
                <div className="text-2xl">
                  {wallet.id === 'metamask' && '🦊'}
                  {wallet.id === 'walletconnect' && '🔗'}
                  {wallet.id === 'coinbase' && '🔵'}
                  {wallet.id === 'trust' && '🛡️'}
                </div>
                <div className="text-left">
                  <div className="font-medium">{wallet.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {wallet.id === 'metamask' && 'Most popular wallet'}
                    {wallet.id === 'walletconnect' && 'Scan with mobile wallet'}
                    {wallet.id === 'coinbase' && 'Coinbase users'}
                    {wallet.id === 'trust' && 'Mobile-first wallet'}
                  </div>
                </div>
              </Button>
            ))}
          </div>
          
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              Don't have a wallet? We recommend MetaMask for beginners.
            </p>
          </div>
        </div>
      )}

      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrev} className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        
        <Button 
          onClick={onNext} 
          disabled={!isConnected}
          className="gap-2"
        >
          Continue
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

// Trading Step
function TradingStep({ onNext, onPrev, onSkip, isFirst, isLast }: OnboardingStepProps) {
  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="mx-auto w-20 h-20 bg-doge/10 rounded-full flex items-center justify-center">
          <TrendingUp className="h-10 w-10 text-doge" />
        </div>
        
        <div>
          <h3 className="text-xl font-semibold">Learn to Trade</h3>
          <p className="text-muted-foreground">
            Master the basics of memecoin trading on PawPumps
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Trading Basics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-start gap-3">
              <Badge className="mt-1">1</Badge>
              <div>
                <h4 className="font-medium">Choose Your Tokens</h4>
                <p className="text-sm text-muted-foreground">
                  Select the tokens you want to swap from our extensive list
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <Badge className="mt-1">2</Badge>
              <div>
                <h4 className="font-medium">Set Your Amount</h4>
                <p className="text-sm text-muted-foreground">
                  Enter the amount you want to trade and review the exchange rate
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <Badge className="mt-1">3</Badge>
              <div>
                <h4 className="font-medium">Confirm & Trade</h4>
                <p className="text-sm text-muted-foreground">
                  Review the transaction details and confirm in your wallet
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
          <h4 className="font-medium text-yellow-600 mb-2">💡 Pro Tips</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Always check slippage tolerance before trading</li>
            <li>• Start with small amounts to get familiar</li>
            <li>• Monitor gas fees during high network activity</li>
            <li>• Use limit orders for better price control</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrev} className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        
        <div className="flex gap-2">
          {onSkip && (
            <Button variant="ghost" onClick={onSkip}>
              Skip
            </Button>
          )}
          <Button onClick={onNext} className="gap-2">
            {isLast ? 'Finish' : 'Continue'}
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

// Social Step
function SocialStep({ onNext, onPrev, onSkip, isFirst, isLast }: OnboardingStepProps) {
  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="mx-auto w-20 h-20 bg-doge/10 rounded-full flex items-center justify-center">
          <Users className="h-10 w-10 text-doge" />
        </div>
        
        <div>
          <h3 className="text-xl font-semibold">Join the Community</h3>
          <p className="text-muted-foreground">
            Connect with other traders and stay updated on market trends
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardContent className="p-4">
            <h4 className="font-semibold mb-2">Social Feed</h4>
            <p className="text-sm text-muted-foreground mb-3">
              Share your thoughts, analysis, and connect with other traders
            </p>
            <Button variant="outline" size="sm" className="w-full">
              Explore Feed
            </Button>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <h4 className="font-semibold mb-2">Follow Traders</h4>
            <p className="text-sm text-muted-foreground mb-3">
              Follow successful traders and learn from their strategies
            </p>
            <Button variant="outline" size="sm" className="w-full">
              Find Traders
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrev} className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        
        <div className="flex gap-2">
          {onSkip && (
            <Button variant="ghost" onClick={onSkip}>
              Skip
            </Button>
          )}
          <Button onClick={onNext} className="gap-2">
            {isLast ? 'Finish' : 'Continue'}
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

// Security Step
function SecurityStep({ onNext, onPrev, onSkip, isFirst, isLast }: OnboardingStepProps) {
  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="mx-auto w-20 h-20 bg-doge/10 rounded-full flex items-center justify-center">
          <Shield className="h-10 w-10 text-doge" />
        </div>
        
        <div>
          <h3 className="text-xl font-semibold">Security Best Practices</h3>
          <p className="text-muted-foreground">
            Keep your assets safe with these essential security tips
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="p-4 border rounded-lg space-y-3">
          <h4 className="font-semibold text-red-500">🚨 Never Share Your Private Keys</h4>
          <p className="text-sm text-muted-foreground">
            Your private keys and seed phrases are like your bank account password. 
            Never share them with anyone, including PawPumps support.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-3 border rounded-lg">
            <h5 className="font-medium mb-1">✅ Do</h5>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Use hardware wallets for large amounts</li>
              <li>• Verify contract addresses</li>
              <li>• Start with small test transactions</li>
              <li>• Keep your wallet software updated</li>
            </ul>
          </div>
          
          <div className="p-3 border rounded-lg">
            <h5 className="font-medium mb-1">❌ Don't</h5>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Click suspicious links</li>
              <li>• Share your seed phrase</li>
              <li>• Use public WiFi for trading</li>
              <li>• Ignore transaction warnings</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrev} className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        
        <div className="flex gap-2">
          {onSkip && (
            <Button variant="ghost" onClick={onSkip}>
              Skip
            </Button>
          )}
          <Button onClick={onNext} className="gap-2">
            Complete Onboarding
            <CheckCircle className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
