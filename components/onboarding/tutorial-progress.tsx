"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  Circle, 
  Play, 
  RotateCcw, 
  Trophy,
  Target,
  BookOpen,
  Users,
  TrendingUp,
  Rocket
} from 'lucide-react'

interface TutorialModule {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  route: string
  steps: number
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime: number
  prerequisites?: string[]
}

const tutorialModules: TutorialModule[] = [
  {
    id: 'trading',
    title: 'Trading Basics',
    description: 'Learn how to swap tokens and understand trading fundamentals',
    icon: <TrendingUp className="h-5 w-5" />,
    route: '/trade',
    steps: 6,
    difficulty: 'beginner',
    estimatedTime: 5,
  },
  {
    id: 'launch',
    title: 'Token Launch',
    description: 'Create and deploy your own memecoin',
    icon: <Rocket className="h-5 w-5" />,
    route: '/launch',
    steps: 5,
    difficulty: 'intermediate',
    estimatedTime: 8,
    prerequisites: ['trading'],
  },
  {
    id: 'social',
    title: 'Social Features',
    description: 'Connect with the community and share insights',
    icon: <Users className="h-5 w-5" />,
    route: '/social',
    steps: 4,
    difficulty: 'beginner',
    estimatedTime: 3,
  },
  {
    id: 'advanced-trading',
    title: 'Advanced Trading',
    description: 'Master limit orders, portfolio management, and analytics',
    icon: <Target className="h-5 w-5" />,
    route: '/trade',
    steps: 8,
    difficulty: 'advanced',
    estimatedTime: 12,
    prerequisites: ['trading', 'social'],
  },
]

interface TutorialProgressProps {
  onStartTutorial: (moduleId: string) => void
}

export function TutorialProgress({ onStartTutorial }: TutorialProgressProps) {
  const [completedModules, setCompletedModules] = useState<Set<string>>(new Set())
  const [moduleProgress, setModuleProgress] = useState<Record<string, number>>({})

  useEffect(() => {
    // Load progress from localStorage
    const completed = new Set<string>()
    const progress: Record<string, number> = {}

    tutorialModules.forEach(module => {
      const isCompleted = localStorage.getItem(`tutorial-${module.id}-completed`) === 'true'
      if (isCompleted) {
        completed.add(module.id)
        progress[module.id] = 100
      } else {
        // Calculate partial progress based on individual step completion
        let completedSteps = 0
        for (let i = 0; i < module.steps; i++) {
          if (localStorage.getItem(`tutorial-${module.id}-step-${i}`) === 'true') {
            completedSteps++
          }
        }
        progress[module.id] = (completedSteps / module.steps) * 100
      }
    })

    setCompletedModules(completed)
    setModuleProgress(progress)
  }, [])

  const getTotalProgress = () => {
    const totalSteps = tutorialModules.reduce((sum, module) => sum + module.steps, 0)
    const completedSteps = tutorialModules.reduce((sum, module) => {
      return sum + Math.floor((moduleProgress[module.id] || 0) / 100 * module.steps)
    }, 0)
    return (completedSteps / totalSteps) * 100
  }

  const isModuleUnlocked = (module: TutorialModule) => {
    if (!module.prerequisites) return true
    return module.prerequisites.every(prereq => completedModules.has(prereq))
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-500'
      case 'intermediate':
        return 'bg-yellow-500'
      case 'advanced':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getModuleStatus = (module: TutorialModule) => {
    if (completedModules.has(module.id)) {
      return { status: 'completed', icon: <CheckCircle className="h-4 w-4 text-green-500" /> }
    } else if ((moduleProgress[module.id] || 0) > 0) {
      return { status: 'in-progress', icon: <Circle className="h-4 w-4 text-yellow-500" /> }
    } else if (isModuleUnlocked(module)) {
      return { status: 'available', icon: <Play className="h-4 w-4 text-blue-500" /> }
    } else {
      return { status: 'locked', icon: <Circle className="h-4 w-4 text-gray-400" /> }
    }
  }

  const totalProgress = getTotalProgress()
  const completedCount = completedModules.size

  return (
    <div className="space-y-6">
      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Tutorial Progress
              </CardTitle>
              <CardDescription>
                Master PawPumps with our interactive tutorials
              </CardDescription>
            </div>
            {completedCount === tutorialModules.length && (
              <div className="flex items-center gap-2 text-yellow-500">
                <Trophy className="h-5 w-5" />
                <span className="font-semibold">All Complete!</span>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span>{Math.round(totalProgress)}% Complete</span>
            </div>
            <Progress value={totalProgress} className="h-2" />
          </div>
          
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>{completedCount} of {tutorialModules.length} modules completed</span>
            <span>
              {tutorialModules.reduce((sum, m) => sum + m.estimatedTime, 0) - 
               tutorialModules.filter(m => completedModules.has(m.id)).reduce((sum, m) => sum + m.estimatedTime, 0)} 
              min remaining
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Tutorial Modules */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {tutorialModules.map((module) => {
          const { status, icon } = getModuleStatus(module)
          const progress = moduleProgress[module.id] || 0
          const isLocked = !isModuleUnlocked(module)

          return (
            <Card 
              key={module.id} 
              className={`transition-all ${
                isLocked ? 'opacity-60' : 'hover:shadow-md cursor-pointer'
              }`}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-doge/10 rounded-lg">
                      {module.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{module.title}</CardTitle>
                      <CardDescription>{module.description}</CardDescription>
                    </div>
                  </div>
                  {icon}
                </div>
                
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${getDifficultyColor(module.difficulty)}`} />
                  <span className="text-xs capitalize">{module.difficulty}</span>
                  <span className="text-xs text-muted-foreground">•</span>
                  <span className="text-xs text-muted-foreground">{module.estimatedTime} min</span>
                  <span className="text-xs text-muted-foreground">•</span>
                  <span className="text-xs text-muted-foreground">{module.steps} steps</span>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {progress > 0 && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{Math.round(progress)}%</span>
                    </div>
                    <Progress value={progress} className="h-1" />
                  </div>
                )}

                {module.prerequisites && (
                  <div className="space-y-2">
                    <span className="text-xs font-medium">Prerequisites:</span>
                    <div className="flex gap-1">
                      {module.prerequisites.map(prereq => (
                        <Badge 
                          key={prereq} 
                          variant={completedModules.has(prereq) ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {tutorialModules.find(m => m.id === prereq)?.title}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex gap-2">
                  <Button
                    onClick={() => onStartTutorial(module.id)}
                    disabled={isLocked}
                    className="flex-1"
                    variant={status === 'completed' ? 'outline' : 'default'}
                  >
                    {status === 'completed' ? (
                      <>
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Restart
                      </>
                    ) : status === 'in-progress' ? (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Continue
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Start
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Achievement Section */}
      {completedCount > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              Achievements
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className={`text-center p-3 rounded-lg ${completedCount >= 1 ? 'bg-green-500/10 border border-green-500/20' : 'bg-gray-500/10'}`}>
                <div className="text-2xl mb-1">🎯</div>
                <div className="text-xs font-medium">First Steps</div>
                <div className="text-xs text-muted-foreground">Complete 1 tutorial</div>
              </div>
              
              <div className={`text-center p-3 rounded-lg ${completedCount >= 2 ? 'bg-blue-500/10 border border-blue-500/20' : 'bg-gray-500/10'}`}>
                <div className="text-2xl mb-1">🚀</div>
                <div className="text-xs font-medium">Getting Started</div>
                <div className="text-xs text-muted-foreground">Complete 2 tutorials</div>
              </div>
              
              <div className={`text-center p-3 rounded-lg ${completedCount >= 3 ? 'bg-purple-500/10 border border-purple-500/20' : 'bg-gray-500/10'}`}>
                <div className="text-2xl mb-1">⭐</div>
                <div className="text-xs font-medium">Expert</div>
                <div className="text-xs text-muted-foreground">Complete 3 tutorials</div>
              </div>
              
              <div className={`text-center p-3 rounded-lg ${completedCount === tutorialModules.length ? 'bg-yellow-500/10 border border-yellow-500/20' : 'bg-gray-500/10'}`}>
                <div className="text-2xl mb-1">🏆</div>
                <div className="text-xs font-medium">Master</div>
                <div className="text-xs text-muted-foreground">Complete all tutorials</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
