import { Progress } from "@/components/ui/progress"

// This is a Server Component that fetches data on the server
export async function TokenPerformanceSSR() {
  // In a real app, this would be a database or API call
  const tokenData = await fetchTokenPerformanceData()

  return (
    <div className="space-y-4">
      {tokenData.map((token) => (
        <div key={token.id} className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="h-6 w-6 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600"></div>
              <span className="font-medium">{token.name}</span>
            </div>
            <div className="flex items-center gap-2">
              <span
                className={`text-sm ${token.change >= 0 ? "text-green-500" : "text-red-500"}`}
              >{`${token.change >= 0 ? "+" : ""}${token.change.toFixed(2)}%`}</span>
              <span className="text-sm font-medium">${token.price.toFixed(4)}</span>
            </div>
          </div>
          <Progress
            value={50 + token.change}
            className="h-2 bg-white/10"
            indicatorClassName={token.change >= 0 ? "bg-green-500" : "bg-red-500"}
          />
        </div>
      ))}
    </div>
  )
}

// Mock data fetching function (in a real app, this would be a database or API call)
async function fetchTokenPerformanceData() {
  // Only simulate delay in development, not during build
  if (process.env.NODE_ENV === 'development') {
    await new Promise((resolve) => setTimeout(resolve, 1500))
  }

  return [
    { id: 1, name: "DOGE", price: 0.1234, change: 5.67 },
    { id: 2, name: "SHIB", price: 0.00002345, change: -2.34 },
    { id: 3, name: "PAW", price: 0.5678, change: 12.45 },
    { id: 4, name: "DOGEK", price: 0.0345, change: -8.91 },
  ]
}
