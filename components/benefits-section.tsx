"use client"

import type React from "react"
import { Coins, Shield, Users, Zap } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ShimmerText } from "./shimmer-text"

export function BenefitsSection() {
  return (
    <section className="py-16 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="mb-2 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">
            <ShimmerText>The PawPumps Advantage</ShimmerText>
          </h2>
          <p className="mx-auto mt-4 max-w-[700px] text-lg text-white/70">
            Enjoy a premium experience with features designed for both beginners and advanced traders
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          <BenefitCard
            icon={Zap}
            title="Ultra-Low Fees"
            description="Dogechain's $wDOGE gas fees make participation affordable for all users, with transactions costing just pennies."
          />
          <BenefitCard
            icon={Shield}
            title="Enhanced Security"
            description="Liquidity pool burning/locking, anti-bot mechanisms, and gas optimization guidance to protect creators and traders."
          />
          <BenefitCard
            icon={Users}
            title="Social Integration"
            description="Built-in profiles, comments, activity feeds, and follow functionality to build vibrant token communities."
          />
          <BenefitCard
            icon={Coins}
            title="$PAW Rewards"
            description="Earn rewards through the $PAW ecosystem with time-weighted staking and increasing APY based on lock periods."
          />
        </div>
      </div>
    </section>
  )
}

export function BenefitCard({
  icon: Icon,
  title,
  description,
}: {
  icon: React.ElementType
  title: string
  description: string
}) {
  return (
    <Card className="glass-card border-white/5 overflow-hidden transition-all duration-300 hover:border-white/10 liquid-glow">
      <CardHeader className="pb-2 flex flex-col items-center text-center">
        <div className="mb-2 inline-flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 text-black">
          <Icon className="h-6 w-6" />
        </div>
        <CardTitle className="text-xl text-white">{title}</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <CardDescription className="text-base text-white/70">{description}</CardDescription>
      </CardContent>
    </Card>
  )
}
