"use client"

import { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
// Button import removed as unused
import { RechartsWrapper } from '@/components/charts/recharts-wrapper'
import { 
  generateMockChartData, 
  createRealtimeDataStream, 
  TIMEFRAME_OPTIONS,
  ChartDataPoint,
  MOCK_TOKEN_DATA 
} from '@/lib/mock-chart-data'
import { TrendingUp, TrendingDown, BarChart3 } from 'lucide-react'

interface EnhancedTradingChartProps {
  token?: string
  height?: number
  showVolume?: boolean
  showRealtime?: boolean
}

export function EnhancedTradingChart({ 
  token = 'wDOGE', 
  height = 400,
  showVolume = false,
  showRealtime = true 
}: EnhancedTradingChartProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState('1d')
  const [chartData, setChartData] = useState<ChartDataPoint[]>([])
  const [isLoading, setIsLoading] = useState(true)
  // Chart type and realtime controls removed for now
  const [realtimeEnabled] = useState(showRealtime)

  // Get token info
  const tokenInfo = MOCK_TOKEN_DATA[token as keyof typeof MOCK_TOKEN_DATA] || MOCK_TOKEN_DATA.wDOGE

  // Calculate price change
  const priceChange = useMemo(() => {
    if (chartData.length < 2) return { change: 0, percentage: 0 }
    
    const latest = chartData[chartData.length - 1]
    const previous = chartData[chartData.length - 2]
    const change = latest.price - previous.price
    const percentage = (change / previous.price) * 100
    
    return { change, percentage }
  }, [chartData])

  // Load initial chart data
  const loadChartData = useCallback(async (timeframe: string) => {
    setIsLoading(true)
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const data = generateMockChartData(token, timeframe)
    setChartData(data)
    setIsLoading(false)
  }, [token])

  // Handle timeframe change
  const handleTimeframeChange = useCallback((newTimeframe: string) => {
    setSelectedTimeframe(newTimeframe)
    loadChartData(newTimeframe)
  }, [loadChartData])

  // Setup realtime data stream
  useEffect(() => {
    if (!realtimeEnabled) return

    const cleanup = createRealtimeDataStream(token, selectedTimeframe, (newDataPoint) => {
      setChartData(prevData => {
        // For smooth updates, replace the last point if it's within the same second
        const lastPoint = prevData[prevData.length - 1]
        const newTime = new Date(newDataPoint.timestamp).getTime()
        const lastTime = lastPoint ? new Date(lastPoint.timestamp).getTime() : 0

        if (lastPoint && newTime - lastTime < 1000) {
          // Update the last point for smooth animation
          const updatedData = [...prevData]
          updatedData[updatedData.length - 1] = newDataPoint
          return updatedData.slice(-100)
        } else {
          // Add new point
          const newData = [...prevData, newDataPoint]
          return newData.slice(-100)
        }
      })
    })

    return cleanup
  }, [token, selectedTimeframe, realtimeEnabled])

  // Initial data load
  useEffect(() => {
    loadChartData(selectedTimeframe)
  }, [loadChartData, selectedTimeframe])

  // Format price for display
  const formatPrice = (price: number) => {
    if (price < 0.001) {
      return price.toFixed(8)
    } else if (price < 1) {
      return price.toFixed(6)
    } else {
      return price.toFixed(4)
    }
  }

  // Get current price
  const currentPrice = chartData.length > 0 ? chartData[chartData.length - 1].price : tokenInfo.basePrice

  return (
    <Card className="glass-card border-white/5 liquid-glow">
      <CardHeader className="pb-4">
        {/* Mobile-first responsive layout */}
        <div className="space-y-4 md:space-y-0">
          {/* Token title - always on top */}
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {tokenInfo.name} ({tokenInfo.symbol})
            </CardTitle>
            {/* Timeframe selector - top right on mobile */}
            <div className="md:hidden">
              <Select value={selectedTimeframe} onValueChange={handleTimeframeChange}>
                <SelectTrigger className="w-[80px] glass-input border-white/10">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="glass max-h-[300px] overflow-y-auto">
                  {TIMEFRAME_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Price and change info - stacked on mobile, inline on desktop */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
              <span className="text-2xl font-bold text-white">
                ${formatPrice(currentPrice)}
              </span>
              <div className={`flex items-center gap-1 text-sm ${
                priceChange.percentage >= 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                {priceChange.percentage >= 0 ? (
                  <TrendingUp className="h-4 w-4" />
                ) : (
                  <TrendingDown className="h-4 w-4" />
                )}
                <span>
                  {priceChange.percentage >= 0 ? '+' : ''}
                  {priceChange.percentage.toFixed(2)}%
                </span>
              </div>
            </div>

            {/* Timeframe selector - hidden on mobile, shown on desktop */}
            <div className="hidden md:flex items-center gap-2">
              <Select value={selectedTimeframe} onValueChange={handleTimeframeChange}>
                <SelectTrigger className="w-[80px] glass-input border-white/10">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="glass max-h-[300px] overflow-y-auto">
                  {TIMEFRAME_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
                </Select>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="h-[400px] bg-white/5 rounded-lg flex items-center justify-center">
            <div className="text-white/60">Loading chart data...</div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Main chart */}
            <div className="h-[400px]">
              <RechartsWrapper 
                data={chartData}
                height={height}
                type="trading"
              />
            </div>
            
            {/* Volume chart (if enabled) */}
            {showVolume && (
              <div className="h-[100px]">
                <RechartsWrapper 
                  data={chartData.map(d => ({ 
                    name: d.name, 
                    value: d.volume,
                    volume: d.volume 
                  }))}
                  height={100}
                  type="analytics"
                />
              </div>
            )}
            
            {/* Chart stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-white/10">
              <div className="text-center">
                <div className="text-sm text-white/60">24h High</div>
                <div className="text-white font-medium">
                  ${formatPrice(Math.max(...chartData.map(d => d.high)))}
                </div>
              </div>
              <div className="text-center">
                <div className="text-sm text-white/60">24h Low</div>
                <div className="text-white font-medium">
                  ${formatPrice(Math.min(...chartData.map(d => d.low)))}
                </div>
              </div>
              <div className="text-center">
                <div className="text-sm text-white/60">24h Volume</div>
                <div className="text-white font-medium">
                  ${(chartData.reduce((sum, d) => sum + d.volume, 0) / 1000000).toFixed(2)}M
                </div>
              </div>
              <div className="text-center">
                <div className="text-sm text-white/60">Market Cap</div>
                <div className="text-white font-medium">
                  ${(currentPrice * 1000000000 / 1000000).toFixed(2)}M
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
