"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { useWallet } from "@/components/wallet-provider"
import { useNotification } from "@/hooks/use-notification"
import { ArrowUpRight, CheckCircle2, Clock, AlertTriangle, ThumbsUp, ThumbsDown, Plus, Users, X } from "lucide-react"
import Link from "next/link"

// Task status types
type TaskStatus = "completed" | "in-progress" | "planned" | "blocked"

// Priority levels
type PriorityLevel = "critical" | "high" | "medium" | "low"

// Category types
type TaskCategory =
  | "core-functionality"
  | "user-experience"
  | "visual-design"
  | "mobile"
  | "performance"
  | "accessibility"
  | "content"
  | "technical"
  | "security"
  | "social"

// Task interface
interface Task {
  id: string
  title: string
  description: string
  status: TaskStatus
  priority: PriorityLevel
  category: TaskCategory
  estimatedHours: number
  assignedTo?: string
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  dependencies?: string[]
  notes?: string
  votes?: {
    for: number
    against: number
  }
  communityPriority?: number // 1-100 score based on community votes
}

// Community proposal interface
interface CommunityProposal {
  id: string
  title: string
  description: string
  proposedBy: string
  createdAt: Date
  status: "active" | "passed" | "failed" | "implemented"
  category: TaskCategory
  votesFor: number
  votesAgainst: number
  quorum: number
  implementationTask?: string // Task ID if implemented
}

// Mock tasks with voting data
const mockTasks: Task[] = [
  {
    id: "CF-001",
    title: "Implement Token Launch Confirmation Flow",
    description: "Add a confirmation step before token launch to review parameters and confirm deployment",
    status: "planned",
    priority: "critical",
    category: "core-functionality",
    estimatedHours: 16,
    createdAt: new Date(),
    updatedAt: new Date(),
    votes: {
      for: 1250000,
      against: 50000,
    },
    communityPriority: 92,
  },
  {
    id: "CF-007",
    title: "Integrate Multiple Wallet Providers",
    description: "Add support for multiple wallet providers (MetaMask, WalletConnect, etc.)",
    status: "in-progress",
    priority: "critical",
    category: "core-functionality",
    estimatedHours: 40,
    createdAt: new Date(),
    updatedAt: new Date(),
    votes: {
      for: 1800000,
      against: 100000,
    },
    communityPriority: 95,
  },
  {
    id: "UX-004",
    title: "Design User Onboarding Flow",
    description: "Create guided tour for new users to learn platform features",
    status: "planned",
    priority: "high",
    category: "user-experience",
    estimatedHours: 40,
    createdAt: new Date(),
    updatedAt: new Date(),
    votes: {
      for: 950000,
      against: 350000,
    },
    communityPriority: 73,
  },
  {
    id: "MR-002",
    title: "Improve Chart Touch Interactions",
    description: "Enhance charts for better touch interaction on mobile devices",
    status: "in-progress",
    priority: "high",
    category: "mobile",
    estimatedHours: 32,
    createdAt: new Date(),
    updatedAt: new Date(),
    votes: {
      for: 1100000,
      against: 200000,
    },
    communityPriority: 85,
  },
  {
    id: "PF-001",
    title: "Add Skeleton Loaders",
    description: "Implement skeleton loading states for asynchronous content",
    status: "planned",
    priority: "high",
    category: "performance",
    estimatedHours: 16,
    createdAt: new Date(),
    updatedAt: new Date(),
    votes: {
      for: 800000,
      against: 300000,
    },
    communityPriority: 68,
  },
]

// Mock community proposals
const mockProposals: CommunityProposal[] = [
  {
    id: "PROP-005",
    title: "Add Multi-Chain Support",
    description: "Expand beyond Dogechain to support Ethereum, Binance Smart Chain, and other EVM chains",
    proposedBy: "0x1234...5678",
    createdAt: new Date("2025-05-03"),
    status: "active",
    category: "core-functionality",
    votesFor: 950000,
    votesAgainst: 350000,
    quorum: 2000000,
  },
  {
    id: "PROP-006",
    title: "Implement Dark Mode",
    description: "Add a dark mode option for the entire platform to reduce eye strain and save battery",
    proposedBy: "0x8765...4321",
    createdAt: new Date("2025-05-02"),
    status: "active",
    category: "user-experience",
    votesFor: 1200000,
    votesAgainst: 100000,
    quorum: 2000000,
  },
  {
    id: "PROP-007",
    title: "Create Mobile App",
    description: "Develop native mobile applications for iOS and Android",
    proposedBy: "0x5678...1234",
    createdAt: new Date("2025-05-01"),
    status: "active",
    category: "mobile",
    votesFor: 800000,
    votesAgainst: 600000,
    quorum: 2000000,
  },
  {
    id: "PROP-008",
    title: "Add NFT Support",
    description: "Implement NFT creation, trading, and display functionality",
    proposedBy: "0x4321...8765",
    createdAt: new Date("2025-04-30"),
    status: "passed",
    category: "core-functionality",
    votesFor: 1500000,
    votesAgainst: 300000,
    quorum: 1500000,
    implementationTask: "CF-010", // This would be the task ID created after approval
  },
]

// Helper function to get status icon
const getStatusIcon = (status: TaskStatus) => {
  switch (status) {
    case "completed":
      return <CheckCircle2 className="h-4 w-4 text-green-500" />
    case "in-progress":
      return <Clock className="h-4 w-4 text-blue-500" />
    case "planned":
      return <Clock className="h-4 w-4 text-gray-400" />
    case "blocked":
      return <AlertTriangle className="h-4 w-4 text-red-500" />
    default:
      return <Clock className="h-4 w-4 text-gray-400" />
  }
}

// Helper function to format votes
const formatVotes = (votes: number) => {
  if (votes >= 1000000) {
    return `${(votes / 1000000).toFixed(1)}M`
  } else if (votes >= 1000) {
    return `${(votes / 1000).toFixed(1)}K`
  }
  return votes.toString()
}

// Helper function to calculate progress
const calculateProgress = (votesFor: number, votesAgainst: number) => {
  const total = votesFor + votesAgainst
  if (total === 0) return 0
  return (votesFor / total) * 100
}

export function GovernanceDevelopmentIntegration() {
  const { isConnected } = useWallet()
  const { showNotification } = useNotification()
  const [activeTab, setActiveTab] = useState("community-priorities")
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [selectedProposal, setSelectedProposal] = useState<CommunityProposal | null>(null)

  // Handle voting on a task priority
  const handleTaskVote = (vote: "for" | "against") => {
    if (!selectedTask) return

    showNotification({
      title: "Vote Submitted",
      message: `You voted ${vote} prioritizing "${selectedTask.title}"`,
      type: "success",
      addToCenter: true,
    })

    setSelectedTask(null)
  }

  // Handle voting on a community proposal
  const handleProposalVote = (vote: "for" | "against") => {
    if (!selectedProposal) return

    showNotification({
      title: "Vote Submitted",
      message: `You voted ${vote} the proposal "${selectedProposal.title}"`,
      type: "success",
      addToCenter: true,
    })

    setSelectedProposal(null)
  }

  return (
    <div className="space-y-6">
      <Card className="glass-card border-white/5 doge-glow">
        <CardHeader>
          <CardTitle className="text-white">Community-Driven Development</CardTitle>
          <CardDescription className="text-white/70">
            Shape the future of PawPumps by voting on development priorities and proposing new features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 glass mb-6">
              <TabsTrigger
                value="community-priorities"
                className="data-[state=active]:text-doge data-[state=active]:bg-doge/10"
              >
                Community Priorities
              </TabsTrigger>
              <TabsTrigger
                value="feature-proposals"
                className="data-[state=active]:text-dogechain data-[state=active]:bg-dogechain/10"
              >
                Feature Proposals
              </TabsTrigger>
              <TabsTrigger
                value="development-progress"
                className="data-[state=active]:text-green-500 data-[state=active]:bg-green-500/10"
              >
                Development Progress
              </TabsTrigger>
            </TabsList>

            {/* Community Priorities Tab */}
            <TabsContent value="community-priorities" className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <p className="text-white/70">Vote on which features should be prioritized in the development roadmap</p>
                <Link href="/development-tracker">
                  <Button variant="outline" size="sm" className="glass-button">
                    <span>View All Tasks</span>
                    <ArrowUpRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>

              {mockTasks.map((task) => (
                <div
                  key={task.id}
                  className="glass-card border border-white/5 rounded-lg p-4 cursor-pointer hover:border-doge/20 transition-all"
                  onClick={() => setSelectedTask(task)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-white/60">{task.id}</span>
                      <div className="px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1 border border-white/10 bg-white/5">
                        {getStatusIcon(task.status)}
                        <span className="capitalize">{task.status.replace("-", " ")}</span>
                      </div>
                    </div>
                    <Badge className="bg-doge text-black">Community Priority: {task.communityPriority}%</Badge>
                  </div>
                  <h3 className="text-lg font-medium text-white mb-2">{task.title}</h3>
                  <p className="text-sm text-white/70 mb-4">{task.description}</p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-white/60">For: {formatVotes(task.votes?.for || 0)}</span>
                      <span className="text-white/60">Against: {formatVotes(task.votes?.against || 0)}</span>
                    </div>
                    <Progress
                      value={calculateProgress(task.votes?.for || 0, task.votes?.against || 0)}
                      className="h-2 bg-white/10"
                      indicatorClassName="bg-doge"
                    />
                  </div>
                </div>
              ))}
            </TabsContent>

            {/* Feature Proposals Tab */}
            <TabsContent value="feature-proposals" className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <p className="text-white/70">Propose new features and vote on community suggestions</p>
                <Button className="doge-button doge-shine">
                  <Plus className="mr-2 h-4 w-4" />
                  <span>Create Proposal</span>
                </Button>
              </div>

              {mockProposals.map((proposal) => (
                <div
                  key={proposal.id}
                  className="glass-card border border-white/5 rounded-lg p-4 cursor-pointer hover:border-dogechain/20 transition-all"
                  onClick={() => setSelectedProposal(proposal)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-white/60">{proposal.id}</span>
                      <div
                        className={`px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1 border ${
                          proposal.status === "active"
                            ? "text-dogechain bg-dogechain/10 border-dogechain/20"
                            : proposal.status === "passed"
                              ? "text-green-500 bg-green-500/10 border-green-500/20"
                              : proposal.status === "failed"
                                ? "text-red-500 bg-red-500/10 border-red-500/20"
                                : proposal.status === "implemented"
                                  ? "text-purple-500 bg-purple-500/10 border-purple-500/20"
                                  : "text-white/70 bg-white/5 border-white/10"
                        }`}
                      >
                        <span className="capitalize">{proposal.status}</span>
                      </div>
                    </div>
                    <Badge className="bg-dogechain text-black">
                      {proposal.category
                        .split("-")
                        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                        .join(" ")}
                    </Badge>
                  </div>
                  <h3 className="text-lg font-medium text-white mb-2">{proposal.title}</h3>
                  <p className="text-sm text-white/70 mb-4">{proposal.description}</p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-white/60">For: {formatVotes(proposal.votesFor)}</span>
                      <span className="text-white/60">Against: {formatVotes(proposal.votesAgainst)}</span>
                    </div>
                    <Progress
                      value={calculateProgress(proposal.votesFor, proposal.votesAgainst)}
                      className="h-2 bg-white/10"
                      indicatorClassName="bg-dogechain"
                    />
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-white/60">
                        Proposed by {proposal.proposedBy.slice(0, 6)}...{proposal.proposedBy.slice(-4)}
                      </span>
                      <span className="text-white/60">
                        Quorum: {formatVotes(proposal.votesFor + proposal.votesAgainst)}/{formatVotes(proposal.quorum)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </TabsContent>

            {/* Development Progress Tab */}
            <TabsContent value="development-progress" className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <p className="text-white/70">Track the progress of approved features and development milestones</p>
                <div className="flex gap-2">
                  <Link href="/roadmap">
                    <Button variant="outline" size="sm" className="glass-button">
                      <span>View Roadmap</span>
                      <ArrowUpRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                  <Link href="/progress-report">
                    <Button variant="outline" size="sm" className="glass-button">
                      <span>Progress Report</span>
                      <ArrowUpRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>

              <Card className="glass-card border-white/5">
                <CardHeader className="pb-2">
                  <CardTitle className="text-white text-lg">Development Milestones</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/90">Core Infrastructure</span>
                        <span className="text-white/70">20% complete</span>
                      </div>
                      <Progress value={20} className="h-2 bg-white/10" indicatorClassName="bg-green-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/90">Token Launch Experience</span>
                        <span className="text-white/70">10% complete</span>
                      </div>
                      <Progress value={10} className="h-2 bg-white/10" indicatorClassName="bg-green-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/90">Trading Enhancements</span>
                        <span className="text-white/70">0% complete</span>
                      </div>
                      <Progress value={0} className="h-2 bg-white/10" indicatorClassName="bg-green-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/90">Mobile & Accessibility</span>
                        <span className="text-white/70">15% complete</span>
                      </div>
                      <Progress value={15} className="h-2 bg-white/10" indicatorClassName="bg-green-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white/90">Performance & Technical Debt</span>
                        <span className="text-white/70">5% complete</span>
                      </div>
                      <Progress value={5} className="h-2 bg-white/10" indicatorClassName="bg-green-500" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/5">
                <CardHeader className="pb-2">
                  <CardTitle className="text-white text-lg">Recently Completed Tasks</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3 p-3 rounded-md bg-white/5">
                      <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5" />
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-xs text-white/60">VD-001</span>
                          <span className="text-white font-medium">Standardize Card Styling</span>
                        </div>
                        <p className="text-sm text-white/70 mt-1">
                          Created consistent card styling patterns across the platform
                        </p>
                        <p className="text-xs text-white/50 mt-1">Completed 2 days ago</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3 p-3 rounded-md bg-white/5">
                      <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5" />
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-xs text-white/60">UX-006</span>
                          <span className="text-white font-medium">Add Contextual Tooltips</span>
                        </div>
                        <p className="text-sm text-white/70 mt-1">Implemented tooltips for complex UI elements</p>
                        <p className="text-xs text-white/50 mt-1">Completed 5 days ago</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3 p-3 rounded-md bg-white/5">
                      <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5" />
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-xs text-white/60">AC-005</span>
                          <span className="text-white font-medium">Add Alternative Text</span>
                        </div>
                        <p className="text-sm text-white/70 mt-1">Ensured all images have proper alternative text</p>
                        <p className="text-xs text-white/50 mt-1">Completed 1 week ago</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Link href="/development-tracker" className="w-full">
                    <Button variant="outline" className="w-full glass-button">
                      View All Tasks
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="glass-card border-white/5 doge-glow">
          <CardHeader>
            <CardTitle className="text-white">Development Participation Rewards</CardTitle>
            <CardDescription className="text-white/70">
              Earn rewards for participating in the development process
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <h3 className="text-white font-medium mb-2">Voting Rewards</h3>
              <p className="text-sm text-white/70 mb-3">
                Earn $PAW tokens for voting on development priorities and feature proposals
              </p>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Reward per vote:</span>
                <span className="text-doge font-medium">5 $PAW</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Your votes this week:</span>
                <span className="text-white">12</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Rewards earned:</span>
                <span className="text-doge font-medium">60 $PAW</span>
              </div>
            </div>

            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <h3 className="text-white font-medium mb-2">Proposal Rewards</h3>
              <p className="text-sm text-white/70 mb-3">
                Earn $PAW tokens when your feature proposals are approved and implemented
              </p>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Reward per approved proposal:</span>
                <span className="text-doge font-medium">1,000 $PAW</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Your approved proposals:</span>
                <span className="text-white">1</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Rewards earned:</span>
                <span className="text-doge font-medium">1,000 $PAW</span>
              </div>
            </div>

            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <h3 className="text-white font-medium mb-2">Bug Bounty Rewards</h3>
              <p className="text-sm text-white/70 mb-3">
                Earn $PAW tokens for reporting bugs and suggesting improvements
              </p>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Reward per verified bug:</span>
                <span className="text-doge font-medium">50-5,000 $PAW</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Your verified reports:</span>
                <span className="text-white">2</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-1">
                <span className="text-white/70">Rewards earned:</span>
                <span className="text-doge font-medium">350 $PAW</span>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full doge-button doge-shine">Claim Rewards</Button>
          </CardFooter>
        </Card>

        <Card className="glass-card border-white/5 dogechain-glow">
          <CardHeader>
            <CardTitle className="text-white">Development DAO</CardTitle>
            <CardDescription className="text-white/70">
              Stake $PAW tokens to increase your voting power in development decisions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {isConnected ? (
              <>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">$PAW Balance</span>
                    <span className="text-white font-medium">10,000</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Staked for Development</span>
                    <span className="text-doge font-medium doge-text-glow">5,000</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Development Voting Power</span>
                    <span className="text-dogechain font-medium dogechain-text-glow">5,000</span>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h4 className="text-sm font-medium text-white mb-2">Development Staking Rewards</h4>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs text-white/70">APR</span>
                    <span className="text-xs text-doge">15.0%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-white/70">Earned</span>
                    <span className="text-xs text-doge">+125 $PAW</span>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h4 className="text-sm font-medium text-white mb-2">Your Development Influence</h4>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs text-white/70">Voting Weight</span>
                    <span className="text-xs text-dogechain">0.25% of total</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-white/70">Proposal Threshold</span>
                    <span className="text-xs text-green-500">Eligible to Propose</span>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button className="flex-1 doge-button doge-shine">Stake for Development</Button>
                  <Button className="flex-1 glass-button">Unstake</Button>
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center py-6 text-center">
                <Users className="h-12 w-12 text-white/20 mb-4" />
                <p className="text-white/70 mb-4">
                  Connect your wallet to stake $PAW tokens and participate in development governance
                </p>
                <Button className="doge-button doge-shine">Connect Wallet</Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Task Voting Modal */}
      {selectedTask && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto">
          <Card className="glass-card border-white/5 w-full max-w-2xl max-h-[80vh] overflow-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium text-white/60">{selectedTask.id}</span>
                    <div className="px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1 border border-white/10 bg-white/5">
                      {getStatusIcon(selectedTask.status)}
                      <span className="capitalize">{selectedTask.status.replace("-", " ")}</span>
                    </div>
                  </div>
                  <CardTitle className="text-white">{selectedTask.title}</CardTitle>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white/70 hover:text-white"
                  onClick={() => setSelectedTask(null)}
                >
                  <span className="sr-only">Close</span>
                  <X className="h-6 w-6" />
                </Button>
              </div>
              <CardDescription className="text-white/70">
                Community Priority: {selectedTask.communityPriority}%
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <p className="text-white/90">{selectedTask.description}</p>
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h4 className="text-sm font-medium text-white mb-2">Priority Voting</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-white/60">For: {formatVotes(selectedTask.votes?.for || 0)}</span>
                      <span className="text-white/60">Against: {formatVotes(selectedTask.votes?.against || 0)}</span>
                    </div>
                    <Progress
                      value={calculateProgress(selectedTask.votes?.for || 0, selectedTask.votes?.against || 0)}
                      className="h-2 bg-white/10"
                      indicatorClassName="bg-doge"
                    />
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-white/60">
                        Category:{" "}
                        {selectedTask.category
                          .split("-")
                          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                          .join(" ")}
                      </span>
                      <span className="text-white/60">Estimated Hours: {selectedTask.estimatedHours}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex gap-4">
              <Button className="flex-1 doge-button doge-shine" onClick={() => handleTaskVote("for")}>
                <ThumbsUp className="mr-2 h-4 w-4" />
                Prioritize
              </Button>
              <Button className="flex-1 glass-button" onClick={() => handleTaskVote("against")}>
                <ThumbsDown className="mr-2 h-4 w-4" />
                Deprioritize
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}

      {/* Proposal Voting Modal */}
      {selectedProposal && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto">
          <Card className="glass-card border-white/5 w-full max-w-2xl max-h-[80vh] overflow-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium text-white/60">{selectedProposal.id}</span>
                    <div
                      className={`px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1 border ${
                        selectedProposal.status === "active"
                          ? "text-dogechain bg-dogechain/10 border-dogechain/20"
                          : selectedProposal.status === "passed"
                            ? "text-green-500 bg-green-500/10 border-green-500/20"
                            : selectedProposal.status === "failed"
                              ? "text-red-500 bg-red-500/10 border-red-500/20"
                              : selectedProposal.status === "implemented"
                                ? "text-purple-500 bg-purple-500/10 border-purple-500/20"
                                : "text-white/70 bg-white/5 border-white/10"
                      }`}
                    >
                      <span className="capitalize">{selectedProposal.status}</span>
                    </div>
                  </div>
                  <CardTitle className="text-white">{selectedProposal.title}</CardTitle>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white/70 hover:text-white"
                  onClick={() => setSelectedProposal(null)}
                >
                  <span className="sr-only">Close</span>
                  <X className="h-6 w-6" />
                </Button>
              </div>
              <CardDescription className="text-white/70">
                Proposed by {selectedProposal.proposedBy.slice(0, 6)}...{selectedProposal.proposedBy.slice(-4)} on{" "}
                {selectedProposal.createdAt.toLocaleDateString()}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <p className="text-white/90">{selectedProposal.description}</p>
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h4 className="text-sm font-medium text-white mb-2">Voting Status</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-white/60">For: {formatVotes(selectedProposal.votesFor)}</span>
                      <span className="text-white/60">Against: {formatVotes(selectedProposal.votesAgainst)}</span>
                    </div>
                    <Progress
                      value={calculateProgress(selectedProposal.votesFor, selectedProposal.votesAgainst)}
                      className="h-2 bg-white/10"
                      indicatorClassName="bg-dogechain"
                    />
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-white/60">
                        Quorum: {formatVotes(selectedProposal.votesFor + selectedProposal.votesAgainst)}/
                        {formatVotes(selectedProposal.quorum)}
                      </span>
                      <span className="text-white/60">
                        Category:{" "}
                        {selectedProposal.category
                          .split("-")
                          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                          .join(" ")}
                      </span>
                    </div>
                  </div>
                </div>
                {selectedProposal.implementationTask && (
                  <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/20">
                    <h4 className="text-sm font-medium text-green-500 mb-2">Implementation Status</h4>
                    <p className="text-sm text-white/90">
                      This proposal has been approved and added to the development roadmap as task{" "}
                      <span className="font-mono">{selectedProposal.implementationTask}</span>.
                    </p>
                    <Link
                      href="/development-tracker"
                      className="text-sm text-green-500 hover:underline mt-2 inline-block"
                    >
                      View implementation status →
                    </Link>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex gap-4">
              {selectedProposal.status === "active" && (
                <>
                  <Button className="flex-1 doge-button doge-shine" onClick={() => handleProposalVote("for")}>
                    <ThumbsUp className="mr-2 h-4 w-4" />
                    Vote For
                  </Button>
                  <Button className="flex-1 glass-button" onClick={() => handleProposalVote("against")}>
                    <ThumbsDown className="mr-2 h-4 w-4" />
                    Vote Against
                  </Button>
                </>
              )}
              {selectedProposal.status === "passed" && !selectedProposal.implementationTask && (
                <Button className="w-full glass-button" disabled>
                  Awaiting Implementation
                </Button>
              )}
              {(selectedProposal.status === "failed" ||
                selectedProposal.status === "implemented" ||
                (selectedProposal.status === "passed" && selectedProposal.implementationTask)) && (
                <Button className="w-full glass-button" disabled>
                  Voting Ended
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      )}
    </div>
  )
}
