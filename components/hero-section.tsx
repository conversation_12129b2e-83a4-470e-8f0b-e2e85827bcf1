"use client"

import Link from "next/link"
import Image from "next/image"
import { ShimmerText } from "@/components/shimmer-text"

export function HeroSection() {
  return (
    <section className="relative border-b border-white/5 py-16 md:py-24">
      {/* Liquid glass orbs - using absolute positioning but not overflow:hidden */}
      <div className="absolute inset-0 z-0">
        <div className="absolute -top-[300px] left-1/4 h-[600px] w-[600px] rounded-full bg-doge/[0.02] blur-[100px] animate-float"></div>
        <div
          className="absolute -bottom-[400px] right-1/4 h-[800px] w-[800px] rounded-full bg-dogechain/[0.01] blur-[120px] animate-float"
          style={{ animationDelay: "-3s" }}
        ></div>
      </div>

      <div className="container relative z-10">
        <div className="mx-auto max-w-[800px] text-center">
          <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
            <span className="hidden md:inline">Launch Your </span>
            <span className="hidden md:inline">
              <ShimmerText>Memecoin</ShimmerText>
            </span>
            <span className="hidden md:inline"> in Minutes</span>

            {/* Mobile version - stacked */}
            <span className="md:hidden">Launch Your</span>
            <span className="md:hidden block">
              <ShimmerText>Memecoin</ShimmerText>
            </span>
            <span className="md:hidden block">in Minutes</span>
          </h1>

          <div className="flex justify-center mb-6">
            <div className="rounded-full glass px-6 py-1.5 text-sm font-medium border border-white/5 flex items-center justify-center">
              <span>The Premier Memecoin Launchpad & DEX on</span>
              <div className="relative h-5 w-5 ml-2">
                <Image
                  src="/images/dogechain-logo.png"
                  alt="Dogechain"
                  fill
                  sizes="20px"
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>

          <div className="mb-8">
            <p className="text-xl text-white/80">
              PawPumps delivers a professional-grade platform with no-code token creation, bonding curve pricing, and
              community-driven governance.
            </p>
          </div>
          <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
            <Link
              href="/launch"
              className="pawpumps-button flex items-center justify-center gap-2 px-6 py-3 rounded-full text-white font-medium"
            >
              <span>Launch a Token</span>
            </Link>
            <Link
              href="/trade"
              className="cosmic-button flex items-center justify-center gap-2 px-6 py-3 text-white font-medium"
            >
              <span>Start Trading</span>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
