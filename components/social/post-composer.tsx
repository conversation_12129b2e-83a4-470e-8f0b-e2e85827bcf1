"use client"

import { useState, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useWallet } from '@/components/wallet-provider'
import { 
  Image, 
  TrendingUp, 
  TrendingDown, 
  Hash, 
  X, 
  Loader2,
  BarChart3,
  Coins
} from 'lucide-react'

interface PostComposerProps {
  onPost?: (content: string, sentiment?: string, tags?: string[]) => void
  placeholder?: string
  maxLength?: number
}

export function PostComposer({ 
  onPost, 
  placeholder = "What's happening in the memecoin world?",
  maxLength = 280 
}: PostComposerProps) {
  const { address, isConnected } = useWallet()
  const [content, setContent] = useState('')
  const [sentiment, setSentiment] = useState<string>('')
  const [tags, setTags] = useState<string[]>([])
  const [currentTag, setCurrentTag] = useState('')
  const [isPosting, setIsPosting] = useState(false)
  const [attachments, setAttachments] = useState<any[]>([])
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const handleContentChange = (value: string) => {
    if (value.length <= maxLength) {
      setContent(value)
      
      // Auto-detect hashtags
      const hashtagMatches = value.match(/#\w+/g)
      if (hashtagMatches) {
        const newTags = hashtagMatches.map(tag => tag.slice(1))
        setTags(prev => [...new Set([...prev, ...newTags])])
      }
    }
  }

  const addTag = () => {
    if (currentTag && !tags.includes(currentTag)) {
      setTags(prev => [...prev, currentTag])
      setCurrentTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove))
  }

  const handlePost = async () => {
    if (!content.trim() || !isConnected) return

    setIsPosting(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      onPost?.(content, sentiment, tags)
      
      // Reset form
      setContent('')
      setSentiment('')
      setTags([])
      setAttachments([])
      
    } catch (error) {
      console.error('Error posting:', error)
    } finally {
      setIsPosting(false)
    }
  }

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`
  }

  const remainingChars = maxLength - content.length
  const isOverLimit = remainingChars < 0
  const canPost = content.trim().length > 0 && !isOverLimit && isConnected

  if (!isConnected) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground mb-4">
            Connect your wallet to join the conversation
          </p>
          <Button variant="outline">Connect Wallet</Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Avatar className="w-8 h-8">
            <AvatarFallback>
              {address ? address.slice(2, 4).toUpperCase() : 'U'}
            </AvatarFallback>
          </Avatar>
          <span className="text-sm">
            {address ? formatAddress(address) : 'Anonymous'}
          </span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Content input */}
        <div className="space-y-2">
          <Textarea
            ref={textareaRef}
            placeholder={placeholder}
            value={content}
            onChange={(e) => handleContentChange(e.target.value)}
            className="min-h-[100px] resize-none"
            disabled={isPosting}
          />
          
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">
                {content.length}/{maxLength}
              </span>
              {isOverLimit && (
                <span className="text-red-500">Character limit exceeded</span>
              )}
            </div>
          </div>
        </div>

        {/* Sentiment selector */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Market Sentiment (Optional)</label>
          <Select value={sentiment} onValueChange={setSentiment}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select sentiment" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">No sentiment</SelectItem>
              <SelectItem value="bullish">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  Bullish
                </div>
              </SelectItem>
              <SelectItem value="bearish">
                <div className="flex items-center gap-2">
                  <TrendingDown className="h-4 w-4 text-red-500" />
                  Bearish
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Tags */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Tags</label>
          
          {/* Current tags */}
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="gap-1">
                  #{tag}
                  <button
                    onClick={() => removeTag(tag)}
                    className="hover:text-red-500"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}
          
          {/* Add tag input */}
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Add tag"
                value={currentTag}
                onChange={(e) => setCurrentTag(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addTag()}
                className="w-full pl-10 pr-3 py-2 border rounded-md text-sm"
                disabled={isPosting}
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={addTag}
              disabled={!currentTag || isPosting}
            >
              Add
            </Button>
          </div>
        </div>

        {/* Attachment options */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Attachments (Coming Soon)</label>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" disabled>
              <Image className="h-4 w-4 mr-1" />
              Image
            </Button>
            <Button variant="outline" size="sm" disabled>
              <BarChart3 className="h-4 w-4 mr-1" />
              Chart
            </Button>
            <Button variant="outline" size="sm" disabled>
              <Coins className="h-4 w-4 mr-1" />
              Token
            </Button>
          </div>
        </div>

        {/* Post button */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-xs text-muted-foreground">
            Your post will be visible to all PawPumps users
          </div>
          
          <Button
            onClick={handlePost}
            disabled={!canPost || isPosting}
            className="min-w-[100px]"
          >
            {isPosting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Posting...
              </>
            ) : (
              'Post'
            )}
          </Button>
        </div>

        {/* Guidelines */}
        <div className="text-xs text-muted-foreground space-y-1 pt-2 border-t">
          <p>Community Guidelines:</p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            <li>Be respectful and constructive</li>
            <li>No financial advice or pump/dump schemes</li>
            <li>Share your own analysis and opinions</li>
            <li>Use appropriate tags for better discovery</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}

// Quick post buttons for common actions
export function QuickPostButtons() {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)

  const templates = [
    {
      id: 'bullish',
      label: 'Bullish',
      icon: <TrendingUp className="h-4 w-4" />,
      content: 'Feeling bullish on ',
      sentiment: 'bullish',
    },
    {
      id: 'bearish',
      label: 'Bearish',
      icon: <TrendingDown className="h-4 w-4" />,
      content: 'Market looking bearish, time to be cautious ',
      sentiment: 'bearish',
    },
    {
      id: 'analysis',
      label: 'Analysis',
      icon: <BarChart3 className="h-4 w-4" />,
      content: 'Technical analysis shows ',
      sentiment: '',
    },
  ]

  return (
    <div className="flex gap-2 mb-4">
      {templates.map((template) => (
        <Button
          key={template.id}
          variant={selectedTemplate === template.id ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedTemplate(template.id)}
          className="gap-1"
        >
          {template.icon}
          {template.label}
        </Button>
      ))}
    </div>
  )
}
