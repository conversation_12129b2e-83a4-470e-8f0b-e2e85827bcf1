"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Heart, MessageCircle, Share2, TrendingUp, TrendingDown, MoreHorizontal } from 'lucide-react'
// import { useRealtimeData } from '@/components/realtime-data-provider'
import { formatDistanceToNow } from 'date-fns'

interface SocialPost {
  id: string
  author: {
    address: string
    username?: string
    avatar?: string
    verified: boolean
    reputation: number
  }
  content: string
  timestamp: number
  likes: number
  comments: number
  shares: number
  isLiked: boolean
  tags: string[]
  attachments?: {
    type: 'image' | 'chart' | 'token'
    url?: string
    tokenAddress?: string
    tokenSymbol?: string
  }[]
  sentiment?: 'bullish' | 'bearish' | 'neutral'
}

interface SocialFeedProps {
  filter?: 'all' | 'following' | 'trending' | 'bullish' | 'bearish'
  tokenSymbol?: string
}

export function SocialFeed({ filter = 'all', tokenSymbol }: SocialFeedProps) {
  const [posts, setPosts] = useState<SocialPost[]>([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const isConnected = true // Mock connection status

  // Mock data for demonstration
  useEffect(() => {
    const mockPosts: SocialPost[] = [
      {
        id: '1',
        author: {
          address: '0x1234...5678',
          username: 'DogeWhale',
          verified: true,
          reputation: 95,
        },
        content: 'Just bought another 1M DOGE! 🚀 This dip is a gift. Diamond hands! #DOGE #ToTheMoon',
        timestamp: Date.now() - 300000, // 5 minutes ago
        likes: 42,
        comments: 8,
        shares: 3,
        isLiked: false,
        tags: ['DOGE', 'ToTheMoon'],
        sentiment: 'bullish',
      },
      {
        id: '2',
        author: {
          address: '0x9876...4321',
          username: 'ShibArmy',
          verified: false,
          reputation: 78,
        },
        content: 'SHIB looking strong! Technical analysis shows potential breakout above resistance. What do you think? 📈',
        timestamp: Date.now() - 900000, // 15 minutes ago
        likes: 23,
        comments: 12,
        shares: 5,
        isLiked: true,
        tags: ['SHIB', 'TA'],
        sentiment: 'bullish',
        attachments: [
          {
            type: 'chart',
            url: '/charts/shib-analysis.png',
          },
        ],
      },
      {
        id: '3',
        author: {
          address: '0x5555...7777',
          username: 'PepeTrader',
          verified: true,
          reputation: 88,
        },
        content: 'Market looking shaky today. Might be time to take some profits and wait for better entry points. Stay safe out there! ⚠️',
        timestamp: Date.now() - 1800000, // 30 minutes ago
        likes: 15,
        comments: 6,
        shares: 2,
        isLiked: false,
        tags: ['MarketUpdate', 'TakeProfit'],
        sentiment: 'bearish',
      },
    ]

    // Remove artificial loading delay - load posts immediately
    setPosts(mockPosts)
    setLoading(false)
  }, [filter, tokenSymbol])

  const handleLike = async (postId: string) => {
    setPosts(prev => prev.map(post => 
      post.id === postId 
        ? { 
            ...post, 
            isLiked: !post.isLiked,
            likes: post.isLiked ? post.likes - 1 : post.likes + 1
          }
        : post
    ))
  }

  const handleShare = async (postId: string) => {
    // Implement share functionality
    if (navigator.share) {
      await navigator.share({
        title: 'PawPumps Social Post',
        url: `${window.location.origin}/social/post/${postId}`,
      })
    } else {
      // Fallback to clipboard
      await navigator.clipboard.writeText(`${window.location.origin}/social/post/${postId}`)
    }
  }

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`
  }

  const getSentimentIcon = (sentiment?: string) => {
    switch (sentiment) {
      case 'bullish':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'bearish':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  const getSentimentColor = (sentiment?: string) => {
    switch (sentiment) {
      case 'bullish':
        return 'border-l-green-500'
      case 'bearish':
        return 'border-l-red-500'
      default:
        return 'border-l-gray-500'
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <div className="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
              <div className="space-y-1">
                <div className="h-4 bg-gray-300 rounded w-24"></div>
                <div className="h-3 bg-gray-300 rounded w-16"></div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-4 bg-gray-300 rounded w-full"></div>
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Connection status */}
      {!isConnected && (
        <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
          <p className="text-sm text-yellow-600">
            Real-time updates are currently offline. Posts may not be up to date.
          </p>
        </div>
      )}

      {/* Posts */}
      {posts.map((post) => (
        <Card key={post.id} className={`border-l-4 ${getSentimentColor(post.sentiment)}`}>
          <CardHeader className="flex flex-row items-start space-y-0 pb-2">
            <Avatar className="w-10 h-10 mr-3">
              <AvatarImage src={post.author.avatar} />
              <AvatarFallback>
                {post.author.username?.[0] || post.author.address.slice(2, 4).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <h4 className="font-semibold text-sm truncate">
                  {post.author.username || formatAddress(post.author.address)}
                </h4>
                {post.author.verified && (
                  <Badge variant="secondary" className="text-xs">
                    ✓
                  </Badge>
                )}
                <Badge variant="outline" className="text-xs">
                  Rep: {post.author.reputation}
                </Badge>
                {getSentimentIcon(post.sentiment)}
              </div>
              <p className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(post.timestamp), { addSuffix: true })}
              </p>
            </div>
            
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </CardHeader>
          
          <CardContent className="space-y-3">
            <p className="text-sm leading-relaxed">{post.content}</p>
            
            {/* Tags */}
            {post.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {post.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    #{tag}
                  </Badge>
                ))}
              </div>
            )}
            
            {/* Attachments */}
            {post.attachments && post.attachments.length > 0 && (
              <div className="space-y-2">
                {post.attachments.map((attachment, index) => (
                  <div key={index} className="border rounded-lg p-3 bg-muted/50">
                    {attachment.type === 'chart' && (
                      <div className="text-sm text-muted-foreground">
                        📊 Chart Analysis Attached
                      </div>
                    )}
                    {attachment.type === 'token' && (
                      <div className="text-sm text-muted-foreground">
                        🪙 Token: {attachment.tokenSymbol}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
            
            {/* Actions */}
            <div className="flex items-center justify-between pt-2 border-t">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleLike(post.id)}
                  className={`gap-1 ${post.isLiked ? 'text-red-500' : ''}`}
                >
                  <Heart className={`h-4 w-4 ${post.isLiked ? 'fill-current' : ''}`} />
                  {post.likes}
                </Button>
                
                <Button variant="ghost" size="sm" className="gap-1">
                  <MessageCircle className="h-4 w-4" />
                  {post.comments}
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleShare(post.id)}
                  className="gap-1"
                >
                  <Share2 className="h-4 w-4" />
                  {post.shares}
                </Button>
              </div>
              
              <div className="text-xs text-muted-foreground">
                {post.sentiment && (
                  <Badge variant="outline" className="text-xs">
                    {post.sentiment}
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
      
      {/* Load more */}
      {hasMore && (
        <div className="text-center">
          <Button variant="outline" onClick={() => setPage(prev => prev + 1)}>
            Load More Posts
          </Button>
        </div>
      )}
    </div>
  )
}
