"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { TrendingUp, TrendingDown, Hash, Users, Crown, Star } from 'lucide-react'

interface TrendingTopic {
  tag: string
  posts: number
  change: number
  sentiment: 'bullish' | 'bearish' | 'neutral'
}

interface SuggestedUser {
  address: string
  username?: string
  avatar?: string
  verified: boolean
  reputation: number
  followers: number
  bio?: string
  isFollowing: boolean
}

interface TopTrader {
  address: string
  username?: string
  avatar?: string
  verified: boolean
  pnl24h: number
  winRate: number
  totalTrades: number
}

export function SocialSidebar() {
  const [trendingTopics, setTrendingTopics] = useState<TrendingTopic[]>([])
  const [suggestedUsers, setSuggestedUsers] = useState<SuggestedUser[]>([])
  const [topTraders, setTopTraders] = useState<TopTrader[]>([])

  useEffect(() => {
    // Mock data - in real app, fetch from API
    setTrendingTopics([
      { tag: 'DOGE', posts: 1247, change: 15.3, sentiment: 'bullish' },
      { tag: 'SHIB', posts: 892, change: -8.2, sentiment: 'bearish' },
      { tag: 'PEPE', posts: 634, change: 23.1, sentiment: 'bullish' },
      { tag: 'ToTheMoon', posts: 445, change: 5.7, sentiment: 'bullish' },
      { tag: 'DiamondHands', posts: 321, change: -2.1, sentiment: 'neutral' },
    ])

    setSuggestedUsers([
      {
        address: '0x1234...5678',
        username: 'CryptoWhale',
        verified: true,
        reputation: 98,
        followers: 15420,
        bio: 'DeFi researcher & memecoin enthusiast',
        isFollowing: false,
      },
      {
        address: '0x9876...4321',
        username: 'DogeAnalyst',
        verified: true,
        reputation: 94,
        followers: 8930,
        bio: 'Technical analysis for memecoins',
        isFollowing: false,
      },
      {
        address: '0x5555...7777',
        username: 'ShibArmy',
        verified: false,
        reputation: 87,
        followers: 5670,
        bio: 'SHIB community leader',
        isFollowing: false,
      },
    ])

    setTopTraders([
      {
        address: '0xaaaa...bbbb',
        username: 'AlphaTrader',
        verified: true,
        pnl24h: 15420.50,
        winRate: 78.5,
        totalTrades: 234,
      },
      {
        address: '0xcccc...dddd',
        username: 'MemeKing',
        verified: true,
        pnl24h: 8930.25,
        winRate: 72.1,
        totalTrades: 189,
      },
      {
        address: '0xeeee...ffff',
        username: 'DogeCoin',
        verified: false,
        pnl24h: 5670.75,
        winRate: 69.8,
        totalTrades: 156,
      },
    ])
  }, [])

  const handleFollow = (userAddress: string) => {
    setSuggestedUsers(prev => 
      prev.map(user => 
        user.address === userAddress 
          ? { ...user, isFollowing: !user.isFollowing }
          : user
      )
    )
  }

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  return (
    <div className="space-y-6">
      {/* Trending Topics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Hash className="h-5 w-5" />
            Trending Topics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {trendingTopics.map((topic, index) => (
            <div key={topic.tag} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-muted-foreground">
                  {index + 1}
                </span>
                <div>
                  <div className="flex items-center gap-1">
                    <span className="font-medium">#{topic.tag}</span>
                    {topic.sentiment === 'bullish' && (
                      <TrendingUp className="h-3 w-3 text-green-500" />
                    )}
                    {topic.sentiment === 'bearish' && (
                      <TrendingDown className="h-3 w-3 text-red-500" />
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {formatNumber(topic.posts)} posts
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-sm font-medium ${
                  topic.change >= 0 ? 'text-green-500' : 'text-red-500'
                }`}>
                  {topic.change >= 0 ? '+' : ''}{topic.change.toFixed(1)}%
                </div>
              </div>
            </div>
          ))}
          
          <Button variant="ghost" className="w-full text-sm">
            View All Trending
          </Button>
        </CardContent>
      </Card>

      {/* Top Traders */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Crown className="h-5 w-5 text-yellow-500" />
            Top Traders (24h)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {topTraders.map((trader, index) => (
            <div key={trader.address} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-muted-foreground">
                  {index + 1}
                </span>
                <Avatar className="w-8 h-8">
                  <AvatarImage src={trader.avatar} />
                  <AvatarFallback>
                    {trader.username?.[0] || trader.address.slice(2, 4).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-1">
                    <span className="font-medium text-sm">
                      {trader.username || formatAddress(trader.address)}
                    </span>
                    {trader.verified && (
                      <Badge variant="secondary" className="text-xs">✓</Badge>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {trader.winRate.toFixed(1)}% win rate
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-sm font-medium ${
                  trader.pnl24h >= 0 ? 'text-green-500' : 'text-red-500'
                }`}>
                  {trader.pnl24h >= 0 ? '+' : ''}${formatNumber(trader.pnl24h)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {trader.totalTrades} trades
                </p>
              </div>
            </div>
          ))}
          
          <Button variant="ghost" className="w-full text-sm">
            View Leaderboard
          </Button>
        </CardContent>
      </Card>

      {/* Suggested Users */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Users className="h-5 w-5" />
            Who to Follow
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {suggestedUsers.map((user) => (
            <div key={user.address} className="space-y-2">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={user.avatar} />
                    <AvatarFallback>
                      {user.username?.[0] || user.address.slice(2, 4).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-1">
                      <h4 className="font-medium text-sm truncate">
                        {user.username || formatAddress(user.address)}
                      </h4>
                      {user.verified && (
                        <Badge variant="secondary" className="text-xs">✓</Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{formatNumber(user.followers)} followers</span>
                      <span>•</span>
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3" />
                        {user.reputation}
                      </div>
                    </div>
                  </div>
                </div>
                <Button
                  variant={user.isFollowing ? "outline" : "default"}
                  size="sm"
                  onClick={() => handleFollow(user.address)}
                  className="ml-2"
                >
                  {user.isFollowing ? 'Following' : 'Follow'}
                </Button>
              </div>
              
              {user.bio && (
                <p className="text-xs text-muted-foreground pl-12">
                  {user.bio}
                </p>
              )}
            </div>
          ))}
          
          <Button variant="ghost" className="w-full text-sm">
            Find More Users
          </Button>
        </CardContent>
      </Card>

      {/* Community Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Community Stats</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Active Users</span>
            <span className="font-medium">12.4K</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Posts Today</span>
            <span className="font-medium">3.2K</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Total Volume</span>
            <span className="font-medium">$2.1M</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Top Gainers</span>
            <span className="font-medium text-green-500">+15.3%</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
