"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { useResponsive } from "@/hooks/use-responsive"

export function PerformanceMonitor() {
  const { isMobile } = useResponsive()
  const [fps, setFps] = useState(0)
  const [memory, setMemory] = useState(0)
  const [cpuUsage, setCpuUsage] = useState(0)
  const [renderTime, setRenderTime] = useState(0)
  const frameRef = useRef(0)
  const lastTimeRef = useRef(performance.now())
  const framesRef = useRef(0)
  const renderStartTimeRef = useRef(performance.now())

  useEffect(() => {
    // Start measuring render time
    renderStartTimeRef.current = performance.now()

    // Measure FPS
    const measureFps = () => {
      const now = performance.now()
      const elapsed = now - lastTimeRef.current

      if (elapsed >= 1000) {
        setFps(Math.round((framesRef.current * 1000) / elapsed))
        framesRef.current = 0
        lastTimeRef.current = now

        // Simulate CPU usage (in a real app, you'd use more sophisticated metrics)
        setCpuUsage(Math.min(100, Math.random() * 30 + fps / 2))

        // Simulate memory usage (in a real app, you'd use actual memory metrics)
        if (window.performance && (performance as any).memory) {
          const memoryInfo = (performance as any).memory
          setMemory(Math.round((memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100))
        } else {
          // Fallback for browsers that don't support memory API
          setMemory(Math.min(100, Math.random() * 40 + 30))
        }
      }

      framesRef.current++
      frameRef.current = requestAnimationFrame(measureFps)
    }

    frameRef.current = requestAnimationFrame(measureFps)

    // Measure render time when component is mounted
    const renderEndTime = performance.now()
    setRenderTime(renderEndTime - renderStartTimeRef.current)

    return () => {
      cancelAnimationFrame(frameRef.current)
    }
  }, [])

  return (
    <Card className="glass-card border-white/5">
      <CardHeader className="pb-2">
        <CardTitle className="text-white text-lg">Performance Monitor</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-white/80">FPS</span>
            <span
              className={`text-sm font-medium ${fps < 30 ? "text-red-500" : fps < 50 ? "text-yellow-500" : "text-green-500"}`}
            >
              {fps}
            </span>
          </div>
          <Progress
            value={fps / 0.6}
            className="h-2 bg-white/10"
            indicatorClassName={fps < 30 ? "bg-red-500" : fps < 50 ? "bg-yellow-500" : "bg-green-500"}
          />
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-white/80">Memory Usage</span>
            <span
              className={`text-sm font-medium ${memory > 80 ? "text-red-500" : memory > 60 ? "text-yellow-500" : "text-green-500"}`}
            >
              {memory}%
            </span>
          </div>
          <Progress
            value={memory}
            className="h-2 bg-white/10"
            indicatorClassName={memory > 80 ? "bg-red-500" : memory > 60 ? "bg-yellow-500" : "bg-green-500"}
          />
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-white/80">CPU Usage</span>
            <span
              className={`text-sm font-medium ${cpuUsage > 80 ? "text-red-500" : cpuUsage > 60 ? "text-yellow-500" : "text-green-500"}`}
            >
              {cpuUsage.toFixed(1)}%
            </span>
          </div>
          <Progress
            value={cpuUsage}
            className="h-2 bg-white/10"
            indicatorClassName={cpuUsage > 80 ? "bg-red-500" : cpuUsage > 60 ? "bg-yellow-500" : "bg-green-500"}
          />
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-white/80">Initial Render Time</span>
            <span
              className={`text-sm font-medium ${renderTime > 100 ? "text-red-500" : renderTime > 50 ? "text-yellow-500" : "text-green-500"}`}
            >
              {renderTime.toFixed(2)}ms
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
