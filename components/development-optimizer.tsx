"use client"

import { useEffect } from 'react'

export function DevelopmentOptimizer() {
  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') return

    console.log('Development optimizer active - reducing overhead')

    // Reduce performance monitoring overhead
    const optimizeDevelopment = () => {
      // Disable heavy performance monitoring in development
      if (typeof window !== 'undefined') {
        // Reduce intersection observer frequency
        const originalIntersectionObserver = window.IntersectionObserver
        window.IntersectionObserver = class extends originalIntersectionObserver {
          constructor(callback: IntersectionObserverCallback, options?: IntersectionObserverInit) {
            // Reduce frequency of intersection checks
            const optimizedOptions = {
              ...options,
              rootMargin: options?.rootMargin || '100px', // Larger margin for earlier loading
            }
            super(callback, optimizedOptions)
          }
        }

        // Optimize animation frame requests - less aggressive throttling
        const originalRequestAnimationFrame = window.requestAnimationFrame
        let rafThrottle = false
        window.requestAnimationFrame = (callback: FrameRequestCallback) => {
          if (rafThrottle) return originalRequestAnimationFrame(callback) // Don't block, just pass through
          rafThrottle = true
          return originalRequestAnimationFrame(() => {
            callback(performance.now())
            setTimeout(() => { rafThrottle = false }, 8) // ~120fps max, less aggressive
          })
        }

        // Reduce console logging overhead
        const originalConsoleLog = console.log
        console.log = (...args: any[]) => {
          // Only log important messages in development
          if (args[0]?.includes?.('Arc Browser') || 
              args[0]?.includes?.('Performance') ||
              args[0]?.includes?.('Error') ||
              args[0]?.includes?.('Warning')) {
            originalConsoleLog.apply(console, args)
          }
        }

        // Optimize mutation observers - less aggressive for animations
        const originalMutationObserver = window.MutationObserver
        window.MutationObserver = class extends originalMutationObserver {
          constructor(callback: MutationCallback) {
            // Light throttling for mutation observer callbacks
            let mutationThrottle = false
            const throttledCallback: MutationCallback = (mutations, observer) => {
              if (mutationThrottle) {
                // Don't drop mutations, just delay them slightly
                setTimeout(() => callback(mutations, observer), 16)
                return
              }
              mutationThrottle = true
              callback(mutations, observer)
              setTimeout(() => { mutationThrottle = false }, 32) // Light throttling
            }
            super(throttledCallback)
          }
        }

        // Reduce resize event frequency
        const originalAddEventListener = window.addEventListener
        window.addEventListener = (type: string, listener: any, options?: any) => {
          if (type === 'resize') {
            let resizeThrottle = false
            const throttledListener = (event: Event) => {
              if (resizeThrottle) return
              resizeThrottle = true
              setTimeout(() => {
                listener(event)
                resizeThrottle = false
              }, 250) // Throttle resize events
            }
            return originalAddEventListener.call(window, type, throttledListener, options)
          }
          return originalAddEventListener.call(window, type, listener, options)
        }
      }
    }

    // Apply optimizations
    optimizeDevelopment()

    // Cleanup function
    return () => {
      // Restore original functions if needed
      console.log('Development optimizer cleanup')
    }
  }, [])

  return null // This component doesn't render anything
}

// Hook for development performance monitoring
export function useDevelopmentPerformance() {
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return

    let startTime = performance.now()
    let pageLoadTime = 0

    const measurePageLoad = () => {
      pageLoadTime = performance.now() - startTime
      if (pageLoadTime > 5000) { // More than 5 seconds
        console.warn(`Slow page load detected: ${Math.round(pageLoadTime)}ms`)
        
        // Suggest optimizations
        const suggestions = [
          'Consider reducing the number of dynamic imports',
          'Check for large bundle sizes in development',
          'Verify that heavy components are properly lazy-loaded',
          'Consider using React.memo for expensive components'
        ]
        
        console.group('Performance Suggestions:')
        suggestions.forEach(suggestion => console.log(`• ${suggestion}`))
        console.groupEnd()
      }
    }

    // Measure when page is fully loaded
    if (document.readyState === 'complete') {
      measurePageLoad()
    } else {
      window.addEventListener('load', measurePageLoad, { once: true })
    }

    // Monitor for slow navigation
    const navigationStart = performance.now()
    const checkNavigation = () => {
      const navTime = performance.now() - navigationStart
      if (navTime > 3000) {
        console.warn(`Slow navigation detected: ${Math.round(navTime)}ms`)
      }
    }

    // Check navigation performance after a delay
    setTimeout(checkNavigation, 3000)

    return () => {
      window.removeEventListener('load', measurePageLoad)
    }
  }, [])
}

// Development-specific performance tips
export function showDevelopmentTips() {
  if (process.env.NODE_ENV !== 'development') return

  const tips = [
    '🚀 For faster development builds, avoid importing large libraries in components that re-render frequently',
    '⚡ Use React.memo() for components that receive the same props often',
    '📦 Check bundle size with: npm run build && npm run analyze',
    '🔍 Use React DevTools Profiler to identify slow components',
    '💾 Clear Next.js cache if builds are slow: rm -rf .next',
    '🌐 Test in production mode occasionally: npm run build && npm run start'
  ]

  console.group('🛠️ Development Performance Tips:')
  tips.forEach(tip => console.log(tip))
  console.groupEnd()
}

// Auto-show tips on first load
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  const hasShownTips = sessionStorage.getItem('dev-tips-shown')
  if (!hasShownTips) {
    setTimeout(() => {
      showDevelopmentTips()
      sessionStorage.setItem('dev-tips-shown', 'true')
    }, 2000)
  }
}
