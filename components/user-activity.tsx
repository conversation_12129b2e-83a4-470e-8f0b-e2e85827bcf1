"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Enhanced mock data for user activity with more realistic patterns
const generateUserData = (timeframe: string) => {
  const data = []
  let days = 7
  let pointsPerDay = 1

  switch (timeframe) {
    case "24h":
      days = 1
      pointsPerDay = 24
      break
    case "7d":
      days = 7
      pointsPerDay = 1
      break
    case "30d":
      days = 30
      pointsPerDay = 1
      break
    case "90d":
      days = 90
      pointsPerDay = 1
      break
    default:
      days = 7
      pointsPerDay = 1
  }

  const totalPoints = days * pointsPerDay

  for (let i = 0; i < totalPoints; i++) {
    const date = new Date()

    if (timeframe === "24h") {
      date.setHours(date.getHours() - i)
    } else {
      date.setDate(date.getDate() - i)
    }

    // More realistic patterns with day/night cycles and weekend effects
    const dayOfWeek = date.getDay()
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
    const weekendMultiplier = isWeekend ? 0.8 : 1

    // Hour-based activity for 24h view
    const hour = date.getHours()
    const hourMultiplier = timeframe === "24h"
      ? 0.5 + 0.5 * Math.sin((hour - 6) * Math.PI / 12) // Peak around 2-6 PM
      : 1

    const baseNewUsers = (120 + Math.sin(i * 0.15) * 30 + Math.random() * 40) * weekendMultiplier * hourMultiplier
    const baseActiveUsers = (1100 + Math.sin(i * 0.1) * 200 + Math.random() * 300) * weekendMultiplier * hourMultiplier
    const baseTransactions = (2200 + Math.sin(i * 0.08) * 400 + Math.random() * 500) * weekendMultiplier * hourMultiplier
    const baseReturnUsers = (850 + Math.sin(i * 0.12) * 150 + Math.random() * 200) * weekendMultiplier * hourMultiplier

    // Add growth trend over time
    const growthFactor = 1 + (totalPoints - i) / totalPoints * 0.3

    data.push({
      date: timeframe === "24h"
        ? date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
        : date.toLocaleDateString([], { month: "short", day: "numeric" }),
      newUsers: Math.round(baseNewUsers * growthFactor),
      activeUsers: Math.round(baseActiveUsers * growthFactor),
      transactions: Math.round(baseTransactions * growthFactor),
      returnUsers: Math.round(baseReturnUsers * growthFactor),
      timestamp: date.getTime()
    })
  }

  return data.reverse()
}

// Dynamic mock data for user actions based on timeframe
const generateUserActions = (timeframe: string) => {
  const baseActions = [
    { name: "Trading", value: 45 },
    { name: "Token Creation", value: 15 },
    { name: "Liquidity Provision", value: 20 },
    { name: "Governance", value: 10 },
    { name: "Social", value: 10 },
  ]

  // Adjust percentages based on timeframe
  switch (timeframe) {
    case "24h":
      return [
        { name: "Trading", value: 50 },
        { name: "Token Creation", value: 12 },
        { name: "Liquidity Provision", value: 18 },
        { name: "Governance", value: 8 },
        { name: "Social", value: 12 },
      ]
    case "7d":
      return baseActions
    case "30d":
      return [
        { name: "Trading", value: 42 },
        { name: "Token Creation", value: 18 },
        { name: "Liquidity Provision", value: 22 },
        { name: "Governance", value: 12 },
        { name: "Social", value: 6 },
      ]
    case "90d":
      return [
        { name: "Trading", value: 38 },
        { name: "Token Creation", value: 20 },
        { name: "Liquidity Provision", value: 25 },
        { name: "Governance", value: 15 },
        { name: "Social", value: 2 },
      ]
    default:
      return baseActions
  }
}

// Mock data for top users
const topUsers = [
  { name: "DogeWhale", handle: "dogewhale", avatar: "/placeholder.svg?key=jg7nh", score: 12500 },
  { name: "MoonShot", handle: "moonshot", avatar: "/placeholder.svg?key=5s4nu", score: 10750 },
  { name: "CryptoBarker", handle: "cryptobarker", avatar: "/placeholder.svg?key=1yd2z", score: 9200 },
  { name: "TokenMaster", handle: "tokenmaster", avatar: "/placeholder.svg?key=2o150", score: 8100 },
  { name: "DogeFan", handle: "dogefan", avatar: "/placeholder.svg?key=bm5k2", score: 7300 },
]

const COLORS = ["#FFC107", "#8A2BE2", "#FF6B6B", "#4CAF50", "#2196F3"]

export function UserActivity({ timeframe }: { timeframe: string }) {
  const userData = generateUserData(timeframe)
  const userActions = generateUserActions(timeframe)

  return (
    <div className="space-y-6">
      <Card className="glass-card border-white/5 liquid-glow">
        <CardHeader>
          <CardTitle className="text-white">User Activity</CardTitle>
          <CardDescription className="text-white/70">New users, active users, and transactions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={userData} margin={{ top: 5, right: 30, left: 20, bottom: 25 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis
                  dataKey="date"
                  stroke="rgba(255,255,255,0.5)"
                  tick={{ fill: "rgba(255,255,255,0.5)" }}
                  tickLine={{ stroke: "rgba(255,255,255,0.2)" }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis
                  stroke="rgba(255,255,255,0.5)"
                  tick={{ fill: "rgba(255,255,255,0.5)" }}
                  tickLine={{ stroke: "rgba(255,255,255,0.2)" }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px'
                  }}
                  labelStyle={{ color: 'white', fontWeight: 'bold', marginBottom: '8px' }}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="newUsers"
                  stroke="#FFC107"
                  fill="#FFC107"
                  fillOpacity={0.3}
                  stackId="1"
                  name="New Users"
                />
                <Area
                  type="monotone"
                  dataKey="activeUsers"
                  stroke="#8A2BE2"
                  fill="#8A2BE2"
                  fillOpacity={0.3}
                  stackId="2"
                  name="Active Users"
                />
                <Area
                  type="monotone"
                  dataKey="transactions"
                  stroke="#4CAF50"
                  fill="#4CAF50"
                  fillOpacity={0.3}
                  stackId="3"
                  name="Transactions"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card className="glass-card border-white/5 liquid-glow">
          <CardHeader>
            <CardTitle className="text-white">User Actions</CardTitle>
            <CardDescription className="text-white/70">Distribution of user activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[220px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                  <Pie
                    data={userActions}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={70}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                  >
                    {userActions.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value: number) => [`${value}%`, "Percentage"]}
                    contentStyle={{
                      backgroundColor: "rgba(20, 20, 25, 0.8)",
                      borderColor: "rgba(255, 255, 255, 0.1)",
                      borderRadius: "0.5rem",
                      color: "white",
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5 liquid-glow">
          <CardHeader>
            <CardTitle className="text-white">Top Users</CardTitle>
            <CardDescription className="text-white/70">Most active users by engagement score</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topUsers.slice(0, 5).map((user, index) => (
                <div key={user.handle} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-white/10 text-xs font-bold text-white">
                      {index + 1}
                    </div>
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
                      <AvatarFallback>{user.name.slice(0, 2)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-white">{user.name}</p>
                      <p className="text-xs text-white/60">@{user.handle}</p>
                    </div>
                  </div>
                  <p className="font-medium text-doge">{user.score.toLocaleString()} pts</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
