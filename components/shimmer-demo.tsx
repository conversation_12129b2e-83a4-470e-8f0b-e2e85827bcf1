"use client"
import { ShimmerText } from "./shimmer-text"

export function ShimmerDemo() {
  return (
    <div className="flex flex-col items-center justify-center space-y-8 p-8">
      <h1 className="text-4xl font-bold">
        <ShimmerText>The PawPumps Advantage</ShimmerText>
      </h1>

      <p className="text-xl max-w-2xl text-center">
        Enjoy a premium experience with features designed for both beginners and advanced traders
      </p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-4xl mt-8">
        <div className="bg-black p-6 rounded-lg flex flex-col items-center text-center">
          <div className="w-16 h-16 rounded-full bg-yellow-500 flex items-center justify-center mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-black"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">
            <ShimmerText>Enhanced Security</ShimmerText>
          </h3>
          <p className="text-gray-300">State-of-the-art protection for your assets and transactions</p>
        </div>

        <div className="bg-black p-6 rounded-lg flex flex-col items-center text-center">
          <div className="w-16 h-16 rounded-full bg-yellow-500 flex items-center justify-center mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-black"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">
            <ShimmerText>Ultra-Low Fees</ShimmerText>
          </h3>
          <p className="text-gray-300">Trade with minimal costs and maximize your profits</p>
        </div>

        <div className="bg-black p-6 rounded-lg flex flex-col items-center text-center">
          <div className="w-16 h-16 rounded-full bg-yellow-500 flex items-center justify-center mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-black"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">
            <ShimmerText>Social Tools</ShimmerText>
          </h3>
          <p className="text-gray-300">Connect with other traders and share strategies</p>
        </div>
      </div>
    </div>
  )
}
