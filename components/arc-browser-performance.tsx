"use client"

import { useEffect } from 'react'

export function ArcBrowserPerformanceOptimizer() {
  useEffect(() => {
    // Precise Arc Browser detection - only detect actual Arc browser
    const userAgent = navigator.userAgent
    const isArcBrowser = userAgent.includes('Arc/') && userAgent.includes('Safari') && !userAgent.includes('Chrome')

    if (!isArcBrowser) return

    console.log('Arc Browser detected - applying comprehensive fixes')

    // Force background fixes for Arc Browser only
    const applyArcBrowserFixes = () => {
      // Force background
      document.documentElement.style.setProperty('background', 'hsl(240, 10%, 4%)', 'important')
      document.documentElement.style.setProperty('background-color', 'hsl(240, 10%, 4%)', 'important')
      document.body.style.setProperty('background', 'hsl(240, 10%, 4%)', 'important')
      document.body.style.setProperty('background-color', 'hsl(240, 10%, 4%)', 'important')

      // Only fix specific text effects that break in Arc, preserve original styles
      const heroTitle = document.querySelector('.hero-title')
      if (heroTitle) {
        const element = heroTitle as HTMLElement
        element.style.setProperty('background', 'linear-gradient(135deg, #8B5CF6, #3B82F6, #10B981)', 'important')
        element.style.setProperty('-webkit-background-clip', 'text', 'important')
        element.style.setProperty('background-clip', 'text', 'important')
        element.style.setProperty('-webkit-text-fill-color', 'transparent', 'important')
      }

      // Preserve original text-shadow effects for doge elements
      const dogeElements = document.querySelectorAll('.doge-text-glow')
      dogeElements.forEach((el) => {
        const element = el as HTMLElement
        element.style.setProperty('text-shadow', '0 0 10px rgba(255, 193, 7, 0.5)', 'important')
      })

      const dogechainElements = document.querySelectorAll('.dogechain-text-glow')
      dogechainElements.forEach((el) => {
        const element = el as HTMLElement
        element.style.setProperty('text-shadow', '0 0 10px rgba(138, 43, 226, 0.5)', 'important')
      })
    }

    // Apply fixes immediately and on DOM changes
    applyArcBrowserFixes()

    // Re-apply fixes when new content loads
    const observer = new MutationObserver(() => {
      applyArcBrowserFixes()
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    // Arc Browser specific performance optimizations
    let isScrolling = false
    let scrollTimeout: NodeJS.Timeout
    let animationElements: Element[] = []

    // Find only truly animated elements (not static content)
    const findAnimatedElements = () => {
      animationElements = Array.from(document.querySelectorAll([
        // Only target actual animated elements, not static content
        'canvas', // Background animations
        '.shimmer-text',
        '.text-glow',
        '.doge-text-glow',
        '.dogechain-text-glow',
        '.gradient-text',
        '.pawpumps-text',
        '.border-glow',
        '[class*="animate-pulse"]',
        '[class*="animate-bounce"]',
        '[class*="animate-spin"]',
        '[class*="animate-ping"]',
        '[class*="animate-fade"]',
        '[class*="animate-float"]',
        '[class*="animate-shimmer"]',
        // Exclude static elements that shouldn't be modified
        ':not(h1):not(h2):not(h3):not(h4):not(h5):not(h6):not(p):not(nav):not(footer):not(.navbar):not(.footer)'
      ].join(',')))

      // Filter out static content elements to prevent reloading issues
      animationElements = animationElements.filter(element => {
        const tagName = element.tagName.toLowerCase()
        const classList = element.classList

        // Exclude static content elements
        if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'nav', 'footer', 'header'].includes(tagName)) {
          return false
        }

        // Exclude navigation and layout elements
        if (classList.contains('navbar') || classList.contains('footer') || classList.contains('header')) {
          return false
        }

        return true
      })
    }

    // Force animations to continue during scroll with memory optimization
    const forceAnimationContinuation = () => {
      // Use requestAnimationFrame to batch DOM operations
      requestAnimationFrame(() => {
        animationElements.forEach(element => {
          const htmlElement = element as HTMLElement
          const tagName = htmlElement.tagName.toLowerCase()

          // Skip static content elements to prevent reloading issues
          if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'nav', 'footer', 'header'].includes(tagName)) {
            return
          }

          // Only apply optimizations to truly animated elements
          if (htmlElement.tagName === 'CANVAS' ||
              htmlElement.classList.contains('shimmer-text') ||
              htmlElement.classList.contains('text-glow') ||
              htmlElement.classList.contains('gradient-text') ||
              htmlElement.className.includes('animate-')) {

            // Force GPU acceleration only if not already set
            if (!htmlElement.style.transform.includes('translateZ')) {
              htmlElement.style.transform = (htmlElement.style.transform || '') + ' translateZ(0)'
            }

            // Set will-change only for animated elements during scroll
            htmlElement.style.willChange = 'transform, opacity, background-position'

            // Prevent animation pausing
            htmlElement.style.animationPlayState = 'running'
            htmlElement.style.setProperty('-webkit-animation-play-state', 'running')

            // Force repaint only for canvas and critical animated elements
            if (htmlElement.tagName === 'CANVAS' || htmlElement.classList.contains('shimmer-text')) {
              htmlElement.offsetHeight
            }
          }
        })
      })
    }

    // Handle scroll events
    const handleScroll = () => {
      if (!isScrolling) {
        // Only run expensive operations on first scroll event
        isScrolling = true
        forceAnimationContinuation()
      }

      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        isScrolling = false

        // Clean up will-change after scroll ends to save memory
        requestAnimationFrame(() => {
          animationElements.forEach(element => {
            const htmlElement = element as HTMLElement
            htmlElement.style.willChange = 'auto'
          })
        })
      }, 200)
    }

    // Optimize CSS animations for Arc Browser
    const optimizeAnimations = () => {
      // Create Arc Browser specific CSS optimizations
      const style = document.createElement('style')
      style.id = 'arc-browser-animation-fix'
      style.textContent = `
        /* Arc Browser Animation Optimizations - Targeted Only */
        @media screen and (-webkit-min-device-pixel-ratio: 0) {
          /* Only apply to animated elements, not static content */
          canvas,
          .shimmer-text,
          .text-glow,
          .gradient-text,
          [class*="animate-"] {
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            -webkit-perspective: 1000px;
            perspective: 1000px;
          }
          
          [class*="animate-"],
          [class*="transition-"],
          [style*="animation"],
          [style*="transition"] {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            will-change: transform, opacity;
            animation-play-state: running !important;
            -webkit-animation-play-state: running !important;
          }
          
          /* Force text effects to continue during scroll */
          .shimmer,
          .shimmer-text,
          .shimmer-text::after,
          .glow,
          .text-glow,
          .doge-text-glow,
          .dogechain-text-glow,
          .gradient-text,
          .pawpumps-text,
          .border-glow,
          .border-glow::before,
          .pulse,
          .bounce,
          .spin,
          .ping,
          .fade,
          .float {
            animation-play-state: running !important;
            -webkit-animation-play-state: running !important;
            will-change: transform, opacity, background-position !important;
            -webkit-transform: translateZ(0) !important;
            transform: translateZ(0) !important;
          }

          /* Specific shimmer text optimizations */
          .shimmer-text::after {
            animation: shimmer 3s infinite !important;
            animation-play-state: running !important;
            -webkit-animation-play-state: running !important;
          }
          
          /* Canvas optimizations */
          canvas {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            will-change: contents;
          }
          
          /* Prevent scroll-based animation pausing - Only for animated elements */
          body.scrolling canvas,
          body.scrolling .shimmer-text,
          body.scrolling .text-glow,
          body.scrolling .gradient-text,
          body.scrolling [class*="animate-"] {
            animation-play-state: running !important;
            -webkit-animation-play-state: running !important;
          }

          /* Ensure static content is not affected */
          body.scrolling h1,
          body.scrolling h2,
          body.scrolling h3,
          body.scrolling h4,
          body.scrolling h5,
          body.scrolling h6,
          body.scrolling p,
          body.scrolling nav,
          body.scrolling footer,
          body.scrolling .navbar,
          body.scrolling .footer {
            will-change: auto !important;
            transform: none !important;
            animation-play-state: initial !important;
          }
        }
      `
      
      // Remove existing optimization and add new one
      const existingStyle = document.getElementById('arc-browser-animation-fix')
      if (existingStyle) {
        existingStyle.remove()
      }
      document.head.appendChild(style)
    }

    // Add scroll class to body during scroll
    const addScrollClass = () => {
      document.body.classList.add('scrolling')
      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        document.body.classList.remove('scrolling')
      }, 200)
    }

    // Initialize optimizations
    optimizeAnimations()
    findAnimatedElements()

    // Add event listeners
    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('wheel', handleScroll, { passive: true })
    window.addEventListener('touchmove', handleScroll, { passive: true })
    
    window.addEventListener('scroll', addScrollClass, { passive: true })
    window.addEventListener('wheel', addScrollClass, { passive: true })

    // Re-scan for animated elements periodically
    const rescanInterval = setInterval(findAnimatedElements, 2000)

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('wheel', handleScroll)
      window.removeEventListener('touchmove', handleScroll)
      window.removeEventListener('scroll', addScrollClass)
      window.removeEventListener('wheel', addScrollClass)

      clearTimeout(scrollTimeout)
      clearInterval(rescanInterval)
      observer.disconnect()

      // Remove optimization styles
      const style = document.getElementById('arc-browser-animation-fix')
      if (style) {
        style.remove()
      }

      // Clean up will-change properties
      animationElements.forEach(element => {
        const htmlElement = element as HTMLElement
        htmlElement.style.willChange = 'auto'
      })

      document.body.classList.remove('scrolling')
    }
  }, [])

  return null // This component doesn't render anything
}
