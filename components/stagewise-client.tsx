'use client'

import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'

// Dynamically import the Stagewise toolbar to avoid SSR issues
const StagewiseToolbar = dynamic(
  () => import('@stagewise/toolbar-next').then(mod => ({ default: mod.StagewiseToolbar })),
  {
    ssr: false,
    loading: () => null
  }
)

export function StagewiseClient() {
  const [isConnected, setIsConnected] = useState(false)
  const [extensionPort, setExtensionPort] = useState<number | null>(null)
  const [showIndicator, setShowIndicator] = useState(false)

  useEffect(() => {
    // Test connection to VS Code extension
    const testConnection = async () => {
      try {
        console.log('🔄 Testing Stagewise extension connection...')

        for (let port = 5746; port <= 5756; port++) {
          try {
            const response = await fetch(`http://localhost:${port}/ping/stagewise`, {
              method: 'GET',
              headers: { 'Accept': 'text/plain' }
            })
            if (response.ok) {
              const result = await response.text()
              if (result === 'stagewise') {
                console.log(`✅ VS Code extension found on port ${port}`)
                setExtensionPort(port)
                setIsConnected(true)
                setShowIndicator(true)

                // Hide indicator after 5 seconds
                setTimeout(() => setShowIndicator(false), 5000)
                return port
              }
            }
          } catch (error) {
            // Continue to next port
          }
        }
        throw new Error('VS Code extension not found on ports 5746-5756')
      } catch (error) {
        console.error('❌ Failed to connect to Stagewise extension:', error)
        setIsConnected(false)
        setShowIndicator(true)

        // Hide error indicator after 8 seconds
        setTimeout(() => setShowIndicator(false), 8000)
      }
    }

    // Only test connection in development and on localhost
    if (process.env.NODE_ENV === 'development' &&
        typeof window !== 'undefined' &&
        window.location.hostname === 'localhost') {
      console.log('🚀 Development mode detected, testing Stagewise connection...')
      testConnection()
    } else {
      console.log('⚠️ Stagewise only available in development mode on localhost')
    }
  }, [])

  // Only render the toolbar if we're connected and in development
  const shouldRenderToolbar = process.env.NODE_ENV === 'development' &&
                              typeof window !== 'undefined' &&
                              window.location.hostname === 'localhost' &&
                              isConnected

  return (
    <>
      {/* Connection status indicator */}
      {showIndicator && (
        <div
          style={{
            position: 'fixed',
            top: '10px',
            right: '10px',
            background: isConnected ? '#10b981' : '#ef4444',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '6px',
            fontSize: '12px',
            zIndex: 10000,
            fontFamily: 'monospace',
          }}
        >
          {isConnected
            ? `✅ Stagewise Connected (${extensionPort})`
            : '❌ Stagewise Connection Failed'
          }
        </div>
      )}

      {/* Render the Stagewise toolbar if connected */}
      {shouldRenderToolbar && <StagewiseToolbar />}
    </>
  )
}