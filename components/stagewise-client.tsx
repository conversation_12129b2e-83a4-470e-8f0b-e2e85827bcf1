'use client'

import { useEffect } from 'react'

export function StagewiseClient() {
  useEffect(() => {
    // Initialize Stagewise toolbar for Next.js
    const initStagewise = async () => {
      try {
        console.log('🔄 Initializing Stagewise toolbar...')

        // Test connection to VS Code extension first
        const testConnection = async () => {
          for (let port = 5746; port <= 5756; port++) {
            try {
              const response = await fetch(`http://localhost:${port}/ping/stagewise`, {
                method: 'GET',
                headers: { 'Accept': 'text/plain' }
              })
              if (response.ok) {
                const result = await response.text()
                if (result === 'stagewise') {
                  console.log(`✅ VS Code extension found on port ${port}`)
                  return port
                }
              }
            } catch (error) {
              // Continue to next port
            }
          }
          throw new Error('VS Code extension not found on ports 5746-5756')
        }

        // Test connection first
        const extensionPort = await testConnection()

        // Import the Next.js toolbar package
        const { initToolbar } = await import('@stagewise/toolbar-next')

        // Initialize with default configuration
        initToolbar({
          // Add any custom configuration here
          plugins: [],
          // Explicitly set the extension port if needed
          extensionPort: extensionPort,
        })

        console.log(`✅ Stagewise toolbar initialized and connected to port ${extensionPort}`)

        // Add a visual indicator that it's working
        setTimeout(() => {
          const indicator = document.createElement('div')
          indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #10b981;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 10000;
            font-family: monospace;
          `
          indicator.textContent = `✅ Stagewise Connected (${extensionPort})`
          document.body.appendChild(indicator)

          // Remove after 5 seconds
          setTimeout(() => {
            document.body.removeChild(indicator)
          }, 5000)
        }, 1000)

      } catch (error) {
        console.error('❌ Failed to initialize Stagewise toolbar:', error)

        // Add a visual error indicator
        setTimeout(() => {
          const indicator = document.createElement('div')
          indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #ef4444;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 10000;
            font-family: monospace;
          `
          indicator.textContent = `❌ Stagewise Connection Failed`
          document.body.appendChild(indicator)

          // Remove after 8 seconds
          setTimeout(() => {
            if (document.body.contains(indicator)) {
              document.body.removeChild(indicator)
            }
          }, 8000)
        }, 1000)
      }
    }

    // Only initialize in development and on localhost
    if (process.env.NODE_ENV === 'development' &&
        typeof window !== 'undefined' &&
        window.location.hostname === 'localhost') {
      console.log('🚀 Development mode detected, initializing Stagewise...')
      initStagewise()
    } else {
      console.log('⚠️ Stagewise only available in development mode on localhost')
    }
  }, [])

  // Return null since the toolbar is injected globally
  return null
}