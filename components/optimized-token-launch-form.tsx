"use client"

import type React from "react"

import { useState, use<PERSON><PERSON><PERSON>, lazy, Suspense } from "react"
import { Rocket, AlertCircle, Info, HelpCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useWallet } from "@/components/wallet-provider"
import { cn } from "@/lib/utils"
import { useNotification } from "@/hooks/use-notification"
import { ShimmerText } from "@/components/shimmer-text"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>rigger } from "@/components/ui/tooltip"
import { useResponsive } from "@/hooks/use-responsive"
import { useDebounce } from "@/hooks/use-debounce"
import { validateForm, required, minLength, maxLength, isTokenSymbol, isPositiveNumber } from "@/utils/form-validation"

// Lazy load the BondingCurveVisualization component for performance
const BondingCurveVisualization = lazy(() =>
  import("@/components/bonding-curve-visualization").then((module) => ({ default: module.BondingCurveVisualization })),
)

type BondingCurve = "linear" | "exponential" | "logarithmic"

export function OptimizedTokenLaunchForm() {
  const { isConnected } = useWallet()
  const { toast } = useToast()
  const { showNotification } = useNotification()
  const { isMobile, isTablet } = useResponsive()
  const [chartSize, setChartSize] = useState({ width: 0, height: 250 })
  const [chartContainerRef, setChartContainerRef] = useState<HTMLDivElement | null>(null)

  const [formState, setFormState] = useState({
    name: "",
    symbol: "",
    supply: 1000000000, // 1 billion default
    description: "",
    bondingCurve: "linear" as BondingCurve,
  })

  // Debounce form state to prevent excessive re-renders
  const debouncedFormState = useDebounce(formState, 300)

  const [errors, setErrors] = useState<Record<string, string[]>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Form validation rules
  const validations = {
    name: [
      required("Token name is required"),
      minLength(3, "Token name must be at least 3 characters"),
      maxLength(32, "Token name must be no more than 32 characters"),
    ],
    symbol: [required("Token symbol is required"), isTokenSymbol()],
    supply: [required("Total supply is required"), isPositiveNumber("Supply must be a positive number")],
  }

  // Update chart size when window resizes
  const updateChartSize = useCallback(() => {
    if (chartContainerRef) {
      setChartSize({
        width: chartContainerRef.clientWidth,
        height: 250,
      })
    }
  }, [chartContainerRef])

  // Handle input change with validation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target

    setFormState((prev) => ({ ...prev, [name]: value }))

    // Mark field as touched
    setTouched((prev) => ({ ...prev, [name]: true }))

    // Validate the field
    if (validations[name as keyof typeof validations]) {
      const fieldErrors = validations[name as keyof typeof validations]
        .filter((rule) => !rule.test(value))
        .map((rule) => rule.message)

      setErrors((prev) => ({ ...prev, [name]: fieldErrors }))
    }
  }

  const handleSupplyChange = (value: number[]) => {
    setFormState((prev) => ({ ...prev, supply: value[0] }))
    setTouched((prev) => ({ ...prev, supply: true }))

    // Validate supply
    const supplyErrors = validations.supply.filter((rule) => !rule.test(value[0])).map((rule) => rule.message)

    setErrors((prev) => ({ ...prev, supply: supplyErrors }))
  }

  const handleBondingCurveChange = (value: BondingCurve) => {
    setFormState((prev) => ({ ...prev, bondingCurve: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!isConnected) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to launch a token",
        variant: "destructive",
      })
      return
    }

    // Mark all fields as touched
    const allTouched = {
      name: true,
      symbol: true,
      supply: true,
      description: true,
    }
    setTouched(allTouched)

    // Validate all fields
    const formErrors = validateForm(formState, validations)
    setErrors(formErrors)

    // If there are errors, don't submit
    if (Object.keys(formErrors).length > 0) {
      toast({
        title: "Invalid form data",
        description: "Please fix the errors in the form",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Simulate token deployment
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Show toast notification
      showNotification({
        title: "Token Launched!",
        message: `Your token ${formState.name} (${formState.symbol}) has been deployed to the Dogechain Network.`,
        type: "success",
        addToCenter: true,
      })

      // Reset form
      setFormState({
        name: "",
        symbol: "",
        supply: 1000000000,
        description: "",
        bondingCurve: "linear",
      })

      // Reset touched and errors
      setTouched({})
      setErrors({})
    } catch (error) {
      showNotification({
        title: "Error Launching Token",
        message: "There was an error deploying your token. Please try again.",
        type: "error",
        addToCenter: true,
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatSupply = (value: number) => {
    if (value >= 1000000000) {
      return `${(value / 1000000000).toFixed(1)}B`
    } else if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    }
    return value.toString()
  }

  const getBondingCurveInfo = (type: BondingCurve) => {
    switch (type) {
      case "linear":
        return {
          title: "Linear Curve",
          description: "Price increases at a constant rate as tokens are sold.",
          initialPrice: "Low",
          priceGrowth: "Steady",
          bestFor: "Predictability",
          benefits: [
            "Predictable price growth",
            "Fair distribution across all buyers",
            "Balanced incentives for early and late adopters",
          ],
        }
      case "exponential":
        return {
          title: "Exponential Curve",
          description: "Price starts low and increases rapidly as more tokens are sold.",
          initialPrice: "Very Low",
          priceGrowth: "Accelerating",
          bestFor: "Long Term",
          benefits: [
            "Strong incentives for early adoption",
            "Potential for significant price appreciation",
            "Creates FOMO (Fear Of Missing Out) effect",
          ],
        }
      case "logarithmic":
        return {
          title: "Logarithmic Curve",
          description: "Price rises quickly at first, then stabilizes as more tokens are sold.",
          initialPrice: "High",
          priceGrowth: "Diminishing",
          bestFor: "Quick Start",
          benefits: [
            "Quick initial price discovery",
            "Stabilizes as market matures",
            "Balances short-term excitement with long-term stability",
          ],
        }
    }
  }

  const curveInfo = getBondingCurveInfo(debouncedFormState.bondingCurve)

  return (
    <Card className="glass-card border-white/5 liquid-glow">
      <CardHeader>
        <CardTitle className="text-white">
          <ShimmerText>Create Your Memecoin</ShimmerText>
        </CardTitle>
        <CardDescription className="text-white/70">
          Fill in the details below to launch your own memecoin on the Dogechain Network
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6" aria-label="Token launch form">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label
                htmlFor="name"
                className={`text-white/80 ${touched.name && errors.name?.length ? "text-red-500" : ""}`}
              >
                Token Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                name="name"
                placeholder="e.g. DogeKing"
                value={formState.name}
                onChange={handleInputChange}
                className={`glass-input border-white/10 focus:border-white/20 ${
                  touched.name && errors.name?.length ? "border-red-500 focus:border-red-500" : ""
                }`}
                required
                maxLength={32}
                aria-invalid={touched.name && errors.name?.length ? "true" : "false"}
                aria-describedby={touched.name && errors.name?.length ? "name-error" : undefined}
              />
              {touched.name && errors.name?.length ? (
                <p id="name-error" className="text-xs text-red-500" aria-live="polite">
                  {errors.name[0]}
                </p>
              ) : (
                <p className="text-xs text-white/60">3-32 characters, alphanumeric</p>
              )}
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="symbol"
                className={`text-white/80 ${touched.symbol && errors.symbol?.length ? "text-red-500" : ""}`}
              >
                Token Symbol <span className="text-red-500">*</span>
              </Label>
              <Input
                id="symbol"
                name="symbol"
                placeholder="e.g. DGK"
                value={formState.symbol}
                onChange={handleInputChange}
                className={`glass-input border-white/10 focus:border-white/20 uppercase ${
                  touched.symbol && errors.symbol?.length ? "border-red-500 focus:border-red-500" : ""
                }`}
                required
                maxLength={6}
                aria-invalid={touched.symbol && errors.symbol?.length ? "true" : "false"}
                aria-describedby={touched.symbol && errors.symbol?.length ? "symbol-error" : undefined}
              />
              {touched.symbol && errors.symbol?.length ? (
                <p id="symbol-error" className="text-xs text-red-500" aria-live="polite">
                  {errors.symbol[0]}
                </p>
              ) : (
                <p className="text-xs text-white/60">2-6 characters, uppercase</p>
              )}
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="supply"
                className={`text-white/80 ${touched.supply && errors.supply?.length ? "text-red-500" : ""}`}
              >
                Total Supply <span className="text-red-500">*</span>
              </Label>
              <div className="pt-6">
                <Slider
                  id="supply"
                  min={1000000}
                  max={100000000000}
                  step={1000000}
                  value={[formState.supply]}
                  onValueChange={handleSupplyChange}
                  className="py-4"
                  aria-invalid={touched.supply && errors.supply?.length ? "true" : "false"}
                  aria-describedby={touched.supply && errors.supply?.length ? "supply-error" : undefined}
                />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-white/60">1M</span>
                <span className="text-sm font-medium text-white/80">{formatSupply(formState.supply)}</span>
                <span className="text-xs text-white/60">100B</span>
              </div>
              {touched.supply && errors.supply?.length && (
                <p id="supply-error" className="text-xs text-red-500" aria-live="polite">
                  {errors.supply[0]}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-white/80">
                Description (Optional)
              </Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Describe your token's purpose or mission"
                value={formState.description}
                onChange={handleInputChange}
                className="glass-input border-white/10 focus:border-white/20"
                maxLength={280}
              />
              <p className="text-xs text-white/60">{formState.description.length}/280 characters</p>
            </div>

            <div className="space-y-2">
              <Label className="text-white/80">Bonding Curve Type</Label>
              <RadioGroup
                value={formState.bondingCurve}
                onValueChange={handleBondingCurveChange as (value: string) => void}
                className={`grid gap-4 pt-2 ${isMobile ? "grid-cols-1" : isTablet ? "grid-cols-2" : "grid-cols-3"}`}
              >
                <BondingCurveOption
                  id="linear"
                  value="linear"
                  title="Linear"
                  description="Price increases linearly with supply sold"
                  selected={formState.bondingCurve === "linear"}
                />
                <BondingCurveOption
                  id="exponential"
                  value="exponential"
                  title="Exponential"
                  description="Price rises faster as supply diminishes"
                  selected={formState.bondingCurve === "exponential"}
                />
                <BondingCurveOption
                  id="logarithmic"
                  value="logarithmic"
                  title="Logarithmic"
                  description="Price stabilizes as supply grows"
                  selected={formState.bondingCurve === "logarithmic"}
                />
              </RadioGroup>
            </div>

            <div className="mt-6 rounded-lg bg-black border border-white/10 overflow-hidden">
              <div className="p-4 border-b border-white/10 bg-black">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <h3 className="text-sm font-medium text-white/80">Bonding Curve Preview</h3>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full">
                            <HelpCircle className="h-3 w-3 text-white/60" />
                            <span className="sr-only">Bonding Curve Info</span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-xs">
                          <p>
                            This curve determines how your token price will change as more tokens are bought and sold.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <span className="px-2 py-1 text-xs rounded-full bg-white/10 text-white/80">
                    {formState.bondingCurve.charAt(0).toUpperCase() + formState.bondingCurve.slice(1)}
                  </span>
                </div>
                <p className="text-xs text-white/60 mt-1">{curveInfo.description}</p>
              </div>

              <div className={`flex ${isMobile ? "flex-col" : "flex-row"}`}>
                <div
                  className={`h-[250px] ${isMobile ? "w-full" : "w-2/3"} ${
                    isMobile ? "border-b" : "border-r"
                  } border-white/10`}
                  ref={setChartContainerRef}
                >
                  <Suspense
                    fallback={
                      <div className="h-full w-full flex items-center justify-center bg-black/20">
                        <p className="text-white/60">Loading chart...</p>
                      </div>
                    }
                  >
                    <BondingCurveVisualization
                      type={formState.bondingCurve}
                      width={chartSize.width}
                      height={chartSize.height}
                    />
                  </Suspense>
                </div>

                <div className="p-4 w-full md:w-1/3 bg-black">
                  <h4 className="text-sm font-medium text-white/80 mb-3">{curveInfo.title}</h4>

                  <div className="space-y-3">
                    <div>
                      <span className="text-xs text-white/60 block">Initial Price</span>
                      <span className="text-sm text-white/90 font-medium">{curveInfo.initialPrice}</span>
                    </div>

                    <div>
                      <span className="text-xs text-white/60 block">Price Growth</span>
                      <span className="text-sm text-white/90 font-medium">{curveInfo.priceGrowth}</span>
                    </div>

                    <div>
                      <span className="text-xs text-white/60 block">Best For</span>
                      <span className="text-sm text-white/90 font-medium">{curveInfo.bestFor}</span>
                    </div>

                    <div>
                      <span className="text-xs text-white/60 block">Benefits</span>
                      <ul className="list-disc list-inside text-xs text-white/80 mt-1 space-y-1">
                        {curveInfo.benefits.map((benefit, index) => (
                          <li key={index}>{benefit}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-black border-t border-white/10">
                <p className="text-xs text-white/70 italic">
                  <Info className="h-3 w-3 inline mr-1" />
                  Hover over the chart to see exact price points at different supply levels
                </p>
              </div>
            </div>
          </div>

          <Alert className="glass border-white/10 bg-white/5">
            <AlertCircle className="h-4 w-4 text-white" />
            <AlertTitle className="text-white">Gas Fee Required</AlertTitle>
            <AlertDescription className="text-white/70">
              Launching a token requires approximately 0.1 wDOGE (~$0.02) in gas fees
            </AlertDescription>
          </Alert>
        </form>
      </CardContent>
      <CardFooter>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={isSubmitting || !isConnected}
          className="w-full glass-button liquid-shine"
          aria-busy={isSubmitting ? "true" : "false"}
        >
          {isSubmitting ? (
            <>Deploying Token...</>
          ) : (
            <>
              <Rocket className="mr-2 h-4 w-4" />
              Launch Token
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

function BondingCurveOption({
  id,
  value,
  title,
  description,
  selected,
}: {
  id: string
  value: string
  title: string
  description: string
  selected: boolean
}) {
  return (
    <div className="relative">
      <RadioGroupItem value={value} id={id} className="peer sr-only" />
      <Label
        htmlFor={id}
        className={cn(
          "flex h-full cursor-pointer flex-col rounded-md glass-card border border-white/5 p-4 hover:border-white/10",
          selected && "border-white/20 border-glow",
        )}
      >
        <span className="mb-1 font-medium text-white">{title}</span>
        <span className="text-xs text-white/60">{description}</span>
      </Label>
    </div>
  )
}
