"use client"

import type React from "react"

import { useState } from "react"
import { RefreshCw, TrendingUp, TrendingDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast"
import { useWallet } from "@/components/wallet-provider"
import { TradingChart } from "@/components/trading-chart"
import { useNotification } from "@/hooks/use-notification"

export function PerpetualTrading() {
  const { isConnected } = useWallet()
  const { toast } = useToast()
  const { showNotification } = useNotification()

  const [tradeState, setTradeState] = useState({
    market: "PAW-USD",
    position: "long",
    amount: "",
    leverage: 5,
    collateral: "",
    takeProfit: "",
    stopLoss: "",
    reduceOnly: false,
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setTradeState((prev) => ({ ...prev, [name]: value }))

    // Calculate collateral based on amount and leverage
    if (name === "amount" && value) {
      const numValue = Number.parseFloat(value)
      if (!isNaN(numValue)) {
        const calculatedCollateral = (numValue / tradeState.leverage).toFixed(2)
        setTradeState((prev) => ({ ...prev, collateral: calculatedCollateral }))
      }
    }
  }

  const handleLeverageChange = (value: number[]) => {
    const leverage = value[0]
    setTradeState((prev) => ({ ...prev, leverage }))

    // Recalculate collateral if amount exists
    if (tradeState.amount) {
      const numValue = Number.parseFloat(tradeState.amount)
      if (!isNaN(numValue)) {
        const calculatedCollateral = (numValue / leverage).toFixed(2)
        setTradeState((prev) => ({ ...prev, collateral: calculatedCollateral }))
      }
    }
  }

  const handlePositionChange = (value: string) => {
    setTradeState((prev) => ({ ...prev, position: value }))
  }

  const handleMarketChange = (value: string) => {
    setTradeState((prev) => ({ ...prev, market: value }))
  }

  const handleSubmitTrade = async () => {
    if (!isConnected) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to open a position",
        variant: "destructive",
      })
      return
    }

    if (!tradeState.amount || !tradeState.collateral) {
      toast({
        title: "Invalid input",
        description: "Please enter a valid amount and collateral",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Simulate opening a position
      await new Promise((resolve) => setTimeout(resolve, 2000))

      showNotification({
        title: "Position Opened!",
        message: `Successfully opened a ${tradeState.position} position on ${tradeState.market} with ${tradeState.leverage}x leverage.`,
        type: "success",
        addToCenter: true,
      })

      // Reset form
      setTradeState((prev) => ({
        ...prev,
        amount: "",
        collateral: "",
        takeProfit: "",
        stopLoss: "",
      }))
    } catch (error) {
      showNotification({
        title: "Failed to Open Position",
        message: "There was an error opening your position. Please try again.",
        type: "error",
        addToCenter: true,
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="grid gap-8 lg:grid-cols-3">
      <div className="lg:col-span-2">
        <Card className="glass-card border-white/5 liquid-glow">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-white">{tradeState.market} Perpetual</CardTitle>
              <Select defaultValue="1d">
                <SelectTrigger className="w-[100px] glass-input border-white/10">
                  <SelectValue placeholder="Timeframe" />
                </SelectTrigger>
                <SelectContent className="glass">
                  <SelectItem value="1h">1H</SelectItem>
                  <SelectItem value="4h">4H</SelectItem>
                  <SelectItem value="1d">1D</SelectItem>
                  <SelectItem value="1w">1W</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <TradingChart />
          </CardContent>
        </Card>

        <div className="mt-8">
          <Card className="glass-card border-white/5 liquid-glow">
            <CardHeader>
              <CardTitle className="text-white">Open Positions</CardTitle>
              <CardDescription className="text-white/70">Your active perpetual positions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-lg border border-white/10 overflow-hidden">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-white/10 bg-white/5">
                      <th className="px-4 py-3 text-left text-sm font-medium text-white/70">Market</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-white/70">Side</th>
                      <th className="px-4 py-3 text-right text-sm font-medium text-white/70">Size</th>
                      <th className="px-4 py-3 text-right text-sm font-medium text-white/70">Leverage</th>
                      <th className="px-4 py-3 text-right text-sm font-medium text-white/70">Entry Price</th>
                      <th className="px-4 py-3 text-right text-sm font-medium text-white/70">Mark Price</th>
                      <th className="px-4 py-3 text-right text-sm font-medium text-white/70">PnL</th>
                      <th className="px-4 py-3 text-right text-sm font-medium text-white/70">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-white/5">
                      <td className="px-4 py-3 text-white">PAW-USD</td>
                      <td className="px-4 py-3">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500/10 text-green-500">
                          <TrendingUp className="mr-1 h-3 w-3" />
                          Long
                        </span>
                      </td>
                      <td className="px-4 py-3 text-right text-white">$100.00</td>
                      <td className="px-4 py-3 text-right text-white">5x</td>
                      <td className="px-4 py-3 text-right text-white">$0.0042</td>
                      <td className="px-4 py-3 text-right text-white">$0.0045</td>
                      <td className="px-4 py-3 text-right text-green-500">+$7.14 (7.14%)</td>
                      <td className="px-4 py-3 text-right">
                        <Button variant="outline" size="sm" className="glass-button text-xs h-7">
                          Close
                        </Button>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-4 py-3 text-white">DC-USD</td>
                      <td className="px-4 py-3">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-500/10 text-red-500">
                          <TrendingDown className="mr-1 h-3 w-3" />
                          Short
                        </span>
                      </td>
                      <td className="px-4 py-3 text-right text-white">$50.00</td>
                      <td className="px-4 py-3 text-right text-white">10x</td>
                      <td className="px-4 py-3 text-right text-white">$0.0105</td>
                      <td className="px-4 py-3 text-right text-white">$0.0102</td>
                      <td className="px-4 py-3 text-right text-green-500">+$1.43 (2.86%)</td>
                      <td className="px-4 py-3 text-right">
                        <Button variant="outline" size="sm" className="glass-button text-xs h-7">
                          Close
                        </Button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <div>
        <Card className="glass-card border-white/5 liquid-glow sticky top-20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white">Trade {tradeState.market}</CardTitle>
              <Select value={tradeState.market} onValueChange={handleMarketChange}>
                <SelectTrigger className="w-[120px] glass-input border-white/10">
                  <SelectValue placeholder="Market" />
                </SelectTrigger>
                <SelectContent className="glass">
                  <SelectItem value="PAW-USD">PAW-USD</SelectItem>
                  <SelectItem value="DC-USD">DC-USD</SelectItem>
                  <SelectItem value="SHIB-USD">SHIB-USD</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <CardDescription className="text-white/70">
              Trade perpetual contracts with up to 100x leverage
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-2">
              <Button
                className={`${
                  tradeState.position === "long" ? "bg-green-500 hover:bg-green-600" : "glass-button"
                } h-12`}
                onClick={() => handlePositionChange("long")}
              >
                <TrendingUp className="mr-2 h-4 w-4" />
                Long
              </Button>
              <Button
                className={`${tradeState.position === "short" ? "bg-red-500 hover:bg-red-600" : "glass-button"} h-12`}
                onClick={() => handlePositionChange("short")}
              >
                <TrendingDown className="mr-2 h-4 w-4" />
                Short
              </Button>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="amount" className="text-white/80">
                  Position Size (USD)
                </Label>
                <span className="text-xs text-white/60">Balance: $1000</span>
              </div>
              <div className="relative">
                <Input
                  id="amount"
                  name="amount"
                  type="number"
                  placeholder="0.00"
                  value={tradeState.amount}
                  onChange={handleInputChange}
                  className="glass-input border-white/10 pr-16"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span className="text-sm text-white/60">USD</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="leverage" className="text-white/80">
                  Leverage: {tradeState.leverage}x
                </Label>
                <span className="text-xs text-white/60">Max: 100x</span>
              </div>
              <Slider
                id="leverage"
                min={1}
                max={100}
                step={1}
                value={[tradeState.leverage]}
                onValueChange={handleLeverageChange}
                className={`${
                  tradeState.leverage > 20
                    ? "accent-red-500"
                    : tradeState.leverage > 10
                      ? "accent-yellow-500"
                      : "accent-green-500"
                }`}
              />
              <div className="flex items-center justify-between text-xs text-white/60">
                <span>1x</span>
                <span>25x</span>
                <span>50x</span>
                <span>100x</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="collateral" className="text-white/80">
                Required Collateral (USD)
              </Label>
              <div className="relative">
                <Input
                  id="collateral"
                  name="collateral"
                  type="number"
                  placeholder="0.00"
                  value={tradeState.collateral}
                  readOnly
                  className="glass-input border-white/10 pr-16 bg-white/5"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span className="text-sm text-white/60">USD</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="takeProfit" className="text-white/80">
                  Take Profit
                </Label>
                <Input
                  id="takeProfit"
                  name="takeProfit"
                  type="number"
                  placeholder="Optional"
                  value={tradeState.takeProfit}
                  onChange={handleInputChange}
                  className="glass-input border-white/10"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="stopLoss" className="text-white/80">
                  Stop Loss
                </Label>
                <Input
                  id="stopLoss"
                  name="stopLoss"
                  type="number"
                  placeholder="Optional"
                  value={tradeState.stopLoss}
                  onChange={handleInputChange}
                  className="glass-input border-white/10"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="reduceOnly" className="text-white/80 cursor-pointer">
                Reduce Only
              </Label>
              <Switch
                id="reduceOnly"
                checked={tradeState.reduceOnly}
                onCheckedChange={(checked) => setTradeState((prev) => ({ ...prev, reduceOnly: checked }))}
              />
            </div>

            <div className="rounded-md bg-white/5 p-3 text-sm">
              <div className="flex items-center justify-between mb-1">
                <span className="text-white/60">Entry Price</span>
                <span className="text-white/90">$0.0042</span>
              </div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-white/60">Fees</span>
                <span className="text-white/90">$0.10 (0.1%)</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/60">Liquidation Price</span>
                <span className={`${tradeState.position === "long" ? "text-red-500" : "text-green-500"}`}>
                  {tradeState.position === "long" ? "$0.0034" : "$0.0050"}
                </span>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleSubmitTrade}
              disabled={isSubmitting || !tradeState.amount || !tradeState.collateral || !isConnected}
              className={`w-full ${
                tradeState.position === "long" ? "bg-green-500 hover:bg-green-600" : "bg-red-500 hover:bg-red-600"
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </div>
              ) : !isConnected ? (
                "Connect Wallet"
              ) : !tradeState.amount || !tradeState.collateral ? (
                "Enter Amount"
              ) : (
                `${tradeState.position === "long" ? "Long" : "Short"} ${tradeState.market}`
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
