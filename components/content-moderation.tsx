"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { 
  AlertTriangle, 
  Shield, 
  Eye, 
  Ban, 
  CheckCircle, 
  XCircle,
  Flag,
  MessageSquare,
  User,
  Clock
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"

interface Report {
  id: string
  type: 'spam' | 'harassment' | 'scam' | 'inappropriate' | 'fake'
  status: 'pending' | 'approved' | 'rejected' | 'resolved'
  priority: 'low' | 'medium' | 'high' | 'critical'
  reportedBy: {
    name: string
    username: string
    avatar?: string
  }
  reportedUser: {
    name: string
    username: string
    avatar?: string
  }
  content: string
  reason: string
  timestamp: Date
  moderatorNotes?: string
}

const mockReports: Report[] = [
  {
    id: "1",
    type: "scam",
    status: "pending",
    priority: "critical",
    reportedBy: {
      name: "John Doe",
      username: "johndoe",
    },
    reportedUser: {
      name: "Fake Elon",
      username: "elonmusk_fake",
    },
    content: "🚀 EXCLUSIVE GIVEAWAY! Send 1 ETH to get 10 ETH back! Limited time offer! DM me now! 💰",
    reason: "This user is impersonating Elon Musk and running a cryptocurrency scam",
    timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
  },
  {
    id: "2",
    type: "spam",
    status: "pending",
    priority: "medium",
    reportedBy: {
      name: "Alice Smith",
      username: "alicesmith",
    },
    reportedUser: {
      name: "Spam Bot",
      username: "cryptobot123",
    },
    content: "🔥🔥🔥 PUMP ALERT! $SCAMCOIN TO THE MOON! 1000X GUARANTEED! BUY NOW! 🔥🔥🔥",
    reason: "Repetitive spam messages promoting questionable tokens",
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
  },
  {
    id: "3",
    type: "harassment",
    status: "pending",
    priority: "high",
    reportedBy: {
      name: "Bob Wilson",
      username: "bobwilson",
    },
    reportedUser: {
      name: "Toxic User",
      username: "toxictrader",
    },
    content: "You're an idiot for buying that coin. Hope you lose everything, loser!",
    reason: "Harassment and toxic behavior towards other community members",
    timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
  }
]

export function ContentModeration() {
  const [reports, setReports] = useState(mockReports)
  const [selectedTab, setSelectedTab] = useState("pending")
  const [moderatorNotes, setModeratorNotes] = useState<{[key: string]: string}>({})

  const handleReportAction = (reportId: string, action: 'approve' | 'reject' | 'resolve') => {
    setReports(reports.map(report => 
      report.id === reportId 
        ? { 
            ...report, 
            status: action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'resolved',
            moderatorNotes: moderatorNotes[reportId] || ''
          }
        : report
    ))
    
    // Clear notes for this report
    setModeratorNotes(prev => {
      const newNotes = { ...prev }
      delete newNotes[reportId]
      return newNotes
    })
  }

  const updateModeratorNotes = (reportId: string, notes: string) => {
    setModeratorNotes(prev => ({
      ...prev,
      [reportId]: notes
    }))
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-500'
      case 'high': return 'bg-orange-500'
      case 'medium': return 'bg-yellow-500'
      case 'low': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'scam': return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'spam': return <MessageSquare className="h-4 w-4 text-yellow-500" />
      case 'harassment': return <Ban className="h-4 w-4 text-orange-500" />
      case 'inappropriate': return <Eye className="h-4 w-4 text-purple-500" />
      case 'fake': return <User className="h-4 w-4 text-blue-500" />
      default: return <Flag className="h-4 w-4 text-gray-500" />
    }
  }

  const filteredReports = reports.filter(report => {
    if (selectedTab === 'pending') return report.status === 'pending'
    if (selectedTab === 'resolved') return ['approved', 'rejected', 'resolved'].includes(report.status)
    return true
  })

  const pendingCount = reports.filter(r => r.status === 'pending').length
  const resolvedCount = reports.filter(r => ['approved', 'rejected', 'resolved'].includes(r.status)).length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Content Moderation</h1>
          <p className="text-white/70">Review and moderate community reports</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Shield className="h-4 w-4 mr-2" />
            Moderation Rules
          </Button>
          <Button className="bg-doge hover:bg-doge/90" size="sm">
            <Flag className="h-4 w-4 mr-2" />
            Create Report
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-sm text-white/70">Pending Reports</p>
                <p className="text-2xl font-bold text-white">{pendingCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-white/70">Resolved Today</p>
                <p className="text-2xl font-bold text-white">24</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Ban className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm text-white/70">Users Banned</p>
                <p className="text-2xl font-bold text-white">3</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-white/70">Response Time</p>
                <p className="text-2xl font-bold text-white">12m</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reports */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="pending">
            Pending ({pendingCount})
          </TabsTrigger>
          <TabsTrigger value="resolved">
            Resolved ({resolvedCount})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={selectedTab} className="space-y-4">
          {filteredReports.length === 0 ? (
            <Card className="glass-card border-white/5">
              <CardContent className="p-8 text-center">
                <Shield className="h-12 w-12 text-white/50 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">No Reports</h3>
                <p className="text-white/70">
                  {selectedTab === 'pending' ? 'No pending reports to review' : 'No resolved reports'}
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredReports.map((report) => (
              <Card key={report.id} className="glass-card border-white/5">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      {getTypeIcon(report.type)}
                      <div>
                        <CardTitle className="text-white capitalize">
                          {report.type} Report
                        </CardTitle>
                        <CardDescription className="text-white/70">
                          Reported {formatDistanceToNow(report.timestamp, { addSuffix: true })}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getPriorityColor(report.priority)}>
                        {report.priority}
                      </Badge>
                      <Badge variant="outline" className="text-white/70">
                        {report.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Reporter and Reported User */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-white/70 mb-2">Reported by:</p>
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={report.reportedBy.avatar} />
                          <AvatarFallback className="bg-doge text-black text-xs">
                            {report.reportedBy.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-semibold text-white">{report.reportedBy.name}</p>
                          <p className="text-xs text-white/70">@{report.reportedBy.username}</p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-white/70 mb-2">Reported user:</p>
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={report.reportedUser.avatar} />
                          <AvatarFallback className="bg-red-500 text-white text-xs">
                            {report.reportedUser.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-semibold text-white">{report.reportedUser.name}</p>
                          <p className="text-xs text-white/70">@{report.reportedUser.username}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Reported Content */}
                  <div>
                    <p className="text-sm text-white/70 mb-2">Reported content:</p>
                    <Card className="bg-red-500/10 border-red-500/20">
                      <CardContent className="p-3">
                        <p className="text-white text-sm">{report.content}</p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Reason */}
                  <div>
                    <p className="text-sm text-white/70 mb-2">Reason for report:</p>
                    <p className="text-white text-sm">{report.reason}</p>
                  </div>

                  {/* Moderator Notes */}
                  {report.status === 'pending' && (
                    <div>
                      <p className="text-sm text-white/70 mb-2">Moderator notes:</p>
                      <Textarea
                        placeholder="Add notes about your decision..."
                        value={moderatorNotes[report.id] || ''}
                        onChange={(e) => updateModeratorNotes(report.id, e.target.value)}
                        className="bg-white/5 border-white/10 text-white"
                      />
                    </div>
                  )}

                  {/* Existing Moderator Notes */}
                  {report.moderatorNotes && (
                    <div>
                      <p className="text-sm text-white/70 mb-2">Moderator notes:</p>
                      <Card className="bg-white/5 border-white/10">
                        <CardContent className="p-3">
                          <p className="text-white text-sm">{report.moderatorNotes}</p>
                        </CardContent>
                      </Card>
                    </div>
                  )}

                  {/* Actions */}
                  {report.status === 'pending' && (
                    <div className="flex justify-end space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleReportAction(report.id, 'reject')}
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        Reject
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleReportAction(report.id, 'resolve')}
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Resolve
                      </Button>
                      <Button 
                        size="sm"
                        className="bg-red-500 hover:bg-red-600"
                        onClick={() => handleReportAction(report.id, 'approve')}
                      >
                        <Ban className="h-4 w-4 mr-2" />
                        Take Action
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
