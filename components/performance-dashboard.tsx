"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { useResponsive } from "@/hooks/use-responsive"
import { initPerformanceMonitoring } from "@/utils/performance-monitoring"

type PerformanceMetric = {
  name: string
  value: number
  unit: string
  target: number
  status: "good" | "warning" | "critical"
}

export function PerformanceDashboard() {
  const { isMobile } = useResponsive()
  const [metrics, setMetrics] = useState<{
    vitals: PerformanceMetric[]
    resources: PerformanceMetric[]
    memory: PerformanceMetric[]
  }>({
    vitals: [],
    resources: [],
    memory: [],
  })

  // Generate metrics with status already calculated
  const generateMetrics = () => {
    const calculateStatus = (value: number, target: number): "good" | "warning" | "critical" => {
      return value > target ? "critical" : value > target * 0.8 ? "warning" : "good"
    }

    const vitals = [
      {
        name: "First Contentful Paint",
        value: Math.random() * 1000 + 500,
        unit: "ms",
        target: 1500,
        status: "good" as const,
      },
      {
        name: "Largest Contentful Paint",
        value: Math.random() * 1500 + 1000,
        unit: "ms",
        target: 2500,
        status: "good" as const,
      },
      {
        name: "Cumulative Layout Shift",
        value: Math.random() * 0.15,
        unit: "",
        target: 0.1,
        status: "good" as const,
      },
      {
        name: "First Input Delay",
        value: Math.random() * 80 + 20,
        unit: "ms",
        target: 100,
        status: "good" as const,
      },
      {
        name: "Time to Interactive",
        value: Math.random() * 2000 + 1500,
        unit: "ms",
        target: 3000,
        status: "good" as const,
      },
    ].map((metric) => ({
      ...metric,
      status: calculateStatus(metric.value, metric.target),
    }))

    const resources = [
      {
        name: "JavaScript Size",
        value: Math.random() * 200 + 100,
        unit: "KB",
        target: 300,
        status: "good" as const,
      },
      {
        name: "CSS Size",
        value: Math.random() * 60 + 40,
        unit: "KB",
        target: 100,
        status: "good" as const,
      },
      {
        name: "Image Size",
        value: Math.random() * 200 + 50,
        unit: "KB",
        target: 250,
        status: "good" as const,
      },
      {
        name: "Total Resources",
        value: Math.random() * 30 + 20,
        unit: "",
        target: 50,
        status: "good" as const,
      },
    ].map((metric) => ({
      ...metric,
      status: calculateStatus(metric.value, metric.target),
    }))

    const memory = [
      {
        name: "JS Heap Size",
        value: Math.random() * 40 + 20,
        unit: "MB",
        target: 60,
        status: "good" as const,
      },
      {
        name: "DOM Nodes",
        value: Math.random() * 1000 + 500,
        unit: "",
        target: 1500,
        status: "good" as const,
      },
    ].map((metric) => ({
      ...metric,
      status: calculateStatus(metric.value, metric.target),
    }))

    return { vitals, resources, memory }
  }

  useEffect(() => {
    // Initialize performance monitoring
    initPerformanceMonitoring()

    // Initial simulation
    setMetrics(generateMetrics())

    // Update metrics every 5 seconds
    const interval = setInterval(() => {
      setMetrics(generateMetrics())
    }, 5000)

    return () => clearInterval(interval)
  }, []) // Empty dependency array ensures this only runs once on mount

  return (
    <Card className="glass-card border-white/5">
      <CardHeader>
        <CardTitle>Performance Dashboard</CardTitle>
        <CardDescription>Monitor application performance metrics and budgets</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="vitals" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="vitals">Core Web Vitals</TabsTrigger>
            <TabsTrigger value="resources">Resources</TabsTrigger>
            <TabsTrigger value="memory">Memory</TabsTrigger>
          </TabsList>

          <TabsContent value="vitals" className="space-y-4">
            {metrics.vitals.map((metric) => (
              <MetricItem key={metric.name} metric={metric} />
            ))}
          </TabsContent>

          <TabsContent value="resources" className="space-y-4">
            {metrics.resources.map((metric) => (
              <MetricItem key={metric.name} metric={metric} />
            ))}
          </TabsContent>

          <TabsContent value="memory" className="space-y-4">
            {metrics.memory.map((metric) => (
              <MetricItem key={metric.name} metric={metric} />
            ))}
          </TabsContent>
        </Tabs>

        <div className="mt-6 p-4 bg-black/20 rounded-lg border border-white/5">
          <h3 className="text-sm font-medium mb-2">Performance Budget Status</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-xs text-white/70">Core Web Vitals</p>
              <div className="flex items-center gap-2">
                <div
                  className={`h-3 w-3 rounded-full ${
                    metrics.vitals.some((m) => m.status === "critical")
                      ? "bg-red-500"
                      : metrics.vitals.some((m) => m.status === "warning")
                        ? "bg-yellow-500"
                        : "bg-green-500"
                  }`}
                ></div>
                <span className="text-sm">
                  {metrics.vitals.some((m) => m.status === "critical")
                    ? "Critical"
                    : metrics.vitals.some((m) => m.status === "warning")
                      ? "Warning"
                      : "Good"}
                </span>
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-white/70">Resources</p>
              <div className="flex items-center gap-2">
                <div
                  className={`h-3 w-3 rounded-full ${
                    metrics.resources.some((m) => m.status === "critical")
                      ? "bg-red-500"
                      : metrics.resources.some((m) => m.status === "warning")
                        ? "bg-yellow-500"
                        : "bg-green-500"
                  }`}
                ></div>
                <span className="text-sm">
                  {metrics.resources.some((m) => m.status === "critical")
                    ? "Critical"
                    : metrics.resources.some((m) => m.status === "warning")
                      ? "Warning"
                      : "Good"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function MetricItem({ metric }: { metric: PerformanceMetric }) {
  const percentage = Math.min(100, (metric.value / metric.target) * 100)

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">{metric.name}</span>
        <div className="flex items-center gap-2">
          <span
            className={`text-sm ${
              metric.status === "critical"
                ? "text-red-500"
                : metric.status === "warning"
                  ? "text-yellow-500"
                  : "text-green-500"
            }`}
          >
            {metric.value.toFixed(metric.unit === "" ? 0 : 2)}
            {metric.unit}
          </span>
          <span className="text-xs text-white/60">
            / {metric.target}
            {metric.unit}
          </span>
        </div>
      </div>
      <Progress
        value={percentage}
        className="h-2 bg-white/10"
        indicatorClassName={
          metric.status === "critical" ? "bg-red-500" : metric.status === "warning" ? "bg-yellow-500" : "bg-green-500"
        }
      />
    </div>
  )
}
