"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  Al<PERSON><PERSON><PERSON>gle,
  BarChart,
  BarChart2,
  Calendar,
  CheckCircle,
  Clock,
  Code,
  Download,
  Edit,
  Filter,
  Lock,
  MessageSquare,
  Plus,
  Save,
  Search,
  Settings,
  Shield,
  Sliders,
  Star,
  Trash2,
  <PERSON>r,
  <PERSON>r<PERSON><PERSON>,
  <PERSON>,
  Wallet,
  <PERSON>et<PERSON><PERSON>,
} from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>back, AvatarImage } from "@/components/ui/avatar"

// Mock data for development tasks
const mockTasks = [
  {
    id: "task-1",
    title: "Implement Bonding Curve Visualization",
    assignee: "Doge Master",
    status: "completed",
    priority: "high",
    dueDate: "2025-04-15",
    category: "frontend",
  },
  {
    id: "task-2",
    title: "Optimize Token Launch Contract",
    assignee: "Crypto Dev",
    status: "in-progress",
    priority: "critical",
    dueDate: "2025-04-20",
    category: "smart-contract",
  },
  {
    id: "task-3",
    title: "Add Multi-signature Support",
    assignee: "Unassigned",
    status: "pending",
    priority: "medium",
    dueDate: "2025-04-25",
    category: "security",
  },
  {
    id: "task-4",
    title: "Create Analytics Dashboard",
    assignee: "Trading Pro",
    status: "in-review",
    priority: "high",
    dueDate: "2025-04-18",
    category: "frontend",
  },
  {
    id: "task-5",
    title: "Implement Governance Voting",
    assignee: "Paw Wizard",
    status: "in-progress",
    priority: "high",
    dueDate: "2025-04-22",
    category: "governance",
  },
]

// Mock data for staking pools
const mockStakingPools = [
  {
    id: "pool-1",
    name: "Development DAO Staking",
    token: "PAWP",
    totalStaked: "1,250,000",
    apy: "12.5%",
    lockPeriod: "30 days",
    status: "active",
  },
  {
    id: "pool-2",
    name: "Governance Staking",
    token: "PAWP",
    totalStaked: "3,750,000",
    apy: "8.2%",
    lockPeriod: "None",
    status: "active",
  },
  {
    id: "pool-3",
    name: "Liquidity Provider Rewards",
    token: "LP-PAWP-DOGE",
    totalStaked: "850,000",
    apy: "18.7%",
    lockPeriod: "14 days",
    status: "active",
  },
  {
    id: "pool-4",
    name: "Early Adopter Bonus",
    token: "PAWP",
    totalStaked: "500,000",
    apy: "25.0%",
    lockPeriod: "90 days",
    status: "ended",
  },
]

// Mock data for stakers
const mockStakers = [
  {
    address: "0x1a2b...3c4d",
    pool: "Development DAO Staking",
    amountStaked: "125,000",
    rewards: "4,562",
    stakedSince: "2025-02-15",
    unlockDate: "2025-03-17",
  },
  {
    address: "0x5e6f...7g8h",
    pool: "Governance Staking",
    amountStaked: "350,000",
    rewards: "8,750",
    stakedSince: "2025-01-20",
    unlockDate: "N/A",
  },
  {
    address: "0x9i0j...1k2l",
    pool: "Liquidity Provider Rewards",
    amountStaked: "75,000",
    rewards: "3,281",
    stakedSince: "2025-03-01",
    unlockDate: "2025-03-15",
  },
  {
    address: "0x3m4n...5o6p",
    pool: "Early Adopter Bonus",
    amountStaked: "200,000",
    rewards: "12,500",
    stakedSince: "2024-12-10",
    unlockDate: "2025-03-10",
  },
]

// Mock data for reward programs
const mockRewardPrograms = [
  {
    id: "reward-1",
    name: "Trading Volume Rewards",
    description: "Rewards based on weekly trading volume",
    totalBudget: "500,000 PAWP",
    distributed: "125,000 PAWP",
    remaining: "375,000 PAWP",
    status: "active",
    frequency: "Weekly",
  },
  {
    id: "reward-2",
    name: "Liquidity Provider Incentives",
    description: "Rewards for providing liquidity to pools",
    totalBudget: "1,000,000 PAWP",
    distributed: "450,000 PAWP",
    remaining: "550,000 PAWP",
    status: "active",
    frequency: "Daily",
  },
  {
    id: "reward-3",
    name: "Governance Participation",
    description: "Rewards for voting on proposals",
    totalBudget: "250,000 PAWP",
    distributed: "75,000 PAWP",
    remaining: "175,000 PAWP",
    status: "active",
    frequency: "Per proposal",
  },
  {
    id: "reward-4",
    name: "Launch Participation",
    description: "Rewards for participating in token launches",
    totalBudget: "300,000 PAWP",
    distributed: "120,000 PAWP",
    remaining: "180,000 PAWP",
    status: "paused",
    frequency: "Per launch",
  },
]

// Mock data for recent reward distributions
const mockDistributions = [
  {
    id: "dist-1",
    program: "Trading Volume Rewards",
    date: "2025-04-07",
    amount: "25,000 PAWP",
    recipients: 128,
    status: "completed",
  },
  {
    id: "dist-2",
    program: "Liquidity Provider Incentives",
    date: "2025-04-10",
    amount: "15,000 PAWP",
    recipients: 75,
    status: "completed",
  },
  {
    id: "dist-3",
    program: "Governance Participation",
    date: "2025-04-05",
    amount: "10,000 PAWP",
    recipients: 42,
    status: "completed",
  },
  {
    id: "dist-4",
    program: "Trading Volume Rewards",
    date: "2025-04-14",
    amount: "25,000 PAWP",
    recipients: 0,
    status: "pending",
  },
]

// Mock data for users
const mockUsers = [
  {
    id: "USR-001",
    name: "Doge Master",
    address: "0x1234...5678",
    email: "<EMAIL>",
    role: "admin",
    status: "active",
    joined: "Apr 15, 2025",
    avatar: "/avatar-doge.png",
  },
  {
    id: "USR-002",
    name: "Paw Wizard",
    address: "0xabcd...efgh",
    email: "<EMAIL>",
    role: "moderator",
    status: "active",
    joined: "Apr 18, 2025",
    avatar: "/avatar-wizard.png",
  },
  {
    id: "USR-003",
    name: "Trading Pro",
    address: "0x9876...5432",
    email: "<EMAIL>",
    role: "user",
    status: "active",
    joined: "Apr 20, 2025",
    avatar: "/generic-character-exchange.png",
  },
  {
    id: "USR-004",
    name: "Crypto Dev",
    address: "0xijkl...mnop",
    email: "<EMAIL>",
    role: "developer",
    status: "active",
    joined: "Apr 22, 2025",
    avatar: "/avatar-developer.png",
  },
  {
    id: "USR-005",
    name: "Memecoin Enthusiast",
    address: "0xqrst...uvwx",
    email: "<EMAIL>",
    role: "user",
    status: "suspended",
    joined: "Apr 25, 2025",
    avatar: "/avatar-enthusiast.png",
  },
]

// Mock data for roles
const roles = [
  { id: "admin", name: "Admin", description: "Full access to all resources" },
  { id: "moderator", name: "Moderator", description: "Can moderate users and content" },
  { id: "developer", name: "Developer", description: "Access to development tools and resources" },
  { id: "user", name: "User", description: "Standard user access" },
]

// Mock data for feedback
const mockFeedback = [
  {
    id: "fb-1",
    type: "bug",
    message: "Swap function fails when slippage is set below 0.5%",
    email: "<EMAIL>",
    status: "new",
    createdAt: new Date("2025-04-05").toISOString(),
    updatedAt: new Date("2025-04-05").toISOString(),
  },
  {
    id: "fb-2",
    type: "feature",
    message: "Would love to see a dark mode option for the interface",
    email: "<EMAIL>",
    status: "reviewing",
    createdAt: new Date("2025-04-03").toISOString(),
    updatedAt: new Date("2025-04-04").toISOString(),
  },
  {
    id: "fb-3",
    type: "ux",
    message: "The staking interface is confusing for new users",
    email: "<EMAIL>",
    status: "resolved",
    createdAt: new Date("2025-04-01").toISOString(),
    updatedAt: new Date("2025-04-06").toISOString(),
  },
  {
    id: "fb-4",
    type: "other",
    message: "Documentation for bonding curves needs more examples",
    email: "<EMAIL>",
    status: "closed",
    createdAt: new Date("2025-03-28").toISOString(),
    updatedAt: new Date("2025-04-02").toISOString(),
  },
]

export function DevelopmentDAOAdmin() {
  const [activeStakingTab, setActiveStakingTab] = useState("pools")
  const [activeRewardsTab, setActiveRewardsTab] = useState("programs")
  const [activeUsersTab, setActiveUsersTab] = useState("users")
  const [activeFeedbackTab, setActiveFeedbackTab] = useState("all")
  const [activeSettingsTab, setActiveSettingsTab] = useState("general")
  const [activeAnalyticsTab, setActiveAnalyticsTab] = useState("overview")
  const [searchQuery, setSearchQuery] = useState("")

  // Filter users based on search query
  const filteredUsers = mockUsers.filter(
    (user) =>
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.address.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // Filter feedback by type
  const filteredFeedback =
    activeFeedbackTab === "all" ? mockFeedback : mockFeedback.filter((item) => item.type === activeFeedbackTab)

  // Get counts for each feedback type
  const bugCount = mockFeedback.filter((item) => item.type === "bug").length
  const featureCount = mockFeedback.filter((item) => item.type === "feature").length
  const uxCount = mockFeedback.filter((item) => item.type === "ux").length
  const otherCount = mockFeedback.filter((item) => item.type === "other").length

  // Get status icon for feedback
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "new":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case "reviewing":
        return <Clock className="h-4 w-4 text-blue-500" />
      case "resolved":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "closed":
        return <CheckCircle className="h-4 w-4 text-gray-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    }
  }

  return (
    <Tabs defaultValue="development" className="w-full">
      <TabsList className="grid grid-cols-7 w-full bg-black/20 border border-white/10">
        <TabsTrigger value="development" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
          <Code className="mr-2 h-4 w-4" />
          Development
        </TabsTrigger>
        <TabsTrigger value="staking" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
          <Shield className="mr-2 h-4 w-4" />
          Staking
        </TabsTrigger>
        <TabsTrigger value="rewards" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
          <Star className="mr-2 h-4 w-4" />
          Rewards
        </TabsTrigger>
        <TabsTrigger value="users" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
          <Users className="mr-2 h-4 w-4" />
          Users
        </TabsTrigger>
        <TabsTrigger value="feedback" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
          <MessageSquare className="mr-2 h-4 w-4" />
          Feedback
        </TabsTrigger>
        <TabsTrigger value="settings" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
          <Settings className="mr-2 h-4 w-4" />
          Settings
        </TabsTrigger>
        <TabsTrigger value="analytics" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
          <BarChart className="mr-2 h-4 w-4" />
          Analytics
        </TabsTrigger>
      </TabsList>

      {/* Development Tasks Tab */}
      <TabsContent value="development" className="mt-6">
        <Card className="glass-card border-white/5 bg-black/20">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-lg text-white">Development Tasks</CardTitle>
              <CardDescription className="text-white/70">
                Manage and track development tasks for the platform
              </CardDescription>
            </div>
            <Button className="bg-doge text-black hover:bg-doge/90">
              <Plus className="mr-2 h-4 w-4" />
              New Task
            </Button>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mb-6">
              <div className="relative w-full sm:w-96">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
                <Input placeholder="Search tasks..." className="pl-10 border-white/20 bg-black/20" />
              </div>
              <div className="flex items-center gap-2">
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px] border-white/20 bg-black/20">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent className="bg-black border-white/10">
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="in-review">In Review</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" className="border-white/20 bg-black/20 text-white">
                  <Filter className="mr-2 h-4 w-4" />
                  Filter
                </Button>
              </div>
            </div>

            <Table>
              <TableHeader>
                <TableRow className="border-white/10">
                  <TableHead className="text-white">Task</TableHead>
                  <TableHead className="text-white">Assignee</TableHead>
                  <TableHead className="text-white">Status</TableHead>
                  <TableHead className="text-white">Priority</TableHead>
                  <TableHead className="text-white">Due Date</TableHead>
                  <TableHead className="text-white">Category</TableHead>
                  <TableHead className="text-white">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockTasks.map((task) => (
                  <TableRow key={task.id} className="border-white/10">
                    <TableCell className="font-medium text-white">{task.title}</TableCell>
                    <TableCell className="text-white/80">{task.assignee}</TableCell>
                    <TableCell>
                      <Badge
                        className={
                          task.status === "completed"
                            ? "bg-green-500/20 text-green-500"
                            : task.status === "in-progress"
                              ? "bg-blue-500/20 text-blue-500"
                              : task.status === "in-review"
                                ? "bg-purple-500/20 text-purple-500"
                                : "bg-yellow-500/20 text-yellow-500"
                        }
                      >
                        {task.status
                          .split("-")
                          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                          .join(" ")}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge
                        className={
                          task.priority === "critical"
                            ? "bg-red-500/20 text-red-500"
                            : task.priority === "high"
                              ? "bg-orange-500/20 text-orange-500"
                              : "bg-blue-500/20 text-blue-500"
                        }
                      >
                        {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-white/80">{task.dueDate}</TableCell>
                    <TableCell>
                      <Badge className="bg-white/10 text-white/80">
                        {task.category.charAt(0).toUpperCase() + task.category.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-white/70 hover:text-white hover:bg-white/10"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-500 hover:text-red-400 hover:bg-red-500/10"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </TabsContent>

      {/* Staking Management Tab */}
      <TabsContent value="staking" className="mt-6">
        <Card className="glass-card border-white/5 bg-black/20">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-lg text-white">Staking Management</CardTitle>
              <CardDescription className="text-white/70">Manage staking pools and monitor stakers</CardDescription>
            </div>
            <Button className="bg-doge text-black hover:bg-doge/90">Create New Pool</Button>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="pools" value={activeStakingTab} onValueChange={setActiveStakingTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 bg-black/20 border border-white/10">
                <TabsTrigger value="pools" className="data-[state=active]:bg-white/10">
                  Staking Pools
                </TabsTrigger>
                <TabsTrigger value="stakers" className="data-[state=active]:bg-white/10">
                  Stakers
                </TabsTrigger>
              </TabsList>

              <TabsContent value="pools" className="mt-6">
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/10">
                      <TableHead className="text-white">Name</TableHead>
                      <TableHead className="text-white">Token</TableHead>
                      <TableHead className="text-white">Total Staked</TableHead>
                      <TableHead className="text-white">APY</TableHead>
                      <TableHead className="text-white">Lock Period</TableHead>
                      <TableHead className="text-white">Status</TableHead>
                      <TableHead className="text-white">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockStakingPools.map((pool) => (
                      <TableRow key={pool.id} className="border-white/10">
                        <TableCell className="text-white font-medium">{pool.name}</TableCell>
                        <TableCell className="text-white/80">{pool.token}</TableCell>
                        <TableCell className="text-white/80">{pool.totalStaked}</TableCell>
                        <TableCell className="text-white/80">{pool.apy}</TableCell>
                        <TableCell className="text-white/80">{pool.lockPeriod}</TableCell>
                        <TableCell>
                          <Badge
                            className={
                              pool.status === "active"
                                ? "bg-green-500/20 text-green-500 border-green-500/20"
                                : "bg-gray-500/20 text-gray-500 border-gray-500/20"
                            }
                          >
                            {pool.status.charAt(0).toUpperCase() + pool.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                            >
                              Edit
                            </Button>
                            {pool.status === "active" && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-red-500/20 bg-red-500/5 text-red-500 hover:bg-red-500/10"
                              >
                                Pause
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent value="stakers" className="mt-6">
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/10">
                      <TableHead className="text-white">Address</TableHead>
                      <TableHead className="text-white">Pool</TableHead>
                      <TableHead className="text-white">Amount Staked</TableHead>
                      <TableHead className="text-white">Rewards</TableHead>
                      <TableHead className="text-white">Staked Since</TableHead>
                      <TableHead className="text-white">Unlock Date</TableHead>
                      <TableHead className="text-white">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockStakers.map((staker, index) => (
                      <TableRow key={index} className="border-white/10">
                        <TableCell className="text-white/80">{staker.address}</TableCell>
                        <TableCell className="text-white/80">{staker.pool}</TableCell>
                        <TableCell className="text-white/80">{staker.amountStaked}</TableCell>
                        <TableCell className="text-white/80">{staker.rewards}</TableCell>
                        <TableCell className="text-white/80">{staker.stakedSince}</TableCell>
                        <TableCell className="text-white/80">{staker.unlockDate}</TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </TabsContent>

      {/* Rewards Programs Tab */}
      <TabsContent value="rewards" className="mt-6">
        <Card className="glass-card border-white/5 bg-black/20">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-lg text-white">Rewards Management</CardTitle>
              <CardDescription className="text-white/70">Manage reward programs and distributions</CardDescription>
            </div>
            <Button className="bg-doge text-black hover:bg-doge/90">Create New Program</Button>
          </CardHeader>
          <CardContent>
            <Tabs
              defaultValue="programs"
              value={activeRewardsTab}
              onValueChange={setActiveRewardsTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2 bg-black/20 border border-white/10">
                <TabsTrigger value="programs" className="data-[state=active]:bg-white/10">
                  Reward Programs
                </TabsTrigger>
                <TabsTrigger value="distributions" className="data-[state=active]:bg-white/10">
                  Recent Distributions
                </TabsTrigger>
              </TabsList>

              <TabsContent value="programs" className="mt-6">
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/10">
                      <TableHead className="text-white">Name</TableHead>
                      <TableHead className="text-white">Description</TableHead>
                      <TableHead className="text-white">Budget</TableHead>
                      <TableHead className="text-white">Distributed</TableHead>
                      <TableHead className="text-white">Remaining</TableHead>
                      <TableHead className="text-white">Frequency</TableHead>
                      <TableHead className="text-white">Status</TableHead>
                      <TableHead className="text-white">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockRewardPrograms.map((program) => (
                      <TableRow key={program.id} className="border-white/10">
                        <TableCell className="text-white font-medium">{program.name}</TableCell>
                        <TableCell className="text-white/80">{program.description}</TableCell>
                        <TableCell className="text-white/80">{program.totalBudget}</TableCell>
                        <TableCell className="text-white/80">{program.distributed}</TableCell>
                        <TableCell className="text-white/80">{program.remaining}</TableCell>
                        <TableCell className="text-white/80">{program.frequency}</TableCell>
                        <TableCell>
                          <Badge
                            className={
                              program.status === "active"
                                ? "bg-green-500/20 text-green-500 border-green-500/20"
                                : "bg-yellow-500/20 text-yellow-500 border-yellow-500/20"
                            }
                          >
                            {program.status.charAt(0).toUpperCase() + program.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                            >
                              Edit
                            </Button>
                            {program.status === "active" ? (
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-yellow-500/20 bg-yellow-500/5 text-yellow-500 hover:bg-yellow-500/10"
                              >
                                Pause
                              </Button>
                            ) : (
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-green-500/20 bg-green-500/5 text-green-500 hover:bg-green-500/10"
                              >
                                Activate
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent value="distributions" className="mt-6">
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/10">
                      <TableHead className="text-white">Program</TableHead>
                      <TableHead className="text-white">Date</TableHead>
                      <TableHead className="text-white">Amount</TableHead>
                      <TableHead className="text-white">Recipients</TableHead>
                      <TableHead className="text-white">Status</TableHead>
                      <TableHead className="text-white">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockDistributions.map((dist) => (
                      <TableRow key={dist.id} className="border-white/10">
                        <TableCell className="text-white font-medium">{dist.program}</TableCell>
                        <TableCell className="text-white/80">{dist.date}</TableCell>
                        <TableCell className="text-white/80">{dist.amount}</TableCell>
                        <TableCell className="text-white/80">{dist.recipients}</TableCell>
                        <TableCell>
                          <Badge
                            className={
                              dist.status === "completed"
                                ? "bg-green-500/20 text-green-500 border-green-500/20"
                                : "bg-blue-500/20 text-blue-500 border-blue-500/20"
                            }
                          >
                            {dist.status.charAt(0).toUpperCase() + dist.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </TabsContent>

      {/* User Management Tab */}
      <TabsContent value="users" className="mt-6">
        <Card className="glass-card border-white/5 bg-black/20">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-lg text-white">User Management</CardTitle>
              <CardDescription className="text-white/70">Manage platform users, roles, and permissions</CardDescription>
            </div>
            <Button className="bg-doge text-black hover:bg-doge/90">
              <UserPlus className="mr-2 h-4 w-4" /> Add User
            </Button>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mb-6">
              <div className="relative w-full sm:w-96">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
                <Input
                  placeholder="Search users..."
                  className="pl-10 border-white/20 bg-black/20"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <Tabs defaultValue="users" value={activeUsersTab} onValueChange={setActiveUsersTab} className="w-full">
              <TabsList className="bg-black/20">
                <TabsTrigger value="users" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
                  <Users className="mr-2 h-4 w-4" />
                  Users
                </TabsTrigger>
                <TabsTrigger value="roles" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
                  <Shield className="mr-2 h-4 w-4" />
                  Roles & Permissions
                </TabsTrigger>
              </TabsList>

              <TabsContent value="users" className="mt-6">
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableHead className="text-white">User</TableHead>
                      <TableHead className="text-white">Wallet Address</TableHead>
                      <TableHead className="text-white">Role</TableHead>
                      <TableHead className="text-white">Status</TableHead>
                      <TableHead className="text-white">Joined</TableHead>
                      <TableHead className="text-white text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map((user) => (
                      <TableRow key={user.id} className="border-white/5 hover:bg-white/5">
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
                              <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium text-white">{user.name}</p>
                              <p className="text-xs text-white/60">{user.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="font-mono text-sm text-white">{user.address}</TableCell>
                        <TableCell>
                          <Badge
                            className={
                              user.role === "admin"
                                ? "bg-red-500/20 text-red-500"
                                : user.role === "moderator"
                                  ? "bg-blue-500/20 text-blue-500"
                                  : user.role === "developer"
                                    ? "bg-purple-500/20 text-purple-500"
                                    : "bg-green-500/20 text-green-500"
                            }
                          >
                            {user.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={
                              user.status === "active" ? "bg-green-500/20 text-green-500" : "bg-red-500/20 text-red-500"
                            }
                          >
                            {user.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-white">{user.joined}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="icon" className="h-8 w-8 text-white/70">
                              <Edit className="h-4 w-4" />
                            </Button>
                            {user.status === "active" ? (
                              <Button variant="ghost" size="icon" className="h-8 w-8 text-white/70">
                                <Lock className="h-4 w-4" />
                              </Button>
                            ) : (
                              <Button variant="ghost" size="icon" className="h-8 w-8 text-white/70">
                                <User className="h-4 w-4" />
                              </Button>
                            )}
                            <Button variant="ghost" size="icon" className="h-8 w-8 text-white/70">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent value="roles" className="mt-6">
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/5 hover:bg-white/5">
                      <TableHead className="text-white">Role</TableHead>
                      <TableHead className="text-white">Description</TableHead>
                      <TableHead className="text-white">Users</TableHead>
                      <TableHead className="text-white">Permissions</TableHead>
                      <TableHead className="text-white text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roles.map((role) => (
                      <TableRow key={role.id} className="border-white/5 hover:bg-white/5">
                        <TableCell>
                          <Badge
                            className={
                              role.id === "admin"
                                ? "bg-red-500/20 text-red-500"
                                : role.id === "moderator"
                                  ? "bg-blue-500/20 text-blue-500"
                                  : role.id === "developer"
                                    ? "bg-purple-500/20 text-purple-500"
                                    : "bg-green-500/20 text-green-500"
                            }
                          >
                            {role.name}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-white">{role.description}</TableCell>
                        <TableCell className="text-white">
                          {mockUsers.filter((user) => user.role === role.id).length}
                        </TableCell>
                        <TableCell>
                          {role.id === "admin" ? (
                            <span className="text-white/70">All permissions</span>
                          ) : (
                            <Button variant="outline" size="sm" className="border-white/20 bg-black/20 text-white/70">
                              View Permissions
                            </Button>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="sm" className="h-8 text-white/70">
                              Edit
                            </Button>
                            {role.id !== "admin" && role.id !== "user" && (
                              <Button variant="ghost" size="sm" className="h-8 text-white/70">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </TabsContent>

      {/* User Feedback Tab */}
      <TabsContent value="feedback" className="mt-6">
        <Card className="glass-card border-white/5 bg-black/20">
          <CardHeader>
            <CardTitle className="text-lg text-white">User Feedback</CardTitle>
            <CardDescription className="text-white/70">
              Manage and respond to user feedback and suggestions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card className="glass-card border-white/5">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg text-white">All</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-white">{mockFeedback.length}</div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/5">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg text-white">Bugs</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-500">{bugCount}</div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/5">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg text-white">Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-500">{featureCount}</div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/5">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg text-white">UX</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-500">{uxCount}</div>
                </CardContent>
              </Card>
            </div>

            <Tabs defaultValue="all" value={activeFeedbackTab} onValueChange={setActiveFeedbackTab} className="w-full">
              <TabsList className="grid grid-cols-5 mb-6">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="bug">Bugs</TabsTrigger>
                <TabsTrigger value="feature">Features</TabsTrigger>
                <TabsTrigger value="ux">UX</TabsTrigger>
                <TabsTrigger value="other">Other</TabsTrigger>
              </TabsList>

              <TabsContent value={activeFeedbackTab}>
                {filteredFeedback.length === 0 ? (
                  <div className="text-center py-12 bg-white/5 rounded-lg">
                    <p className="text-white/50">No feedback found</p>
                  </div>
                ) : (
                  <div className="rounded-md border border-white/10 overflow-hidden overflow-x-auto">
                    <Table>
                      <TableHeader className="bg-white/5">
                        <TableRow className="hover:bg-transparent border-white/10">
                          <TableHead className="text-white/70">Type</TableHead>
                          <TableHead className="text-white/70">Message</TableHead>
                          <TableHead className="text-white/70">Email</TableHead>
                          <TableHead className="text-white/70">Status</TableHead>
                          <TableHead className="text-white/70">Date</TableHead>
                          <TableHead className="text-white/70">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredFeedback.map((item) => (
                          <TableRow key={item.id} className="hover:bg-white/5 border-white/10">
                            <TableCell>
                              <Badge
                                className={
                                  item.type === "bug"
                                    ? "bg-red-500/20 text-red-500 hover:bg-red-500/30"
                                    : item.type === "feature"
                                      ? "bg-blue-500/20 text-blue-500 hover:bg-blue-500/30"
                                      : item.type === "ux"
                                        ? "bg-purple-500/20 text-purple-500 hover:bg-purple-500/30"
                                        : "bg-gray-500/20 text-gray-500 hover:bg-gray-500/30"
                                }
                              >
                                {item.type}
                              </Badge>
                            </TableCell>
                            <TableCell className="font-medium text-white max-w-xs truncate">{item.message}</TableCell>
                            <TableCell className="text-white/60">{item.email || "-"}</TableCell>
                            <TableCell>
                              <Select
                                value={item.status}
                                onValueChange={(value) => {
                                  // This would update the status in a real app
                                }}
                              >
                                <SelectTrigger className="w-[130px] bg-white/5 border-white/10">
                                  <SelectValue placeholder="Status">
                                    <div className="flex items-center gap-2">
                                      {getStatusIcon(item.status)}
                                      <span className="capitalize">{item.status}</span>
                                    </div>
                                  </SelectValue>
                                </SelectTrigger>
                                <SelectContent className="bg-black border-white/10">
                                  <SelectItem value="new">New</SelectItem>
                                  <SelectItem value="reviewing">Reviewing</SelectItem>
                                  <SelectItem value="resolved">Resolved</SelectItem>
                                  <SelectItem value="closed">Closed</SelectItem>
                                </SelectContent>
                              </Select>
                            </TableCell>
                            <TableCell className="text-white/60">{new Date(item.createdAt).toLocaleString()}</TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-red-500 hover:text-red-400 hover:bg-red-500/10"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </TabsContent>

      {/* System Settings Tab */}
      <TabsContent value="settings" className="mt-6">
        <Card className="glass-card border-white/5 bg-black/20">
          <CardHeader>
            <CardTitle className="text-lg text-white">System Settings</CardTitle>
            <CardDescription className="text-white/70">
              Configure global platform settings and parameters
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs
              defaultValue="general"
              value={activeSettingsTab}
              onValueChange={setActiveSettingsTab}
              className="w-full"
            >
              <TabsList className="bg-black/20">
                <TabsTrigger value="general" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
                  <Settings className="mr-2 h-4 w-4" />
                  General
                </TabsTrigger>
                <TabsTrigger value="trading" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
                  <Sliders className="mr-2 h-4 w-4" />
                  Trading
                </TabsTrigger>
                <TabsTrigger value="security" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
                  <Shield className="mr-2 h-4 w-4" />
                  Security
                </TabsTrigger>
                <TabsTrigger value="fees" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
                  <WalletCards className="mr-2 h-4 w-4" />
                  Fees & Limits
                </TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="mt-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="platform-name" className="text-white">
                      Platform Name
                    </Label>
                    <Input
                      id="platform-name"
                      defaultValue="PawPumps"
                      className="border-white/20 bg-black/20 text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="platform-url" className="text-white">
                      Platform URL
                    </Label>
                    <Input
                      id="platform-url"
                      defaultValue="https://pawpumps.io"
                      className="border-white/20 bg-black/20 text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="contact-email" className="text-white">
                      Contact Email
                    </Label>
                    <Input
                      id="contact-email"
                      defaultValue="<EMAIL>"
                      className="border-white/20 bg-black/20 text-white"
                    />
                  </div>

                  <Separator className="my-4 bg-white/10" />

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-white">Maintenance Mode</Label>
                      <p className="text-sm text-white/70">Temporarily disable the platform for maintenance</p>
                    </div>
                    <Switch />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-white">Enable Analytics</Label>
                      <p className="text-sm text-white/70">Collect anonymous usage data to improve the platform</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>
                <div className="mt-6 flex justify-end">
                  <Button className="bg-doge text-black hover:bg-doge/90">
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="trading" className="mt-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="default-slippage" className="text-white">
                      Default Slippage Tolerance (%)
                    </Label>
                    <Input
                      id="default-slippage"
                      type="number"
                      defaultValue="0.5"
                      min="0.1"
                      max="5"
                      step="0.1"
                      className="border-white/20 bg-black/20 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="default-bonding-curve" className="text-white">
                      Default Bonding Curve Function
                    </Label>
                    <Input
                      id="default-bonding-curve"
                      defaultValue="y = x^2"
                      className="border-white/20 bg-black/20 text-white"
                    />
                    <p className="text-sm text-white/70">Bonding curve formula used for new token launches</p>
                  </div>

                  <Separator className="my-4 bg-white/10" />

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-white">Enable New Token Launches</Label>
                      <p className="text-sm text-white/70">Allow users to launch new tokens on the platform</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>
                <div className="mt-6 flex justify-end">
                  <Button className="bg-doge text-black hover:bg-doge/90">
                    <Save className="mr-2 h-4 w-4" />
                    Save Trading Settings
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="security" className="mt-6">
                {/* Security settings content */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="admin-addresses" className="text-white">
                      Admin Wallet Addresses
                    </Label>
                    <Input
                      id="admin-addresses"
                      defaultValue="******************************************, ******************************************"
                      className="border-white/20 bg-black/20 text-white"
                    />
                    <p className="text-sm text-white/70">Comma-separated list of wallet addresses with admin access</p>
                  </div>

                  <Separator className="my-4 bg-white/10" />

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-white">Two-Factor Authentication</Label>
                      <p className="text-sm text-white/70">Require 2FA for administrative actions</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>
                <div className="mt-6 flex justify-end">
                  <Button className="bg-doge text-black hover:bg-doge/90">
                    <Lock className="mr-2 h-4 w-4" />
                    Update Security Settings
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="fees" className="mt-6">
                {/* Fees settings content */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="trading-fee" className="text-white">
                      Trading Fee (%)
                    </Label>
                    <Input
                      id="trading-fee"
                      type="number"
                      defaultValue="0.3"
                      min="0"
                      max="5"
                      step="0.1"
                      className="border-white/20 bg-black/20 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="launch-fee" className="text-white">
                      Token Launch Fee (%)
                    </Label>
                    <Input
                      id="launch-fee"
                      type="number"
                      defaultValue="1.0"
                      min="0"
                      max="5"
                      step="0.1"
                      className="border-white/20 bg-black/20 text-white"
                    />
                  </div>
                </div>
                <div className="mt-6 flex justify-end">
                  <Button className="bg-doge text-black hover:bg-doge/90">
                    <Save className="mr-2 h-4 w-4" />
                    Save Fee Settings
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </TabsContent>

      {/* Analytics & Reporting Tab */}
      <TabsContent value="analytics" className="mt-6">
        <Card className="glass-card border-white/5 bg-black/20">
          <CardHeader>
            <CardTitle className="text-lg text-white">Analytics & Reporting</CardTitle>
            <CardDescription className="text-white/70">View detailed platform analytics and reports</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-6 flex flex-col sm:flex-row items-center gap-4">
              <div className="relative w-full sm:w-48">
                <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
                <Input
                  type="date"
                  placeholder="Start Date"
                  className="pl-10 border-white/20 bg-black/20"
                  defaultValue="2025-05-01"
                />
              </div>
              <div className="relative w-full sm:w-48">
                <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
                <Input
                  type="date"
                  placeholder="End Date"
                  className="pl-10 border-white/20 bg-black/20"
                  defaultValue="2025-05-07"
                />
              </div>
              <Button className="bg-doge text-black hover:bg-doge/90 w-full sm:w-auto">Apply Filter</Button>
              <Button variant="outline" className="border-white/20 bg-black/20 text-white w-full sm:w-auto">
                <Download className="mr-2 h-4 w-4" />
                Export Data
              </Button>
            </div>

            <Tabs
              defaultValue="overview"
              value={activeAnalyticsTab}
              onValueChange={setActiveAnalyticsTab}
              className="w-full"
            >
              <TabsList className="bg-black/20">
                <TabsTrigger value="overview" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
                  <BarChart2 className="mr-2 h-4 w-4" />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="trading" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
                  <Sliders className="mr-2 h-4 w-4" />
                  Trading
                </TabsTrigger>
                <TabsTrigger value="users" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
                  <Users className="mr-2 h-4 w-4" />
                  Users
                </TabsTrigger>
                <TabsTrigger value="revenue" className="data-[state=active]:bg-doge/20 data-[state=active]:text-doge">
                  <Wallet className="mr-2 h-4 w-4" />
                  Revenue
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-6">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <Card className="border-white/5 bg-black/20">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-white">Total Volume (7d)</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-white">12.5M DOGE</div>
                      <p className="text-xs text-green-500">↑ 15.3% from previous period</p>
                    </CardContent>
                  </Card>
                  <Card className="border-white/5 bg-black/20">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-white">Active Users (7d)</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-white">4,281</div>
                      <p className="text-xs text-green-500">↑ 8.7% from previous period</p>
                    </CardContent>
                  </Card>
                  <Card className="border-white/5 bg-black/20">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-white">New Tokens (7d)</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-white">15</div>
                      <p className="text-xs text-red-500">↓ 6.2% from previous period</p>
                    </CardContent>
                  </Card>
                  <Card className="border-white/5 bg-black/20">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-white">Revenue (7d)</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-white">37,500 DOGE</div>
                      <p className="text-xs text-green-500">↑ 12.8% from previous period</p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="trading" className="mt-6">
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-3">
                    <Card className="border-white/10 bg-black/10">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-white">Total Trades</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-white">32,145</div>
                        <p className="text-xs text-green-500">↑ 11.2% from previous period</p>
                      </CardContent>
                    </Card>
                    <Card className="border-white/10 bg-black/10">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-white">Average Trade Size</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-white">389 DOGE</div>
                        <p className="text-xs text-green-500">↑ 3.7% from previous period</p>
                      </CardContent>
                    </Card>
                    <Card className="border-white/10 bg-black/10">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-white">Unique Traders</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-white">2,864</div>
                        <p className="text-xs text-green-500">↑ 5.9% from previous period</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="users" className="mt-6">
                <div className="grid gap-4 md:grid-cols-3">
                  <Card className="border-white/10 bg-black/10">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-white">New Users</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-white">387</div>
                      <p className="text-xs text-green-500">↑ 15.8% from previous period</p>
                    </CardContent>
                  </Card>
                  <Card className="border-white/10 bg-black/10">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-white">Total Users</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-white">12,854</div>
                      <p className="text-xs text-green-500">↑ 3.1% from previous period</p>
                    </CardContent>
                  </Card>
                  <Card className="border-white/10 bg-black/10">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-white">Retention Rate</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-white">68.4%</div>
                      <p className="text-xs text-green-500">↑ 2.5% from previous period</p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="revenue" className="mt-6">
                <div className="grid gap-4 md:grid-cols-3">
                  <Card className="border-white/10 bg-black/10">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-white">Total Fees Collected</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-white">37,500 DOGE</div>
                      <p className="text-xs text-green-500">↑ 12.8% from previous period</p>
                    </CardContent>
                  </Card>
                  <Card className="border-white/10 bg-black/10">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-white">Development DAO Allocation</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-white">11,250 DOGE</div>
                      <p className="text-xs text-white/60">30% of total fees</p>
                    </CardContent>
                  </Card>
                  <Card className="border-white/10 bg-black/10">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-white">Protocol Revenue</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-white">26,250 DOGE</div>
                      <p className="text-xs text-white/60">70% of total fees</p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
