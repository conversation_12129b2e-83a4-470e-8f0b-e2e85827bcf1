"use client"

import type React from "react"

import { useContext } from "react"
import { <PERSON>ert<PERSON><PERSON>gle, Shield } from "lucide-react"
import { AdminAuthContext } from "@/contexts/admin-auth-context"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

interface AdminOnlyProps {
  children: React.ReactNode
}

export function AdminOnly({ children }: AdminOnlyProps) {
  const { isAdmin, isLoading, login } = useContext(AdminAuthContext)

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <div className="h-12 w-12 rounded-full border-4 border-t-doge border-white/10 animate-spin"></div>
          <p className="text-white font-medium">Loading admin access...</p>
        </div>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="container py-8 md:py-12">
        <Card className="glass-card border-white/5 max-w-md mx-auto">
          <CardHeader>
            <div className="flex items-center justify-center mb-4">
              <Shield className="h-12 w-12 text-doge" />
            </div>
            <CardTitle className="text-white text-center">Admin Access Required</CardTitle>
            <CardDescription className="text-white/70 text-center">
              You need admin privileges to access this section
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-white/70 text-center mb-4">
              For demo purposes, click the button below to gain admin access.
            </p>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button className="doge-button doge-shine" onClick={() => login("demo-password")}>
              Grant Admin Access
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return <>{children}</>
}

interface RequirePermissionProps {
  children: React.ReactNode
  permission: string
}

export function RequirePermission({ children, permission }: RequirePermissionProps) {
  const { hasPermission } = useContext(AdminAuthContext)

  if (!hasPermission(permission)) {
    return (
      <Alert className="glass-card border-yellow-500/20 bg-yellow-500/5">
        <AlertTriangle className="h-5 w-5 text-yellow-500" />
        <AlertTitle className="text-white">Permission Required</AlertTitle>
        <AlertDescription className="text-white/70">
          You need the "{permission}" permission to access this content.
        </AlertDescription>
      </Alert>
    )
  }

  return <>{children}</>
}
