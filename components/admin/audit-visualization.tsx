"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
} from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

// Sample data for visualization
const activityData = [
  { name: "<PERSON>", proposals: 12, votes: 145, executions: 8 },
  { name: "Feb", proposals: 19, votes: 238, executions: 12 },
  { name: "<PERSON>", proposals: 15, votes: 192, executions: 10 },
  { name: "Apr", proposals: 21, votes: 312, executions: 15 },
  { name: "May", proposals: 25, votes: 350, executions: 18 },
  { name: "<PERSON>", proposals: 18, votes: 271, executions: 14 },
]

const actionTypeData = [
  { name: "Proposals", value: 110 },
  { name: "Votes", value: 1508 },
  { name: "<PERSON>", value: 47 },
  { name: "Admin", value: 83 },
  { name: "Emergency", value: 12 },
]

const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff8042", "#0088fe"]

export function AuditVisualization() {
  const [timeRange, setTimeRange] = useState("6m")

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Audit Log Visualization</h2>
        <div className="flex items-center gap-4">
          <Select defaultValue={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px] glass-input">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="1m">Last month</SelectItem>
              <SelectItem value="3m">Last 3 months</SelectItem>
              <SelectItem value="6m">Last 6 months</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="activity" className="space-y-6">
        <TabsList className="glass">
          <TabsTrigger value="activity">Activity Timeline</TabsTrigger>
          <TabsTrigger value="distribution">Action Distribution</TabsTrigger>
          <TabsTrigger value="users">User Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="activity">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle className="text-white">Governance Activity Timeline</CardTitle>
              <CardDescription className="text-white/70">Overview of governance activity over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ChartContainer
                  config={{
                    proposals: {
                      label: "Proposals",
                      color: "hsl(var(--chart-1))",
                    },
                    votes: {
                      label: "Votes",
                      color: "hsl(var(--chart-2))",
                    },
                    executions: {
                      label: "Executions",
                      color: "hsl(var(--chart-3))",
                    },
                  }}
                  className="h-full"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={activityData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis dataKey="name" stroke="rgba(255,255,255,0.5)" />
                      <YAxis stroke="rgba(255,255,255,0.5)" />
                      <ChartTooltip />
                      <Legend />
                      <Bar dataKey="proposals" fill="var(--color-proposals)" />
                      <Bar dataKey="votes" fill="var(--color-votes)" />
                      <Bar dataKey="executions" fill="var(--color-executions)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle className="text-white">Action Type Distribution</CardTitle>
              <CardDescription className="text-white/70">
                Breakdown of different types of actions in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={actionTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={150}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                    >
                      {actionTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card className="glass-card border-white/5">
            <CardHeader>
              <CardTitle className="text-white">Top User Activity</CardTitle>
              <CardDescription className="text-white/70">Most active users in the governance system</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 rounded-md border border-white/10 bg-white/5 flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-white">0x7a...3f21</h3>
                    <p className="text-sm text-white/70">127 actions</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-white/70">Last active: 2 hours ago</p>
                    <p className="text-sm text-white/70">Role: Admin</p>
                  </div>
                </div>

                <div className="p-4 rounded-md border border-white/10 bg-white/5 flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-white">0x3b...9c45</h3>
                    <p className="text-sm text-white/70">98 actions</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-white/70">Last active: 1 day ago</p>
                    <p className="text-sm text-white/70">Role: Moderator</p>
                  </div>
                </div>

                <div className="p-4 rounded-md border border-white/10 bg-white/5 flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-white">0x5f...2d78</h3>
                    <p className="text-sm text-white/70">76 actions</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-white/70">Last active: 3 days ago</p>
                    <p className="text-sm text-white/70">Role: Proposer</p>
                  </div>
                </div>

                <div className="p-4 rounded-md border border-white/10 bg-white/5 flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-white">0x9d...4e12</h3>
                    <p className="text-sm text-white/70">64 actions</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-white/70">Last active: 5 days ago</p>
                    <p className="text-sm text-white/70">Role: Treasury Manager</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
