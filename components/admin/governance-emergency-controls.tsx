"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { useAuditLog } from "@/utils/audit-logger"
import { useWallet } from "@/components/wallet-provider"
import { AlertTriangle, Clock, Shield } from "lucide-react"
import { MultiSignatureConfirmation } from "@/components/admin/multi-signature-confirmation"

export function GovernanceEmergencyControls() {
  const { toast } = useToast()
  const { logAction } = useAuditLog()
  const { address } = useWallet()

  const [governanceStatus, setGovernanceStatus] = useState({
    voting: true,
    proposalCreation: true,
    execution: true,
    delegation: true,
  })

  const [showConfirmation, setShowConfirmation] = useState(false)
  const [pendingStatusChange, setPendingStatusChange] = useState<{
    system: keyof typeof governanceStatus
    enabled: boolean
  } | null>(null)

  const [showEmergencyShutdownConfirmation, setShowEmergencyShutdownConfirmation] = useState(false)
  const [emergencyActions, setEmergencyActions] = useState([
    {
      action: "Voting System Paused",
      timestamp: "2025-04-28T14:32:00Z",
      actor: "0x1a2b...3c4d",
      reason: "Unusual voting pattern detected",
    },
    {
      action: "Voting System Resumed",
      timestamp: "2025-04-28T16:45:00Z",
      actor: "0x1a2b...3c4d",
      reason: "Issue resolved after investigation",
    },
  ])

  const handleStatusChange = async (system: keyof typeof governanceStatus, enabled: boolean) => {
    // For critical systems, require confirmation
    if (system === "voting" || system === "execution") {
      setShowConfirmation(true)
      // Store the pending change to apply after confirmation
      setPendingStatusChange({ system, enabled })
      return
    }

    // For non-critical systems, apply immediately
    await applyStatusChange(system, enabled)
  }

  const applyStatusChange = async (system: keyof typeof governanceStatus, enabled: boolean) => {
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setGovernanceStatus({ ...governanceStatus, [system]: enabled })

      // Log the action
      await logAction(enabled ? "governance:resume" : "governance:pause", address || "unknown", {
        system,
        previousState: governanceStatus[system],
        newState: enabled,
      })

      // Add to emergency actions
      const newAction = {
        action: `${system.charAt(0).toUpperCase() + system.slice(1)} System ${enabled ? "Resumed" : "Paused"}`,
        timestamp: new Date().toISOString(),
        actor: address || "0x1a2b...3c4d",
        reason: enabled ? "System operation restored" : "Emergency pause activated",
      }

      setEmergencyActions([newAction, ...emergencyActions])

      toast({
        title: `${system.charAt(0).toUpperCase() + system.slice(1)} ${enabled ? "Resumed" : "Paused"}`,
        description: `Governance ${system} has been ${enabled ? "resumed" : "paused"} successfully.`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${enabled ? "resume" : "pause"} ${system}. Please try again.`,
        variant: "destructive",
      })
    }
  }

  const handleEmergencyShutdown = () => {
    setShowEmergencyShutdownConfirmation(true)
  }

  const executeEmergencyShutdown = async () => {
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Update all systems to paused
      const newStatus = {
        voting: false,
        proposalCreation: false,
        execution: false,
        delegation: false,
      }

      setGovernanceStatus(newStatus)

      // Log the action
      await logAction("governance:emergency_shutdown", address || "unknown", {
        previousState: governanceStatus,
        newState: newStatus,
      })

      // Add to emergency actions
      const newAction = {
        action: "Emergency Shutdown Executed",
        timestamp: new Date().toISOString(),
        actor: address || "0x1a2b...3c4d",
        reason: "Critical emergency response activated",
      }

      setEmergencyActions([newAction, ...emergencyActions])

      toast({
        title: "Emergency Shutdown Executed",
        description: "All governance functions have been paused.",
      })

      setShowEmergencyShutdownConfirmation(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to execute emergency shutdown. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <Card className="glass-card border-white/5 bg-black/20">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle className="text-white">Governance Emergency Controls</CardTitle>
            <CardDescription className="text-white/70">
              Critical controls for governance system emergency situations
            </CardDescription>
          </div>
          <Badge className={governanceStatus.voting ? "bg-green-500/20 text-green-500" : "bg-red-500/20 text-red-500"}>
            <Shield className="mr-1 h-4 w-4" />
            {governanceStatus.voting ? "System Operational" : "System Partially Paused"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert className="glass-card border-red-500/20 bg-red-500/5">
          <AlertTriangle className="h-5 w-5 text-red-500" />
          <AlertTitle className="text-white">Critical Controls</AlertTitle>
          <AlertDescription className="text-white/70">
            These controls should only be used in emergency situations. All actions are logged and require
            multi-signature approval for critical functions.
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-white">Voting System</Label>
              <p className="text-sm text-white/70">Allow users to vote on proposals</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                className={governanceStatus.voting ? "bg-green-500/20 text-green-500" : "bg-red-500/20 text-red-500"}
              >
                {governanceStatus.voting ? "Active" : "Paused"}
              </Badge>
              <Switch
                checked={governanceStatus.voting}
                onCheckedChange={(checked) => handleStatusChange("voting", checked)}
              />
            </div>
          </div>

          <Separator className="bg-white/10" />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-white">Proposal Creation</Label>
              <p className="text-sm text-white/70">Allow users to create new proposals</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                className={
                  governanceStatus.proposalCreation ? "bg-green-500/20 text-green-500" : "bg-red-500/20 text-red-500"
                }
              >
                {governanceStatus.proposalCreation ? "Active" : "Paused"}
              </Badge>
              <Switch
                checked={governanceStatus.proposalCreation}
                onCheckedChange={(checked) => handleStatusChange("proposalCreation", checked)}
              />
            </div>
          </div>

          <Separator className="bg-white/10" />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-white">Proposal Execution</Label>
              <p className="text-sm text-white/70">Allow execution of passed proposals</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                className={governanceStatus.execution ? "bg-green-500/20 text-green-500" : "bg-red-500/20 text-red-500"}
              >
                {governanceStatus.execution ? "Active" : "Paused"}
              </Badge>
              <Switch
                checked={governanceStatus.execution}
                onCheckedChange={(checked) => handleStatusChange("execution", checked)}
              />
            </div>
          </div>

          <Separator className="bg-white/10" />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-white">Vote Delegation</Label>
              <p className="text-sm text-white/70">Allow users to delegate their voting power</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                className={
                  governanceStatus.delegation ? "bg-green-500/20 text-green-500" : "bg-red-500/20 text-red-500"
                }
              >
                {governanceStatus.delegation ? "Active" : "Paused"}
              </Badge>
              <Switch
                checked={governanceStatus.delegation}
                onCheckedChange={(checked) => handleStatusChange("delegation", checked)}
              />
            </div>
          </div>
        </div>

        <div className="pt-4">
          <Button className="w-full bg-red-600 hover:bg-red-700 text-white" onClick={handleEmergencyShutdown}>
            <AlertTriangle className="mr-2 h-4 w-4" />
            Initiate Governance Emergency Shutdown
          </Button>
        </div>

        <div className="rounded-md border border-white/10 p-4">
          <h3 className="mb-3 text-lg font-medium text-white">Recent Emergency Actions</h3>
          <div className="space-y-4 max-h-[300px] overflow-y-auto pr-2">
            {emergencyActions.map((action, index) => (
              <div key={index} className="flex items-start gap-3">
                <Clock className="mt-0.5 h-4 w-4 text-white/50" />
                <div>
                  <p className="text-sm font-medium text-white">{action.action}</p>
                  <p className="text-xs text-white/60">
                    {new Date(action.timestamp).toLocaleString()} by {action.actor}
                  </p>
                  <p className="text-xs text-white/60">Reason: {action.reason}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>

      {/* Confirmation Dialogs */}
      {showConfirmation && pendingStatusChange && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <MultiSignatureConfirmation
            title={`${pendingStatusChange.enabled ? "Resume" : "Pause"} ${pendingStatusChange.system}`}
            description={`This will ${pendingStatusChange.enabled ? "resume" : "pause"} the ${pendingStatusChange.system} system`}
            action={`${pendingStatusChange.enabled ? "RESUME" : "PAUSE"}_${pendingStatusChange.system.toUpperCase()}`}
            details={{
              system: pendingStatusChange.system,
              currentState: governanceStatus[pendingStatusChange.system],
              newState: pendingStatusChange.enabled,
              initiatedAt: new Date().toISOString(),
            }}
            requiredSignatures={2}
            onConfirm={async () => {
              await applyStatusChange(pendingStatusChange.system, pendingStatusChange.enabled)
              setPendingStatusChange(null)
              setShowConfirmation(false)
            }}
            onCancel={() => {
              setPendingStatusChange(null)
              setShowConfirmation(false)
            }}
          />
        </div>
      )}

      {showEmergencyShutdownConfirmation && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <MultiSignatureConfirmation
            title="Governance Emergency Shutdown"
            description="This will immediately pause all governance functionality"
            action="GOVERNANCE_EMERGENCY_SHUTDOWN"
            details={{
              affectedSystems: ["voting", "proposalCreation", "execution", "delegation"],
              initiatedAt: new Date().toISOString(),
            }}
            requiredSignatures={3}
            onConfirm={executeEmergencyShutdown}
            onCancel={() => setShowEmergencyShutdownConfirmation(false)}
          />
        </div>
      )}
    </Card>
  )
}
