"use client"

import type React from "react"

import { useState } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import {
  BarChart3,
  Bell,
  Cog,
  Coins,
  FileText,
  Gauge,
  Gift,
  Home,
  MessageSquare,
  Shield,
  Users,
  Vote,
} from "lucide-react"

interface NavItem {
  title: string
  href: string
  icon: React.ReactNode
  active?: boolean
}

export function UnifiedAdminNav() {
  const pathname = usePathname()
  const [expanded, setExpanded] = useState(true)

  const navItems: NavItem[] = [
    {
      title: "Dashboard",
      href: "/admin",
      icon: <Home className="h-5 w-5" />,
      active: pathname === "/admin",
    },
    {
      title: "Users",
      href: "/admin/users",
      icon: <Users className="h-5 w-5" />,
      active: pathname === "/admin/users",
    },
    {
      title: "Analytics",
      href: "/admin/analytics",
      icon: <BarChart3 className="h-5 w-5" />,
      active: pathname === "/admin/analytics",
    },
    {
      title: "Governance",
      href: "/admin/governance",
      icon: <Vote className="h-5 w-5" />,
      active: pathname.startsWith("/admin/governance"),
    },
    {
      title: "Proposals",
      href: "/admin/proposals",
      icon: <FileText className="h-5 w-5" />,
      active: pathname === "/admin/proposals",
    },
    {
      title: "Staking",
      href: "/admin/staking",
      icon: <Coins className="h-5 w-5" />,
      active: pathname === "/admin/staking",
    },
    {
      title: "Rewards",
      href: "/admin/rewards",
      icon: <Gift className="h-5 w-5" />,
      active: pathname === "/admin/rewards",
    },
    {
      title: "Feedback",
      href: "/admin/feedback",
      icon: <MessageSquare className="h-5 w-5" />,
      active: pathname === "/admin/feedback",
    },
    {
      title: "Notifications",
      href: "/admin/notifications",
      icon: <Bell className="h-5 w-5" />,
      active: pathname === "/admin/notifications",
    },
    {
      title: "Security",
      href: "/admin/security",
      icon: <Shield className="h-5 w-5" />,
      active: pathname === "/admin/security",
    },
    {
      title: "Performance",
      href: "/admin/performance",
      icon: <Gauge className="h-5 w-5" />,
      active: pathname === "/admin/performance",
    },
    {
      title: "Settings",
      href: "/admin/settings",
      icon: <Cog className="h-5 w-5" />,
      active: pathname === "/admin/settings",
    },
  ]

  return (
    <div className="flex flex-col gap-1">
      {navItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className={cn(
            "flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors",
            item.active ? "bg-white/10 text-white" : "text-white/70 hover:bg-white/5 hover:text-white",
          )}
        >
          {item.icon}
          {expanded && <span>{item.title}</span>}
        </Link>
      ))}
    </div>
  )
}
