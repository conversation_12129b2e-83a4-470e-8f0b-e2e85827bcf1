"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { type AuditAction, type AuditLogEntry, getAuditLogs } from "@/utils/audit-logger"
import { CalendarIcon, Download, Filter, RefreshCw, Search, AlertTriangle } from "lucide-react"
import { format } from "date-fns"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export function AuditLogViewer() {
  const [logs, setLogs] = useState<AuditLogEntry[]>([])
  const [filteredLogs, setFilteredLogs] = useState<AuditLogEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [actionFilter, setActionFilter] = useState<AuditAction | "all">("all")
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })

  // Add state for anomaly detection
  const [anomalies, setAnomalies] = useState<AuditLogEntry[]>([])
  const [showAnomaliesOnly, setShowAnomaliesOnly] = useState(false)

  // Load logs
  useEffect(() => {
    loadLogs()
  }, [])

  // Apply filters
  useEffect(() => {
    applyFilters()
  }, [logs, searchQuery, actionFilter, dateRange, showAnomaliesOnly])

  // Update the loadLogs function to include anomaly detection
  const loadLogs = () => {
    setLoading(true)
    // In a real app, this would fetch from an API
    const allLogs = getAuditLogs({ limit: 100 })

    // Simple anomaly detection
    // In a real app, this would use more sophisticated algorithms
    const detectedAnomalies = allLogs.filter((log) => {
      // Consider emergency actions as anomalies
      if (log.action.startsWith("emergency:")) return true

      // Consider treasury withdrawals as potential anomalies
      if (log.action === "treasury:withdraw") return true

      // Consider multiple failed login attempts as anomalies
      if (log.action === "login" && log.details.success === false) return true

      return false
    })

    setLogs(allLogs)
    setAnomalies(detectedAnomalies)
    setFilteredLogs(showAnomaliesOnly ? detectedAnomalies : allLogs)
    setLoading(false)
  }

  // Update the applyFilters function to include anomaly filtering
  const applyFilters = () => {
    let filtered = [...logs]

    // Apply anomaly filter if enabled
    if (showAnomaliesOnly) {
      filtered = anomalies
    }

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (log) =>
          log.actor.toLowerCase().includes(query) ||
          (log.target && log.target.toLowerCase().includes(query)) ||
          JSON.stringify(log.details).toLowerCase().includes(query),
      )
    }

    // Apply action filter
    if (actionFilter !== "all") {
      filtered = filtered.filter((log) => log.action === actionFilter)
    }

    // Apply date range
    if (dateRange.from) {
      filtered = filtered.filter((log) => new Date(log.timestamp) >= dateRange.from!)
    }
    if (dateRange.to) {
      // Add one day to include the end date fully
      const endDate = new Date(dateRange.to)
      endDate.setDate(endDate.getDate() + 1)
      filtered = filtered.filter((log) => new Date(log.timestamp) <= endDate)
    }

    setFilteredLogs(filtered)
  }

  // Add a function to check if a log entry is an anomaly
  const isAnomaly = (log: AuditLogEntry) => {
    return anomalies.some((anomaly) => anomaly.id === log.id)
  }

  const handleRefresh = () => {
    loadLogs()
  }

  const handleExport = () => {
    // Create CSV content
    const headers = ["Timestamp", "Action", "Actor", "Target", "Details"]
    const csvContent = [
      headers.join(","),
      ...filteredLogs.map((log) => {
        return [
          log.timestamp,
          log.action,
          log.actor,
          log.target || "",
          JSON.stringify(log.details).replace(/,/g, ";"), // Replace commas in JSON to avoid CSV issues
        ].join(",")
      }),
    ].join("\n")

    // Create and download file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `audit-logs-${format(new Date(), "yyyy-MM-dd")}.csv`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const getActionBadgeColor = (action: AuditAction) => {
    if (action.startsWith("emergency:")) return "bg-red-500/20 text-red-500"
    if (action.startsWith("treasury:")) return "bg-purple-500/20 text-purple-500"
    if (action.startsWith("proposal:")) return "bg-blue-500/20 text-blue-500"
    if (action.startsWith("user:")) return "bg-green-500/20 text-green-500"
    if (action.startsWith("role:")) return "bg-yellow-500/20 text-yellow-500"
    if (action.startsWith("content:")) return "bg-orange-500/20 text-orange-500"
    return "bg-white/20 text-white"
  }

  return (
    <Card className="glass-card border-white/5">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle className="text-white">Audit Logs</CardTitle>
            <CardDescription className="text-white/70">
              View and filter system audit logs for administrative actions
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleRefresh} className="border-white/10 bg-white/5">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport} className="border-white/10 bg-white/5">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {anomalies.length > 0 && (
          <Alert className="mb-4 glass-card border-red-500/20 bg-red-500/5">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <AlertTitle className="text-white">Anomalies Detected</AlertTitle>
            <AlertDescription className="text-white/70">
              {anomalies.length} potential anomalies detected in the audit logs. These may require investigation.
            </AlertDescription>
          </Alert>
        )}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
              <Input
                placeholder="Search logs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="glass-input text-white pl-9"
              />
            </div>
            <div className="flex items-center gap-2 ml-2">
              <Switch
                checked={showAnomaliesOnly}
                onCheckedChange={(checked) => {
                  setShowAnomaliesOnly(checked)
                  setFilteredLogs(checked ? anomalies : logs)
                }}
                id="anomaly-filter"
              />
              <Label htmlFor="anomaly-filter" className="text-white cursor-pointer">
                Anomalies Only ({anomalies.length})
              </Label>
            </div>
            <div className="flex gap-2">
              <Select value={actionFilter} onValueChange={(value) => setActionFilter(value as AuditAction | "all")}>
                <SelectTrigger className="glass-input text-white w-[180px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Filter by action" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Actions</SelectItem>
                  <SelectItem value="login">Login</SelectItem>
                  <SelectItem value="logout">Logout</SelectItem>
                  <SelectItem value="user:create">User Create</SelectItem>
                  <SelectItem value="user:update">User Update</SelectItem>
                  <SelectItem value="user:delete">User Delete</SelectItem>
                  <SelectItem value="proposal:create">Proposal Create</SelectItem>
                  <SelectItem value="proposal:vote">Proposal Vote</SelectItem>
                  <SelectItem value="proposal:execute">Proposal Execute</SelectItem>
                  <SelectItem value="treasury:withdraw">Treasury Withdraw</SelectItem>
                  <SelectItem value="emergency:pause">Emergency Pause</SelectItem>
                  <SelectItem value="emergency:resume">Emergency Resume</SelectItem>
                </SelectContent>
              </Select>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="glass-input text-white">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(dateRange.from, "LLL dd, y")
                      )
                    ) : (
                      <span>Date Range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={(range) => setDateRange({ from: range?.from, to: range?.to })}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center py-8">
              <div className="h-8 w-8 rounded-full border-4 border-t-doge border-white/10 animate-spin"></div>
            </div>
          ) : filteredLogs.length === 0 ? (
            <div className="text-center py-8 text-white/70">No audit logs found matching your filters.</div>
          ) : (
            <div className="rounded-md border border-white/10 overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow className="border-white/10 hover:bg-white/5">
                    <TableHead className="text-white">Timestamp</TableHead>
                    <TableHead className="text-white">Action</TableHead>
                    <TableHead className="text-white">Actor</TableHead>
                    <TableHead className="text-white">Target</TableHead>
                    <TableHead className="text-white">Details</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.map((log) => (
                    <TableRow
                      key={log.id}
                      className={`border-white/10 hover:bg-white/5 ${isAnomaly(log) ? "bg-red-500/10" : ""}`}
                    >
                      <TableCell className="text-white/70">
                        {format(new Date(log.timestamp), "MMM dd, yyyy HH:mm:ss")}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge className={getActionBadgeColor(log.action)}>{log.action}</Badge>
                          {isAnomaly(log) && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger>
                                  <AlertTriangle className="h-4 w-4 text-red-500" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Potential anomaly detected</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-white/70">
                        {log.actor.substring(0, 6)}...{log.actor.substring(log.actor.length - 4)}
                      </TableCell>
                      <TableCell className="text-white/70">
                        {log.target ? `${log.target.substring(0, 8)}...` : "-"}
                      </TableCell>
                      <TableCell className="text-white/70">
                        <div className="max-w-xs truncate">
                          {JSON.stringify(log.details).substring(0, 50)}
                          {JSON.stringify(log.details).length > 50 ? "..." : ""}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
