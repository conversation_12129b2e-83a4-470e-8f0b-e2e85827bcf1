"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import { useAuditLog } from "@/utils/audit-logger"
import { useWallet } from "@/components/wallet-provider"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit, Eye, Filter, Plus, Search, Trash2 } from "lucide-react"
import Link from "next/link"

// Define a consistent proposal structure
interface ProposalVotes {
  for: number
  against: number
  abstain?: number
}

interface Proposal {
  id: string
  title: string
  description: string
  category: string
  status: string
  createdAt: string
  createdBy?: string
  votingPeriod?: number
  votes: ProposalVotes
  quorum?: number
  comments?: any[]
}

// Mock data with consistent structure
const proposals = [
  {
    id: "PROP-001",
    title: "Implement Token Burning Mechanism",
    status: "active",
    category: "protocol",
    votes: { for: 65, against: 35 },
    createdAt: "2025-04-01",
    description: "A proposal to implement a token burning mechanism to reduce supply over time.",
  },
  {
    id: "PROP-002",
    title: "Increase Development Fund Allocation",
    status: "passed",
    category: "treasury",
    votes: { for: 82, against: 18 },
    createdAt: "2025-03-15",
    description: "Increase the allocation of funds to the development team to accelerate roadmap delivery.",
  },
  {
    id: "PROP-003",
    title: "Add New Trading Pair: DOGE/SHIB",
    status: "rejected",
    category: "technical",
    votes: { for: 42, against: 58 },
    createdAt: "2025-03-01",
    description: "Add a new trading pair to increase liquidity and trading options.",
  },
  {
    id: "PROP-004",
    title: "Reduce Platform Fees by 0.1%",
    status: "pending",
    category: "community",
    votes: { for: 0, against: 0 },
    createdAt: "2025-04-10",
    description: "Reduce platform fees to attract more users and increase volume.",
  },
]

export function AdminProposalManagement() {
  const { toast } = useToast()
  const { logAction } = useAuditLog()
  const { address, isConnected } = useWallet()
  const [filteredProposals, setFilteredProposals] = useState<Proposal[]>([])
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(null)
  const [newProposal, setNewProposal] = useState({
    title: "",
    description: "",
    category: "protocol",
    votingPeriod: "7",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [proposalsState, setProposalsState] = useState<Proposal[]>(proposals)

  // Load proposals
  useEffect(() => {
    // In a real app, this would fetch from an API
    setProposalsState(proposals)
  }, [])

  // Filter proposals based on search and status
  useEffect(() => {
    let filtered = [...proposalsState]

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (p) =>
          p.title.toLowerCase().includes(query) ||
          p.description.toLowerCase().includes(query) ||
          p.id.toLowerCase().includes(query),
      )
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((p) => p.status === statusFilter)
    }

    setFilteredProposals(filtered)
  }, [proposalsState, searchQuery, statusFilter])

  const handleCreateProposal = async () => {
    setIsSubmitting(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const proposal: Proposal = {
        id: `PROP-${Math.floor(Math.random() * 1000)}`,
        title: newProposal.title,
        description: newProposal.description,
        category: newProposal.category,
        status: "pending",
        createdAt: new Date().toISOString(),
        createdBy: address || "0x1234...5678",
        votingPeriod: Number.parseInt(newProposal.votingPeriod),
        votes: {
          for: 0,
          against: 0,
          abstain: 0,
        },
        quorum: 10,
        comments: [],
      }

      setProposalsState((prev) => [proposal, ...prev])

      // Log the action
      await logAction("proposal:create", address || "unknown", {
        proposalId: proposal.id,
        title: proposal.title,
        category: proposal.category,
      })

      toast({
        title: "Proposal Created",
        description: "The proposal has been created successfully.",
      })

      setNewProposal({
        title: "",
        description: "",
        category: "protocol",
        votingPeriod: "7",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create proposal. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
      setShowCreateDialog(false)
    }
  }

  const handleEditProposal = async () => {
    if (!selectedProposal) return

    setIsSubmitting(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setProposalsState((prev) => prev.map((p) => (p.id === selectedProposal.id ? { ...selectedProposal } : p)))

      // Log the action
      await logAction("proposal:update", address || "unknown", {
        proposalId: selectedProposal.id,
        title: selectedProposal.title,
      })

      toast({
        title: "Proposal Updated",
        description: "The proposal has been updated successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update proposal. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
      setShowEditDialog(false)
      setSelectedProposal(null)
    }
  }

  const handleDeleteProposal = async () => {
    if (!selectedProposal) return

    setIsSubmitting(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setProposalsState((prev) => prev.filter((p) => p.id !== selectedProposal.id))

      // Log the action
      await logAction("proposal:delete", address || "unknown", {
        proposalId: selectedProposal.id,
        title: selectedProposal.title,
      })

      toast({
        title: "Proposal Deleted",
        description: "The proposal has been deleted successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete proposal. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
      setShowDeleteDialog(false)
      setSelectedProposal(null)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-blue-500/20 text-blue-500 border-blue-500/20"
      case "passed":
        return "bg-green-500/20 text-green-500 border-green-500/20"
      case "rejected":
        return "bg-red-500/20 text-red-500 border-red-500/20"
      case "pending":
        return "bg-yellow-500/20 text-yellow-500 border-yellow-500/20"
      case "executed":
        return "bg-purple-500/20 text-purple-500 border-purple-500/20"
      default:
        return "bg-gray-500/20 text-gray-500 border-gray-500/20"
    }
  }

  // Helper function to safely display votes
  const displayVotes = (proposal: Proposal) => {
    // Ensure votes object exists
    const votes = proposal.votes || { for: 0, against: 0 }

    return (
      <div className="flex items-center gap-2">
        <span className="text-green-500">{votes.for || 0}</span>
        <span>/</span>
        <span className="text-red-500">{votes.against || 0}</span>
        {votes.abstain !== undefined && votes.abstain > 0 && (
          <>
            <span>/</span>
            <span className="text-gray-500">{votes.abstain}</span>
          </>
        )}
      </div>
    )
  }

  return (
    <Card className="glass-card border-white/5 bg-black/20">
      <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <CardTitle className="text-white">Governance Proposals</CardTitle>
          <CardDescription className="text-white/70">Manage and monitor governance proposals</CardDescription>
        </div>
        <Button className="bg-doge text-black hover:bg-doge/90" onClick={() => setShowCreateDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Proposal
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
            <Input
              placeholder="Search proposals..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 border-white/20 bg-black/20 text-white"
            />
          </div>
          <div className="flex gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px] border-white/20 bg-black/20 text-white">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="passed">Passed</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="executed">Executed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="rounded-md border border-white/10 overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="border-white/10">
                <TableHead className="text-white">ID</TableHead>
                <TableHead className="text-white">Title</TableHead>
                <TableHead className="text-white">Category</TableHead>
                <TableHead className="text-white">Status</TableHead>
                <TableHead className="text-white">Created</TableHead>
                <TableHead className="text-white">Votes</TableHead>
                <TableHead className="text-white">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProposals.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-white/70">
                    {proposalsState.length === 0
                      ? "No proposals found. Create your first proposal!"
                      : "No proposals match your search criteria."}
                  </TableCell>
                </TableRow>
              ) : (
                filteredProposals.map((proposal) => (
                  <TableRow key={proposal.id} className="border-white/10">
                    <TableCell className="text-white/80">{proposal.id}</TableCell>
                    <TableCell className="text-white font-medium">{proposal.title}</TableCell>
                    <TableCell>
                      <Badge className="bg-white/10 text-white/80">
                        {proposal.category.charAt(0).toUpperCase() + proposal.category.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={`${getStatusColor(proposal.status)}`}>
                        {proposal.status.charAt(0).toUpperCase() + proposal.status.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-white/80">{new Date(proposal.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell className="text-white/80">{displayVotes(proposal)}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Link href={`/governance/proposals/${proposal.id}`} passHref>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                          >
                            <Eye className="h-4 w-4" />
                            <span className="sr-only">View</span>
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-white/10 bg-white/5 text-white hover:bg-white/10"
                          onClick={() => {
                            setSelectedProposal(proposal)
                            setShowEditDialog(true)
                          }}
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-red-500/20 bg-red-500/5 text-red-500 hover:bg-red-500/10"
                          onClick={() => {
                            setSelectedProposal(proposal)
                            setShowDeleteDialog(true)
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* Create Proposal Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="glass-card border-white/5">
          <DialogHeader>
            <DialogTitle className="text-white">Create New Proposal</DialogTitle>
            <DialogDescription className="text-white/70">
              Create a new governance proposal for the community to vote on.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title" className="text-white">
                Title
              </Label>
              <Input
                id="title"
                value={newProposal.title}
                onChange={(e) => setNewProposal({ ...newProposal, title: e.target.value })}
                className="border-white/20 bg-black/20 text-white"
                placeholder="Enter proposal title"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-white">
                Description
              </Label>
              <Textarea
                id="description"
                value={newProposal.description}
                onChange={(e) => setNewProposal({ ...newProposal, description: e.target.value })}
                className="min-h-[100px] border-white/20 bg-black/20 text-white"
                placeholder="Enter proposal description"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category" className="text-white">
                  Category
                </Label>
                <Select
                  value={newProposal.category}
                  onValueChange={(value) => setNewProposal({ ...newProposal, category: value })}
                >
                  <SelectTrigger className="border-white/20 bg-black/20 text-white">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="protocol">Protocol</SelectItem>
                    <SelectItem value="treasury">Treasury</SelectItem>
                    <SelectItem value="community">Community</SelectItem>
                    <SelectItem value="technical">Technical</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="votingPeriod" className="text-white">
                  Voting Period (days)
                </Label>
                <Select
                  value={newProposal.votingPeriod}
                  onValueChange={(value) => setNewProposal({ ...newProposal, votingPeriod: value })}
                >
                  <SelectTrigger className="border-white/20 bg-black/20 text-white">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="3">3 days</SelectItem>
                    <SelectItem value="5">5 days</SelectItem>
                    <SelectItem value="7">7 days</SelectItem>
                    <SelectItem value="14">14 days</SelectItem>
                    <SelectItem value="30">30 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              className="border-white/10 bg-white/5 text-white"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateProposal}
              className="bg-doge text-black hover:bg-doge/90"
              disabled={isSubmitting || !newProposal.title || !newProposal.description}
            >
              {isSubmitting ? (
                <>
                  <div className="h-4 w-4 rounded-full border-2 border-t-black border-black/10 animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                "Create Proposal"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Proposal Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="glass-card border-white/5">
          <DialogHeader>
            <DialogTitle className="text-white">Edit Proposal</DialogTitle>
            <DialogDescription className="text-white/70">
              Update the details of this governance proposal.
            </DialogDescription>
          </DialogHeader>

          {selectedProposal && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="edit-title" className="text-white">
                  Title
                </Label>
                <Input
                  id="edit-title"
                  value={selectedProposal.title}
                  onChange={(e) => setSelectedProposal({ ...selectedProposal, title: e.target.value })}
                  className="border-white/20 bg-black/20 text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-description" className="text-white">
                  Description
                </Label>
                <Textarea
                  id="edit-description"
                  value={selectedProposal.description}
                  onChange={(e) => setSelectedProposal({ ...selectedProposal, description: e.target.value })}
                  className="min-h-[100px] border-white/20 bg-black/20 text-white"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-category" className="text-white">
                    Category
                  </Label>
                  <Select
                    value={selectedProposal.category}
                    onValueChange={(value) => setSelectedProposal({ ...selectedProposal, category: value })}
                  >
                    <SelectTrigger className="border-white/20 bg-black/20 text-white">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="protocol">Protocol</SelectItem>
                      <SelectItem value="treasury">Treasury</SelectItem>
                      <SelectItem value="community">Community</SelectItem>
                      <SelectItem value="technical">Technical</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-status" className="text-white">
                    Status
                  </Label>
                  <Select
                    value={selectedProposal.status}
                    onValueChange={(value) => setSelectedProposal({ ...selectedProposal, status: value })}
                  >
                    <SelectTrigger className="border-white/20 bg-black/20 text-white">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="passed">Passed</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                      <SelectItem value="executed">Executed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowEditDialog(false)}
              className="border-white/10 bg-white/5 text-white"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleEditProposal}
              className="bg-doge text-black hover:bg-doge/90"
              disabled={isSubmitting || !selectedProposal?.title || !selectedProposal?.description}
            >
              {isSubmitting ? (
                <>
                  <div className="h-4 w-4 rounded-full border-2 border-t-black border-black/10 animate-spin mr-2" />
                  Updating...
                </>
              ) : (
                "Update Proposal"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="glass-card border-white/5">
          <DialogHeader>
            <DialogTitle className="text-white">Confirm Deletion</DialogTitle>
            <DialogDescription className="text-white/70">
              Are you sure you want to delete this proposal? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {selectedProposal && (
            <div className="p-4 rounded-md bg-white/5 border border-white/10">
              <h3 className="font-medium text-white">{selectedProposal.title}</h3>
              <p className="text-sm text-white/70 mt-1">ID: {selectedProposal.id}</p>
            </div>
          )}

          <Alert className="glass-card border-red-500/20 bg-red-500/5">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <AlertTitle className="text-white">Warning</AlertTitle>
            <AlertDescription className="text-white/70">
              Deleting a proposal will remove all associated votes and comments.
            </AlertDescription>
          </Alert>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              className="border-white/10 bg-white/5 text-white"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteProposal}
              className="bg-red-600 hover:bg-red-700 text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="h-4 w-4 rounded-full border-2 border-t-white border-white/10 animate-spin mr-2" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Proposal
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
