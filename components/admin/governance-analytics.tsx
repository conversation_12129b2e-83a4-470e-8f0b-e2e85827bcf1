"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts"
import { Download } from "lucide-react"

// Sample data for visualization
const activityData = [
  { name: "Jan", proposals: 12, votes: 145, executions: 8 },
  { name: "Feb", proposals: 19, votes: 238, executions: 12 },
  { name: "<PERSON>", proposals: 15, votes: 192, executions: 10 },
  { name: "Apr", proposals: 21, votes: 312, executions: 15 },
  { name: "May", proposals: 25, votes: 350, executions: 18 },
  { name: "<PERSON>", proposals: 18, votes: 271, executions: 14 },
]

const statusData = [
  { name: "Active", value: 12 },
  { name: "Passed", value: 45 },
  { name: "Rejected", value: 18 },
  { name: "Executed", value: 32 },
]

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"]

export function AdminGovernanceAnalytics() {
  const [timeframe, setTimeframe] = useState("6m")

  return (
    <div className="space-y-6">
      <Card className="glass-card border-white/5">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-white">Governance Overview</CardTitle>
            <CardDescription className="text-white/70">Summary of governance activity and metrics</CardDescription>
          </div>
          <Button variant="outline" size="sm" className="border-white/10 bg-white/5">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={activityData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis dataKey="name" stroke="rgba(255,255,255,0.5)" />
                <YAxis stroke="rgba(255,255,255,0.5)" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "rgba(0, 0, 0, 0.8)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "4px",
                    color: "white",
                  }}
                />
                <Bar dataKey="proposals" name="Proposals" fill="#8884d8" />
                <Bar dataKey="votes" name="Votes" fill="#82ca9d" />
                <Bar dataKey="executions" name="Executions" fill="#ffc658" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Proposal Status Distribution</CardTitle>
            <CardDescription className="text-white/70">Breakdown of proposal statuses</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "rgba(0, 0, 0, 0.8)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "4px",
                      color: "white",
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Recent Activity</CardTitle>
            <CardDescription className="text-white/70">Latest governance actions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <ActivityItem
                title="Proposal Created"
                description="New proposal: Increase Liquidity Rewards"
                time="2 hours ago"
                user="0x7a...3f21"
              />
              <ActivityItem
                title="Proposal Executed"
                description="Executed: Update Fee Structure"
                time="5 hours ago"
                user="0x3b...9c45"
              />
              <ActivityItem
                title="Vote Cast"
                description="Vote: Against - Reduce Staking Requirements"
                time="1 day ago"
                user="0x5f...2d78"
              />
              <ActivityItem
                title="Treasury Transfer"
                description="Transfer: 5,000 DOGE to Development Fund"
                time="2 days ago"
                user="0x9d...4e12"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

function ActivityItem({ title, description, time, user }: { title: string; description: string; time: string; user: string }) {
  return (
    <div className="p-3 rounded-md border border-white/10 bg-white/5">
      <div className="flex justify-between">
        <div>
          <h4 className="text-sm font-medium text-white">{title}</h4>
          <p className="text-xs text-white/70 mt-1">{description}</p>
        </div>
        <div className="text-right">
          <p className="text-xs text-white/50">{time}</p>
          <p className="text-xs text-white/50 mt-1">{user}</p>
        </div>
      </div>
    </div>
  )
}
