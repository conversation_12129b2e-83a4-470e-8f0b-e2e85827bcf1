"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { AlertTriangle, Check, Shield, User } from "lucide-react"

interface MultiSignatureConfirmationProps {
  title: string
  description: string
  action: string
  details: Record<string, any>
  requiredSignatures: number
  onConfirm: () => Promise<void>
  onCancel: () => void
}

export function MultiSignatureConfirmation({
  title,
  description,
  action,
  details,
  requiredSignatures,
  onConfirm,
  onCancel,
}: MultiSignatureConfirmationProps) {
  const [signatures, setSignatures] = useState<string[]>([])
  const [currentSignature, setCurrentSignature] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState("")

  // Mock admin addresses for demo
  const adminAddresses = [
    { address: "0x1a2b3c4d5e6f7g8h9i0j", name: "Admin 1", avatar: "/avatar-doge.png" },
    { address: "0xabcdef1234567890abcd", name: "Admin 2", avatar: "/avatar-wizard.png" },
    { address: "0x9876543210fedcba9876", name: "Admin 3", avatar: "/avatar-developer.png" },
  ]

  const handleAddSignature = () => {
    if (!currentSignature.trim()) {
      setError("Please enter a valid signature")
      return
    }

    // Check if signature already exists
    if (signatures.includes(currentSignature)) {
      setError("This signature has already been added")
      return
    }

    // In a real app, we would verify the signature
    // For demo purposes, we'll just add it
    setSignatures([...signatures, currentSignature])
    setCurrentSignature("")
    setError("")
  }

  const handleConfirm = async () => {
    if (signatures.length < requiredSignatures) {
      setError(`At least ${requiredSignatures} signatures are required`)
      return
    }

    setIsSubmitting(true)
    setError("")

    try {
      await onConfirm()
    } catch (error) {
      setError("Failed to execute action. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="glass-card border-white/5 w-full max-w-lg">
      <CardHeader>
        <div className="flex items-center gap-3 mb-2">
          <Shield className="h-6 w-6 text-red-500" />
          <CardTitle className="text-white">{title}</CardTitle>
        </div>
        <CardDescription className="text-white/70">{description}</CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <Alert className="glass-card border-red-500/20 bg-red-500/5">
          <AlertTriangle className="h-5 w-5 text-red-500" />
          <AlertTitle className="text-white">Critical Action</AlertTitle>
          <AlertDescription className="text-white/70">
            This action requires {requiredSignatures} admin signatures to proceed.
          </AlertDescription>
        </Alert>

        <div className="p-4 rounded-md bg-white/5 border border-white/10">
          <h4 className="text-sm font-medium text-white mb-2">Action Details</h4>
          <pre className="text-xs text-white/70 overflow-auto p-2 bg-black/20 rounded-md">
            {JSON.stringify({ action, ...details }, null, 2)}
          </pre>
        </div>

        <div className="space-y-2">
          <Label htmlFor="signature" className="text-white">
            Admin Signature ({signatures.length}/{requiredSignatures})
          </Label>
          <div className="flex gap-2">
            <Input
              id="signature"
              value={currentSignature}
              onChange={(e) => setCurrentSignature(e.target.value)}
              placeholder="Enter admin signature"
              className="glass-input text-white"
            />
            <Button
              variant="outline"
              className="border-white/10 bg-white/5 text-white hover:bg-white/10"
              onClick={handleAddSignature}
            >
              Add
            </Button>
          </div>
          {error && <p className="text-sm text-red-500">{error}</p>}
        </div>

        {signatures.length > 0 && (
          <div className="space-y-2">
            <Label className="text-white">Current Signatures</Label>
            <div className="space-y-2">
              {signatures.map((sig, index) => {
                // Find matching admin for demo
                const admin = adminAddresses.find((a) => a.address.substring(0, 5) === sig.substring(0, 5))

                return (
                  <div key={index} className="flex items-center gap-2 p-2 rounded-md bg-white/5 border border-white/10">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={admin?.avatar || "/placeholder.svg"} alt={admin?.name || "Admin"} />
                      <AvatarFallback>
                        <User className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm text-white/90 font-mono">{sig}</span>
                    <Check className="h-4 w-4 text-green-500 ml-auto" />
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="flex justify-end gap-3">
        <Button
          variant="outline"
          className="border-white/10 bg-white/5 text-white hover:bg-white/10"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          className="bg-red-600 hover:bg-red-700 text-white"
          onClick={handleConfirm}
          disabled={signatures.length < requiredSignatures || isSubmitting}
        >
          {isSubmitting ? (
            <>
              <div className="h-4 w-4 rounded-full border-2 border-t-white border-white/10 animate-spin mr-2" />
              Processing...
            </>
          ) : (
            "Confirm Action"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
