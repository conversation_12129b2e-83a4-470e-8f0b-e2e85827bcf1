"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  Edit,
  MoreHorizontal,
  Search,
  Shield,
  Trash2,
  User,
  UserPlus,
  Users,
  AlertTriangle,
  Filter,
} from "lucide-react"
import { useUserManagement, type User as UserType, type UserRole, type UserStatus } from "@/hooks/use-user-management"
import { useNotification } from "@/hooks/use-notification"
import { Label } from "@/components/ui/label"

export function EnhancedUserManagement() {
  const {
    users,
    loading,
    selectedUsers,
    toggleUserSelection,
    selectAllUsers,
    clearSelection,
    updateUserStatus,
    updateUserRole,
    deleteUser,
    bulkUpdateStatus,
    bulkUpdateRole,
    bulkDeleteUsers,
  } = useUserManagement()

  const { showNotification } = useNotification()

  // State
  const [searchQuery, setSearchQuery] = useState("")
  const [roleFilter, setRoleFilter] = useState<UserRole | "all">("all")
  const [statusFilter, setStatusFilter] = useState<UserStatus | "all">("all")
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [userToDelete, setUserToDelete] = useState<UserType | null>(null)
  const [showBulkActionDialog, setShowBulkActionDialog] = useState(false)
  const [bulkAction, setBulkAction] = useState<"status" | "role" | "delete" | null>(null)
  const [bulkActionValue, setBulkActionValue] = useState<string>("")
  const [isProcessing, setIsProcessing] = useState(false)

  // Filter users
  const filteredUsers = users.filter((user) => {
    // Apply search filter
    if (
      searchQuery &&
      !user.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !user.email.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !user.address.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !user.id.toLowerCase().includes(searchQuery.toLowerCase())
    ) {
      return false
    }

    // Apply role filter
    if (roleFilter !== "all" && user.role !== roleFilter) {
      return false
    }

    // Apply status filter
    if (statusFilter !== "all" && user.status !== statusFilter) {
      return false
    }

    return true
  })

  // Check if all visible users are selected
  const allVisibleSelected = filteredUsers.length > 0 && filteredUsers.every((user) => selectedUsers.includes(user.id))

  // Handle select all checkbox
  const handleSelectAllChange = () => {
    if (allVisibleSelected) {
      // Unselect all visible users
      const visibleUserIds = filteredUsers.map((user) => user.id)
      const newSelection = selectedUsers.filter((id) => !visibleUserIds.includes(id))
      clearSelection()
    } else {
      // Select all visible users
      const visibleUserIds = filteredUsers.map((user) => user.id)
      const newSelection = [...new Set([...selectedUsers, ...visibleUserIds])]
      selectAllUsers()
    }
  }

  // Handle user status change
  const handleStatusChange = async (userId: string, newStatus: UserStatus) => {
    const success = await updateUserStatus(userId, newStatus)

    if (success) {
      showNotification({
        title: "User Updated",
        message: `User status has been updated to ${newStatus}`,
        type: "success",
      })
    } else {
      showNotification({
        title: "Update Failed",
        message: "Failed to update user status",
        type: "error",
      })
    }
  }

  // Handle user role change
  const handleRoleChange = async (userId: string, newRole: UserRole) => {
    const success = await updateUserRole(userId, newRole)

    if (success) {
      showNotification({
        title: "User Updated",
        message: `User role has been updated to ${newRole}`,
        type: "success",
      })
    } else {
      showNotification({
        title: "Update Failed",
        message: "Failed to update user role",
        type: "error",
      })
    }
  }

  // Handle user delete
  const handleDeleteUser = async () => {
    if (!userToDelete) return

    setIsProcessing(true)

    const success = await deleteUser(userToDelete.id)

    setIsProcessing(false)
    setShowDeleteConfirm(false)
    setUserToDelete(null)

    if (success) {
      showNotification({
        title: "User Deleted",
        message: `User ${userToDelete.name} has been deleted`,
        type: "success",
      })
    } else {
      showNotification({
        title: "Delete Failed",
        message: "Failed to delete user",
        type: "error",
      })
    }
  }

  // Handle bulk action
  const handleBulkAction = async () => {
    if (!bulkAction || selectedUsers.length === 0) return

    setIsProcessing(true)

    let success = false

    switch (bulkAction) {
      case "status":
        success = await bulkUpdateStatus(selectedUsers, bulkActionValue as UserStatus)
        break
      case "role":
        success = await bulkUpdateRole(selectedUsers, bulkActionValue as UserRole)
        break
      case "delete":
        success = await bulkDeleteUsers(selectedUsers)
        break
    }

    setIsProcessing(false)
    setShowBulkActionDialog(false)
    setBulkAction(null)
    setBulkActionValue("")

    if (success) {
      showNotification({
        title: "Bulk Action Completed",
        message: `Successfully processed ${selectedUsers.length} users`,
        type: "success",
      })
      clearSelection()
    } else {
      showNotification({
        title: "Bulk Action Failed",
        message: "Failed to process bulk action",
        type: "error",
      })
    }
  }

  // Get status badge color
  const getStatusBadgeColor = (status: UserStatus) => {
    switch (status) {
      case "active":
        return "bg-green-500/20 text-green-500"
      case "suspended":
        return "bg-yellow-500/20 text-yellow-500"
      case "pending":
        return "bg-blue-500/20 text-blue-500"
      case "banned":
        return "bg-red-500/20 text-red-500"
      default:
        return "bg-white/20 text-white"
    }
  }

  // Get role badge color
  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case "admin":
        return "bg-red-500/20 text-red-500"
      case "moderator":
        return "bg-blue-500/20 text-blue-500"
      case "developer":
        return "bg-purple-500/20 text-purple-500"
      case "user":
        return "bg-green-500/20 text-green-500"
      default:
        return "bg-white/20 text-white"
    }
  }

  return (
    <>
      <Card className="border-white/5 bg-black/20">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-lg text-white">Platform Users</CardTitle>
          <div className="flex items-center gap-2">
            {selectedUsers.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                className="border-white/10 bg-white/5 text-white"
                onClick={() => {
                  setShowBulkActionDialog(true)
                  setBulkAction("status")
                }}
              >
                <Shield className="mr-2 h-4 w-4" />
                Bulk Actions ({selectedUsers.length})
              </Button>
            )}
            <Button className="bg-doge text-black hover:bg-doge/90">
              <UserPlus className="mr-2 h-4 w-4" /> Add User
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/40" />
              <Input
                placeholder="Search users..."
                className="pl-10 border-white/20 bg-black/20 text-white"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select value={roleFilter} onValueChange={(value) => setRoleFilter(value as UserRole | "all")}>
                <SelectTrigger className="w-[150px] border-white/20 bg-black/20 text-white">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="moderator">Moderator</SelectItem>
                  <SelectItem value="developer">Developer</SelectItem>
                  <SelectItem value="user">User</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as UserStatus | "all")}>
                <SelectTrigger className="w-[150px] border-white/20 bg-black/20 text-white">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="banned">Banned</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center py-8">
              <div className="h-8 w-8 rounded-full border-4 border-t-doge border-white/10 animate-spin"></div>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8 text-white/70">No users found matching your filters.</div>
          ) : (
            <div className="rounded-md border border-white/10 overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow className="border-white/5 hover:bg-white/5">
                    <TableHead className="w-[50px] text-white">
                      <Checkbox
                        checked={allVisibleSelected}
                        onCheckedChange={handleSelectAllChange}
                        aria-label="Select all"
                      />
                    </TableHead>
                    <TableHead className="text-white">User</TableHead>
                    <TableHead className="text-white">Wallet Address</TableHead>
                    <TableHead className="text-white">Role</TableHead>
                    <TableHead className="text-white">Status</TableHead>
                    <TableHead className="text-white">Joined</TableHead>
                    <TableHead className="text-white text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map((user) => (
                    <TableRow key={user.id} className="border-white/5 hover:bg-white/5">
                      <TableCell>
                        <Checkbox
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={() => toggleUserSelection(user.id)}
                          aria-label={`Select ${user.name}`}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
                            <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-white">{user.name}</p>
                            <p className="text-xs text-white/60">{user.email}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm text-white">{user.address}</TableCell>
                      <TableCell>
                        <Badge className={getRoleBadgeColor(user.role)}>{user.role}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusBadgeColor(user.status)}>{user.status}</Badge>
                      </TableCell>
                      <TableCell className="text-white">{user.joined}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8 text-white/70">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem
                              onClick={() => {
                                /* View user profile */
                              }}
                            >
                              <User className="mr-2 h-4 w-4" />
                              View Profile
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => {
                                /* Edit user */
                              }}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit User
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuLabel>Change Role</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleRoleChange(user.id, "admin")}>
                              Make Admin
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleRoleChange(user.id, "moderator")}>
                              Make Moderator
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleRoleChange(user.id, "developer")}>
                              Make Developer
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleRoleChange(user.id, "user")}>
                              Make Regular User
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuLabel>Change Status</DropdownMenuLabel>
                            {user.status !== "active" && (
                              <DropdownMenuItem onClick={() => handleStatusChange(user.id, "active")}>
                                Activate User
                              </DropdownMenuItem>
                            )}
                            {user.status !== "suspended" && (
                              <DropdownMenuItem onClick={() => handleStatusChange(user.id, "suspended")}>
                                Suspend User
                              </DropdownMenuItem>
                            )}
                            {user.status !== "banned" && (
                              <DropdownMenuItem onClick={() => handleStatusChange(user.id, "banned")}>
                                Ban User
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => {
                                setUserToDelete(user)
                                setShowDeleteConfirm(true)
                              }}
                              className="text-red-500"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete User
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent className="glass-card border-white/5">
          <DialogHeader>
            <DialogTitle className="text-white">Confirm Deletion</DialogTitle>
            <DialogDescription className="text-white/70">
              Are you sure you want to delete this user? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {userToDelete && (
            <div className="flex items-center gap-3 p-4 rounded-md bg-white/5 border border-white/10">
              <Avatar>
                <AvatarImage src={userToDelete.avatar || "/placeholder.svg"} alt={userToDelete.name} />
                <AvatarFallback>{userToDelete.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium text-white">{userToDelete.name}</p>
                <p className="text-xs text-white/60">{userToDelete.email}</p>
              </div>
            </div>
          )}

          <Alert className="glass-card border-red-500/20 bg-red-500/5">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <AlertTitle className="text-white">Warning</AlertTitle>
            <AlertDescription className="text-white/70">
              Deleting a user will remove all their data and cannot be reversed.
            </AlertDescription>
          </Alert>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(false)}
              className="border-white/10 bg-white/5"
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteUser}
              className="bg-red-600 hover:bg-red-700 text-white"
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <div className="h-4 w-4 rounded-full border-2 border-t-white border-white/10 animate-spin mr-2" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete User
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Action Dialog */}
      <Dialog open={showBulkActionDialog} onOpenChange={setShowBulkActionDialog}>
        <DialogContent className="glass-card border-white/5">
          <DialogHeader>
            <DialogTitle className="text-white">Bulk Action</DialogTitle>
            <DialogDescription className="text-white/70">
              Apply changes to {selectedUsers.length} selected users
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-white">Action Type</Label>
              <Select value={bulkAction || ""} onValueChange={(value) => setBulkAction(value as any)}>
                <SelectTrigger className="border-white/20 bg-black/20 text-white">
                  <SelectValue placeholder="Select action" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="status">Change Status</SelectItem>
                  <SelectItem value="role">Change Role</SelectItem>
                  <SelectItem value="delete">Delete Users</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {bulkAction === "status" && (
              <div className="space-y-2">
                <Label className="text-white">New Status</Label>
                <Select value={bulkActionValue} onValueChange={setBulkActionValue}>
                  <SelectTrigger className="border-white/20 bg-black/20 text-white">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="banned">Banned</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {bulkAction === "role" && (
              <div className="space-y-2">
                <Label className="text-white">New Role</Label>
                <Select value={bulkActionValue} onValueChange={setBulkActionValue}>
                  <SelectTrigger className="border-white/20 bg-black/20 text-white">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="moderator">Moderator</SelectItem>
                    <SelectItem value="developer">Developer</SelectItem>
                    <SelectItem value="user">User</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {bulkAction === "delete" && (
              <Alert className="glass-card border-red-500/20 bg-red-500/5">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <AlertTitle className="text-white">Warning</AlertTitle>
                <AlertDescription className="text-white/70">
                  You are about to delete {selectedUsers.length} users. This action cannot be undone.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowBulkActionDialog(false)}
              className="border-white/10 bg-white/5"
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={handleBulkAction}
              className={
                bulkAction === "delete"
                  ? "bg-red-600 hover:bg-red-700 text-white"
                  : "bg-doge text-black hover:bg-doge/90"
              }
              disabled={isProcessing || !bulkAction || (bulkAction !== "delete" && !bulkActionValue)}
            >
              {isProcessing ? (
                <>
                  <div className="h-4 w-4 rounded-full border-2 border-t-white border-white/10 animate-spin mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  {bulkAction === "status" && <Shield className="mr-2 h-4 w-4" />}
                  {bulkAction === "role" && <Users className="mr-2 h-4 w-4" />}
                  {bulkAction === "delete" && <Trash2 className="mr-2 h-4 w-4" />}
                  Apply to {selectedUsers.length} Users
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
