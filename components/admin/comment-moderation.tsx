"use client"
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Check, Trash2, <PERSON><PERSON><PERSON><PERSON>gle, MessageSquare } from "lucide-react"

// Sample data
const comments = [
  {
    id: "comment-1",
    text: "This proposal makes a lot of sense. I fully support it!",
    author: "DogeWhale",
    authorAddress: "******************************************",
    authorAvatar: "/avatar-doge.png",
    timestamp: "2025-04-05T14:30:00Z",
    proposalId: "PROP-001",
    proposalTitle: "Implement Token Burning Mechanism",
    flagged: false,
    moderated: false,
  },
  {
    id: "comment-2",
    text: "I disagree with this proposal. It will negatively impact small holders.",
    author: "CryptoWizard",
    authorAddress: "******************************************",
    authorAvatar: "/avatar-wizard.png",
    timestamp: "2025-04-05T15:45:00Z",
    proposalId: "PROP-001",
    proposalTitle: "Implement Token Burning Mechanism",
    flagged: true,
    moderated: false,
  },
  {
    id: "comment-3",
    text: "Great initiative! This will help our ecosystem grow.",
    author: "BlockchainDev",
    authorAddress: "******************************************",
    authorAvatar: "/avatar-developer.png",
    timestamp: "2025-04-04T10:15:00Z",
    proposalId: "PROP-002",
    proposalTitle: "Increase Development Fund Allocation",
    flagged: false,
    moderated: true,
  },
]

export function AdminCommentModeration() {
  return (
    <Card className="glass-card border-white/5">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle className="text-white">Comment Moderation</CardTitle>
            <CardDescription className="text-white/70">
              Moderate and manage comments on governance proposals
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge className="bg-blue-500/20 text-blue-500">
              <MessageSquare className="mr-1 h-3 w-3" />
              {comments.length} Comments
            </Badge>
            {comments.filter((c) => c.flagged).length > 0 && (
              <Badge className="bg-red-500/20 text-red-500">
                <AlertTriangle className="mr-1 h-3 w-3" />
                {comments.filter((c) => c.flagged).length} Flagged
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow className="border-white/10 hover:bg-white/5">
              <TableHead className="text-white">Author</TableHead>
              <TableHead className="text-white">Comment</TableHead>
              <TableHead className="text-white">Proposal</TableHead>
              <TableHead className="text-white">Posted</TableHead>
              <TableHead className="text-white">Status</TableHead>
              <TableHead className="text-white">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {comments.map((comment) => (
              <TableRow
                key={comment.id}
                className={`border-white/10 hover:bg-white/5 ${comment.flagged ? "bg-red-500/5" : ""}`}
              >
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={comment.authorAvatar || "/placeholder.svg"} alt={comment.author} />
                      <AvatarFallback>{comment.author.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium text-white">{comment.author}</p>
                      <p className="text-xs text-white/60">{comment.authorAddress.substring(0, 8)}...</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="max-w-xs">
                    <p className="text-sm text-white truncate">{comment.text}</p>
                  </div>
                </TableCell>
                <TableCell className="text-white/70">
                  <span className="text-sm">{comment.proposalTitle}</span>
                  <p className="text-xs text-white/50">{comment.proposalId}</p>
                </TableCell>
                <TableCell className="text-white/70 whitespace-nowrap">
                  {new Date(comment.timestamp).toLocaleString()}
                </TableCell>
                <TableCell>
                  {comment.flagged ? (
                    <Badge className="bg-red-500/20 text-red-500">
                      <AlertTriangle className="mr-1 h-3 w-3" />
                      Flagged
                    </Badge>
                  ) : comment.moderated ? (
                    <Badge className="bg-green-500/20 text-green-500">
                      <Check className="mr-1 h-3 w-3" />
                      Approved
                    </Badge>
                  ) : (
                    <Badge className="bg-blue-500/20 text-blue-500">
                      <MessageSquare className="mr-1 h-3 w-3" />
                      Active
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    {comment.flagged && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-green-500/20 bg-green-500/5 text-green-500 hover:bg-green-500/10"
                      >
                        <Check className="h-4 w-4" />
                        <span className="sr-only">Approve</span>
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-red-500/20 bg-red-500/5 text-red-500 hover:bg-red-500/10"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Delete</span>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
