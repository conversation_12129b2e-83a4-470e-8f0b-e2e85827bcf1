"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  AlertTriangle, 
  XCircle, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  TrendingDown,
  Activity,
  Bug,
  Shield,
  Zap,
  RefreshCw
} from "lucide-react"
import { errorHandler, ErrorSeverity, ErrorCategory, type AppError } from "@/lib/error-handler"

interface ErrorStats {
  total: number
  unresolved: number
  bySeverity: Record<ErrorSeverity, number>
  byCategory: Record<string, number>
}

export function ErrorDashboard() {
  const [errors, setErrors] = useState<AppError[]>([])
  const [stats, setStats] = useState<ErrorStats | null>(null)
  const [selectedError, setSelectedError] = useState<AppError | null>(null)
  const [filter, setFilter] = useState<{
    severity?: ErrorSeverity
    category?: ErrorCategory
    resolved?: boolean
  }>({})

  useEffect(() => {
    loadErrors()
    
    // Set up real-time error listener
    const unsubscribe = errorHandler.addListener((error) => {
      setErrors(prev => [error, ...prev])
      loadStats()
    })

    return unsubscribe
  }, [])

  const loadErrors = () => {
    const allErrors = errorHandler.getErrors()
    setErrors(allErrors.sort((a, b) => b.context.timestamp - a.context.timestamp))
    loadStats()
  }

  const loadStats = () => {
    const errorStats = errorHandler.getStats()
    setStats(errorStats)
  }

  const filteredErrors = errors.filter(error => {
    if (filter.severity && error.severity !== filter.severity) return false
    if (filter.category && error.category !== filter.category) return false
    if (filter.resolved !== undefined && error.resolved !== filter.resolved) return false
    return true
  })

  const getSeverityColor = (severity: ErrorSeverity) => {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case ErrorSeverity.HIGH:
        return 'bg-orange-500/20 text-orange-400 border-orange-500/30'
      case ErrorSeverity.MEDIUM:
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case ErrorSeverity.LOW:
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getSeverityIcon = (severity: ErrorSeverity) => {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return <XCircle className="h-4 w-4" />
      case ErrorSeverity.HIGH:
        return <AlertTriangle className="h-4 w-4" />
      case ErrorSeverity.MEDIUM:
        return <Clock className="h-4 w-4" />
      case ErrorSeverity.LOW:
        return <Activity className="h-4 w-4" />
      default:
        return <Bug className="h-4 w-4" />
    }
  }

  const getCategoryIcon = (category: ErrorCategory) => {
    switch (category) {
      case ErrorCategory.SECURITY:
        return <Shield className="h-4 w-4" />
      case ErrorCategory.PERFORMANCE:
        return <Zap className="h-4 w-4" />
      case ErrorCategory.NETWORK:
        return <Activity className="h-4 w-4" />
      default:
        return <Bug className="h-4 w-4" />
    }
  }

  const resolveError = (errorId: string) => {
    const success = errorHandler.resolveError(errorId, 'admin')
    if (success) {
      loadErrors()
    }
  }

  const clearAllErrors = () => {
    errorHandler.clearErrors()
    setErrors([])
    setStats(null)
  }

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  if (!stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-6 w-6 animate-spin text-white/60" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Error Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Bug className="h-5 w-5 text-blue-400" />
              <div>
                <p className="text-sm text-white/60">Total Errors</p>
                <p className="text-xl font-bold text-white">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-400" />
              <div>
                <p className="text-sm text-white/60">Unresolved</p>
                <p className="text-xl font-bold text-white">{stats.unresolved}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-400" />
              <div>
                <p className="text-sm text-white/60">Critical</p>
                <p className="text-xl font-bold text-white">{stats.bySeverity[ErrorSeverity.CRITICAL] || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-400" />
              <div>
                <p className="text-sm text-white/60">Resolution Rate</p>
                <p className="text-xl font-bold text-white">
                  {stats.total > 0 ? Math.round(((stats.total - stats.unresolved) / stats.total) * 100) : 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="errors" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="errors">Error List</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="errors" className="space-y-4">
          <Card className="glass-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Error Log</CardTitle>
                  <CardDescription>
                    Recent errors and their status
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button onClick={loadErrors} variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                  <Button onClick={clearAllErrors} variant="outline" size="sm">
                    Clear All
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {filteredErrors.length === 0 ? (
                  <p className="text-white/60 text-center py-8">No errors found</p>
                ) : (
                  filteredErrors.slice(0, 50).map((error) => (
                    <div 
                      key={error.id} 
                      className="flex items-center gap-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
                      onClick={() => setSelectedError(error)}
                    >
                      <div className="flex items-center gap-2">
                        {getSeverityIcon(error.severity)}
                        {getCategoryIcon(error.category)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-white truncate">{error.message}</p>
                        <p className="text-sm text-white/60">
                          {formatTimestamp(error.context.timestamp)} • {error.category}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getSeverityColor(error.severity)}>
                          {error.severity}
                        </Badge>
                        {error.resolved ? (
                          <CheckCircle className="h-4 w-4 text-green-400" />
                        ) : (
                          <Button
                            onClick={(e) => {
                              e.stopPropagation()
                              resolveError(error.id)
                            }}
                            variant="outline"
                            size="sm"
                          >
                            Resolve
                          </Button>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle>Error Analytics</CardTitle>
              <CardDescription>
                Error patterns and trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-white mb-3">By Severity</h4>
                  <div className="space-y-2">
                    {Object.entries(stats.bySeverity).map(([severity, count]) => (
                      <div key={severity} className="flex items-center justify-between">
                        <span className="text-white/60 capitalize">{severity}</span>
                        <span className="text-white font-medium">{count}</span>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-3">By Category</h4>
                  <div className="space-y-2">
                    {Object.entries(stats.byCategory).map(([category, count]) => (
                      <div key={category} className="flex items-center justify-between">
                        <span className="text-white/60 capitalize">{category}</span>
                        <span className="text-white font-medium">{count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle>Error Tracking Settings</CardTitle>
              <CardDescription>
                Configure error handling and monitoring
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                  <div>
                    <p className="font-medium text-white">Real-time Monitoring</p>
                    <p className="text-sm text-white/60">Track errors as they occur</p>
                  </div>
                  <Badge className="bg-green-500/20 text-green-400">ENABLED</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                  <div>
                    <p className="font-medium text-white">Error Notifications</p>
                    <p className="text-sm text-white/60">Get notified of critical errors</p>
                  </div>
                  <Badge className="bg-green-500/20 text-green-400">ENABLED</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                  <div>
                    <p className="font-medium text-white">Performance Monitoring</p>
                    <p className="text-sm text-white/60">Track performance issues</p>
                  </div>
                  <Badge className="bg-green-500/20 text-green-400">ENABLED</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Error Detail Modal */}
      {selectedError && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="glass-card max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Error Details</CardTitle>
                <Button onClick={() => setSelectedError(null)} variant="outline" size="sm">
                  Close
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-white/60">Message</p>
                  <p className="text-white">{selectedError.message}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-white/60">Severity</p>
                    <Badge className={getSeverityColor(selectedError.severity)}>
                      {selectedError.severity}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-white/60">Category</p>
                    <p className="text-white capitalize">{selectedError.category}</p>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm text-white/60">Timestamp</p>
                  <p className="text-white">{formatTimestamp(selectedError.context.timestamp)}</p>
                </div>
                
                {selectedError.stack && (
                  <div>
                    <p className="text-sm text-white/60">Stack Trace</p>
                    <pre className="text-xs text-white/80 bg-black/20 p-3 rounded overflow-x-auto">
                      {selectedError.stack}
                    </pre>
                  </div>
                )}
                
                {selectedError.context.metadata && (
                  <div>
                    <p className="text-sm text-white/60">Context</p>
                    <pre className="text-xs text-white/80 bg-black/20 p-3 rounded overflow-x-auto">
                      {JSON.stringify(selectedError.context.metadata, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
