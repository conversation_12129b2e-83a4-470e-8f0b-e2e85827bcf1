"use client"

import { useState } from "react"
import { Heart, MessageSquare, Share2, MoreHorizontal, Search, Users, Trophy, Flag, AlertTriangle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useWallet } from "@/components/wallet-provider"
import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { useNotification } from "@/hooks/use-notification"
import { ReportPostDialog } from "./report-post-dialog"

type Post = {
  id: string
  author: {
    name: string
    handle: string
    avatar: string
  }
  content: string
  timestamp: string
  likes: number
  comments: number
  shares: number
  isLiked: boolean
  isReported?: boolean
  reportReason?: string
  token?: {
    name: string
    symbol: string
    price: number
    change24h: number
  }
}

const mockPosts: Post[] = [
  {
    id: "post1",
    author: {
      name: "DogeWhale",
      handle: "dogewhale",
      avatar: "/placeholder.svg?key=jg7nh",
    },
    content: "Just launched my new memecoin $ROCKET on PawPumps! To the moon! 🚀🌕",
    timestamp: "10 minutes ago",
    likes: 42,
    comments: 7,
    shares: 3,
    isLiked: false,
    token: {
      name: "RocketDoge",
      symbol: "ROCKET",
      price: 0.00042,
      change24h: 125.5,
    },
  },
  {
    id: "post2",
    author: {
      name: "MoonShot",
      handle: "moonshot",
      avatar: "/placeholder.svg?key=k0zw5",
    },
    content: "The new bonding curve mechanism on PawPumps is a game changer for fair launches. No more rug pulls! 💪",
    timestamp: "2 hours ago",
    likes: 128,
    comments: 24,
    shares: 18,
    isLiked: true,
  },
  {
    id: "post3",
    author: {
      name: "CryptoBarker",
      handle: "cryptobarker",
      avatar: "/placeholder.svg?key=htio3",
    },
    content:
      "Just provided liquidity to the PAW/wDOGE pool. Earning those sweet fees while supporting the ecosystem! 💰",
    timestamp: "5 hours ago",
    likes: 76,
    comments: 12,
    shares: 5,
    isLiked: false,
  },
  {
    id: "post4",
    author: {
      name: "PawPumps",
      handle: "pawpumps",
      avatar: "/placeholder.svg?key=tq1y5",
    },
    content:
      "We're excited to announce our new governance system is now live! Stake your $PAW tokens to vote on proposals and shape the future of the platform. #DogechainDeFi",
    timestamp: "1 day ago",
    likes: 215,
    comments: 43,
    shares: 87,
    isLiked: true,
  },
]

type Leaderboard = {
  id: string
  name: string
  avatar: string
  score: number
  rank: number
  change: number
}

const mockLeaderboard: Leaderboard[] = [
  {
    id: "user1",
    name: "DogeWhale",
    avatar: "/placeholder.svg?key=hk766",
    score: 12500,
    rank: 1,
    change: 0,
  },
  {
    id: "user2",
    name: "MoonShot",
    avatar: "/placeholder.svg?key=5s4nu",
    score: 10750,
    rank: 2,
    change: 1,
  },
  {
    id: "user3",
    name: "CryptoBarker",
    avatar: "/placeholder.svg?key=1yd2z",
    score: 9200,
    rank: 3,
    change: -1,
  },
  {
    id: "user4",
    name: "TokenMaster",
    avatar: "/placeholder.svg?key=2o150",
    score: 8100,
    rank: 4,
    change: 2,
  },
  {
    id: "user5",
    name: "DogeFan",
    avatar: "/placeholder.svg?key=bm5k2",
    score: 7300,
    rank: 5,
    change: 0,
  },
]

// Report reasons
const reportReasons = [
  "Spam",
  "Scam or fraud",
  "False information",
  "Hate speech",
  "Harassment",
  "Inappropriate content",
  "Intellectual property violation",
  "Other",
]

export function SocialInterface() {
  const { isConnected } = useWallet()
  const { showNotification } = useNotification()
  const [posts, setPosts] = useState<Post[]>(mockPosts)
  const [newPostContent, setNewPostContent] = useState("")
  const [reportDialogOpen, setReportDialogOpen] = useState(false)
  const [selectedPost, setSelectedPost] = useState<Post | null>(null)
  const [reportReason, setReportReason] = useState("")
  const [reportDetails, setReportDetails] = useState("")
  const [reportingPost, setReportingPost] = useState<{
    id: string
    content: string
    author: string
  } | null>(null)

  const handleLike = (postId: string) => {
    setPosts((prevPosts) =>
      prevPosts.map((post) => {
        if (post.id === postId) {
          return {
            ...post,
            likes: post.isLiked ? post.likes - 1 : post.likes + 1,
            isLiked: !post.isLiked,
          }
        }
        return post
      }),
    )
  }

  const handleNewPost = () => {
    if (!newPostContent.trim() || !isConnected) return

    const newPost: Post = {
      id: `post${Date.now()}`,
      author: {
        name: "You",
        handle: "you",
        avatar: "/placeholder.svg?key=6xblg",
      },
      content: newPostContent,
      timestamp: "Just now",
      likes: 0,
      comments: 0,
      shares: 0,
      isLiked: false,
    }

    setPosts([newPost, ...posts])
    setNewPostContent("")
  }

  const openReportDialog = (post: Post) => {
    setSelectedPost(post)
    setReportDialogOpen(true)
  }

  const handleReport = () => {
    if (!selectedPost || !reportReason) return

    // In a real app, this would send the report to a backend
    setPosts((prevPosts) =>
      prevPosts.map((post) => {
        if (post.id === selectedPost.id) {
          return {
            ...post,
            isReported: true,
            reportReason,
          }
        }
        return post
      }),
    )

    // Store report in localStorage for admin panel
    const reports = JSON.parse(localStorage.getItem("pawpumps_reports") || "[]")
    reports.push({
      id: `report_${Date.now()}`,
      postId: selectedPost.id,
      postContent: selectedPost.content,
      authorName: selectedPost.author.name,
      authorHandle: selectedPost.author.handle,
      reason: reportReason,
      details: reportDetails,
      timestamp: new Date().toISOString(),
      status: "pending", // pending, reviewed, resolved, dismissed
    })
    localStorage.setItem("pawpumps_reports", JSON.stringify(reports))

    // Close dialog and reset form
    setReportDialogOpen(false)
    setSelectedPost(null)
    setReportReason("")
    setReportDetails("")

    // Show notification
    showNotification({
      title: "Report Submitted",
      message: "Thank you for helping keep our community safe. Our team will review this report.",
      type: "success",
    })
  }

  const handleReportSubmit = (reason: string, details: string) => {
    console.log(`Reporting post ${reportingPost?.id} for ${reason}: ${details}`)
    // In a real app, this would call an API to submit the report
    showNotification({
      title: "Report submitted",
      message: "Thank you for helping keep our community safe.",
      type: "success",
    })
    setReportingPost(null)
  }

  return (
    <div className="grid gap-8 lg:grid-cols-3">
      <div className="lg:col-span-2 space-y-8">
        {/* Post creation card */}
        <Card className="glass-card border-white/5 doge-glow">
          <CardContent className="pt-6">
            {isConnected ? (
              <div className="space-y-4">
                <div className="flex gap-4">
                  <Avatar>
                    <AvatarImage src="/placeholder.svg?key=ofh48" alt="Your avatar" />
                    <AvatarFallback>YO</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <textarea
                      className="w-full glass-input border-white/10 focus:border-white/20 rounded-lg p-3 min-h-[100px] resize-none bg-transparent text-white"
                      placeholder="What's happening in the memecoin world?"
                      value={newPostContent}
                      onChange={(e) => setNewPostContent(e.target.value)}
                    ></textarea>
                  </div>
                </div>
                <div className="flex justify-end">
                  <Button onClick={handleNewPost} disabled={!newPostContent.trim()} className="doge-button doge-shine">
                    Post
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-6 text-center">
                <Users className="h-12 w-12 text-white/20 mb-4" />
                <p className="text-white/70 mb-4">Connect your wallet to join the conversation</p>
                <Button className="doge-button doge-shine">Connect Wallet</Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Feed */}
        <div className="space-y-4">
          {posts.map((post) => (
            <Card
              key={post.id}
              className={cn(
                "glass-card border-white/5 overflow-hidden transition-all duration-300 hover:border-white/10 liquid-glow",
                post.isReported && "opacity-70",
              )}
            >
              <CardContent className="pt-6">
                <div className="flex gap-4">
                  <Avatar>
                    <AvatarImage src={post.author.avatar || "/placeholder.svg"} alt={post.author.name} />
                    <AvatarFallback>{post.author.name.slice(0, 2)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-white">{post.author.name}</p>
                        <p className="text-sm text-white/60">
                          @{post.author.handle} · {post.timestamp}
                        </p>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="text-white/60 hover:text-white">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="glass-card border-white/10 bg-black/80">
                          <DropdownMenuItem
                            className="text-white/80 hover:text-white hover:bg-white/10 cursor-pointer"
                            onClick={() => {
                              navigator.clipboard.writeText(post.content)
                              showNotification({
                                title: "Copied",
                                message: "Post content copied to clipboard",
                                type: "success",
                              })
                            }}
                          >
                            Copy text
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-white/80 hover:text-white hover:bg-white/10 cursor-pointer">
                            Mute @{post.author.handle}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator className="bg-white/10" />
                          <DropdownMenuItem
                            className="text-red-400 hover:text-red-300 hover:bg-red-500/10 cursor-pointer"
                            onClick={() => openReportDialog(post)}
                            disabled={post.isReported}
                          >
                            {post.isReported ? "Reported" : "Report post"}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() =>
                              setReportingPost({
                                id: post.id,
                                content: post.content,
                                author: post.author.name,
                              })
                            }
                            className="text-red-500 focus:bg-red-500/10 focus:text-red-500"
                          >
                            <Flag className="mr-2 h-4 w-4" />
                            <span>Report Post</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <p className="my-3 text-white/90">{post.content}</p>
                    {post.isReported && (
                      <div className="mb-3 p-2 rounded-md bg-red-500/10 border border-red-500/20 flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-400" />
                        <span className="text-sm text-red-400">This post has been reported and is under review</span>
                      </div>
                    )}
                    {post.token && (
                      <div className="my-4 p-3 rounded-lg bg-white/5 border border-white/10">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center gap-2">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-doge/10 text-xs font-bold text-doge">
                              {post.token.symbol.slice(0, 2)}
                            </div>
                            <div>
                              <p className="font-medium text-white">{post.token.name}</p>
                              <p className="text-xs text-white/60">${post.token.symbol}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-white">${post.token.price.toFixed(6)}</p>
                            <p className={`text-xs ${post.token.change24h >= 0 ? "text-green-500" : "text-red-500"}`}>
                              {post.token.change24h >= 0 ? "+" : ""}
                              {post.token.change24h.toFixed(2)}%
                            </p>
                          </div>
                        </div>
                        <Button className="w-full mt-2 glass-button text-sm h-8">View Token</Button>
                      </div>
                    )}
                    <div className="flex items-center justify-between mt-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "flex items-center gap-1 text-white/60 hover:text-doge hover:bg-doge/10",
                          post.isLiked && "text-doge",
                        )}
                        onClick={() => handleLike(post.id)}
                      >
                        <Heart className="h-4 w-4" fill={post.isLiked ? "currentColor" : "none"} />
                        <span>{post.likes}</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center gap-1 text-white/60 hover:text-dogechain hover:bg-dogechain/10"
                      >
                        <MessageSquare className="h-4 w-4" />
                        <span>{post.comments}</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center gap-1 text-white/60 hover:text-doge hover:bg-doge/10"
                      >
                        <Share2 className="h-4 w-4" />
                        <span>{post.shares}</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "flex items-center gap-1 text-white/60 hover:text-red-400 hover:bg-red-500/10",
                          post.isReported && "text-red-400",
                        )}
                        onClick={() => openReportDialog(post)}
                        disabled={post.isReported}
                      >
                        <Flag className="h-4 w-4" />
                        <span className="sr-only">Report</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="space-y-8">
        {/* Search */}
        <Card className="glass-card border-white/5">
          <CardContent className="pt-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-white/60" />
              <Input placeholder="Search users and tokens" className="glass-input border-white/10 pl-10" />
            </div>
          </CardContent>
        </Card>

        {/* Leaderboard */}
        <Card className="glass-card border-white/5 dogechain-glow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white flex items-center gap-2">
                <Trophy className="h-5 w-5 text-dogechain" />
                Leaderboard
              </CardTitle>
              <Button variant="ghost" size="sm" className="text-white/60 hover:text-white">
                See All
              </Button>
            </div>
            <CardDescription className="text-white/70">Top contributors in the community</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockLeaderboard.map((user) => (
                <div key={user.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-white/10 text-xs font-bold text-white">
                      {user.rank}
                    </div>
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
                      <AvatarFallback>{user.name.slice(0, 2)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-white">{user.name}</p>
                      <p className="text-xs text-white/60">{user.score.toLocaleString()} points</p>
                    </div>
                  </div>
                  <div
                    className={cn(
                      "text-xs font-medium",
                      user.change > 0 ? "text-green-500" : user.change < 0 ? "text-red-500" : "text-white/60",
                    )}
                  >
                    {user.change > 0 ? `↑${user.change}` : user.change < 0 ? `↓${Math.abs(user.change)}` : "–"}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Trending Topics */}
        <Card className="glass-card border-white/5">
          <CardHeader>
            <CardTitle className="text-white">Trending Topics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors cursor-pointer">
                <p className="text-xs text-white/60">Trending in Memecoin</p>
                <p className="font-medium text-white">#DogeDay</p>
                <p className="text-xs text-white/60">1,234 posts</p>
              </div>
              <div className="p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors cursor-pointer">
                <p className="text-xs text-white/60">Trending in DeFi</p>
                <p className="font-medium text-white">#BondingCurves</p>
                <p className="text-xs text-white/60">892 posts</p>
              </div>
              <div className="p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors cursor-pointer">
                <p className="text-xs text-white/60">Trending in Trading</p>
                <p className="font-medium text-white">#PerpetualTrading</p>
                <p className="text-xs text-white/60">567 posts</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Report Dialog */}
      <Dialog open={reportDialogOpen} onOpenChange={setReportDialogOpen}>
        <DialogContent className="glass-card border-white/10 bg-black/90 text-white">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Flag className="h-5 w-5 text-red-400" />
              Report Post
            </DialogTitle>
            <DialogDescription className="text-white/70">
              Help us keep the community safe by reporting content that violates our guidelines.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-3 rounded-lg bg-white/5 border border-white/10">
              <p className="text-sm text-white/70 mb-1">Post by @{selectedPost?.author.handle}</p>
              <p className="text-white">{selectedPost?.content}</p>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-white">Reason for reporting</label>
              <div className="grid grid-cols-2 gap-2">
                {reportReasons.map((reason) => (
                  <Button
                    key={reason}
                    type="button"
                    variant="outline"
                    className={cn(
                      "border-white/10 bg-white/5 text-white hover:bg-white/10 justify-start",
                      reportReason === reason && "border-red-400 bg-red-500/10 text-red-400",
                    )}
                    onClick={() => setReportReason(reason)}
                  >
                    {reason}
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-white">Additional details (optional)</label>
              <Textarea
                placeholder="Please provide any additional context that will help us understand the issue"
                className="glass-input border-white/10 bg-transparent text-white"
                value={reportDetails}
                onChange={(e) => setReportDetails(e.target.value)}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setReportDialogOpen(false)}
              className="border-white/10 bg-white/5 text-white hover:bg-white/10"
            >
              Cancel
            </Button>
            <Button onClick={handleReport} disabled={!reportReason} className="bg-red-500 text-white hover:bg-red-600">
              Submit Report
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {reportingPost && (
        <ReportPostDialog
          postId={reportingPost.id}
          postContent={reportingPost.content}
          postAuthor={reportingPost.author}
          onClose={() => setReportingPost(null)}
          onSubmit={handleReportSubmit}
        />
      )}
    </div>
  )
}
