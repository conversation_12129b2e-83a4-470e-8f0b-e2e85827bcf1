"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { ArrowUp, ArrowDown, Search, ChevronLeft, ChevronRight } from "lucide-react"
import { useSorting, createSortableHeader, createSortableHeaderRight } from "@/hooks/use-sorting"

// Enhanced mock data for token performance with more tokens for pagination
const allTokens = [
  { id: "paw", name: "PawPumps", symbol: "PAW", price: 0.0042, change24h: 12.5, volume: 1250000, marketCap: 4200000 },
  { id: "dc", name: "<PERSON><PERSON><PERSON>hai<PERSON>", symbol: "DC", price: 0.0105, change24h: 3.2, volume: 3450000, marketCap: 10500000 },
  { id: "rdog<PERSON>", name: "<PERSON><PERSON><PERSON><PERSON>", symbol: "RDO<PERSON>", price: 0.00078, change24h: 28.4, volume: 2100000, marketCap: 780000 },
  { id: "dgk", name: "DogeKing", symbol: "DGK", price: 0.00023, change24h: -5.7, volume: 890000, marketCap: 230000 },
  { id: "moon", name: "MoonShot", symbol: "MOON", price: 0.00056, change24h: -2.1, volume: 560000, marketCap: 560000 },
  { id: "shibdoge", name: "ShibaDoge", symbol: "SHIBDOGE", price: 0.00012, change24h: 15.3, volume: 420000, marketCap: 120000 },
  { id: "dmoon", name: "DogeMoon", symbol: "DMOON", price: 0.00034, change24h: 7.8, volume: 340000, marketCap: 340000 },
  { id: "pawt", name: "PawToken", symbol: "PAWT", price: 0.00009, change24h: -8.2, volume: 290000, marketCap: 90000 },
  { id: "shib", name: "ShibaInu", symbol: "SHIB", price: 0.000015, change24h: 4.6, volume: 1800000, marketCap: 8900000 },
  { id: "floki", name: "Floki", symbol: "FLOKI", price: 0.000032, change24h: -1.8, volume: 750000, marketCap: 320000 },
  { id: "pepe", name: "Pepe", symbol: "PEPE", price: 0.0000012, change24h: 18.7, volume: 2200000, marketCap: 1200000 },
  { id: "bonk", name: "Bonk", symbol: "BONK", price: 0.0000089, change24h: -3.4, volume: 980000, marketCap: 890000 },
  { id: "wif", name: "DogWifHat", symbol: "WIF", price: 0.0234, change24h: 9.2, volume: 1560000, marketCap: 2340000 },
  { id: "meme", name: "Meme", symbol: "MEME", price: 0.0156, change24h: -7.1, volume: 670000, marketCap: 1560000 },
  { id: "doge2", name: "Doge2.0", symbol: "DOGE2", price: 0.00045, change24h: 22.3, volume: 1100000, marketCap: 450000 },
]

export function TokenPerformance() {
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)

  // Filter tokens based on search term
  const filteredTokens = allTokens.filter(token =>
    token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    token.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Calculate pagination
  const totalPages = Math.ceil(filteredTokens.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentTokens = filteredTokens.slice(startIndex, endIndex)

  // Use sorting hook for the tokens table
  const {
    sortedData,
    handleSort,
    getSortIcon
  } = useSorting({
    initialField: 'volume',
    initialDirection: 'desc',
    data: currentTokens
  })

  // Reset to first page when search changes
  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }

  const formatPrice = (price: number) => {
    return `$${price.toFixed(6)}`
  }

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `$${(volume / 1000000).toFixed(2)}M`
    }
    return `$${(volume / 1000).toFixed(1)}K`
  }

  const formatMarketCap = (marketCap: number) => {
    if (marketCap >= 1000000) {
      return `$${(marketCap / 1000000).toFixed(2)}M`
    }
    return `$${(marketCap / 1000).toFixed(1)}K`
  }

  const formatChange = (change: number) => {
    const isPositive = change >= 0
    return (
      <div className={`flex items-center justify-end gap-1 ${isPositive ? "text-green-500" : "text-red-500"}`}>
        {isPositive ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />}
        {Math.abs(change).toFixed(2)}%
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card className="glass-card border-white/5 liquid-glow">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white">Top Tokens</CardTitle>
              <CardDescription className="text-white/70">
                Performance metrics for all tokens ({filteredTokens.length} total)
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/40" />
                <Input
                  placeholder="Search tokens..."
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-10 w-64 glass-input"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-white/10">
                  {createSortableHeader<typeof currentTokens[0]>("Token", "name", getSortIcon, handleSort, "text-left", true)}
                  {createSortableHeaderRight<typeof currentTokens[0]>("Price", "price", getSortIcon, handleSort)}
                  {createSortableHeaderRight<typeof currentTokens[0]>("24h Change", "change24h", getSortIcon, handleSort)}
                  {createSortableHeaderRight<typeof currentTokens[0]>("Volume", "volume", getSortIcon, handleSort)}
                  {createSortableHeaderRight<typeof currentTokens[0]>("Market Cap", "marketCap", getSortIcon, handleSort)}
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedData.map((token, index) => (
                  <TableRow key={token.id} className="border-white/5 hover:bg-white/5 transition-colors duration-200">
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div
                          className={`flex h-8 w-8 items-center justify-center rounded-full ${
                            index % 2 === 0 ? "bg-doge/10 text-doge" : "bg-dogechain/10 text-dogechain"
                          } text-xs font-bold`}
                        >
                          {token.symbol.slice(0, 2)}
                        </div>
                        <div>
                          <div className="font-medium text-white">{token.name}</div>
                          <div className="text-xs text-white/60">{token.symbol}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-right font-medium text-white">
                      {formatPrice(token.price)}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatChange(token.change24h)}
                    </TableCell>
                    <TableCell className="text-right text-white/60">
                      {formatVolume(token.volume)}
                    </TableCell>
                    <TableCell className="text-right text-white/60">
                      {formatMarketCap(token.marketCap)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4 pt-4 border-t border-white/10">
              <div className="text-sm text-white/60">
                Showing {startIndex + 1}-{Math.min(endIndex, filteredTokens.length)} of {filteredTokens.length} tokens
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="glass-button"
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = i + 1
                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(pageNum)}
                        className={currentPage === pageNum ? "cosmic-button" : "glass-button"}
                      >
                        {pageNum}
                      </Button>
                    )
                  })}
                  {totalPages > 5 && (
                    <>
                      <span className="text-white/40">...</span>
                      <Button
                        variant={currentPage === totalPages ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(totalPages)}
                        className={currentPage === totalPages ? "cosmic-button" : "glass-button"}
                      >
                        {totalPages}
                      </Button>
                    </>
                  )}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="glass-button"
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
