"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Award, Check, Clock, Crown, FileText, Gift, Lock, Star, TrendingUp, Users, Vote } from "lucide-react"

// Mock data for badges
const BADGES = [
  {
    id: 1,
    name: "First Vote",
    description: "Cast your first governance vote",
    icon: <Vote className="h-8 w-8" />,
    earned: true,
    date: "Jan 15, 2023",
    category: "voting",
  },
  {
    id: 2,
    name: "Proposal Creator",
    description: "Submit your first governance proposal",
    icon: <FileText className="h-8 w-8" />,
    earned: true,
    date: "Feb 3, 2023",
    category: "proposals",
  },
  {
    id: 3,
    name: "Consistent Voter",
    description: "Vote in 10 consecutive proposals",
    icon: <Check className="h-8 w-8" />,
    earned: false,
    progress: 70,
    category: "voting",
  },
  {
    id: 4,
    name: "Community Pillar",
    description: "Participate in governance for 6 months",
    icon: <Users className="h-8 w-8" />,
    earned: false,
    progress: 50,
    category: "community",
  },
  {
    id: 5,
    name: "Treasury Guardian",
    description: "Vote on 5 treasury proposals",
    icon: <Gift className="h-8 w-8" />,
    earned: true,
    date: "Mar 22, 2023",
    category: "treasury",
  },
  {
    id: 6,
    name: "Governance Elite",
    description: "Reach Level 5 in the reputation system",
    icon: <Crown className="h-8 w-8" />,
    earned: false,
    progress: 30,
    category: "reputation",
  },
  {
    id: 7,
    name: "Early Adopter",
    description: "Join governance in the first month",
    icon: <Clock className="h-8 w-8" />,
    earned: true,
    date: "Dec 5, 2022",
    category: "community",
  },
  {
    id: 8,
    name: "Influencer",
    description: "Have a proposal pass with 80%+ approval",
    icon: <TrendingUp className="h-8 w-8" />,
    earned: false,
    progress: 0,
    category: "proposals",
  },
]

// Achievement levels
const ACHIEVEMENT_LEVELS = [
  { level: "Bronze", color: "bg-amber-600", textColor: "text-amber-600" },
  { level: "Silver", color: "bg-slate-400", textColor: "text-slate-400" },
  { level: "Gold", color: "bg-yellow-400", textColor: "text-yellow-400" },
  { level: "Platinum", color: "bg-emerald-400", textColor: "text-emerald-400" },
]

interface BadgesAchievementsProps {
  userId?: string
}

export function BadgesAchievements({ userId = "user-1" }: BadgesAchievementsProps) {
  const [activeTab, setActiveTab] = useState("all")

  // Filter badges based on active tab
  const filteredBadges =
    activeTab === "all"
      ? BADGES
      : activeTab === "earned"
        ? BADGES.filter((badge) => badge.earned)
        : BADGES.filter((badge) => !badge.earned)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Award className="mr-2 h-5 w-5 text-primary" />
          Badges & Achievements
        </CardTitle>
        <CardDescription>Earn badges by participating in governance activities</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3 mb-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="earned">Earned</TabsTrigger>
            <TabsTrigger value="locked">In Progress</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {filteredBadges.map((badge) => (
                <BadgeCard key={badge.id} badge={badge} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="earned" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {filteredBadges.map((badge) => (
                <BadgeCard key={badge.id} badge={badge} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="locked" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {filteredBadges.map((badge) => (
                <BadgeCard key={badge.id} badge={badge} />
              ))}
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            <Star className="mr-2 h-5 w-5 text-yellow-400" fill="currentColor" />
            Achievement Levels
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {ACHIEVEMENT_LEVELS.map((level, index) => (
              <Card key={level.level} className="border">
                <CardContent className="p-4 flex flex-col items-center">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${level.color} mb-2`}>
                    <Award className="h-6 w-6 text-white" />
                  </div>
                  <h4 className={`font-semibold ${level.textColor}`}>{level.level}</h4>
                  <p className="text-xs text-center text-muted-foreground mt-1">
                    {index === 0
                      ? "Earn 3 badges"
                      : index === 1
                        ? "Earn 7 badges"
                        : index === 2
                          ? "Earn 15 badges"
                          : "Earn 25 badges"}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface BadgeCardProps {
  badge: any
}

function BadgeCard({ badge }: BadgeCardProps) {
  return (
    <Card className={`border ${!badge.earned ? "opacity-70" : ""}`}>
      <CardContent className="p-4 flex flex-col items-center">
        <div className="relative">
          <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-2">
            {badge.earned ? (
              badge.icon
            ) : (
              <>
                {badge.icon}
                <div className="absolute -bottom-1 -right-1 bg-background rounded-full p-1 border">
                  <Lock className="h-3 w-3" />
                </div>
              </>
            )}
          </div>
        </div>

        <h4 className="font-semibold text-center">{badge.name}</h4>
        <p className="text-xs text-center text-muted-foreground mt-1">{badge.description}</p>

        {badge.earned ? (
          <Badge variant="outline" className="mt-2">
            Earned {badge.date}
          </Badge>
        ) : (
          <div className="w-full mt-2">
            <div className="flex justify-between text-xs mb-1">
              <span>Progress</span>
              <span>{badge.progress}%</span>
            </div>
            <Progress value={badge.progress} className="h-1.5" />
          </div>
        )}
      </CardContent>
    </Card>
  )
}
