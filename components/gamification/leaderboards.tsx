"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Avatar } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Award, Calendar, ChevronUp, Crown, Medal, Star, Users, ChevronDown, Minus } from "lucide-react"

// Mock data for leaderboards
const MOCK_LEADERBOARD_DATA = {
  reputation: [
    { id: 1, name: "<PERSON><PERSON><PERSON>hisper<PERSON>", avatar: "/avatar-doge.png", points: 4250, change: 2 },
    { id: 2, name: "CryptoWizard", avatar: "/avatar-wizard.png", points: 3980, change: -1 },
    { id: 3, name: "BlockchainDev", avatar: "/avatar-developer.png", points: 3720, change: 1 },
    { id: 4, name: "TokenEnthusiast", avatar: "/avatar-enthusiast.png", points: 3450, change: -2 },
    { id: 5, name: "DeFiExplorer", avatar: "/generic-character-exchange.png", points: 3210, change: 0 },
    { id: 6, name: "ShibaFan", avatar: "/avatar-doge.png", points: 2980, change: 3 },
    { id: 7, name: "CoinCollector", avatar: "/avatar-enthusiast.png", points: 2760, change: 0 },
    { id: 8, name: "NodeRunner", avatar: "/avatar-developer.png", points: 2540, change: -1 },
    { id: 9, name: "MemeCreator", avatar: "/avatar-doge.png", points: 2320, change: 1 },
    { id: 10, name: "StakingPro", avatar: "/avatar-wizard.png", points: 2150, change: 2 },
  ],
  proposals: [
    { id: 1, name: "BlockchainDev", avatar: "/avatar-developer.png", count: 24, approved: 19 },
    { id: 2, name: "CryptoWizard", avatar: "/avatar-wizard.png", count: 21, approved: 15 },
    { id: 3, name: "DogeWhisperer", avatar: "/avatar-doge.png", count: 18, approved: 14 },
    { id: 4, name: "DeFiExplorer", avatar: "/generic-character-exchange.png", count: 15, approved: 11 },
    { id: 5, name: "TokenEnthusiast", avatar: "/avatar-enthusiast.png", count: 12, approved: 9 },
    { id: 6, name: "NodeRunner", avatar: "/avatar-developer.png", count: 10, approved: 7 },
    { id: 7, name: "ShibaFan", avatar: "/avatar-doge.png", count: 8, approved: 6 },
    { id: 8, name: "MemeCreator", avatar: "/avatar-doge.png", count: 7, approved: 4 },
    { id: 9, name: "CoinCollector", avatar: "/avatar-enthusiast.png", count: 5, approved: 3 },
    { id: 10, name: "StakingPro", avatar: "/avatar-wizard.png", count: 4, approved: 3 },
  ],
  voting: [
    { id: 1, name: "DogeWhisperer", avatar: "/avatar-doge.png", votes: 187, streak: 42 },
    { id: 2, name: "TokenEnthusiast", avatar: "/avatar-enthusiast.png", votes: 175, streak: 38 },
    { id: 3, name: "CryptoWizard", avatar: "/avatar-wizard.png", votes: 164, streak: 35 },
    { id: 4, name: "ShibaFan", avatar: "/avatar-doge.png", votes: 152, streak: 29 },
    { id: 5, name: "BlockchainDev", avatar: "/avatar-developer.png", votes: 143, streak: 27 },
    { id: 6, name: "DeFiExplorer", avatar: "/generic-character-exchange.png", votes: 132, streak: 24 },
    { id: 7, name: "CoinCollector", avatar: "/avatar-enthusiast.png", votes: 121, streak: 21 },
    { id: 8, name: "NodeRunner", avatar: "/avatar-developer.png", votes: 110, streak: 18 },
    { id: 9, name: "MemeCreator", avatar: "/avatar-doge.png", votes: 98, streak: 15 },
    { id: 10, name: "StakingPro", avatar: "/avatar-wizard.png", votes: 87, streak: 12 },
  ],
}

interface LeaderboardsProps {
  userId?: string
}

export function Leaderboards({ userId = "user-1" }: LeaderboardsProps) {
  const [timeframe, setTimeframe] = useState("all-time")

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Trophy className="mr-2 h-5 w-5 text-yellow-400" />
            Governance Leaderboards
          </CardTitle>
          <Select defaultValue={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Select timeframe" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-time">All Time</SelectItem>
              <SelectItem value="this-month">This Month</SelectItem>
              <SelectItem value="this-week">This Week</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <CardDescription>Top contributors in the PawPumps governance ecosystem</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="reputation" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-4">
            <TabsTrigger value="reputation" className="flex items-center">
              <Star className="mr-1 h-4 w-4" />
              Reputation
            </TabsTrigger>
            <TabsTrigger value="proposals" className="flex items-center">
              <FileText className="mr-1 h-4 w-4" />
              Proposals
            </TabsTrigger>
            <TabsTrigger value="voting" className="flex items-center">
              <Users className="mr-1 h-4 w-4" />
              Voting
            </TabsTrigger>
          </TabsList>

          <TabsContent value="reputation" className="mt-0">
            <LeaderboardTable
              data={MOCK_LEADERBOARD_DATA.reputation}
              columns={[
                { header: "Rank", key: "rank" },
                { header: "User", key: "user" },
                { header: "Reputation", key: "points" },
                { header: "Change", key: "change" },
              ]}
              type="reputation"
            />
          </TabsContent>

          <TabsContent value="proposals" className="mt-0">
            <LeaderboardTable
              data={MOCK_LEADERBOARD_DATA.proposals}
              columns={[
                { header: "Rank", key: "rank" },
                { header: "User", key: "user" },
                { header: "Proposals", key: "count" },
                { header: "Approved", key: "approved" },
              ]}
              type="proposals"
            />
          </TabsContent>

          <TabsContent value="voting" className="mt-0">
            <LeaderboardTable
              data={MOCK_LEADERBOARD_DATA.voting}
              columns={[
                { header: "Rank", key: "rank" },
                { header: "User", key: "user" },
                { header: "Votes", key: "votes" },
                { header: "Streak", key: "streak" },
              ]}
              type="voting"
            />
          </TabsContent>
        </Tabs>

        <div className="flex justify-center mt-4">
          <Button variant="outline" className="text-sm">
            View Full Leaderboard
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

interface LeaderboardTableProps {
  data: any[]
  columns: { header: string; key: string }[]
  type: "reputation" | "proposals" | "voting"
}

function LeaderboardTable({ data, columns, type }: LeaderboardTableProps) {
  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            {columns.map((column) => (
              <th key={column.key} className="text-left py-2 px-2 font-medium text-sm">
                {column.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => (
            <tr key={item.id} className={`border-b ${index < 3 ? "bg-primary/5" : ""}`}>
              <td className="py-3 px-2">
                {index === 0 ? (
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-yellow-100">
                    <Crown className="h-4 w-4 text-yellow-600" fill="currentColor" />
                  </div>
                ) : index === 1 ? (
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-slate-100">
                    <Medal className="h-4 w-4 text-slate-600" />
                  </div>
                ) : index === 2 ? (
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-amber-100">
                    <Award className="h-4 w-4 text-amber-600" />
                  </div>
                ) : (
                  <div className="flex items-center justify-center w-8 h-8">{index + 1}</div>
                )}
              </td>
              <td className="py-3 px-2">
                <div className="flex items-center">
                  <Avatar className="h-8 w-8 mr-2">
                    <img src={item.avatar || "/placeholder.svg"} alt={item.name} />
                  </Avatar>
                  <span className="font-medium">{item.name}</span>
                </div>
              </td>
              <td className="py-3 px-2">
                {type === "reputation" && <Badge variant="secondary">{item.points.toLocaleString()} pts</Badge>}
                {type === "proposals" && <span>{item.count}</span>}
                {type === "voting" && <span>{item.votes}</span>}
              </td>
              <td className="py-3 px-2">
                {type === "reputation" && (
                  <div
                    className={`flex items-center ${item.change > 0 ? "text-green-500" : item.change < 0 ? "text-red-500" : "text-gray-500"}`}
                  >
                    {item.change > 0 && <ChevronUp className="h-4 w-4 mr-1" />}
                    {item.change < 0 && <ChevronDown className="h-4 w-4 mr-1" />}
                    {item.change === 0 && <Minus className="h-4 w-4 mr-1" />}
                    {Math.abs(item.change)}
                  </div>
                )}
                {type === "proposals" && (
                  <div className="flex items-center">
                    <span className="text-green-500 mr-1">{item.approved}</span>
                    <span className="text-muted-foreground text-xs">
                      ({Math.round((item.approved / item.count) * 100)}%)
                    </span>
                  </div>
                )}
                {type === "voting" && (
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1 text-primary" />
                    <span>{item.streak} days</span>
                  </div>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function Trophy(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6" />
      <path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18" />
      <path d="M4 22h16" />
      <path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22" />
      <path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22" />
      <path d="M18 2H6v7a6 6 0 0 0 12 0V2Z" />
    </svg>
  )
}

function FileText(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8Z" />
      <polyline points="14 2 14 8 20 8" />
      <line x1="16" x2="8" y1="13" y2="13" />
      <line x1="16" x2="8" y1="17" y2="17" />
      <path d="M10 9H8" />
    </svg>
  )
}
