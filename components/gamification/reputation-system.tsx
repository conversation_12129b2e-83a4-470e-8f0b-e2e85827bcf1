"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Avatar } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { ArrowUp, Award, BarChart2, Check, Clock, Star, ThumbsUp, Users } from "lucide-react"

// Mock data for reputation levels
const REPUTATION_LEVELS = [
  { level: 1, name: "Newcomer", threshold: 0, color: "bg-slate-200" },
  { level: 2, name: "Contributor", threshold: 100, color: "bg-blue-200" },
  { level: 3, name: "Active Voter", threshold: 250, color: "bg-green-200" },
  { level: 4, name: "Proposal Creator", threshold: 500, color: "bg-yellow-200" },
  { level: 5, name: "Governance Expert", threshold: 1000, color: "bg-purple-200" },
  { level: 6, name: "Community Leader", threshold: 2000, color: "bg-pink-200" },
  { level: 7, name: "Governance Elite", threshold: 5000, color: "bg-red-200" },
]

// Mock data for reputation activities
const REPUTATION_ACTIVITIES = [
  { id: 1, action: "Voted on proposal", points: 10, icon: <ThumbsUp size={16} /> },
  { id: 2, action: "Created proposal", points: 50, icon: <BarChart2 size={16} /> },
  { id: 3, action: "Proposal approved", points: 100, icon: <Check size={16} /> },
  { id: 4, action: "Daily participation", points: 5, icon: <Clock size={16} /> },
  { id: 5, action: "Referred new member", points: 25, icon: <Users size={16} /> },
]

interface ReputationSystemProps {
  userId?: string
}

export function ReputationSystem({ userId = "user-1" }: ReputationSystemProps) {
  const [userReputation, setUserReputation] = useState({
    points: 320,
    level: 3,
    rank: 42,
    totalUsers: 156,
    recentActivities: [
      { id: 1, action: "Voted on proposal #127", points: 10, timestamp: "2 hours ago" },
      { id: 2, action: "Daily participation bonus", points: 5, timestamp: "1 day ago" },
      { id: 3, action: "Created proposal #124", points: 50, timestamp: "3 days ago" },
    ],
  })

  // Find current level and next level
  const currentLevel = REPUTATION_LEVELS.find((level) => level.level === userReputation.level)
  const nextLevel = REPUTATION_LEVELS.find((level) => level.level === userReputation.level + 1)

  // Calculate progress to next level
  const currentThreshold = currentLevel?.threshold || 0
  const nextThreshold = nextLevel?.threshold || currentThreshold * 2
  const pointsToNextLevel = nextThreshold - currentThreshold
  const pointsEarnedInLevel = userReputation.points - currentThreshold
  const progressPercentage = Math.min(Math.round((pointsEarnedInLevel / pointsToNextLevel) * 100), 100)

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>Reputation Profile</CardTitle>
            <Badge variant="outline" className="ml-2">
              Rank #{userReputation.rank}/{userReputation.totalUsers}
            </Badge>
          </div>
          <CardDescription>Track your governance participation and reputation</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-4">
            <Avatar className="h-16 w-16 mr-4 border-2 border-primary">
              <img src="/avatar-doge.png" alt="User avatar" />
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center">
                  <h3 className="text-lg font-semibold">
                    Level {userReputation.level}: {currentLevel?.name}
                  </h3>
                  <Badge className={`ml-2 ${currentLevel?.color} text-black`}>{userReputation.points} pts</Badge>
                </div>
                <Star className="text-yellow-400" fill="currentColor" />
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Progress to Level {userReputation.level + 1}</span>
                  <span>
                    {pointsEarnedInLevel}/{pointsToNextLevel} points
                  </span>
                </div>
                <Progress value={progressPercentage} className="h-2" />
              </div>
            </div>
          </div>

          <div className="mt-6">
            <h4 className="font-medium mb-2 flex items-center">
              <Award className="mr-2 h-4 w-4" />
              Ways to Earn Reputation
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {REPUTATION_ACTIVITIES.map((activity) => (
                <div key={activity.id} className="flex items-center p-2 rounded-md border">
                  <div className="mr-3 bg-primary/10 p-2 rounded-full">{activity.icon}</div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{activity.action}</p>
                  </div>
                  <Badge variant="secondary">+{activity.points} pts</Badge>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
        <CardFooter className="border-t pt-4">
          <div className="w-full">
            <h4 className="font-medium mb-2 text-sm">Recent Activity</h4>
            <div className="space-y-2">
              {userReputation.recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <ArrowUp className="mr-2 h-3 w-3 text-green-500" />
                    <span>{activity.action}</span>
                  </div>
                  <div className="flex items-center">
                    <Badge variant="outline" className="mr-2">
                      +{activity.points}
                    </Badge>
                    <span className="text-muted-foreground text-xs">{activity.timestamp}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
