"use client"

import { useEffect, useRef } from "react"

type Particle = {
  x: number
  y: number
  size: number
  speedX: number
  speedY: number
  opacity: number
  color: string
}

type ShootingStar = {
  x: number
  y: number
  length: number
  speed: number
  angle: number
  opacity: number
  active: boolean
  trail: { x: number; y: number }[]
  trailLength: number
  lifetime: number
  age: number
}

type Rocket = {
  x: number
  y: number
  size: number
  speed: number
  rotation: number
  active: boolean
  trail: { x: number; y: number }[]
  trailLength: number
}

type MoonTheme = "yellow" | "grey"

export function AnimatedParticles({ moonTheme = "yellow" }: { moonTheme?: MoonTheme }) {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Detect Arc Browser
    const isArcBrowser = navigator.userAgent.includes('Arc') ||
                        navigator.userAgent.includes('Company') ||
                        (window as Window & { arc?: unknown }).arc !== undefined

    // Set canvas dimensions
    const setDimensions = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }

    setDimensions()
    window.addEventListener("resize", setDimensions)

    // Create particles
    const particles: Particle[] = []
    const particleCount = Math.min(window.innerWidth / 10, 100) // Responsive particle count

    const colors = [
      "rgba(255, 193, 7, 0.5)", // Dogecoin yellow
      "rgba(138, 43, 226, 0.5)", // Dogechain purple
      "rgba(255, 255, 255, 0.3)", // White
    ]

    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        size: Math.random() * 3 + 1,
        speedX: (Math.random() - 0.5) * 0.5,
        speedY: (Math.random() - 0.5) * 0.5,
        opacity: Math.random() * 0.5 + 0.1,
        color: colors[Math.floor(Math.random() * colors.length)],
      })
    }

    // Create shooting stars
    const shootingStars: ShootingStar[] = []
    const maxShootingStars = 3 // Maximum number of active shooting stars

    // Create rocket
    const rocket: Rocket = {
      x: -100, // Start off-screen
      y: canvas.height * 0.7,
      size: 30,
      speed: 1.5 * 0.75, // Reduced by 25%
      rotation: 75, // Changed from 30 to 75 (45 degrees more clockwise)
      active: false,
      trail: [],
      trailLength: 20,
    }

    // Moon properties
    const moon = {
      x: canvas.width * 0.85,
      y: canvas.height * 0.2,
      radius: Math.min(canvas.width, canvas.height) * 0.08,
      glow: Math.min(canvas.width, canvas.height) * 0.12,
    }

    // Moon colors based on theme
    const moonColors = {
      yellow: {
        surface: "rgba(255, 223, 127, 0.9)",
        glow: {
          inner: "rgba(255, 223, 127, 0.8)",
          middle: "rgba(255, 193, 7, 0.3)",
          outer: "rgba(255, 193, 7, 0)",
        },
        crater: "rgba(204, 163, 0, 0.8)", // Darker crater color for better visibility
        craterShadow: "rgba(153, 122, 0, 0.9)", // Even darker shadow for depth
      },
      grey: {
        surface: "rgba(255, 255, 255, 0.9)",
        glow: {
          inner: "rgba(255, 255, 255, 0.8)",
          middle: "rgba(255, 255, 255, 0.3)",
          outer: "rgba(255, 255, 255, 0)",
        },
        crater: "rgba(210, 210, 210, 0.8)",
        craterShadow: "rgba(180, 180, 180, 0.9)",
      },
    }

    const currentMoonTheme = moonColors[moonTheme]

    // Function to create a new shooting star
    function createShootingStar() {
      // Only create a new star if we're below the maximum and canvas exists
      if (!canvas || shootingStars.filter((star) => star.active).length >= maxShootingStars) {
        return
      }

      // Random angle between -30 and -60 degrees (downward trajectory)
      const angle = (Math.random() * 30 + 30) * (Math.random() > 0.5 ? -1 : 1)
      const radians = (angle * Math.PI) / 180

      shootingStars.push({
        x: Math.random() * canvas.width,
        y: Math.random() * (canvas.height * 0.5), // Start in top half
        length: Math.random() * 50 + 30, // Length between 30-80px
        speed: (Math.random() * 3 + 2) * 0.75, // Speed reduced by 25%
        angle: radians,
        opacity: Math.random() * 0.5 + 0.5, // Opacity between 0.5-1
        active: true,
        trail: [],
        trailLength: Math.floor(Math.random() * 10 + 15), // Trail length between 15-25 points
        lifetime: Math.random() * 100 + 100, // Lifetime between 100-200 frames
        age: 0,
      })
    }

    // Function to draw a rocket
    function drawRocket(x: number, y: number, size: number, rotation: number) {
      if (!ctx) return
      ctx.save()
      ctx.translate(x, y)
      ctx.rotate((rotation * Math.PI) / 180)

      // Rocket body
      ctx.beginPath()
      ctx.moveTo(0, -size * 0.5) // Nose
      ctx.lineTo(size * 0.25, size * 0.3) // Right side
      ctx.lineTo(0, size * 0.2) // Bottom indent
      ctx.lineTo(-size * 0.25, size * 0.3) // Left side
      ctx.closePath()
      ctx.fillStyle = "rgba(255, 255, 255, 0.9)"
      ctx.fill()

      // Window
      ctx.beginPath()
      ctx.arc(0, -size * 0.1, size * 0.1, 0, Math.PI * 2)
      ctx.fillStyle = "rgba(100, 200, 255, 0.8)"
      ctx.fill()

      // Fins
      ctx.beginPath()
      ctx.moveTo(0, size * 0.1)
      ctx.lineTo(size * 0.4, size * 0.5)
      ctx.lineTo(size * 0.2, size * 0.5)
      ctx.closePath()
      ctx.fillStyle = "rgba(255, 193, 7, 0.9)" // Yellow fin
      ctx.fill()

      ctx.beginPath()
      ctx.moveTo(0, size * 0.1)
      ctx.lineTo(-size * 0.4, size * 0.5)
      ctx.lineTo(-size * 0.2, size * 0.5)
      ctx.closePath()
      ctx.fillStyle = "rgba(255, 193, 7, 0.9)" // Yellow fin
      ctx.fill()

      // Flame
      if (Math.random() > 0.3) {
        // Flicker effect
        ctx.beginPath()
        ctx.moveTo(-size * 0.15, size * 0.2)
        ctx.lineTo(0, size * 0.6 + Math.random() * size * 0.2) // Randomize flame length
        ctx.lineTo(size * 0.15, size * 0.2)
        ctx.closePath()

        // Gradient for flame
        const flameGradient = ctx.createLinearGradient(0, size * 0.2, 0, size * 0.8)
        flameGradient.addColorStop(0, "rgba(255, 165, 0, 0.9)") // Orange
        flameGradient.addColorStop(1, "rgba(255, 69, 0, 0.5)") // Red-orange

        ctx.fillStyle = flameGradient
        ctx.fill()
      }

      ctx.restore()
    }

    // Arc Browser scroll handling
    let isScrolling = false
    let scrollTimeout: NodeJS.Timeout
    let animationId: number

    // Force animations to continue during scroll in Arc Browser
    const handleScroll = () => {
      isScrolling = true
      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        isScrolling = false
      }, 150)
    }

    if (isArcBrowser) {
      window.addEventListener('scroll', handleScroll, { passive: true })
      window.addEventListener('wheel', handleScroll, { passive: true })
      window.addEventListener('touchmove', handleScroll, { passive: true })
    }

    // Animation loop with Arc Browser optimizations
    let frameCount = 0
    const animate = () => {
      if (!ctx || !canvas) return

      // Arc Browser: Force animation continuation during scroll
      if (isArcBrowser && isScrolling) {
        // Use a more aggressive animation approach for Arc Browser during scroll
        ctx.save()
        ctx.globalCompositeOperation = 'source-over'
      }

      frameCount++
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw moon with glow effect
      const moonGradient = ctx.createRadialGradient(moon.x, moon.y, moon.radius * 0.8, moon.x, moon.y, moon.glow)
      moonGradient.addColorStop(0, currentMoonTheme.glow.inner)
      moonGradient.addColorStop(0.5, currentMoonTheme.glow.middle)
      moonGradient.addColorStop(1, currentMoonTheme.glow.outer)

      ctx.beginPath()
      ctx.arc(moon.x, moon.y, moon.glow, 0, Math.PI * 2)
      ctx.fillStyle = moonGradient
      ctx.fill()

      // Draw moon surface
      ctx.beginPath()
      ctx.arc(moon.x, moon.y, moon.radius, 0, Math.PI * 2)
      ctx.fillStyle = currentMoonTheme.surface
      ctx.fill()

      // Add a subtle outline to the moon for better visibility
      ctx.beginPath()
      ctx.arc(moon.x, moon.y, moon.radius, 0, Math.PI * 2)
      ctx.strokeStyle = "rgba(0, 0, 0, 0.2)"
      ctx.lineWidth = 1
      ctx.stroke()

      // Draw moon craters with enhanced visibility
      drawCrater(moon.x - moon.radius * 0.3, moon.y - moon.radius * 0.2, moon.radius * 0.15)
      drawCrater(moon.x + moon.radius * 0.2, moon.y + moon.radius * 0.3, moon.radius * 0.1)
      drawCrater(moon.x - moon.radius * 0.1, moon.y + moon.radius * 0.4, moon.radius * 0.08)

      // Update and draw particles
      particles.forEach((particle) => {
        // Update position
        particle.x += particle.speedX
        particle.y += particle.speedY

        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width
        if (particle.x > canvas.width) particle.x = 0
        if (particle.y < 0) particle.y = canvas.height
        if (particle.y > canvas.height) particle.y = 0

        // Draw particle
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
        ctx.fillStyle = particle.color
        ctx.fill()
      })

      // Draw connections between nearby particles
      particles.forEach((particle, i) => {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particle.x - particles[j].x
          const dy = particle.y - particles[j].y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < 100) {
            ctx.beginPath()
            ctx.strokeStyle = `rgba(255, 255, 255, ${0.1 * (1 - distance / 100)})`
            ctx.lineWidth = 0.5
            ctx.moveTo(particle.x, particle.y)
            ctx.lineTo(particles[j].x, particles[j].y)
            ctx.stroke()
          }
        }
      })

      // Randomly create shooting stars
      if (Math.random() < 0.01) {
        // 1% chance each frame
        createShootingStar()
      }

      // Update and draw shooting stars
      shootingStars.forEach((star, index) => {
        if (star.active) {
          // Update position
          star.x += Math.cos(star.angle) * star.speed
          star.y += Math.sin(star.angle) * star.speed
          star.age++

          // Add current position to trail
          star.trail.push({ x: star.x, y: star.y })

          // Limit trail length
          if (star.trail.length > star.trailLength) {
            star.trail.shift()
          }

          // Draw trail
          if (star.trail.length > 1) {
            ctx.beginPath()
            ctx.moveTo(star.trail[0].x, star.trail[0].y)

            for (let i = 1; i < star.trail.length; i++) {
              ctx.lineTo(star.trail[i].x, star.trail[i].y)
            }

            // Create gradient for trail
            const gradient = ctx.createLinearGradient(
              star.trail[0].x,
              star.trail[0].y,
              star.trail[star.trail.length - 1].x,
              star.trail[star.trail.length - 1].y,
            )

            gradient.addColorStop(0, `rgba(255, 255, 255, 0)`)
            gradient.addColorStop(1, `rgba(255, 255, 255, ${star.opacity})`)

            ctx.strokeStyle = gradient
            ctx.lineWidth = 2
            ctx.stroke()
          }

          // Draw star head
          ctx.beginPath()
          ctx.arc(star.x, star.y, 2, 0, Math.PI * 2)
          ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity})`
          ctx.fill()

          // Deactivate if off screen or lifetime exceeded
          if (star.x < 0 || star.x > canvas.width || star.y < 0 || star.y > canvas.height || star.age > star.lifetime) {
            star.active = false
          }
        }
      })

      // Handle rocket animation
      if (!rocket.active && Math.random() < 0.002) {
        // 0.2% chance to activate if inactive
        rocket.active = true
        rocket.x = -rocket.size
        rocket.y = canvas.height * (0.3 + Math.random() * 0.5) // Random height in middle area
        rocket.trail = []
      }

      if (rocket.active) {
        // Update rocket position
        rocket.x += rocket.speed
        rocket.y -= rocket.speed * 0.2 // Slight upward trajectory

        // Add current position to trail
        rocket.trail.push({ x: rocket.x, y: rocket.y })

        // Limit trail length
        if (rocket.trail.length > rocket.trailLength) {
          rocket.trail.shift()
        }

        // Draw rocket trail
        if (rocket.trail.length > 1) {
          ctx.beginPath()
          ctx.moveTo(rocket.trail[0].x, rocket.trail[0].y)

          for (let i = 1; i < rocket.trail.length; i++) {
            ctx.lineTo(rocket.trail[i].x, rocket.trail[i].y)
          }

          // Create gradient for trail
          const gradient = ctx.createLinearGradient(
            rocket.trail[0].x,
            rocket.trail[0].y,
            rocket.trail[rocket.trail.length - 1].x,
            rocket.trail[rocket.trail.length - 1].y,
          )

          gradient.addColorStop(0, "rgba(255, 69, 0, 0)")
          gradient.addColorStop(1, "rgba(255, 165, 0, 0.3)")

          ctx.strokeStyle = gradient
          ctx.lineWidth = 3
          ctx.stroke()
        }

        // Draw rocket
        drawRocket(rocket.x, rocket.y, rocket.size, rocket.rotation)

        // Deactivate if off screen
        if (rocket.x > canvas.width + rocket.size) {
          rocket.active = false
        }
      }

      // Arc Browser: Restore context and force continuation
      if (isArcBrowser && isScrolling) {
        ctx.restore()
        // Force immediate next frame for Arc Browser during scroll
        animationId = requestAnimationFrame(animate)
      } else {
        // Normal animation frame request
        animationId = requestAnimationFrame(animate)
      }
    }

    // Function to draw moon craters with enhanced visibility
    function drawCrater(x: number, y: number, size: number) {
      if (!ctx) return
      // Draw crater shadow for depth
      ctx.beginPath()
      ctx.arc(x + size * 0.1, y + size * 0.1, size, 0, Math.PI * 2)
      ctx.fillStyle = currentMoonTheme.craterShadow
      ctx.fill()

      // Draw crater
      ctx.beginPath()
      ctx.arc(x, y, size, 0, Math.PI * 2)
      ctx.fillStyle = currentMoonTheme.crater
      ctx.fill()

      // Add subtle highlight to create 3D effect
      ctx.beginPath()
      ctx.arc(x - size * 0.3, y - size * 0.3, size * 0.4, 0, Math.PI * 2)
      ctx.fillStyle = "rgba(255, 255, 255, 0.2)"
      ctx.fill()
    }

    animate()

    return () => {
      window.removeEventListener("resize", setDimensions)

      // Arc Browser cleanup
      if (isArcBrowser) {
        window.removeEventListener('scroll', handleScroll)
        window.removeEventListener('wheel', handleScroll)
        window.removeEventListener('touchmove', handleScroll)
        clearTimeout(scrollTimeout)
        if (animationId) {
          cancelAnimationFrame(animationId)
        }
      }
    }
  }, [moonTheme])

  return <canvas ref={canvasRef} className="fixed inset-0 z-0 pointer-events-none" style={{ opacity: 0.3 }} />
}
