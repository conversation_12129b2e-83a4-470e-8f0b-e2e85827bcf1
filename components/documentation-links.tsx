import Link from "next/link"
import { FileText } from "lucide-react"

interface DocumentationLinksProps {
  links: {
    title: string
    href: string
    description?: string
  }[]
}

export function DocumentationLinks({ links }: DocumentationLinksProps) {
  return (
    <div className="rounded-lg border border-white/10 bg-white/5 p-4">
      <div className="mb-3 flex items-center gap-2">
        <FileText className="h-5 w-5 text-doge" />
        <h3 className="text-lg font-medium text-white">Documentation</h3>
      </div>
      <div className="space-y-2">
        {links.map((link) => (
          <Link key={link.title} href={link.href} className="block rounded-md p-2 transition-colors hover:bg-white/10">
            <div className="text-sm font-medium text-white">{link.title}</div>
            {link.description && <div className="text-xs text-white/70">{link.description}</div>}
          </Link>
        ))}
      </div>
    </div>
  )
}
