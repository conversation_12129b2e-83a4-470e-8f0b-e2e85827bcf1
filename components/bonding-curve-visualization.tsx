"use client"

import { useEffect, useRef, useState, useCallback } from "react"

type BondingCurveType = "linear" | "exponential" | "logarithmic"

interface BondingCurveVisualizationProps {
  type: BondingCurveType
  width?: number
  height?: number
  interactive?: boolean
}

export function BondingCurveVisualization({
  type,
  width = 300,
  height = 200,
  interactive = true,
}: BondingCurveVisualizationProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [hoverPosition, setHoverPosition] = useState<{ x: number; y: number } | null>(null)
  const [coordinates, setCoordinates] = useState<[number, number][]>([])
  const [canvasSize, setCanvasSize] = useState({ width, height })

  // Memoize curve calculation to improve performance
  const calculateCurvePoints = useCallback(
    (canvasWidth: number, canvasHeight: number) => {
      const padding = 40
      const graphWidth = canvasWidth - padding * 2
      const graphHeight = canvasHeight - padding * 2
      const points = 100
      const newCoordinates: [number, number][] = []

      for (let i = 0; i <= points; i++) {
        const x = padding + (i / points) * graphWidth
        let y

        // Calculate y based on curve type
        switch (type) {
          case "linear":
            // Linear: y = mx + b
            y = canvasHeight - padding - (i / points) * graphHeight
            break
          case "exponential":
            // Exponential: y = a * e^(b*x)
            y = canvasHeight - padding - Math.pow(i / points, 2) * graphHeight
            break
          case "logarithmic":
            // Logarithmic: y = a + b * ln(x)
            if (i === 0) {
              y = canvasHeight - padding
            } else {
              y = canvasHeight - padding - (Math.log(i + 1) / Math.log(points + 1)) * graphHeight
            }
            break
          default:
            y = canvasHeight - padding - (i / points) * graphHeight
        }

        newCoordinates.push([x, y])
      }

      return newCoordinates
    },
    [type],
  )

  // Draw the curve without hover effects - for better performance
  const drawBaseCurve = useCallback(
    (ctx: CanvasRenderingContext2D, canvasWidth: number, canvasHeight: number) => {
      // Clear canvas
      ctx.clearRect(0, 0, canvasWidth, canvasHeight)

      // Set up coordinates
      const padding = 40
      const graphWidth = canvasWidth - padding * 2
      const graphHeight = canvasHeight - padding * 2

      // Draw background
      ctx.fillStyle = "#111111"
      ctx.fillRect(0, 0, canvasWidth, canvasHeight)

      // Draw grid
      ctx.strokeStyle = "rgba(255, 255, 255, 0.1)"
      ctx.lineWidth = 1

      // Horizontal grid lines
      for (let i = 0; i <= 4; i++) {
        const y = padding + (i / 4) * graphHeight
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(canvasWidth - padding, y)
        ctx.stroke()
      }

      // Vertical grid lines
      for (let i = 0; i <= 4; i++) {
        const x = padding + (i / 4) * graphWidth
        ctx.beginPath()
        ctx.moveTo(x, padding)
        ctx.lineTo(x, canvasHeight - padding)
        ctx.stroke()
      }

      // Draw axes
      ctx.strokeStyle = "rgba(255, 255, 255, 0.3)"
      ctx.lineWidth = 1

      // X-axis
      ctx.beginPath()
      ctx.moveTo(padding, canvasHeight - padding)
      ctx.lineTo(canvasWidth - padding, canvasHeight - padding)
      ctx.stroke()

      // Y-axis
      ctx.beginPath()
      ctx.moveTo(padding, padding)
      ctx.lineTo(padding, canvasHeight - padding)
      ctx.stroke()

      // Draw curve based on type
      ctx.strokeStyle = "#FF9900" // Orange color that matches the screenshots
      ctx.lineWidth = 2
      ctx.beginPath()

      if (coordinates.length > 0) {
        ctx.moveTo(coordinates[0][0], coordinates[0][1])
        for (let i = 1; i < coordinates.length; i++) {
          ctx.lineTo(coordinates[i][0], coordinates[i][1])
        }
        ctx.stroke()

        // Add gradient fill
        const gradient = ctx.createLinearGradient(0, padding, 0, canvasHeight - padding)
        gradient.addColorStop(0, "rgba(255, 153, 0, 0.3)") // Orange with opacity
        gradient.addColorStop(1, "rgba(255, 153, 0, 0.05)") // More transparent at bottom

        ctx.fillStyle = gradient
        ctx.beginPath()
        ctx.moveTo(padding, canvasHeight - padding)
        for (const [x, y] of coordinates) {
          ctx.lineTo(x, y)
        }
        ctx.lineTo(canvasWidth - padding, canvasHeight - padding)
        ctx.closePath()
        ctx.fill()
      }

      // Add labels
      ctx.fillStyle = "rgba(255, 255, 255, 0.8)"
      ctx.font = "12px sans-serif"

      // X-axis label
      ctx.textAlign = "center"
      ctx.fillText("Supply Sold", canvasWidth / 2, canvasHeight - 5)

      // Add percentage markers on x-axis
      for (let i = 0; i <= 4; i++) {
        const x = padding + (i / 4) * graphWidth
        const value = i * 25 + "%"
        ctx.fillText(value, x, canvasHeight - padding + 15)
      }
    },
    [coordinates],
  )

  // Draw hover effects separately for better performance
  const drawHoverEffects = useCallback(
    (ctx: CanvasRenderingContext2D, canvasWidth: number, canvasHeight: number, mouseX: number) => {
      if (!interactive || coordinates.length === 0) return

      const padding = 40
      const graphWidth = canvasWidth - padding * 2

      // Only process if mouse is in the valid graph area
      if (mouseX < padding || mouseX > canvasWidth - padding) return

      // Find the closest point on the curve
      const relativeX = mouseX - padding
      const pointIndex = Math.min(Math.floor((relativeX / graphWidth) * 100), coordinates.length - 1)

      if (pointIndex >= 0 && pointIndex < coordinates.length) {
        const [x, y] = coordinates[pointIndex]

        // Draw vertical line
        ctx.strokeStyle = "rgba(255, 255, 255, 0.5)"
        ctx.setLineDash([5, 5])
        ctx.beginPath()
        ctx.moveTo(x, y)
        ctx.lineTo(x, canvasHeight - padding)
        ctx.stroke()
        ctx.setLineDash([])

        // Draw horizontal line
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(x, y)
        ctx.stroke()

        // Draw point
        ctx.fillStyle = "#ffffff"
        ctx.beginPath()
        ctx.arc(x, y, 4, 0, Math.PI * 2)
        ctx.fill()

        // Calculate and display values
        const supplyPercentage = Math.round(((x - padding) / graphWidth) * 100)
        const pricePercentage = Math.round(((canvasHeight - padding - y) / (canvasHeight - padding * 2)) * 100)

        // Draw tooltip background
        ctx.fillStyle = "rgba(0, 0, 0, 0.8)"
        const tooltipWidth = 120
        const tooltipHeight = 60
        const tooltipX = x + 10 > canvasWidth - tooltipWidth ? x - tooltipWidth - 10 : x + 10
        const tooltipY = y - tooltipHeight - 10 < padding ? y + 10 : y - tooltipHeight - 10

        ctx.beginPath()
        ctx.rect(tooltipX, tooltipY, tooltipWidth, tooltipHeight)
        ctx.fill()

        // Draw tooltip text
        ctx.fillStyle = "#ffffff"
        ctx.font = "12px sans-serif"
        ctx.textAlign = "left"
        ctx.fillText(`Supply: ${supplyPercentage}%`, tooltipX + 10, tooltipY + 20)
        ctx.fillText(`Price: ${pricePercentage}%`, tooltipX + 10, tooltipY + 40)
      }
    },
    [interactive, coordinates],
  )

  // Initialize and handle resize
  useEffect(() => {
    const updateCanvasSize = () => {
      if (!containerRef.current) return

      const { width: containerWidth, height: containerHeight } = containerRef.current.getBoundingClientRect()
      setCanvasSize({ width: containerWidth, height: containerHeight })

      // Recalculate curve points when size changes
      const newCoordinates = calculateCurvePoints(containerWidth, containerHeight)
      setCoordinates(newCoordinates)
    }

    // Initial setup
    updateCanvasSize()

    // Handle resize
    const resizeObserver = new ResizeObserver(updateCanvasSize)
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [calculateCurvePoints])

  // Draw the chart
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    canvas.width = canvasSize.width
    canvas.height = canvasSize.height

    // Draw the base curve (without hover effects)
    drawBaseCurve(ctx, canvasSize.width, canvasSize.height)

    // Draw hover effects if needed
    if (hoverPosition) {
      drawHoverEffects(ctx, canvasSize.width, canvasSize.height, hoverPosition.x)
    }
  }, [canvasSize, drawBaseCurve, drawHoverEffects, hoverPosition])

  // Handle mouse interactions
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || !interactive) return

    // Use throttled mouse move for better performance
    let ticking = false
    const handleMouseMove = (e: MouseEvent) => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          const rect = canvas.getBoundingClientRect()
          // Get precise mouse position relative to canvas
          const x = e.clientX - rect.left
          const y = e.clientY - rect.top
          setHoverPosition({ x, y })
          ticking = false
        })
        ticking = true
      }
    }

    const handleMouseLeave = () => {
      setHoverPosition(null)
    }

    canvas.addEventListener("mousemove", handleMouseMove)
    canvas.addEventListener("mouseleave", handleMouseLeave)

    return () => {
      canvas.removeEventListener("mousemove", handleMouseMove)
      canvas.removeEventListener("mouseleave", handleMouseLeave)
    }
  }, [interactive])

  return (
    <div ref={containerRef} className="relative w-full h-full">
      <canvas
        ref={canvasRef}
        className={`w-full h-full ${interactive ? "cursor-crosshair" : ""}`}
        style={{ touchAction: "none" }}
      />
    </div>
  )
}
