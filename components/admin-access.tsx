"use client"

import Link from "next/link"
import { Shield } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useWallet } from "@/components/wallet-provider"
import { useEffect, useState } from "react"

// In a real application, this would check against a list of admin addresses
const ADMIN_ADDRESSES = [
  "******************************************", // Example admin address
]

export function AdminAccess() {
  const { isConnected, address } = useWallet()
  const [isAdmin, setIsAdmin] = useState(false)

  useEffect(() => {
    // Check if connected wallet is an admin
    if (isConnected && address) {
      // For demo purposes, we'll just set isAdmin to true
      // In production, you would check against actual admin addresses
      setIsAdmin(true)

      // Uncomment for production:
      // setIsAdmin(ADMIN_ADDRESSES.includes(address.toLowerCase()))
    } else {
      setIsAdmin(false)
    }
  }, [isConnected, address])

  if (!isConnected || !isAdmin) return null

  return (
    <div className="rounded-lg border border-doge/20 bg-doge/5 p-4">
      <div className="mb-3 flex items-center gap-2">
        <Shield className="h-5 w-5 text-doge" />
        <h3 className="text-lg font-medium text-white">Admin Access</h3>
      </div>
      <div className="space-y-2">
        <Link href="/governance/admin/dashboard" className="block">
          <Button
            variant="outline"
            className="w-full justify-between border-doge/30 bg-doge/10 text-white hover:bg-doge/20"
          >
            Admin Dashboard
          </Button>
        </Link>
        <Link href="/governance/admin/development-dao" className="block">
          <Button
            variant="outline"
            className="w-full justify-between border-white/10 bg-white/5 text-white hover:bg-white/10"
          >
            Manage Development DAO
          </Button>
        </Link>
      </div>
    </div>
  )
}
