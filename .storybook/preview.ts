import type { Preview } from '@storybook/nextjs'
import '../app/globals.css'

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    backgrounds: {
      default: 'dark',
      values: [
        {
          name: 'dark',
          value: '#000000',
        },
        {
          name: 'light',
          value: '#ffffff',
        },
        {
          name: 'pawpumps',
          value: 'linear-gradient(135deg, #000000 0%, #1a1a1a 100%)',
        },
      ],
    },
    viewport: {
      viewports: {
        mobile: {
          name: 'Mobile',
          styles: {
            width: '375px',
            height: '667px',
          },
        },
        tablet: {
          name: 'Tablet',
          styles: {
            width: '768px',
            height: '1024px',
          },
        },
        desktop: {
          name: 'Desktop',
          styles: {
            width: '1440px',
            height: '900px',
          },
        },
      },
    },
    docs: {
      theme: {
        base: 'dark',
        colorPrimary: '#D4AF37',
        colorSecondary: '#FFD700',
        appBg: '#000000',
        appContentBg: '#1a1a1a',
        appBorderColor: '#333333',
        textColor: '#ffffff',
        textInverseColor: '#000000',
        barTextColor: '#ffffff',
        barSelectedColor: '#D4AF37',
        barBg: '#1a1a1a',
        inputBg: '#333333',
        inputBorder: '#555555',
        inputTextColor: '#ffffff',
        inputBorderRadius: 4,
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-black text-white p-4">
        <Story />
      </div>
    ),
  ],
};

export default preview;