"use client"

import type React from "react"

import { createContext, useState, useEffect, useContext } from "react"

interface AdminAuthContextType {
  isAdmin: boolean
  isLoading: boolean
  hasPermission: (permission: string) => boolean
  login: (password: string) => Promise<void>
  logout: () => void
}

export const AdminAuthContext = createContext<AdminAuthContextType>({
  isAdmin: false,
  isLoading: true,
  hasPermission: () => false,
  login: async () => {},
  logout: () => {},
})

export function useAdminAuthContext() {
  return useContext(AdminAuthContext)
}

// For demo purposes, we'll automatically grant admin access
const DEMO_MODE = true

export function AdminAuthProvider({ children }: { children: React.ReactNode }) {
  const [isAdmin, setIsAdmin] = useState(DEMO_MODE)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuth = async () => {
      try {
        // In demo mode, always grant access
        if (DEMO_MODE) {
          setIsAdmin(true)
          setIsLoading(false)
          return
        }

        const storedAuth = localStorage.getItem("pawpumps-admin-auth")
        if (storedAuth) {
          setIsAdmin(true)
        }
      } catch (error) {
        console.error("Error checking authentication:", error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (password: string) => {
    // In demo mode, any password works
    if (DEMO_MODE) {
      setIsAdmin(true)
      return Promise.resolve()
    }

    // In a real app, this would be a server call
    if (password === "pawpumps-admin") {
      localStorage.setItem("pawpumps-admin-auth", "true")
      setIsAdmin(true)
      return Promise.resolve()
    } else {
      return Promise.reject(new Error("Invalid password"))
    }
  }

  const logout = () => {
    if (!DEMO_MODE) {
      localStorage.removeItem("pawpumps-admin-auth")
    }
    setIsAdmin(false)
  }

  const hasPermission = (permission: string) => {
    // In demo mode or if they're an admin, they have all permissions
    return DEMO_MODE || isAdmin
  }

  return (
    <AdminAuthContext.Provider value={{ isAdmin, isLoading, hasPermission, login, logout }}>
      {children}
    </AdminAuthContext.Provider>
  )
}
