# Stagewise MCP Server Usage Examples

This document provides practical examples of how to use the Stagewise MCP Server with Augment Code for enhanced frontend development workflows.

## Prerequisites

1. Stagewise extension installed and running in your code editor
2. Stagewise toolbar integrated into your web application
3. Stagewise MCP Server configured in Augment Code
4. Web application running in development mode

## Basic Workflow Examples

### 1. Analyzing Selected UI Elements

**Scenario**: You want to understand the structure of a specific UI component.

**Steps**:
1. Open your web application in the browser
2. Use the Stagewise toolbar to select the UI element(s) you want to analyze
3. In Augment Code, use the MCP server:

```
@stagewise get_selected_elements with includeParents=true
```

**Expected Result**: Detailed information about the selected DOM elements including attributes, styles, and parent hierarchy.

### 2. Getting Page Context

**Scenario**: You need comprehensive information about the current page state.

**Steps**:
1. Navigate to the page you want to analyze
2. In Augment Code:

```
@stagewise get_page_context with includeElements=true
```

**Expected Result**: Current page URL, title, viewport information, and any selected elements.

### 3. Sending UI Feedback to AI Agent

**Scenario**: You want to request changes to selected UI elements.

**Steps**:
1. Select the UI elements you want to modify using Stagewise toolbar
2. In Augment Code:

```
@stagewise send_ui_comment with comment="Make this button larger and change the color to blue" and mode="agent"
```

**Expected Result**: The AI agent receives your comment along with context about the selected elements and can make appropriate code changes.

## Advanced Usage Examples

### 4. Linking Specific Files to UI Comments

**Scenario**: You want to provide additional context by linking relevant source files.

```
@stagewise send_ui_comment with comment="This form validation is not working correctly" and files=["src/components/ContactForm.tsx", "src/utils/validation.ts"] and mode="agent"
```

### 5. Using Different AI Models

**Scenario**: You want to use a specific AI model for processing your UI feedback.

```
@stagewise send_ui_comment with comment="Optimize this component for accessibility" and model="claude-3-sonnet" and mode="agent"
```

### 6. Manual Mode for Complex Instructions

**Scenario**: You want to provide detailed instructions without automatic processing.

```
@stagewise send_ui_comment with comment="This dropdown menu needs: 1) Better keyboard navigation, 2) ARIA labels, 3) Focus management, 4) Mobile-responsive design" and mode="manual"
```

## Workflow Integration Examples

### Frontend Development Workflow

1. **Design Review**:
   ```
   @stagewise get_page_context
   @stagewise send_ui_comment with comment="Review this page layout for mobile responsiveness"
   ```

2. **Bug Fixing**:
   ```
   # Select the problematic element first
   @stagewise send_ui_comment with comment="This button is not clickable on mobile devices" and files=["src/styles/buttons.css"]
   ```

3. **Feature Enhancement**:
   ```
   @stagewise send_ui_comment with comment="Add loading state animation to this button" and mode="agent"
   ```

### Accessibility Audit Workflow

1. **Element Analysis**:
   ```
   @stagewise get_selected_elements with includeParents=true
   @stagewise send_ui_comment with comment="Audit this form for accessibility compliance"
   ```

2. **Comprehensive Page Audit**:
   ```
   @stagewise get_page_context
   @stagewise send_ui_comment with comment="Perform complete accessibility audit of this page" and mode="agent"
   ```

## Error Handling Examples

### Connection Issues

If you encounter connection errors:

```
# Test the connection
npm run test:connection

# Check if Stagewise is running
@stagewise get_session_info
```

### Feature Not Available

Some features require enhancements to Stagewise:

```
# This will show a helpful error message
@stagewise get_project_structure
```

**Error Response**:
```json
{
  "error": "get_project_structure requires enhancement to Stagewise extension. This method would expose workspace file structure and project metadata.",
  "success": false
}
```

## Best Practices

### 1. Be Specific in Comments

❌ **Bad**: "Fix this"
✅ **Good**: "This button needs better contrast ratio and larger click target for accessibility"

### 2. Provide Context with Files

```
@stagewise send_ui_comment with comment="The hover state animation is too slow" and files=["src/styles/animations.css", "src/components/Button.tsx"]
```

### 3. Use Appropriate Modes

- **agent**: For automatic processing and code changes
- **ask**: For questions and analysis
- **manual**: For complex instructions requiring human review

### 4. Combine with Other MCP Servers

```
# Use with file system MCP server
@filesystem read_file path="src/components/Button.tsx"
@stagewise send_ui_comment with comment="Update this button component based on the current implementation"
```

## Troubleshooting Common Issues

### 1. No Elements Selected

**Problem**: `get_selected_elements` returns empty array
**Solution**: Make sure to select elements using the Stagewise toolbar before calling the MCP server

### 2. Connection Timeout

**Problem**: Server cannot connect to Stagewise extension
**Solutions**:
- Verify Stagewise extension is running
- Check that your web app has the Stagewise toolbar loaded
- Ensure ports 5746-5756 are available

### 3. Session Mismatch

**Problem**: Session ID mismatch errors
**Solution**: The MCP server will automatically reconnect and refresh the session

## Future Enhancements

The following features are planned for future versions:

1. **Real-time Element Selection**: Get currently selected elements without manual refresh
2. **Project Structure Access**: Browse and analyze project file structure
3. **Element Highlighting**: Visually highlight elements in the browser
4. **Component Recognition**: Identify React/Vue components from DOM elements
5. **Style Analysis**: Deep analysis of CSS styles and computed properties

## Contributing

If you'd like to contribute to these examples or suggest new use cases, please visit the [GitHub repository](https://github.com/augmentcode/stagewise-mcp-server).
