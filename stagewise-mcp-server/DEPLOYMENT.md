# Stagewise MCP Server Deployment Guide

This guide covers how to deploy and publish the Stagewise MCP Server to npm and integrate it into Augment Code's official MCP server list.

## Pre-Deployment Checklist

### 1. Code Quality

- [ ] All TypeScript errors resolved
- [ ] Tests passing: `npm test`
- [ ] Linting clean: `npm run lint`
- [ ] Build successful: `npm run build`
- [ ] MCP server functional: `npm run test:connection`

### 2. Documentation

- [ ] README.md updated with latest features
- [ ] INSTALLATION.md complete
- [ ] Usage examples documented
- [ ] API documentation current
- [ ] CHANGELOG.md updated

### 3. Package Configuration

- [ ] package.json version updated
- [ ] Dependencies reviewed and updated
- [ ] Keywords optimized for discoverability
- [ ] License specified (MIT)
- [ ] Repository URLs correct

## Publishing to npm

### 1. Prepare for Publication

```bash
# Ensure you're logged into npm
npm whoami

# If not logged in:
npm login

# Clean build
rm -rf dist node_modules
npm install
npm run build

# Run final tests
npm run test:connection
```

### 2. Version Management

```bash
# For patch releases (bug fixes)
npm version patch

# For minor releases (new features)
npm version minor

# For major releases (breaking changes)
npm version major

# Or set version manually
npm version 1.0.0
```

### 3. Publish Package

```bash
# Dry run to check what will be published
npm publish --dry-run

# Publish to npm
npm publish

# For scoped packages (if needed)
npm publish --access public
```

### 4. Verify Publication

```bash
# Check package on npm
npm view stagewise-mcp-server

# Test installation
npm install -g stagewise-mcp-server@latest
stagewise-mcp-server --help
```

## Integration with Augment Code

### 1. Submit to Official MCP Server List

Create a pull request to add Stagewise to Augment Code's official MCP server list:

#### Repository Information
- **Repository**: `https://github.com/augmentcode/mcp-servers`
- **File to update**: `servers.json` or `README.md`

#### Required Information
```json
{
  "name": "stagewise",
  "package": "stagewise-mcp-server",
  "description": "Stagewise browser toolbar integration for frontend development",
  "category": "development",
  "tags": ["frontend", "ui", "browser", "stagewise", "development"],
  "author": "Augment Code Community",
  "repository": "https://github.com/augmentcode/stagewise-mcp-server",
  "documentation": "https://github.com/augmentcode/stagewise-mcp-server#readme",
  "installation": {
    "command": "npm install -g stagewise-mcp-server",
    "config": {
      "name": "stagewise",
      "command": "stagewise-mcp-server"
    }
  },
  "requirements": [
    "Stagewise extension installed in code editor",
    "Stagewise toolbar integrated in web application",
    "Development server running"
  ],
  "tools": [
    {
      "name": "get_selected_elements",
      "description": "Get currently selected DOM elements from Stagewise toolbar"
    },
    {
      "name": "get_page_context", 
      "description": "Get current page context including URL, title, and viewport info"
    },
    {
      "name": "send_ui_comment",
      "description": "Send comments/instructions about UI elements to AI agent"
    },
    {
      "name": "get_project_structure",
      "description": "Get current project structure and file information"
    },
    {
      "name": "highlight_elements",
      "description": "Highlight specific elements in browser for visual feedback"
    }
  ]
}
```

### 2. Documentation for Augment Code

Create documentation for Augment Code's official docs:

#### Setup Instructions
```markdown
## Stagewise Integration

The Stagewise MCP server enables seamless integration between Augment Code and Stagewise's browser toolbar for enhanced frontend development workflows.

### Installation

1. Install the MCP server:
   ```bash
   npm install -g stagewise-mcp-server
   ```

2. Add to Augment Code MCP configuration:
   ```json
   {
     "name": "stagewise",
     "command": "stagewise-mcp-server"
   }
   ```

3. Install Stagewise extension in your code editor
4. Integrate Stagewise toolbar in your web application

### Usage

Select UI elements in your browser and use Augment Agent:

```
@stagewise send_ui_comment with comment="Make this button more accessible"
```
```

### 3. Community Outreach

#### Stagewise Community
- Create issue/discussion in Stagewise repository
- Share integration in Stagewise Discord
- Write blog post about the integration

#### Augment Code Community  
- Announce in Augment Code Discord
- Share on social media with relevant hashtags
- Create demo video showing the integration

## Continuous Deployment

### 1. GitHub Actions Setup

Create `.github/workflows/publish.yml`:

```yaml
name: Publish to npm

on:
  release:
    types: [published]

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build
        run: npm run build
        
      - name: Test
        run: npm run test:connection
        
      - name: Publish to npm
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
```

### 2. Automated Testing

Create `.github/workflows/test.yml`:

```yaml
name: Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20, 22]
        
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build
        run: npm run build
        
      - name: Test
        run: npm run test:connection
```

## Monitoring and Maintenance

### 1. Package Health

Monitor package health using:
- npm package analytics
- GitHub repository insights
- User feedback and issues

### 2. Dependency Updates

Regular maintenance tasks:
```bash
# Check for outdated dependencies
npm outdated

# Update dependencies
npm update

# Audit for security vulnerabilities
npm audit
npm audit fix
```

### 3. Version Management

Follow semantic versioning:
- **Patch** (1.0.1): Bug fixes, documentation updates
- **Minor** (1.1.0): New features, backward compatible
- **Major** (2.0.0): Breaking changes

## Post-Deployment

### 1. Verification

After deployment, verify:
- [ ] Package installs correctly: `npm install -g stagewise-mcp-server@latest`
- [ ] MCP server starts: `stagewise-mcp-server --help`
- [ ] Integration works with Augment Code
- [ ] Documentation is accessible

### 2. User Support

Prepare for user support:
- Monitor GitHub issues
- Respond to community questions
- Update documentation based on feedback
- Create troubleshooting guides

### 3. Future Enhancements

Plan future improvements:
- Enhanced Stagewise API integration
- Additional MCP tools
- Performance optimizations
- Better error handling
- Extended framework support

## Rollback Procedure

If issues are discovered after deployment:

```bash
# Unpublish specific version (within 24 hours)
npm unpublish stagewise-mcp-server@1.0.1

# Deprecate version
npm deprecate stagewise-mcp-server@1.0.1 "This version has critical bugs, please upgrade"

# Publish fixed version
npm version patch
npm publish
```

## Success Metrics

Track deployment success through:
- npm download statistics
- GitHub stars and forks
- Community adoption
- Issue resolution time
- User satisfaction feedback
