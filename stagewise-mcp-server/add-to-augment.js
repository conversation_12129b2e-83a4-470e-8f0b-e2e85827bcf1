#!/usr/bin/env node

/**
 * Script to add Stagewise MCP Server to Augment Code configuration
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

function getAugmentConfigPath() {
  const platform = os.platform();
  const configPaths = {
    darwin: path.join(os.homedir(), 'Library/Application Support/augment/mcp-servers.json'),
    linux: path.join(os.homedir(), '.config/augment/mcp-servers.json'),
    win32: path.join(os.homedir(), 'AppData/Roaming/augment/mcp-servers.json'),
  };
  return configPaths[platform] || configPaths.linux;
}

function addStagewiseToAugment() {
  console.log('🔧 Adding Stagewise MCP Server to Augment Code...\n');

  const configPath = getAugmentConfigPath();
  const configDir = path.dirname(configPath);
  const currentDir = process.cwd();
  const serverPath = path.join(currentDir, 'dist/cli.js');

  // Verify server exists
  if (!fs.existsSync(serverPath)) {
    console.error('❌ Server not found at:', serverPath);
    console.error('Please run "npm run build" first');
    process.exit(1);
  }

  // Create config directory if needed
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
    console.log('📁 Created Augment config directory');
  }

  // Read or create config
  let config = { servers: [] };
  if (fs.existsSync(configPath)) {
    try {
      const configContent = fs.readFileSync(configPath, 'utf8');
      config = JSON.parse(configContent);
    } catch (error) {
      console.warn('⚠️  Could not parse existing config, creating new one');
    }
  }

  if (!config.servers) {
    config.servers = [];
  }

  // Remove existing stagewise server if present
  config.servers = config.servers.filter(server => server.name !== 'stagewise');

  // Add stagewise server
  const stagewiseServer = {
    name: 'stagewise',
    command: 'node',
    args: [serverPath],
    description: 'Stagewise browser toolbar integration for frontend development',
    enabled: true
  };

  config.servers.push(stagewiseServer);

  // Write config
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

  console.log('✅ Successfully added Stagewise MCP Server to Augment Code!');
  console.log('📁 Configuration saved to:', configPath);
  console.log('\n📋 Server configuration:');
  console.log('   Name:', stagewiseServer.name);
  console.log('   Command:', stagewiseServer.command);
  console.log('   Args:', JSON.stringify(stagewiseServer.args));
  console.log('   Path:', serverPath);
  
  console.log('\n🚀 Next steps:');
  console.log('1. Restart Augment Code to load the new server');
  console.log('2. Install Stagewise extension in your code editor');
  console.log('3. Set up Stagewise toolbar in your web application');
  console.log('4. Test with: @stagewise send_ui_comment with comment="Test message"');
}

if (require.main === module) {
  addStagewiseToAugment();
}
