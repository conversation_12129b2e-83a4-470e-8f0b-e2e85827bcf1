#!/usr/bin/env node

/**
 * <PERSON>ript to add Stagewise MCP Server to Augment Code configuration
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const crypto = require('crypto');

function generateUUID() {
  return crypto.randomUUID();
}

function getAugmentConfigPath() {
  // Augment runs as VS Code extension, so config is in VS Code storage
  const vscodeConfigPath = path.join(
    os.homedir(),
    'Library/Application Support/Code/User/globalStorage/augment.vscode-augment/augment-global-state/mcpServers.json'
  );
  return vscodeConfigPath;
}

function addStagewiseToAugment() {
  console.log('🔧 Adding Stagewise MCP Server to Augment Code...\n');

  const configPath = getAugmentConfigPath();
  const configDir = path.dirname(configPath);
  const currentDir = process.cwd();
  const serverPath = path.join(currentDir, 'dist/cli.js');

  // Verify server exists
  if (!fs.existsSync(serverPath)) {
    console.error('❌ Server not found at:', serverPath);
    console.error('Please run "npm run build" first');
    process.exit(1);
  }

  // Create config directory if needed
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
    console.log('📁 Created Augment config directory');
  }

  // Read or create config (Augment uses array format)
  let servers = [];
  if (fs.existsSync(configPath)) {
    try {
      const configContent = fs.readFileSync(configPath, 'utf8');
      servers = JSON.parse(configContent);
      if (!Array.isArray(servers)) {
        console.warn('⚠️  Config is not an array, creating new one');
        servers = [];
      }
    } catch (error) {
      console.warn('⚠️  Could not parse existing config, creating new one');
      servers = [];
    }
  }

  // Remove existing stagewise server if present
  servers = servers.filter(server => server.name !== 'stagewise');

  // Add stagewise server in Augment's format
  const stagewiseServer = {
    type: 'stdio',
    name: 'stagewise',
    command: `node "${serverPath}"`,
    arguments: '',
    useShellInterpolation: true,
    id: generateUUID()
  };

  servers.push(stagewiseServer);

  // Write config
  fs.writeFileSync(configPath, JSON.stringify(servers, null, 2));

  console.log('✅ Successfully added Stagewise MCP Server to Augment Code!');
  console.log('📁 Configuration saved to:', configPath);
  console.log('\n📋 Server configuration:');
  console.log('   Name:', stagewiseServer.name);
  console.log('   Type:', stagewiseServer.type);
  console.log('   Command:', stagewiseServer.command);
  console.log('   ID:', stagewiseServer.id);
  console.log('   Path:', serverPath);

  console.log('\n🚀 Next steps:');
  console.log('1. Restart VS Code to reload Augment extension');
  console.log('2. Check MCP settings - Stagewise should now appear');
  console.log('3. Test with: @stagewise send_ui_comment with comment="Test message"');
  console.log('4. Install Stagewise VS Code extension for full functionality');
}

if (require.main === module) {
  addStagewiseToAugment();
}
