#!/usr/bin/env node

/**
 * Setup script for Stagewise MCP Server
 * 
 * This script helps users configure the MCP server with Augment Code
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

const AUGMENT_CONFIG_PATHS = {
  darwin: path.join(os.homedir(), 'Library/Application Support/augment/mcp-servers.json'),
  linux: path.join(os.homedir(), '.config/augment/mcp-servers.json'),
  win32: path.join(os.homedir(), 'AppData/Roaming/augment/mcp-servers.json'),
};

function getAugmentConfigPath() {
  const platform = os.platform();
  return AUGMENT_CONFIG_PATHS[platform] || AUGMENT_CONFIG_PATHS.linux;
}

function createMCPServerConfig() {
  return {
    name: 'stagewise',
    command: 'stagewise-mcp-server',
    description: 'Stagewise browser toolbar integration for frontend development',
    enabled: true,
    config: {
      host: 'localhost',
      startPort: 5746,
      endPort: 5756,
      timeout: 5000
    }
  };
}

function addToAugmentConfig() {
  const configPath = getAugmentConfigPath();
  const configDir = path.dirname(configPath);

  // Create config directory if it doesn't exist
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
  }

  let config = {};
  
  // Read existing config if it exists
  if (fs.existsSync(configPath)) {
    try {
      const configContent = fs.readFileSync(configPath, 'utf8');
      config = JSON.parse(configContent);
    } catch (error) {
      console.warn('⚠️  Could not parse existing config, creating new one');
      config = {};
    }
  }

  // Initialize servers array if it doesn't exist
  if (!config.servers) {
    config.servers = [];
  }

  // Check if stagewise server already exists
  const existingIndex = config.servers.findIndex(server => server.name === 'stagewise');
  const stagewiseConfig = createMCPServerConfig();

  if (existingIndex >= 0) {
    config.servers[existingIndex] = stagewiseConfig;
    console.log('✅ Updated existing Stagewise MCP server configuration');
  } else {
    config.servers.push(stagewiseConfig);
    console.log('✅ Added Stagewise MCP server to configuration');
  }

  // Write updated config
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  console.log(`📁 Configuration saved to: ${configPath}`);
}

function printManualInstructions() {
  console.log('\n📋 Manual Setup Instructions:');
  console.log('1. Open Augment Code settings panel (gear icon in upper right)');
  console.log('2. Navigate to the MCP servers section');
  console.log('3. Click the + button to add a new server');
  console.log('4. Configure the server:');
  console.log('   Name: stagewise');
  console.log('   Command: stagewise-mcp-server');
  console.log('5. Save the configuration');
}

function printNextSteps() {
  console.log('\n🚀 Next Steps:');
  console.log('1. Make sure Stagewise extension is installed in your code editor');
  console.log('2. Set up Stagewise toolbar in your web application');
  console.log('3. Start your development server and open your web app');
  console.log('4. Test the integration by selecting UI elements and using Augment Agent');
  console.log('\n📚 Documentation:');
  console.log('- Stagewise: https://stagewise.io/docs');
  console.log('- Augment Code: https://docs.augmentcode.com');
}

function main() {
  console.log('🔧 Stagewise MCP Server Setup\n');

  try {
    addToAugmentConfig();
    console.log('\n🎉 Setup completed successfully!');
  } catch (error) {
    console.error('❌ Automatic setup failed:', error.message);
    printManualInstructions();
  }

  printNextSteps();
}

if (require.main === module) {
  main();
}
