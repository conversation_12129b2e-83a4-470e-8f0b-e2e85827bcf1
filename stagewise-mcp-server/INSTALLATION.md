# Stagewise MCP Server Installation Guide

This guide provides step-by-step instructions for installing and configuring the Stagewise MCP Server with Augment Code.

## Prerequisites

### 1. System Requirements

- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 8.0.0 or higher (comes with Node.js)
- **Operating System**: macOS, Linux, or Windows

### 2. Required Software

#### Stagewise Extension
Install the Stagewise extension in your code editor:

- **Cursor**: `cursor:extension/stagewise.stagewise-vscode-extension`
- **VS Code**: `vscode:extension/stagewise.stagewise-vscode-extension`
- **Windsurf**: `windsurf:extension/stagewise.stagewise-vscode-extension`
- **Trae**: `trae:extension/stagewise.stagewise-vscode-extension`

#### Augment Code
Ensure you have Augment Code installed and configured in your development environment.

## Installation Methods

### Method 1: Install from npm (Recommended)

```bash
# Install globally
npm install -g stagewise-mcp-server

# Verify installation
stagewise-mcp-server --help
```

### Method 2: Build from Source

```bash
# Clone the repository
git clone https://github.com/augmentcode/stagewise-mcp-server.git
cd stagewise-mcp-server

# Install dependencies
npm install

# Build the project
npm run build

# Link globally (optional)
npm link

# Test the installation
npm run test:connection
```

## Configuration

### Automatic Setup (Recommended)

The installation process automatically configures Augment Code:

```bash
# Run setup (automatically runs after npm install)
npm run setup
```

This will:
1. Create or update your Augment Code MCP configuration
2. Add the Stagewise MCP server to your server list
3. Configure default connection settings

### Manual Setup

If automatic setup fails, configure manually:

1. **Open Augment Code Settings**
   - Click the gear icon in the upper right of the Augment panel
   - Navigate to the MCP servers section

2. **Add New Server**
   - Click the `+` button next to the `MCP` header
   - Fill in the configuration:

   ```json
   {
     "name": "stagewise",
     "command": "stagewise-mcp-server",
     "description": "Stagewise browser toolbar integration",
     "enabled": true
   }
   ```

3. **Save Configuration**
   - Click save to apply the changes
   - Restart Augment Code if necessary

### Advanced Configuration

For custom configurations, you can specify additional options:

```json
{
  "name": "stagewise",
  "command": "stagewise-mcp-server --config '{\"host\":\"localhost\",\"startPort\":5746,\"timeout\":10000}'",
  "description": "Stagewise with custom settings",
  "enabled": true
}
```

#### Configuration Options

- `host` (default: `localhost`): Stagewise extension host
- `startPort` (default: `5746`): Starting port to search for extension
- `endPort` (default: `5756`): Ending port to search for extension
- `timeout` (default: `5000`): Connection timeout in milliseconds
- `retryAttempts` (default: `3`): Number of retry attempts
- `retryDelay` (default: `1000`): Delay between retries in milliseconds

## Stagewise Toolbar Setup

### 1. Install Toolbar Package

Choose the appropriate package for your framework:

```bash
# React
npm install -D @stagewise/toolbar-react

# Next.js
npm install -D @stagewise/toolbar-next

# Vue.js
npm install -D @stagewise/toolbar-vue

# Nuxt.js
npm install -D @stagewise/toolbar-vue

# Framework-agnostic
npm install -D @stagewise/toolbar
```

### 2. Integrate Toolbar

#### React Example

```tsx
// src/main.tsx
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import { StagewiseToolbar } from '@stagewise/toolbar-react';

// Render main app
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
);

// Initialize toolbar
if (process.env.NODE_ENV === 'development') {
  const toolbarRoot = document.createElement('div');
  toolbarRoot.id = 'stagewise-toolbar-root';
  document.body.appendChild(toolbarRoot);

  createRoot(toolbarRoot).render(
    <StrictMode>
      <StagewiseToolbar config={{ plugins: [] }} />
    </StrictMode>
  );
}
```

#### Next.js Example

```tsx
// src/app/layout.tsx
import { StagewiseToolbar } from '@stagewise/toolbar-next';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <StagewiseToolbar config={{ plugins: [] }} />
        {children}
      </body>
    </html>
  );
}
```

## Verification

### 1. Test MCP Server

```bash
# Test the server directly
npm run test:connection

# Expected output:
# ✅ Server responded to tools/list request
# 📋 Found 5 tools: get_selected_elements, get_page_context, send_ui_comment, get_project_structure, highlight_elements
```

### 2. Test Stagewise Connection

1. Start your development server
2. Open your web application in the browser
3. Verify the Stagewise toolbar appears in the bottom right corner
4. Check that the Stagewise extension is active in your code editor

### 3. Test Integration

1. In Augment Code, try listing available MCP servers:
   ```
   @stagewise --help
   ```

2. Test a basic command:
   ```
   @stagewise send_ui_comment with comment="Test integration"
   ```

## Troubleshooting

### Common Issues

#### 1. MCP Server Not Found

**Error**: `Command not found: stagewise-mcp-server`

**Solutions**:
- Reinstall globally: `npm install -g stagewise-mcp-server`
- Check PATH: `echo $PATH` and verify npm global bin directory is included
- Use full path: `/usr/local/bin/stagewise-mcp-server` (adjust for your system)

#### 2. Connection Failed

**Error**: `Could not connect to Stagewise extension on ports 5746-5756`

**Solutions**:
- Ensure Stagewise extension is installed and running
- Verify your web app has the Stagewise toolbar loaded
- Check that ports 5746-5756 are not blocked by firewall
- Try restarting your code editor

#### 3. Permission Denied

**Error**: `EACCES: permission denied`

**Solutions**:
- Use `sudo npm install -g stagewise-mcp-server` (not recommended)
- Configure npm to use a different directory:
  ```bash
  mkdir ~/.npm-global
  npm config set prefix '~/.npm-global'
  echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
  source ~/.bashrc
  ```

#### 4. Module Not Found

**Error**: `Cannot find module '@modelcontextprotocol/sdk'`

**Solutions**:
- Clear npm cache: `npm cache clean --force`
- Delete node_modules and reinstall: `rm -rf node_modules && npm install`
- Update npm: `npm install -g npm@latest`

### Getting Help

1. **Check Logs**: Look for error messages in the Augment Code console
2. **Test Connection**: Run `npm run test:connection` to diagnose issues
3. **GitHub Issues**: Report bugs at [GitHub Repository](https://github.com/augmentcode/stagewise-mcp-server/issues)
4. **Discord**: Join the Stagewise Discord for community support

## Next Steps

After successful installation:

1. **Read Usage Examples**: Check `examples/USAGE_EXAMPLES.md` for practical use cases
2. **Explore Tools**: Try all available MCP tools with your projects
3. **Customize Configuration**: Adjust settings based on your workflow
4. **Contribute**: Help improve the integration by contributing to the project

## Uninstallation

To remove the Stagewise MCP Server:

```bash
# Remove global installation
npm uninstall -g stagewise-mcp-server

# Remove from Augment Code configuration
# (manually delete the server entry from MCP settings)

# Clean up any remaining files
rm -rf ~/.npm-global/lib/node_modules/stagewise-mcp-server
```
