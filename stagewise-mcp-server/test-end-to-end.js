#!/usr/bin/env node

/**
 * End-to-End Test Suite for Stagewise MCP Server
 * 
 * This comprehensive test verifies all functionality of the MCP server
 * and ensures it's ready for production deployment.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

class E2ETestSuite {
  constructor() {
    this.testResults = [];
    this.server = null;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '📋',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      test: '🧪'
    }[type] || '📋';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async runTest(name, testFn) {
    this.log(`Running test: ${name}`, 'test');
    try {
      await testFn();
      this.testResults.push({ name, status: 'PASS' });
      this.log(`Test passed: ${name}`, 'success');
    } catch (error) {
      this.testResults.push({ name, status: 'FAIL', error: error.message });
      this.log(`Test failed: ${name} - ${error.message}`, 'error');
    }
  }

  async testBuild() {
    return new Promise((resolve, reject) => {
      const build = spawn('npm', ['run', 'build'], { stdio: 'pipe' });
      
      build.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Build failed with code ${code}`));
        }
      });
    });
  }

  async testMCPServerStartup() {
    return new Promise((resolve, reject) => {
      this.server = spawn('node', ['dist/cli.js'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';

      this.server.stdout.on('data', (data) => {
        output += data.toString();
      });

      this.server.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      // Test tools/list request
      const listToolsRequest = JSON.stringify({
        jsonrpc: "2.0",
        id: 1,
        method: "tools/list",
        params: {}
      }) + '\n';

      this.server.stdin.write(listToolsRequest);

      setTimeout(() => {
        if (output && output.includes('tools')) {
          const response = JSON.parse(output.trim());
          if (response.result && response.result.tools && response.result.tools.length === 5) {
            resolve();
          } else {
            reject(new Error('Invalid tools response'));
          }
        } else {
          reject(new Error('No valid response from server'));
        }
      }, 2000);

      this.server.on('error', (error) => {
        reject(new Error(`Server startup failed: ${error.message}`));
      });
    });
  }

  async testToolsAvailability() {
    const expectedTools = [
      'get_selected_elements',
      'get_page_context', 
      'send_ui_comment',
      'get_project_structure',
      'highlight_elements'
    ];

    return new Promise((resolve, reject) => {
      const listToolsRequest = JSON.stringify({
        jsonrpc: "2.0",
        id: 2,
        method: "tools/list",
        params: {}
      }) + '\n';

      let output = '';
      const dataHandler = (data) => {
        output += data.toString();
      };

      this.server.stdout.on('data', dataHandler);
      this.server.stdin.write(listToolsRequest);

      setTimeout(() => {
        this.server.stdout.removeListener('data', dataHandler);
        
        try {
          const response = JSON.parse(output.trim());
          const tools = response.result.tools.map(tool => tool.name);
          
          const missingTools = expectedTools.filter(tool => !tools.includes(tool));
          if (missingTools.length > 0) {
            reject(new Error(`Missing tools: ${missingTools.join(', ')}`));
          } else {
            resolve();
          }
        } catch (error) {
          reject(new Error(`Failed to parse tools response: ${error.message}`));
        }
      }, 1000);
    });
  }

  async testToolExecution() {
    return new Promise((resolve, reject) => {
      const toolCallRequest = JSON.stringify({
        jsonrpc: "2.0",
        id: 3,
        method: "tools/call",
        params: {
          name: "send_ui_comment",
          arguments: {
            comment: "Test comment from E2E test suite",
            mode: "manual"
          }
        }
      }) + '\n';

      let output = '';
      const dataHandler = (data) => {
        output += data.toString();
      };

      this.server.stdout.on('data', dataHandler);
      this.server.stdin.write(toolCallRequest);

      setTimeout(() => {
        this.server.stdout.removeListener('data', dataHandler);
        
        try {
          const response = JSON.parse(output.trim());
          if (response.result && response.result.content) {
            resolve();
          } else {
            reject(new Error('Tool execution failed'));
          }
        } catch (error) {
          reject(new Error(`Failed to parse tool response: ${error.message}`));
        }
      }, 1000);
    });
  }

  async testConfigurationFiles() {
    const requiredFiles = [
      'package.json',
      'README.md',
      'INSTALLATION.md',
      'DEPLOYMENT.md',
      'examples/USAGE_EXAMPLES.md',
      'examples/augment-mcp-config.json',
      'dist/cli.js',
      'dist/server.js',
      'dist/client.js',
      'dist/types.js'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Required file missing: ${file}`);
      }
    }

    // Verify package.json structure
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredFields = ['name', 'version', 'description', 'main', 'bin', 'scripts'];
    
    for (const field of requiredFields) {
      if (!packageJson[field]) {
        throw new Error(`Missing package.json field: ${field}`);
      }
    }
  }

  async testAugmentConfiguration() {
    const configPath = path.join(os.homedir(), 'Library/Application Support/augment/mcp-servers.json');
    
    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      const stagewiseServer = config.servers?.find(server => server.name === 'stagewise');
      
      if (!stagewiseServer) {
        throw new Error('Stagewise server not found in Augment configuration');
      }
      
      if (stagewiseServer.command !== 'stagewise-mcp-server') {
        throw new Error('Invalid command in Augment configuration');
      }
    } else {
      this.log('Augment configuration not found - this is expected if Augment is not installed', 'warning');
    }
  }

  async testCLIHelp() {
    return new Promise((resolve, reject) => {
      const help = spawn('node', ['dist/cli.js', '--help'], { stdio: 'pipe' });
      
      let output = '';
      help.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      help.on('close', (code) => {
        if (code === 0 && output.includes('Stagewise MCP Server')) {
          resolve();
        } else {
          reject(new Error('CLI help command failed'));
        }
      });
    });
  }

  async cleanup() {
    if (this.server) {
      this.server.kill();
      this.server = null;
    }
  }

  async run() {
    this.log('Starting End-to-End Test Suite for Stagewise MCP Server', 'info');
    this.log('='.repeat(60), 'info');

    await this.runTest('Build Process', () => this.testBuild());
    await this.runTest('Configuration Files', () => this.testConfigurationFiles());
    await this.runTest('CLI Help Command', () => this.testCLIHelp());
    await this.runTest('MCP Server Startup', () => this.testMCPServerStartup());
    await this.runTest('Tools Availability', () => this.testToolsAvailability());
    await this.runTest('Tool Execution', () => this.testToolExecution());
    await this.runTest('Augment Configuration', () => this.testAugmentConfiguration());

    await this.cleanup();

    // Print results
    this.log('='.repeat(60), 'info');
    this.log('Test Results Summary:', 'info');
    
    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    
    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      this.log(`${status} ${result.name}`, result.status === 'PASS' ? 'success' : 'error');
      if (result.error) {
        this.log(`   Error: ${result.error}`, 'error');
      }
    });

    this.log('='.repeat(60), 'info');
    this.log(`Tests completed: ${passed} passed, ${failed} failed`, failed === 0 ? 'success' : 'error');
    
    if (failed === 0) {
      this.log('🎉 All tests passed! The Stagewise MCP Server is ready for deployment.', 'success');
      this.log('📋 Next steps:', 'info');
      this.log('   1. Publish to npm: npm publish', 'info');
      this.log('   2. Submit to Augment Code MCP server list', 'info');
      this.log('   3. Update Stagewise community about the integration', 'info');
      process.exit(0);
    } else {
      this.log('❌ Some tests failed. Please fix the issues before deployment.', 'error');
      process.exit(1);
    }
  }
}

// Run the test suite
const testSuite = new E2ETestSuite();
testSuite.run().catch(error => {
  console.error('❌ Test suite failed:', error.message);
  process.exit(1);
});
