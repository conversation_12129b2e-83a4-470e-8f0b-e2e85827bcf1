{"name": "stagewise-mcp-server", "version": "1.0.0", "description": "Model Context Protocol server for Stagewise integration with Augment Code", "main": "dist/cli.js", "bin": {"stagewise-mcp-server": "dist/cli.js"}, "scripts": {"build": "tsc", "dev": "tsx src/server.ts", "start": "node dist/server.js", "test": "node test-end-to-end.js", "test:connection": "tsx src/test-client.ts", "test:e2e": "node test-end-to-end.js", "setup": "node scripts/setup.js", "add-to-augment": "node add-to-augment.js", "verify": "node verify-integration.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "prepare": "npm run build", "postinstall": "npm run setup"}, "keywords": ["mcp", "model-context-protocol", "stagewise", "augment-code", "frontend", "ui", "browser", "ai", "agent"], "author": "Augment Code Community", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/augmentcode/stagewise-mcp-server.git"}, "bugs": {"url": "https://github.com/augmentcode/stagewise-mcp-server/issues"}, "homepage": "https://github.com/augmentcode/stagewise-mcp-server#readme", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "axios": "^1.6.7", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "files": ["dist/**/*", "README.md", "LICENSE"]}