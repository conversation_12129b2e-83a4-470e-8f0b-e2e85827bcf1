{"name": "stagewise-mcp-test-app", "version": "1.0.0", "description": "Test application for Stagewise MCP Server integration with Augment Code", "main": "index.js", "scripts": {"start": "node server.js", "dev": "node server.js"}, "dependencies": {"express": "^4.18.2", "node-fetch": "^3.3.2"}, "devDependencies": {"@stagewise/toolbar": "^0.5.2"}, "keywords": ["stagewise", "mcp", "augment", "test"], "author": "Stagewise MCP Integration", "license": "MIT"}