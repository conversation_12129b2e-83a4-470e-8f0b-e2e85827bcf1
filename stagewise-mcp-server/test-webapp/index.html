<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stagewise MCP Server Test App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🧪 Stagewise MCP Server Test Application</h1>
    
    <div class="status success">
        <strong>✅ Test App Ready!</strong> This page is set up to test the Stagewise MCP Server integration with Augment Code.
    </div>

    <div class="container">
        <h2>🎯 Testing Instructions</h2>
        <ol>
            <li><strong>Install Stagewise Extension</strong> in your code editor</li>
            <li><strong>Restart Augment Code</strong> to load the MCP server</li>
            <li><strong>Select UI elements</strong> below using the Stagewise toolbar</li>
            <li><strong>Test MCP commands</strong> in Augment Code</li>
        </ol>
    </div>

    <div class="card">
        <h3>🔘 Interactive Elements for Testing</h3>
        <p>Use the Stagewise toolbar to select these elements, then test MCP commands:</p>
        
        <div class="form-group">
            <label for="test-input">Test Input Field:</label>
            <input type="text" id="test-input" placeholder="Select this input field with Stagewise">
        </div>

        <div class="form-group">
            <label for="test-textarea">Test Textarea:</label>
            <textarea id="test-textarea" rows="3" placeholder="Select this textarea with Stagewise"></textarea>
        </div>

        <div class="form-group">
            <button id="primary-btn" onclick="showMessage('Primary button clicked!')">Primary Button</button>
            <button id="secondary-btn" onclick="showMessage('Secondary button clicked!')" style="background: #6c757d;">Secondary Button</button>
        </div>
    </div>

    <div class="card">
        <h3>📋 MCP Commands to Test</h3>
        <p>After selecting elements with Stagewise, try these commands in Augment Code:</p>
        
        <div class="status">
            <strong>Basic Commands:</strong><br>
            <code>@stagewise send_ui_comment with comment="Make this button more accessible"</code><br>
            <code>@stagewise send_ui_comment with comment="Change the color of this input field to blue"</code>
        </div>

        <div class="status">
            <strong>Advanced Commands:</strong><br>
            <code>@stagewise send_ui_comment with comment="Add validation to this form" and mode="agent"</code><br>
            <code>@stagewise send_ui_comment with comment="Improve the mobile responsiveness" and files=["style.css"]</code>
        </div>
    </div>

    <div class="card">
        <h3>🔍 Expected Behavior</h3>
        <ul>
            <li><strong>Stagewise Toolbar</strong> should appear in bottom-right corner</li>
            <li><strong>Element Selection</strong> should highlight elements when clicked</li>
            <li><strong>MCP Commands</strong> should execute without errors in Augment</li>
            <li><strong>send_ui_comment</strong> should work with current Stagewise API</li>
            <li><strong>Other tools</strong> may show "not implemented" messages (expected)</li>
        </ul>
    </div>

    <div id="message-area"></div>

    <script>
        function showMessage(text) {
            const messageArea = document.getElementById('message-area');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'status success';
            messageDiv.innerHTML = `<strong>Action:</strong> ${text}`;
            messageArea.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // Initialize Stagewise toolbar (this would normally be done via npm package)
        function initStagewiseToolbar() {
            // This is a placeholder - in a real app you'd use:
            // import { initToolbar } from '@stagewise/toolbar';
            // initToolbar({ plugins: [] });
            
            console.log('🔧 Stagewise toolbar would be initialized here');
            console.log('📋 In a real app, install: npm install -D @stagewise/toolbar');
            console.log('📋 Then import and initialize the toolbar component');
        }

        // Simulate toolbar initialization
        if (typeof window !== 'undefined') {
            initStagewiseToolbar();
        }
    </script>

    <div class="container">
        <h3>📚 Next Steps</h3>
        <p>For a complete integration:</p>
        <ol>
            <li>Install Stagewise toolbar package: <code>npm install -D @stagewise/toolbar</code></li>
            <li>Initialize toolbar in your app (see <a href="https://stagewise.io/docs/quickstart">Stagewise docs</a>)</li>
            <li>Start your development server</li>
            <li>Test the full workflow with real UI elements</li>
        </ol>
    </div>
</body>
</html>
