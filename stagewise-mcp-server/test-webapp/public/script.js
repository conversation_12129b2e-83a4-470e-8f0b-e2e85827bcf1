// Test application JavaScript for Stagewise + Augment Code integration

let messageCounter = 0;

function showMessage(text, type = 'success') {
    const messageArea = document.getElementById('message-area');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.innerHTML = `<strong>Action:</strong> ${text}`;
    messageArea.appendChild(messageDiv);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);
    
    messageCounter++;
}

function handleSubmit() {
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    const message = document.getElementById('message').value;
    
    if (!username || !email || !message) {
        showMessage('Please fill in all fields', 'error');
        return;
    }
    
    showMessage(`Form submitted for ${username}!`);
    
    // Simulate form processing
    setTimeout(() => {
        showMessage('Form processed successfully!');
    }, 1000);
}

function handleReset() {
    document.getElementById('username').value = '';
    document.getElementById('email').value = '';
    document.getElementById('message').value = '';
    showMessage('Form reset');
}

function navigate(page) {
    showMessage(`Navigating to ${page} page`);
    
    // Update status indicator
    const status = document.getElementById('status');
    status.innerHTML = `<span class="dot"></span><span>Navigated to ${page}</span>`;
    
    // Reset after 2 seconds
    setTimeout(() => {
        status.innerHTML = '<span class="dot"></span><span>Ready for testing</span>';
    }, 2000);
}

// Test API connectivity
async function testAPI() {
    try {
        const response = await fetch('/api/test');
        const data = await response.json();
        showMessage(`API Test: ${data.message}`);
        
        // Update app status
        document.getElementById('app-status').textContent = '✅ API Working';
    } catch (error) {
        showMessage('API test failed', 'error');
        document.getElementById('app-status').textContent = '❌ API Error';
    }
}

// Initialize the application
function initializeApp() {
    console.log('🚀 Stagewise MCP Test App initialized');
    console.log('📋 Ready for Augment Code integration testing');
    
    // Test API on load
    testAPI();
    
    // Add some interactive feedback
    const interactiveElements = document.querySelectorAll('button, input, textarea, a');
    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
        });
        
        element.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // Simulate Stagewise toolbar presence check
    checkStagewiseIntegration();
}

function checkStagewiseIntegration() {
    // This would normally check for Stagewise toolbar
    // For now, we'll simulate the check
    
    console.log('🔍 Checking Stagewise integration...');
    
    // Check if running in VS Code context (simplified check)
    const isVSCode = window.location.protocol === 'http:' && window.location.hostname === 'localhost';
    
    if (isVSCode) {
        console.log('✅ Running in development environment');
        showMessage('Development environment detected');
    }
    
    // Update extension status based on check
    setTimeout(() => {
        const extensionStatus = document.getElementById('extension-status');
        extensionStatus.textContent = '✅ Ready for Testing';
    }, 1000);
}

// Keyboard shortcuts for testing
document.addEventListener('keydown', function(event) {
    // Ctrl/Cmd + T for test message
    if ((event.ctrlKey || event.metaKey) && event.key === 't') {
        event.preventDefault();
        showMessage('Keyboard shortcut test: Ctrl/Cmd + T');
    }
    
    // Ctrl/Cmd + R for reset (prevent default browser refresh)
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        handleReset();
        showMessage('Quick reset via keyboard shortcut');
    }
});

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeApp);

// Export functions for potential Stagewise integration
window.StagewiseTestApp = {
    showMessage,
    handleSubmit,
    handleReset,
    navigate,
    testAPI
};
