* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    text-align: center;
}

.header h1 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 2.5rem;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #e8f5e8;
    padding: 8px 16px;
    border-radius: 20px;
    color: #2d5a2d;
    font-weight: 500;
}

.status-indicator .dot {
    width: 8px;
    height: 8px;
    background: #4caf50;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.instructions, .test-section, .mcp-commands, .status-section {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.instructions h2, .test-section h2, .mcp-commands h2, .status-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.instructions ol {
    padding-left: 20px;
}

.instructions li {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.form-card, .ui-card {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.form-card h3, .ui-card h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.4rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus, .form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.button-group {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn.primary {
    background: #667eea;
    color: white;
}

.btn.primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn.secondary {
    background: #6c757d;
    color: white;
}

.btn.secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn.small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.component-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.card p {
    color: #6c757d;
    margin-bottom: 15px;
}

.navigation {
    margin-top: 20px;
}

.navigation nav {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.navigation a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.navigation a:hover {
    background: #667eea;
    color: white;
}

.command-examples {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.command-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.command-card h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.command-card code {
    display: block;
    background: #2c3e50;
    color: #fff;
    padding: 12px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.9rem;
    margin: 10px 0;
    word-wrap: break-word;
}

.command-card p {
    color: #6c757d;
    font-size: 0.95rem;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.status-label {
    font-weight: 600;
    color: #495057;
}

.status-value {
    font-weight: 600;
}

#message-area {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
}

.message {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 10px;
    border: 1px solid #c3e6cb;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .component-grid {
        grid-template-columns: 1fr;
    }
    
    .command-examples {
        grid-template-columns: 1fr;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .navigation nav {
        flex-direction: column;
        align-items: center;
    }
}
