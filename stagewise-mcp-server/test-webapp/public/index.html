<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stagewise + Augment Code Integration Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🧪 Stagewise + Augment Code Integration Test</h1>
            <div class="status-indicator" id="status">
                <span class="dot"></span>
                <span>Ready for testing</span>
            </div>
        </header>

        <div class="instructions">
            <h2>📋 Testing Instructions</h2>
            <ol>
                <li><strong>Restart VS Code</strong> to load the updated MCP configuration</li>
                <li><strong>Check Augment MCP Settings</strong> - Stagewise should now appear</li>
                <li><strong>Select UI elements</strong> below using Stagewise toolbar (if available)</li>
                <li><strong>Test MCP commands</strong> in Augment Code chat</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🎯 Interactive Test Elements</h2>
            
            <div class="form-card">
                <h3>Form Elements</h3>
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" placeholder="Enter your username">
                </div>
                
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" placeholder="Enter your email">
                </div>
                
                <div class="form-group">
                    <label for="message">Message:</label>
                    <textarea id="message" name="message" rows="4" placeholder="Enter your message here..."></textarea>
                </div>
                
                <div class="button-group">
                    <button id="submit-btn" class="btn primary" onclick="handleSubmit()">Submit Form</button>
                    <button id="reset-btn" class="btn secondary" onclick="handleReset()">Reset</button>
                </div>
            </div>

            <div class="ui-card">
                <h3>UI Components</h3>
                <div class="component-grid">
                    <div class="card" id="card-1">
                        <h4>Card Component</h4>
                        <p>This is a test card component for UI selection.</p>
                        <button class="btn small" onclick="showMessage('Card 1 clicked')">Action</button>
                    </div>
                    
                    <div class="card" id="card-2">
                        <h4>Another Card</h4>
                        <p>Select this card to test element targeting.</p>
                        <button class="btn small" onclick="showMessage('Card 2 clicked')">Action</button>
                    </div>
                </div>
                
                <div class="navigation">
                    <nav id="main-nav">
                        <a href="#" onclick="navigate('home')">Home</a>
                        <a href="#" onclick="navigate('about')">About</a>
                        <a href="#" onclick="navigate('contact')">Contact</a>
                    </nav>
                </div>
            </div>
        </div>

        <div class="mcp-commands">
            <h2>🤖 MCP Commands to Test</h2>
            <div class="command-examples">
                <div class="command-card">
                    <h4>Basic UI Comment</h4>
                    <code>@stagewise send_ui_comment with comment="Make this button more accessible"</code>
                    <p>Test sending UI feedback about selected elements</p>
                </div>
                
                <div class="command-card">
                    <h4>Style Changes</h4>
                    <code>@stagewise send_ui_comment with comment="Change the color scheme to dark mode"</code>
                    <p>Request visual changes to the interface</p>
                </div>
                
                <div class="command-card">
                    <h4>Functionality Request</h4>
                    <code>@stagewise send_ui_comment with comment="Add form validation to this input field"</code>
                    <p>Request functional improvements</p>
                </div>
            </div>
        </div>

        <div class="status-section">
            <h2>📊 Integration Status</h2>
            <div class="status-grid">
                <div class="status-item">
                    <span class="status-label">MCP Server:</span>
                    <span class="status-value" id="mcp-status">✅ Built & Ready</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Augment Config:</span>
                    <span class="status-value" id="config-status">✅ Updated</span>
                </div>
                <div class="status-item">
                    <span class="status-label">VS Code Extension:</span>
                    <span class="status-value" id="extension-status">✅ Installed</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Test App:</span>
                    <span class="status-value" id="app-status">✅ Running</span>
                </div>
            </div>
        </div>

        <div id="message-area"></div>
    </div>

    <script type="module">
        // Initialize Stagewise toolbar first
        import { initToolbar } from '/node_modules/@stagewise/toolbar/dist/index.js';

        // Configure Stagewise
        const stagewiseConfig = {
            plugins: [],
        };

        // Initialize toolbar in development mode
        if (window.location.hostname === 'localhost') {
            try {
                initToolbar(stagewiseConfig);
                console.log('✅ Stagewise toolbar initialized');

                // Show success message after a delay
                setTimeout(() => {
                    if (window.showMessage) {
                        window.showMessage('Stagewise toolbar loaded successfully!');
                    }
                }, 1000);
            } catch (error) {
                console.error('❌ Failed to initialize Stagewise toolbar:', error);
            }
        }
    </script>
    <script src="script.js"></script>
</body>
</html>
