const express = require('express');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Main route
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API endpoint for testing
app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'Stagewise MCP Test API', 
    timestamp: new Date().toISOString(),
    status: 'working'
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Stagewise MCP Test App running on http://localhost:${PORT}`);
  console.log('📋 Ready for Augment Code + Stagewise integration testing');
});
