const express = require('express');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Serve node_modules for Stagewise toolbar
app.use('/node_modules', express.static(path.join(__dirname, 'node_modules')));

// Main route
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API endpoint for testing
app.get('/api/test', (req, res) => {
  res.json({
    message: 'Stagewise MCP Test API',
    timestamp: new Date().toISOString(),
    status: 'working'
  });
});

// Stagewise connection test endpoint
app.get('/api/stagewise/status', async (req, res) => {
  try {
    // Test connection to Stagewise extension
    const fetch = (await import('node-fetch')).default;

    for (let port = 5746; port <= 5756; port++) {
      try {
        const response = await fetch(`http://localhost:${port}/ping/stagewise`, {
          timeout: 1000
        });
        if (response.ok) {
          const result = await response.text();
          if (result === 'stagewise') {
            return res.json({
              connected: true,
              port: port,
              message: `Stagewise extension connected on port ${port}`
            });
          }
        }
      } catch (error) {
        // Continue to next port
      }
    }

    res.json({
      connected: false,
      message: 'Stagewise extension not found on ports 5746-5756'
    });
  } catch (error) {
    res.status(500).json({
      connected: false,
      error: error.message
    });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Stagewise MCP Test App running on http://localhost:${PORT}`);
  console.log('📋 Ready for Augment Code + Stagewise integration testing');
  console.log('🔗 Stagewise toolbar will be available at startup');
});
