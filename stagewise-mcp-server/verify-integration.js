#!/usr/bin/env node

/**
 * Verification script for Stagewise MCP Server integration with Augment Code
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

function checkAugmentConfig() {
  const configPath = path.join(os.homedir(), 'Library/Application Support/augment/mcp-servers.json');
  
  console.log('🔍 Checking Augment Code configuration...');
  
  if (!fs.existsSync(configPath)) {
    console.log('❌ Augment configuration file not found');
    console.log('   Expected at:', configPath);
    return false;
  }
  
  try {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    const stagewiseServer = config.servers?.find(server => server.name === 'stagewise');
    
    if (!stagewiseServer) {
      console.log('❌ Stagewise server not found in Augment configuration');
      return false;
    }
    
    console.log('✅ Stagewise server found in Augment configuration');
    console.log('   Name:', stagewiseServer.name);
    console.log('   Command:', stagewiseServer.command);
    console.log('   Args:', JSON.stringify(stagewiseServer.args));
    console.log('   Enabled:', stagewiseServer.enabled);
    
    // Check if server file exists
    const serverPath = stagewiseServer.args?.[0] || stagewiseServer.command;
    if (fs.existsSync(serverPath)) {
      console.log('✅ Server file exists at:', serverPath);
    } else {
      console.log('❌ Server file not found at:', serverPath);
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ Error reading Augment configuration:', error.message);
    return false;
  }
}

function checkServerBuild() {
  console.log('\n🔍 Checking server build...');
  
  const requiredFiles = [
    'dist/cli.js',
    'dist/server.js', 
    'dist/client.js',
    'dist/types.js'
  ];
  
  let allFilesExist = true;
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      console.log('✅', file);
    } else {
      console.log('❌', file, '(missing)');
      allFilesExist = false;
    }
  }
  
  return allFilesExist;
}

function printTestInstructions() {
  console.log('\n📋 Testing Instructions:');
  console.log('');
  console.log('1. **Restart Augment Code** to load the new MCP server');
  console.log('');
  console.log('2. **Check MCP Settings** - Stagewise should appear alongside other servers');
  console.log('');
  console.log('3. **Test Basic Command** in Augment Code:');
  console.log('   @stagewise send_ui_comment with comment="Test integration"');
  console.log('');
  console.log('4. **For Full Testing** (optional):');
  console.log('   - Install Stagewise extension in your code editor');
  console.log('   - Set up Stagewise toolbar in a web application');
  console.log('   - Test element selection and UI comments');
  console.log('');
  console.log('5. **Test Web App** available at:');
  console.log('   http://localhost:8080');
  console.log('');
}

function main() {
  console.log('🧪 Stagewise MCP Server Integration Verification\n');
  console.log('='.repeat(60));
  
  const configOk = checkAugmentConfig();
  const buildOk = checkServerBuild();
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 Verification Summary:');
  console.log('');
  console.log('Augment Configuration:', configOk ? '✅ OK' : '❌ FAILED');
  console.log('Server Build:', buildOk ? '✅ OK' : '❌ FAILED');
  
  if (configOk && buildOk) {
    console.log('\n🎉 Integration is ready for testing!');
    printTestInstructions();
  } else {
    console.log('\n❌ Issues found. Please fix the above problems and try again.');
    
    if (!buildOk) {
      console.log('\nTo fix build issues:');
      console.log('   npm run build');
    }
    
    if (!configOk) {
      console.log('\nTo fix configuration:');
      console.log('   node add-to-augment.js');
    }
  }
}

if (require.main === module) {
  main();
}
