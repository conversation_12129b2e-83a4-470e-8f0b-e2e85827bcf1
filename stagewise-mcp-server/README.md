# Stagewise MCP Server

A Model Context Protocol (MCP) server that bridges [Augment Code](https://augmentcode.com) with [Stagewise](https://stagewise.io)'s browser toolbar functionality for enhanced frontend development workflows.

## Features

- 🧠 **Get Selected Elements**: Access currently selected DOM elements from Stagewise toolbar
- 🌐 **Page Context**: Retrieve current page information including URL, title, viewport, and selected elements
- 💬 **Send UI Comments**: Send comments/instructions about UI elements directly to AI agents
- 📁 **Project Structure**: Get current project structure and file information
- 🎯 **Element Highlighting**: Highlight specific elements in the browser for visual feedback

## Prerequisites

1. **Stagewise Extension**: Install the Stagewise extension in your code editor:
   - **Cursor**: `cursor:extension/stagewise.stagewise-vscode-extension`
   - **VS Code**: `vscode:extension/stagewise.stagewise-vscode-extension`
   - **Windsurf**: `windsurf:extension/stagewise.stagewise-vscode-extension`

2. **Stagewise Toolbar**: Set up the Stagewise toolbar in your web application (see [Stagewise Quickstart](https://stagewise.io/docs/quickstart))

3. **Augment Code**: Have Augment Code installed and configured

## Installation

### Option 1: Install from npm (Ready for publication)

```bash
npm install -g stagewise-mcp-server
```

### Option 2: Build from source

```bash
git clone https://github.com/augmentcode/stagewise-mcp-server.git
cd stagewise-mcp-server
npm install
npm run build
npm link

# Test the installation
npm run test:connection
```

## Configuration

### Add to Augment Code MCP Settings

1. Open Augment Code settings panel (gear icon in upper right)
2. Navigate to the MCP servers section
3. Click the `+` button to add a new server
4. Configure the server:

**Name**: `stagewise`

**Command**: `stagewise-mcp-server`

Alternatively, you can specify a custom configuration:

**Command**: `stagewise-mcp-server --config '{"host":"localhost","startPort":5746,"endPort":5756,"timeout":5000}'`

### Configuration Options

The server accepts the following configuration options:

- `host` (default: `localhost`): Stagewise extension host
- `startPort` (default: `5746`): Starting port to search for extension
- `endPort` (default: `5756`): Ending port to search for extension  
- `timeout` (default: `5000`): Connection timeout in milliseconds
- `retryAttempts` (default: `3`): Number of retry attempts
- `retryDelay` (default: `1000`): Delay between retries in milliseconds

## Available Tools

### `get_selected_elements`

Get currently selected DOM elements from the Stagewise toolbar.

**Parameters:**
- `includeParents` (boolean, optional): Whether to include parent element information
- `maxDepth` (number, optional): Maximum depth for parent element traversal

**Example:**
```json
{
  "includeParents": true,
  "maxDepth": 2
}
```

### `get_page_context`

Get current page context including URL, title, viewport info, and selected elements.

**Parameters:**
- `includeElements` (boolean, optional): Whether to include selected elements in the context

**Example:**
```json
{
  "includeElements": true
}
```

### `send_ui_comment`

Send a comment/instruction about selected UI elements to the AI agent.

**Parameters:**
- `comment` (string, required): The comment/instruction to send
- `mode` (string, optional): Processing mode (`agent`, `ask`, `manual`)
- `files` (array, optional): Additional project files to link
- `model` (string, optional): Specific AI model to use

**Example:**
```json
{
  "comment": "Make this button larger and change the color to blue",
  "mode": "agent",
  "files": ["src/components/Button.tsx"]
}
```

### `get_project_structure`

Get the current project structure and file information.

**Parameters:**
- `includeNodeModules` (boolean, optional): Whether to include node_modules
- `maxDepth` (number, optional): Maximum directory depth to traverse
- `fileExtensions` (array, optional): Filter by specific file extensions

**Example:**
```json
{
  "includeNodeModules": false,
  "maxDepth": 3,
  "fileExtensions": [".tsx", ".ts", ".jsx", ".js"]
}
```

### `highlight_elements`

Highlight specific elements in the browser for visual feedback.

**Parameters:**
- `xpaths` (array, required): XPath selectors of elements to highlight
- `duration` (number, optional): Duration to highlight in milliseconds
- `color` (string, optional): Highlight color in hex format

**Example:**
```json
{
  "xpaths": ["/html/body/div[1]/button[1]"],
  "duration": 5000,
  "color": "#ff0000"
}
```

## Usage Examples

### Basic Workflow

1. **Select UI elements** in your web app using the Stagewise toolbar
2. **Use Augment Agent** to analyze the selected elements:
   ```
   @stagewise get_selected_elements with includeParents=true
   ```
3. **Send instructions** about the UI:
   ```
   @stagewise send_ui_comment with comment="Make this button more accessible by adding proper ARIA labels"
   ```

### Advanced Usage

Get comprehensive page context and send targeted feedback:

```
@stagewise get_page_context
@stagewise send_ui_comment with comment="The selected form needs better validation styling" and files=["src/styles/forms.css", "src/components/Form.tsx"]
```

## Troubleshooting

### Connection Issues

1. **Ensure Stagewise extension is running**: Check that the extension is active in your code editor
2. **Verify toolbar is loaded**: Make sure the Stagewise toolbar appears in your web application
3. **Check port availability**: The extension runs on ports 5746-5756 by default
4. **Firewall settings**: Ensure localhost connections are allowed

### Common Errors

- `StagewiseConnectionError`: Cannot connect to extension - check if Stagewise is running
- `StagewiseAPIError`: API call failed - verify extension version compatibility
- `session_mismatch`: Session expired - the MCP server will automatically reconnect

## Current Status

✅ **Implemented Features:**
- MCP server with 5 tools (get_selected_elements, get_page_context, send_ui_comment, get_project_structure, highlight_elements)
- Automatic Augment Code configuration
- Connection discovery for Stagewise extension (ports 5746-5756)
- Comprehensive error handling and user feedback
- CLI with help and configuration options

⚠️ **Requires Stagewise Enhancement:**
Some features require enhancements to the Stagewise extension:
- `get_selected_elements`: Real-time element selection API
- `get_page_context`: Page metadata and context API
- `get_project_structure`: Project file structure API
- `highlight_elements`: Browser element highlighting API

✅ **Fully Working:**
- `send_ui_comment`: Send comments to AI agents (works with current Stagewise API)

## Development

### Building

```bash
npm run build
```

### Development Mode

```bash
npm run dev
```

### Testing

```bash
npm run test:connection
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Related Projects

- [Stagewise](https://github.com/stagewise-io/stagewise) - Visual vibe coding browser toolbar
- [Augment Code](https://augmentcode.com) - AI-powered code editor integration
- [Model Context Protocol](https://github.com/modelcontextprotocol) - Open protocol for AI-tool integration
