#!/usr/bin/env node

/**
 * Complete integration test for Stagewise MCP Server + Augment Code
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { spawn } = require('child_process');

console.log('🧪 Stagewise + Augment Code Integration Test\n');
console.log('='.repeat(60));

// Test 1: Check MCP Server Build
function testServerBuild() {
  console.log('\n1️⃣ Testing MCP Server Build...');
  
  const requiredFiles = [
    'dist/cli.js',
    'dist/server.js', 
    'dist/client.js',
    'dist/types.js'
  ];
  
  let allFilesExist = true;
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      console.log('   ✅', file);
    } else {
      console.log('   ❌', file, '(missing)');
      allFilesExist = false;
    }
  }
  
  return allFilesExist;
}

// Test 2: Check Augment Configuration
function testAugmentConfig() {
  console.log('\n2️⃣ Testing Augment Configuration...');
  
  const configPath = path.join(
    os.homedir(), 
    'Library/Application Support/Code/User/globalStorage/augment.vscode-augment/augment-global-state/mcpServers.json'
  );
  
  if (!fs.existsSync(configPath)) {
    console.log('   ❌ Augment config file not found');
    return false;
  }
  
  try {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    const stagewiseServer = config.find(server => server.name === 'stagewise');
    
    if (!stagewiseServer) {
      console.log('   ❌ Stagewise server not found in config');
      return false;
    }
    
    console.log('   ✅ Stagewise server found in Augment config');
    console.log('   📋 Name:', stagewiseServer.name);
    console.log('   📋 Type:', stagewiseServer.type);
    console.log('   📋 ID:', stagewiseServer.id);
    
    return true;
  } catch (error) {
    console.log('   ❌ Error reading config:', error.message);
    return false;
  }
}

// Test 3: Test MCP Server Functionality
function testMCPServer() {
  return new Promise((resolve) => {
    console.log('\n3️⃣ Testing MCP Server Functionality...');
    
    const testInput = JSON.stringify({
      jsonrpc: "2.0",
      id: 1,
      method: "tools/list",
      params: {}
    });
    
    const serverProcess = spawn('node', ['dist/cli.js'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    let hasStarted = false;
    
    serverProcess.stdout.on('data', (data) => {
      const dataStr = data.toString();
      if (dataStr.includes('Stagewise MCP Server running')) {
        hasStarted = true;
        console.log('   ✅ Server started successfully');
      } else if (hasStarted && dataStr.trim()) {
        output += dataStr;
      }
    });
    
    serverProcess.stderr.on('data', (data) => {
      console.log('   ⚠️ Server stderr:', data.toString());
    });
    
    serverProcess.on('close', (code) => {
      try {
        const response = JSON.parse(output);
        if (response.result && response.result.tools) {
          console.log('   ✅ Tools list retrieved successfully');
          console.log('   📋 Available tools:', response.result.tools.length);
          response.result.tools.forEach(tool => {
            console.log('      -', tool.name);
          });
          resolve(true);
        } else {
          console.log('   ❌ Invalid response format');
          resolve(false);
        }
      } catch (error) {
        console.log('   ❌ Error parsing response:', error.message);
        resolve(false);
      }
    });
    
    // Send test input
    serverProcess.stdin.write(testInput + '\n');
    serverProcess.stdin.end();
    
    // Timeout after 5 seconds
    setTimeout(() => {
      serverProcess.kill();
      console.log('   ⚠️ Server test timed out');
      resolve(false);
    }, 5000);
  });
}

// Test 4: Test send_ui_comment Tool
function testUICommentTool() {
  return new Promise((resolve) => {
    console.log('\n4️⃣ Testing send_ui_comment Tool...');
    
    const testInput = JSON.stringify({
      jsonrpc: "2.0",
      id: 2,
      method: "tools/call",
      params: {
        name: "send_ui_comment",
        arguments: {
          comment: "Test comment from integration test"
        }
      }
    });
    
    const serverProcess = spawn('node', ['dist/cli.js'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    let hasStarted = false;
    
    serverProcess.stdout.on('data', (data) => {
      const dataStr = data.toString();
      if (dataStr.includes('Stagewise MCP Server running')) {
        hasStarted = true;
      } else if (hasStarted && dataStr.trim()) {
        output += dataStr;
      }
    });
    
    serverProcess.on('close', (code) => {
      try {
        const response = JSON.parse(output);
        if (response.result && response.result.content) {
          const content = JSON.parse(response.result.content[0].text);
          if (content.error && content.errorCode === 'connection_error') {
            console.log('   ✅ Tool executed correctly (expected connection error)');
            console.log('   📋 Error:', content.error);
            resolve(true);
          } else if (content.success) {
            console.log('   ✅ Tool executed successfully');
            resolve(true);
          } else {
            console.log('   ❌ Unexpected response:', content);
            resolve(false);
          }
        } else {
          console.log('   ❌ Invalid response format');
          resolve(false);
        }
      } catch (error) {
        console.log('   ❌ Error parsing response:', error.message);
        resolve(false);
      }
    });
    
    // Send test input
    serverProcess.stdin.write(testInput + '\n');
    serverProcess.stdin.end();
    
    // Timeout after 5 seconds
    setTimeout(() => {
      serverProcess.kill();
      console.log('   ⚠️ Tool test timed out');
      resolve(false);
    }, 5000);
  });
}

// Test 5: Check VS Code Extension
function testVSCodeExtension() {
  console.log('\n5️⃣ Testing VS Code Extension...');
  
  const extensionPath = path.join(os.homedir(), '.vscode/extensions');
  
  if (!fs.existsSync(extensionPath)) {
    console.log('   ❌ VS Code extensions directory not found');
    return false;
  }
  
  const extensions = fs.readdirSync(extensionPath);
  const stagewiseExtension = extensions.find(ext => ext.includes('stagewise'));
  
  if (stagewiseExtension) {
    console.log('   ✅ Stagewise extension found:', stagewiseExtension);
    return true;
  } else {
    console.log('   ❌ Stagewise extension not found');
    return false;
  }
}

// Main test runner
async function runTests() {
  const results = {
    serverBuild: testServerBuild(),
    augmentConfig: testAugmentConfig(),
    mcpServer: await testMCPServer(),
    uiCommentTool: await testUICommentTool(),
    vsCodeExtension: testVSCodeExtension()
  };
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 Test Results Summary:');
  console.log('');
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    console.log(`${status} ${testName}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  
  console.log('\n' + '='.repeat(60));
  
  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED! Integration is ready.');
    console.log('\n🚀 Next Steps:');
    console.log('1. Restart VS Code to load the MCP server');
    console.log('2. Open Augment Code MCP settings');
    console.log('3. Verify Stagewise appears in the server list');
    console.log('4. Test with: @stagewise send_ui_comment with comment="Hello World"');
    console.log('5. Visit http://localhost:3000 for interactive testing');
  } else {
    console.log('❌ Some tests failed. Please fix the issues above.');
  }
  
  console.log('\n📋 Integration Status: ' + (allPassed ? 'READY' : 'NEEDS FIXES'));
}

if (require.main === module) {
  runTests().catch(console.error);
}
