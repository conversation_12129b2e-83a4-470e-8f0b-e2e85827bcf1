#!/usr/bin/env node

/**
 * Simple test script to verify MCP server functionality
 */

const { spawn } = require('child_process');

function testMCPServer() {
  console.log('🧪 Testing Stagewise MCP Server...\n');

  const server = spawn('node', ['dist/cli.js'], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let output = '';
  let errorOutput = '';

  server.stdout.on('data', (data) => {
    output += data.toString();
  });

  server.stderr.on('data', (data) => {
    errorOutput += data.toString();
  });

  // Test 1: List tools
  const listToolsRequest = JSON.stringify({
    jsonrpc: "2.0",
    id: 1,
    method: "tools/list",
    params: {}
  }) + '\n';

  server.stdin.write(listToolsRequest);

  setTimeout(() => {
    if (output) {
      console.log('✅ Server responded to tools/list request:');
      try {
        const response = JSON.parse(output.trim());
        if (response.result && response.result.tools) {
          console.log(`📋 Found ${response.result.tools.length} tools:`);
          response.result.tools.forEach(tool => {
            console.log(`  - ${tool.name}: ${tool.description}`);
          });
        }
      } catch (e) {
        console.log('Raw output:', output);
      }
    } else {
      console.log('⚠️  No output received from server');
    }

    if (errorOutput) {
      console.log('\n📝 Server logs:', errorOutput);
    }

    console.log('\n🎉 MCP Server test completed!');
    console.log('📋 The server is ready to be used with Augment Code.');
    
    server.kill();
    process.exit(0);
  }, 2000);

  server.on('error', (error) => {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  });
}

testMCPServer();
