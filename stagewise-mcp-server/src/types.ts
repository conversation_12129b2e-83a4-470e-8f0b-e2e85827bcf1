/**
 * Stagewise MCP Server Types
 * 
 * This file defines the types and interfaces for the Stagewise MCP server
 * that bridges Augment Code with Stagewise's browser toolbar functionality.
 */

import { z } from 'zod';

// Define the type first
export type SelectedElement = {
  nodeType: string;
  xpath: string;
  attributes: Record<string, string | boolean | number>;
  textContent: string;
  ownProperties: Record<string, any>;
  boundingClientRect: {
    top: number;
    left: number;
    height: number;
    width: number;
  };
  pluginInfo: Array<{
    pluginName: string;
    content: string;
  }>;
  parent?: SelectedElement;
};

// Re-export key types from Stagewise's agent interface
export const selectedElementSchema = z.object({
  nodeType: z.string().describe('The node type of the element (e.g., "DIV", "BUTTON")'),
  xpath: z.string().describe('The XPath of the element'),
  attributes: z.record(z.union([z.string(), z.boolean(), z.number()]))
    .describe('Element attributes (class, id, style, etc.)'),
  textContent: z.string().describe('Text content of the element'),
  ownProperties: z.record(z.any()).describe('Custom properties of the element'),
  boundingClientRect: z.object({
    top: z.number(),
    left: z.number(),
    height: z.number(),
    width: z.number(),
  }).describe('Element position and dimensions'),
  pluginInfo: z.array(z.object({
    pluginName: z.string(),
    content: z.string(),
  })).describe('Additional plugin-provided context'),
  parent: z.any().optional().describe('Parent element information (recursive)'),
});

export const pageContextSchema = z.object({
  currentUrl: z.string().url().nullable().describe('Current page URL'),
  currentTitle: z.string().nullable().describe('Current page title'),
  currentZoomLevel: z.number().describe('Browser zoom level'),
  viewportResolution: z.object({
    width: z.number(),
    height: z.number(),
  }).describe('Viewport dimensions'),
  devicePixelRatio: z.number().describe('Device pixel ratio'),
  userAgent: z.string().describe('Browser user agent'),
  locale: z.string().describe('Browser locale'),
  selectedElements: z.array(selectedElementSchema).describe('Currently selected elements'),
});

export type PageContext = z.infer<typeof pageContextSchema>;

export const sessionInfoSchema = z.object({
  sessionId: z.string().optional().describe('Current session ID'),
  appName: z.string().describe('IDE name (VS Code, Cursor, etc.)'),
  displayName: z.string().describe('Human-readable window identifier'),
  port: z.number().describe('Port number the extension is running on'),
});

export type SessionInfo = z.infer<typeof sessionInfoSchema>;

// MCP Tool argument schemas
export const getSelectedElementsArgsSchema = z.object({
  includeParents: z.boolean().optional().default(false)
    .describe('Whether to include parent element information'),
  maxDepth: z.number().optional().default(3)
    .describe('Maximum depth for parent element traversal'),
});

export const getPageContextArgsSchema = z.object({
  includeElements: z.boolean().optional().default(true)
    .describe('Whether to include selected elements in the context'),
});

export const sendUICommentArgsSchema = z.object({
  comment: z.string().describe('The comment/instruction to send about the UI elements'),
  mode: z.enum(['agent', 'ask', 'manual']).optional().default('agent')
    .describe('The mode to use for processing the comment'),
  files: z.array(z.string()).optional()
    .describe('Additional project files to link to the comment'),
  model: z.string().optional()
    .describe('Specific AI model to use for processing'),
});

export const getProjectStructureArgsSchema = z.object({
  includeNodeModules: z.boolean().optional().default(false)
    .describe('Whether to include node_modules in the structure'),
  maxDepth: z.number().optional().default(3)
    .describe('Maximum directory depth to traverse'),
  fileExtensions: z.array(z.string()).optional()
    .describe('Filter by specific file extensions (e.g., [".tsx", ".ts"])'),
});

export const highlightElementsArgsSchema = z.object({
  xpaths: z.array(z.string()).describe('XPath selectors of elements to highlight'),
  duration: z.number().optional().default(3000)
    .describe('Duration to highlight elements in milliseconds'),
  color: z.string().optional().default('#ff0000')
    .describe('Highlight color (hex format)'),
});

// Response types
export const stagewiseResponseSchema = z.object({
  success: z.boolean().describe('Whether the operation was successful'),
  error: z.string().optional().describe('Error message if operation failed'),
  errorCode: z.enum(['session_mismatch', 'connection_error', 'invalid_request']).optional()
    .describe('Specific error code for programmatic handling'),
  data: z.any().optional().describe('Response data'),
});

export type StagewiseResponse = z.infer<typeof stagewiseResponseSchema>;

// Connection configuration
export const connectionConfigSchema = z.object({
  host: z.string().default('localhost').describe('Stagewise extension host'),
  startPort: z.number().default(5746).describe('Starting port to search for extension'),
  endPort: z.number().default(5756).describe('Ending port to search for extension'),
  timeout: z.number().default(5000).describe('Connection timeout in milliseconds'),
  retryAttempts: z.number().default(3).describe('Number of retry attempts'),
  retryDelay: z.number().default(1000).describe('Delay between retries in milliseconds'),
});

export type ConnectionConfig = z.infer<typeof connectionConfigSchema>;

// Error types
export class StagewiseConnectionError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'StagewiseConnectionError';
  }
}

export class StagewiseAPIError extends Error {
  constructor(message: string, public code?: string, public statusCode?: number) {
    super(message);
    this.name = 'StagewiseAPIError';
  }
}
