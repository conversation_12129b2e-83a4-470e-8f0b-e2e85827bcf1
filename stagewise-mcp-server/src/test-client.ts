#!/usr/bin/env node

/**
 * Test client for the Stagewise MCP Server
 * 
 * This script tests the basic functionality of the MCP server
 * and its connection to the Stagewise extension.
 */

import { StagewiseClient } from './client';
import { connectionConfigSchema } from './types';

async function testConnection() {
  console.log('🧪 Testing Stagewise MCP Server Connection...\n');

  const config = connectionConfigSchema.parse({
    host: 'localhost',
    startPort: 5746,
    endPort: 5756,
    timeout: 5000,
  });

  const client = new StagewiseClient(config);

  try {
    // Test 1: Get session info
    console.log('📋 Test 1: Getting session info...');
    const sessionInfo = await client.getSessionInfo();
    console.log('✅ Session info:', JSON.stringify(sessionInfo, null, 2));
    console.log();

    // Test 2: Test UI comment (the main working feature)
    console.log('💬 Test 2: Sending UI comment...');
    try {
      const commentResult = await client.sendUIComment({
        comment: 'Test comment from MCP server - please ignore this automated test',
        mode: 'manual',
      });
      console.log('✅ UI comment result:', JSON.stringify(commentResult, null, 2));
    } catch (error: any) {
      console.log('⚠️  UI comment test failed:', error.message);
    }
    console.log();

    // Test 3: Test not-yet-implemented features
    console.log('🚧 Test 3: Testing not-yet-implemented features...');
    
    try {
      await client.getSelectedElements({ includeParents: false, maxDepth: 3 });
    } catch (error: any) {
      console.log('⚠️  get_selected_elements (expected):', error.message);
    }

    try {
      await client.getPageContext({ includeElements: true });
    } catch (error: any) {
      console.log('⚠️  get_page_context (expected):', error.message);
    }

    try {
      await client.getProjectStructure({ includeNodeModules: false, maxDepth: 3 });
    } catch (error: any) {
      console.log('⚠️  get_project_structure (expected):', error.message);
    }

    try {
      await client.highlightElements({ xpaths: ['/html/body'], duration: 3000, color: '#ff0000' });
    } catch (error: any) {
      console.log('⚠️  highlight_elements (expected):', error.message);
    }

    console.log('\n🎉 Connection test completed successfully!');
    console.log('📝 Note: Some features require enhancements to the Stagewise extension.');

  } catch (error: any) {
    console.error('❌ Connection test failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Make sure Stagewise extension is installed and running');
    console.error('2. Ensure you have a web app with Stagewise toolbar loaded');
    console.error('3. Check that ports 5746-5756 are available');
    console.error('4. Verify your firewall allows localhost connections');
    process.exit(1);
  } finally {
    client.disconnect();
  }
}

async function main() {
  await testConnection();
}

if (require.main === module) {
  main().catch((error) => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}
