#!/usr/bin/env node

/**
 * Stagewise MCP Server
 * 
 * A Model Context Protocol server that bridges Augment Code with Stagewise's
 * browser toolbar functionality for enhanced frontend development workflows.
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';
import { StagewiseClient } from './client.js';
import {
  getSelectedElementsArgsSchema,
  getPageContextArgsSchema,
  sendUICommentArgsSchema,
  getProjectStructureArgsSchema,
  highlightElementsArgsSchema,
  ConnectionConfig,
  connectionConfigSchema,
} from './types.js';

class StagewiseMCPServer {
  private server: Server;
  private client: StagewiseClient;

  constructor(config?: Partial<ConnectionConfig>) {
    this.server = new Server(
      {
        name: 'stagewise-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    const connectionConfig = connectionConfigSchema.parse(config || {});
    this.client = new StagewiseClient(connectionConfig);

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  private setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'get_selected_elements',
            description: 'Get currently selected DOM elements from the Stagewise toolbar',
            inputSchema: {
              type: 'object',
              properties: {
                includeParents: {
                  type: 'boolean',
                  description: 'Whether to include parent element information',
                  default: false,
                },
                maxDepth: {
                  type: 'number',
                  description: 'Maximum depth for parent element traversal',
                  default: 3,
                },
              },
            },
          },
          {
            name: 'get_page_context',
            description: 'Get current page context including URL, title, viewport info, and selected elements',
            inputSchema: {
              type: 'object',
              properties: {
                includeElements: {
                  type: 'boolean',
                  description: 'Whether to include selected elements in the context',
                  default: true,
                },
              },
            },
          },
          {
            name: 'send_ui_comment',
            description: 'Send a comment/instruction about selected UI elements to the AI agent',
            inputSchema: {
              type: 'object',
              properties: {
                comment: {
                  type: 'string',
                  description: 'The comment/instruction to send about the UI elements',
                },
                mode: {
                  type: 'string',
                  enum: ['agent', 'ask', 'manual'],
                  description: 'The mode to use for processing the comment',
                  default: 'agent',
                },
                files: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Additional project files to link to the comment',
                },
                model: {
                  type: 'string',
                  description: 'Specific AI model to use for processing',
                },
              },
              required: ['comment'],
            },
          },
          {
            name: 'get_project_structure',
            description: 'Get the current project structure and file information',
            inputSchema: {
              type: 'object',
              properties: {
                includeNodeModules: {
                  type: 'boolean',
                  description: 'Whether to include node_modules in the structure',
                  default: false,
                },
                maxDepth: {
                  type: 'number',
                  description: 'Maximum directory depth to traverse',
                  default: 3,
                },
                fileExtensions: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Filter by specific file extensions',
                },
              },
            },
          },
          {
            name: 'highlight_elements',
            description: 'Highlight specific elements in the browser for visual feedback',
            inputSchema: {
              type: 'object',
              properties: {
                xpaths: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'XPath selectors of elements to highlight',
                },
                duration: {
                  type: 'number',
                  description: 'Duration to highlight elements in milliseconds',
                  default: 3000,
                },
                color: {
                  type: 'string',
                  description: 'Highlight color (hex format)',
                  default: '#ff0000',
                },
              },
              required: ['xpaths'],
            },
          },
        ] as Tool[],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'get_selected_elements': {
            const parsedArgs = getSelectedElementsArgsSchema.parse(args);
            const elements = await this.client.getSelectedElements(parsedArgs);
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(elements, null, 2),
                },
              ],
            };
          }

          case 'get_page_context': {
            const parsedArgs = getPageContextArgsSchema.parse(args);
            const context = await this.client.getPageContext(parsedArgs);
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(context, null, 2),
                },
              ],
            };
          }

          case 'send_ui_comment': {
            const parsedArgs = sendUICommentArgsSchema.parse(args);
            const result = await this.client.sendUIComment(parsedArgs);
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(result, null, 2),
                },
              ],
            };
          }

          case 'get_project_structure': {
            const parsedArgs = getProjectStructureArgsSchema.parse(args);
            const structure = await this.client.getProjectStructure(parsedArgs);
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(structure, null, 2),
                },
              ],
            };
          }

          case 'highlight_elements': {
            const parsedArgs = highlightElementsArgsSchema.parse(args);
            const result = await this.client.highlightElements(parsedArgs);
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(result, null, 2),
                },
              ],
            };
          }

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                error: errorMessage,
                success: false,
              }, null, 2),
            },
          ],
          isError: true,
        };
      }
    });
  }

  private setupErrorHandling() {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  async start() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Stagewise MCP Server running on stdio');
  }
}

// Start the server
async function main() {
  const server = new StagewiseMCPServer();
  await server.start();
}

if (require.main === module) {
  main().catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
}

export { StagewiseMCPServer };
