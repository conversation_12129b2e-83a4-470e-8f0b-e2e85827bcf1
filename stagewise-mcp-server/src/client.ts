/**
 * Stagewise Client
 * 
 * Handles communication with the Stagewise VS Code extension via HTTP API
 */

import axios, { AxiosInstance } from 'axios';
import {
  ConnectionConfig,
  SessionInfo,
  PageContext,
  SelectedElement,
  StagewiseResponse,
  StagewiseConnectionError,
  StagewiseAPIError,
  getSelectedElementsArgsSchema,
  getPageContextArgsSchema,
  sendUICommentArgsSchema,
  getProjectStructureArgsSchema,
  highlightElementsArgsSchema,
} from './types.js';
import { z } from 'zod';

export class StagewiseClient {
  private config: ConnectionConfig;
  private httpClient: AxiosInstance | null = null;
  private sessionInfo: SessionInfo | null = null;
  private connectedPort: number | null = null;

  constructor(config: ConnectionConfig) {
    this.config = config;
  }

  /**
   * Discover and connect to the Stagewise extension
   */
  private async connect(): Promise<void> {
    if (this.httpClient && this.connectedPort) {
      return; // Already connected
    }

    const { host, startPort, endPort, timeout } = this.config;

    // Try to find the extension on available ports
    for (let port = startPort; port <= endPort; port++) {
      try {
        const client = axios.create({
          baseURL: `http://${host}:${port}`,
          timeout,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        // Test connection with ping endpoint
        const response = await client.get('/ping/stagewise');
        if (response.data === 'stagewise') {
          this.httpClient = client;
          this.connectedPort = port;
          
          // Get session info
          await this.refreshSessionInfo();
          
          console.error(`Connected to Stagewise extension on port ${port}`);
          return;
        }
      } catch (error) {
        // Continue trying other ports
        continue;
      }
    }

    throw new StagewiseConnectionError(
      `Could not connect to Stagewise extension on ports ${startPort}-${endPort}`,
      'connection_error'
    );
  }

  /**
   * Refresh session information from the extension
   */
  private async refreshSessionInfo(): Promise<void> {
    if (!this.httpClient) {
      throw new StagewiseConnectionError('Not connected to Stagewise extension');
    }

    try {
      const response = await this.httpClient.post('/srpc/getSessionInfo', {});
      this.sessionInfo = response.data;
    } catch (error: any) {
      throw new StagewiseAPIError(
        'Failed to get session info',
        'session_error',
        error.response?.status
      );
    }
  }

  /**
   * Make an RPC call to the Stagewise extension using SRPC protocol
   */
  private async makeRPCCall(method: string, params: any): Promise<any> {
    await this.connect();

    if (!this.httpClient) {
      throw new StagewiseConnectionError('Not connected to Stagewise extension');
    }

    try {
      // Use SRPC format based on Stagewise's contract
      const response = await this.httpClient.post(`/srpc/${method}`, {
        sessionId: this.sessionInfo?.sessionId,
        ...params,
      });

      if (!response.data.success && response.data.error) {
        throw new StagewiseAPIError(
          response.data.error || 'RPC call failed',
          response.data.errorCode
        );
      }

      return response.data;
    } catch (error: any) {
      if (error instanceof StagewiseAPIError) {
        throw error;
      }

      throw new StagewiseAPIError(
        `RPC call failed: ${error.message}`,
        'rpc_error',
        error.response?.status
      );
    }
  }

  /**
   * Get currently selected DOM elements
   */
  async getSelectedElements(args: z.infer<typeof getSelectedElementsArgsSchema>): Promise<SelectedElement[]> {
    // Since Stagewise doesn't have a direct "get selected elements" RPC method,
    // we'll return a message indicating this needs to be implemented
    throw new StagewiseAPIError(
      'get_selected_elements requires enhancement to Stagewise extension. ' +
      'Currently, selected elements are only available when sending UI comments.',
      'not_implemented'
    );
  }

  /**
   * Get current page context
   */
  async getPageContext(args: z.infer<typeof getPageContextArgsSchema>): Promise<PageContext> {
    // This would need to be implemented as a new RPC method in Stagewise
    throw new StagewiseAPIError(
      'get_page_context requires enhancement to Stagewise extension. ' +
      'This method would expose current page metadata and selected elements.',
      'not_implemented'
    );
  }

  /**
   * Send a UI comment/instruction to the AI agent
   */
  async sendUIComment(args: z.infer<typeof sendUICommentArgsSchema>): Promise<StagewiseResponse> {
    try {
      const result = await this.makeRPCCall('triggerAgentPrompt', {
        prompt: args.comment,
        mode: args.mode,
        files: args.files,
        model: args.model,
      });

      return {
        success: result.result?.success ?? true,
        error: result.result?.error,
        errorCode: result.result?.errorCode,
        data: result.result?.output,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        errorCode: error.code,
      };
    }
  }

  /**
   * Get project structure information
   */
  async getProjectStructure(args: z.infer<typeof getProjectStructureArgsSchema>): Promise<any> {
    // This would need to be implemented as a new RPC method in Stagewise
    throw new StagewiseAPIError(
      'get_project_structure requires enhancement to Stagewise extension. ' +
      'This method would expose workspace file structure and project metadata.',
      'not_implemented'
    );
  }

  /**
   * Highlight elements in the browser
   */
  async highlightElements(args: z.infer<typeof highlightElementsArgsSchema>): Promise<StagewiseResponse> {
    // This would need to be implemented as a custom extension to Stagewise
    throw new StagewiseAPIError(
      'highlight_elements requires enhancement to Stagewise extension. ' +
      'This method would highlight DOM elements in the browser for visual feedback.',
      'not_implemented'
    );
  }

  /**
   * Get current session information
   */
  async getSessionInfo(): Promise<SessionInfo | null> {
    await this.connect();
    return this.sessionInfo;
  }

  /**
   * Check if connected to Stagewise extension
   */
  isConnected(): boolean {
    return this.httpClient !== null && this.connectedPort !== null;
  }

  /**
   * Disconnect from the extension
   */
  disconnect(): void {
    this.httpClient = null;
    this.connectedPort = null;
    this.sessionInfo = null;
  }
}
