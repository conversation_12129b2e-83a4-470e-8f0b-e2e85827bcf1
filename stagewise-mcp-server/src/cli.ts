#!/usr/bin/env node

/**
 * CLI entry point for Stagewise MCP Server
 */

import { StagewiseMCPServer } from './server.js';
import { ConnectionConfig, connectionConfigSchema } from './types.js';

function parseArgs(): { config?: Partial<ConnectionConfig>; help?: boolean } {
  const args = process.argv.slice(2);
  const result: { config?: Partial<ConnectionConfig>; help?: boolean } = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg === '--help' || arg === '-h') {
      result.help = true;
    } else if (arg === '--config' || arg === '-c') {
      const configStr = args[i + 1];
      if (configStr) {
        try {
          result.config = JSON.parse(configStr);
          i++; // Skip next argument as it was the config
        } catch (error) {
          console.error('❌ Invalid JSON config:', error instanceof Error ? error.message : String(error));
          process.exit(1);
        }
      }
    } else if (arg === '--host') {
      const host = args[i + 1];
      if (host) {
        result.config = { ...result.config, host };
        i++;
      }
    } else if (arg === '--port') {
      const portStr = args[i + 1];
      if (portStr) {
        const port = parseInt(portStr);
        if (!isNaN(port)) {
          result.config = { ...result.config, startPort: port, endPort: port };
        }
        i++;
      }
    } else if (arg === '--timeout') {
      const timeoutStr = args[i + 1];
      if (timeoutStr) {
        const timeout = parseInt(timeoutStr);
        if (!isNaN(timeout)) {
          result.config = { ...result.config, timeout };
        }
        i++;
      }
    }
  }

  return result;
}

function printHelp() {
  console.log(`
🔧 Stagewise MCP Server

USAGE:
  stagewise-mcp-server [OPTIONS]

OPTIONS:
  --help, -h              Show this help message
  --config, -c <json>     JSON configuration string
  --host <host>           Stagewise extension host (default: localhost)
  --port <port>           Specific port to connect to (default: search 5746-5756)
  --timeout <ms>          Connection timeout in milliseconds (default: 5000)

EXAMPLES:
  # Start with default configuration
  stagewise-mcp-server

  # Connect to specific port
  stagewise-mcp-server --port 5747

  # Use custom configuration
  stagewise-mcp-server --config '{"host":"localhost","startPort":5746,"timeout":10000}'

  # Connect to remote host
  stagewise-mcp-server --host ************* --port 5746

CONFIGURATION:
  The server accepts the following configuration options:
  - host (default: localhost): Stagewise extension host
  - startPort (default: 5746): Starting port to search for extension
  - endPort (default: 5756): Ending port to search for extension
  - timeout (default: 5000): Connection timeout in milliseconds
  - retryAttempts (default: 3): Number of retry attempts
  - retryDelay (default: 1000): Delay between retries in milliseconds

SETUP:
  To set up the server with Augment Code, run:
  npm run setup

TESTING:
  To test the connection to Stagewise, run:
  npm run test:connection

For more information, visit: https://github.com/augmentcode/stagewise-mcp-server
`);
}

async function main() {
  const { config, help } = parseArgs();

  if (help) {
    printHelp();
    process.exit(0);
  }

  try {
    // Validate configuration
    const validatedConfig = config ? connectionConfigSchema.parse(config) : undefined;
    
    // Start the server
    const server = new StagewiseMCPServer(validatedConfig);
    await server.start();
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      console.error('❌ Invalid configuration:', error.message);
      console.error('Use --help for configuration options');
    } else {
      console.error('❌ Failed to start server:', error instanceof Error ? error.message : String(error));
    }
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}
